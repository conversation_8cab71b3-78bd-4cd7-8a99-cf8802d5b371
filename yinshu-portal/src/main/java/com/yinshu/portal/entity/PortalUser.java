package com.yinshu.portal.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 门户用户实体
 */
@Data
@TableName("portal_user")
public class PortalUser implements Serializable {
    @TableId(type = IdType.INPUT)
    private String id;
    private String userName;
    private String loginName;
    private String password;
    private String unitId;
    private Date createTime;
    private String createUser;
    private Date updateTime;
    private String phone;
    private Long deptId;
    private String deptName;
    private String unitName;
    private Integer dataSource;
    private Integer state;
} 