package com.yinshu.portal.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 应用令牌表
 */
@Data
@TableName("portal_app_ticket")
public class PortalAppTicket implements Serializable {
    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 令牌 */
    private String ticket;
    /** 应用ID */
    private Long appId;
    /** 状态：0-未使用 1-已使用 */
    private Integer status;
    /** 使用时间 */
    private Date useTime;
    /** 创建时间 */
    private Date createTime;
    /** 创建人 */
    private String creator;
} 