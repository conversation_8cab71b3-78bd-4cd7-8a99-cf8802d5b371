package com.yinshu.portal.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.yinshu.utils.PageParam;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 门户应用实体
 *
 * <AUTHOR>
 */
@Data
@TableName("portal_application")
public class PortalApplication extends PageParam<PortalApplication>  implements Serializable {
    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 门户子系统名称 */
    private String appName;
    /** 应用图标URL */
    private String appIcon;
    /** API授权码 */
    private String authCode;
    /** API授权密码 */
    private String authPsw;
    /** 子系统路径 */
    private String appHomeUrl;
    /** 状态：0-禁用 1-启用 */
    private Integer status;
    /** 创建时间 */
    private Date createTime;
    /** 更新时间 */
    private Date updateTime;
    /** 创建人 */
    private String creator;
    /** 更新人 */
    private String updater;
    /** 备注 */
    private String remark;
} 