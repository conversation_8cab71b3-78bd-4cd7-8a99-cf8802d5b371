package com.yinshu.portal.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;

/**
 * 角色应用授权表实体
 *
 * <AUTHOR>
 */
@Data
@TableName("portal_role_app")
public class PortalRoleApp implements Serializable {
    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 角色ID */
    private Long roleId;
    /** 应用ID */
    private Long appId;
} 