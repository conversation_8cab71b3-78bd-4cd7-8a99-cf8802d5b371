package com.yinshu.portal.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 门户机构实体
 *
 * <AUTHOR>
 */
@Data
@TableName("portal_unit")
public class PortalUnit implements Serializable {
    /** 主键ID */
    @TableId(type = IdType.INPUT)
    private String id;
    /** 机构代码 */
    private String ucode;
    /** 机构名称 */
    private String uname;
    /** 父ID */
    private String parentId;
    /** 机构路径 */
    private String unitPath;
    /** 排序 */
    private Integer usort;
    /** 创建时间 */
    private Date createTime;
    /** 对应监控列表目录 */
    private String cameraUnit;
    /** 机构类型: Account 集团、单位 Department 部门 */
    private String type;
    /** 是否是集团 1:是 0:否 */
    private Integer isGroup;
    /** 路径 */
    private String path;
    /** 数据来源 1=系统内部 0=第三方同步 */
    private Integer dataSource;
} 