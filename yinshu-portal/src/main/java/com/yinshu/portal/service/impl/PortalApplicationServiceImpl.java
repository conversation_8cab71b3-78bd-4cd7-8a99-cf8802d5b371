package com.yinshu.portal.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.portal.dao.PortalApplicationMapper;
import com.yinshu.portal.entity.PortalApplication;
import com.yinshu.portal.service.PortalApplicationService;
import org.springframework.stereotype.Service;

@Service
public class PortalApplicationServiceImpl extends ServiceImpl<PortalApplicationMapper, PortalApplication> implements PortalApplicationService {

    @Override
    public IPage<PortalApplication> queryPageList(PortalApplication entity) {
        return this.baseMapper.queryPageList(entity.toPage(), entity);
    }

    @Override
    public PortalApplication getByAuthCode(String authCode) {
        return this.baseMapper.getByAuthCode(authCode);
    }
}