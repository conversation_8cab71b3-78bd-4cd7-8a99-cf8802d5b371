package com.yinshu.portal.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.portal.dao.PortalAppTicketMapper;
import com.yinshu.portal.entity.PortalAppTicket;
import com.yinshu.portal.service.PortalAppTicketService;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class PortalAppTicketServiceImpl extends ServiceImpl<PortalAppTicketMapper, PortalAppTicket> implements PortalAppTicketService {
    @Override
    public void updateByAppIdAndTicket(Long appId, String ticket) {
        PortalAppTicket info =  this.baseMapper.selectInfoByAppIdAndTicket(appId,ticket);
        if(ObjectUtil.isEmpty(info)){
            throw new RuntimeException("令牌不存在或已使用");
        }
        info.setStatus(1);
        info.setUseTime(new Date());
        this.baseMapper.updateById(info);
    }
} 