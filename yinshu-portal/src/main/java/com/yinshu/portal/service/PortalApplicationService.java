package com.yinshu.portal.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yinshu.portal.entity.PortalApplication;

public interface PortalApplicationService extends IService<PortalApplication> {

    IPage<PortalApplication> queryPageList(PortalApplication entity);

    /***
     * 根据授权码信息查询出授权应用详情
     * @param authCode
     * @return
     */
    PortalApplication getByAuthCode(String authCode);
}