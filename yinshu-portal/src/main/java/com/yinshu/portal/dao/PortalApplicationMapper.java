package com.yinshu.portal.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinshu.portal.entity.PortalApplication;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.Map;

public interface PortalApplicationMapper extends BaseMapper<PortalApplication> {

    IPage<PortalApplication> queryPageList(Page<PortalApplication> page, @Param("params") PortalApplication params);

    PortalApplication getByAuthCode(@Param("authCode")String authCode);
}