package com.yinshu.portal.controller;

import com.yinshu.common.ApiConstant;
import com.yinshu.portal.entity.PortalRoleApp;
import com.yinshu.portal.service.PortalRoleAppService;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(ApiConstant.API_PORTAL_PREFIX +"/roleApp")
public class PortalRoleAppController {
    @Resource
    private PortalRoleAppService portalRoleAppService;

    @PostMapping("/save")
    public ResultVO<Boolean> save(@RequestBody PortalRoleApp roleApp) {
        boolean result = portalRoleAppService.save(roleApp);
        return ResultVO.suc(result);
    }

    @PostMapping("/delete")
    public ResultVO<Boolean> delete(@RequestBody PortalRoleApp roleApp) {
        boolean result = portalRoleAppService.removeById(roleApp.getId());
        return ResultVO.suc(result);
    }

    @PostMapping("/update")
    public ResultVO<Boolean> update(@RequestBody PortalRoleApp roleApp) {
        boolean result = portalRoleAppService.updateById(roleApp);
        return ResultVO.suc(result);
    }

    @PostMapping("/list")
    public ResultVO<List<PortalRoleApp>> list() {
        List<PortalRoleApp> list = portalRoleAppService.list();
        return ResultVO.suc(list);
    }
} 