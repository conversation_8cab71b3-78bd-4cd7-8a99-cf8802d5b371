package com.yinshu.portal.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.common.ApiConstant;
import com.yinshu.portal.entity.PortalApplication;
import com.yinshu.portal.service.PortalApplicationService;
import com.yinshu.sys.security.SessionUserUtils;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping(ApiConstant.API_PORTAL_PREFIX +"/application")
public class PortalApplicationController {
    @Resource
    private PortalApplicationService portalApplicationService;


    @PostMapping("/pageList")
    public ResultVO<?> pageList(@RequestBody PortalApplication entity) {
        IPage<PortalApplication> resultList = portalApplicationService.queryPageList(entity);
        return new ResultVO<>(resultList);
    }

    @PostMapping("/save")
    public ResultVO<Boolean> save(@RequestBody PortalApplication app) {
        app.setCreator(SessionUserUtils.getUserId());
        app.setAuthCode(java.util.UUID.randomUUID().toString().replace("-", "").substring(0, 16));
        app.setAuthPsw(java.util.UUID.randomUUID().toString().replace("-", "").substring(0, 24));
        boolean result = portalApplicationService.save(app);
        return ResultVO.suc(result);
    }

    @PostMapping("/delete")
    public ResultVO<Boolean> delete(@RequestBody PortalApplication app) {
        boolean result = portalApplicationService.removeById(app.getId());
        return ResultVO.suc(result);
    }

    @PostMapping("/update")
    public ResultVO<Boolean> update(@RequestBody PortalApplication app) {
        app.setUpdateTime(new Date());
        app.setUpdater(SessionUserUtils.getUserId());
        boolean result = portalApplicationService.updateById(app);
        return ResultVO.suc(result);
    }

    @PostMapping("/list")
    public ResultVO<List<PortalApplication>> list() {
        List<PortalApplication> list = portalApplicationService.list();
        return ResultVO.suc(list);
    }
} 