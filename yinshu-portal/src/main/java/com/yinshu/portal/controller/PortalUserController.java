package com.yinshu.portal.controller;

import com.yinshu.common.ApiConstant;
import com.yinshu.portal.entity.PortalUser;
import com.yinshu.portal.service.PortalUserService;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(ApiConstant.API_PORTAL_PREFIX +"/user")
public class PortalUserController {
    @Resource
    private PortalUserService portalUserService;

    @PostMapping("/save")
    public ResultVO<Boolean> save(@RequestBody PortalUser user) {
        boolean result = portalUserService.save(user);
        return ResultVO.suc(result);
    }

    @PostMapping("/delete")
    public ResultVO<Boolean> delete(@RequestBody PortalUser user) {
        boolean result = portalUserService.removeById(user.getId());
        return ResultVO.suc(result);
    }

    @PostMapping("/update")
    public ResultVO<Boolean> update(@RequestBody PortalUser user) {
        boolean result = portalUserService.updateById(user);
        return ResultVO.suc(result);
    }

    @PostMapping("/list")
    public ResultVO<List<PortalUser>> list() {
        List<PortalUser> list = portalUserService.list();
        return ResultVO.suc(list);
    }
} 