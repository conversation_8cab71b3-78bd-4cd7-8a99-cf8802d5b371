package com.yinshu.portal.controller;

import com.yinshu.common.ApiConstant;
import com.yinshu.portal.entity.PortalUnit;
import com.yinshu.portal.service.PortalUnitService;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(ApiConstant.API_PORTAL_PREFIX +"/unit")
public class PortalUnitController {
    @Resource
    private PortalUnitService portalUnitService;

    @PostMapping("/save")
    public ResultVO<Boolean> save(@RequestBody PortalUnit unit) {
        boolean result = portalUnitService.save(unit);
        return ResultVO.suc(result);
    }

    @PostMapping("/delete")
    public ResultVO<Boolean> delete(@RequestBody PortalUnit unit) {
        boolean result = portalUnitService.removeById(unit.getId());
        return ResultVO.suc(result);
    }

    @PostMapping("/update")
    public ResultVO<Boolean> update(@RequestBody PortalUnit unit) {
        boolean result = portalUnitService.updateById(unit);
        return ResultVO.suc(result);
    }

    @PostMapping("/list")
    public ResultVO<List<PortalUnit>> list() {
        List<PortalUnit> list = portalUnitService.list();
        return ResultVO.suc(list);
    }
} 