package com.yinshu.portal.controller;

import com.yinshu.common.ApiConstant;
import com.yinshu.portal.entity.PortalRole;
import com.yinshu.portal.service.PortalRoleService;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(ApiConstant.API_PORTAL_PREFIX +"/role")
public class PortalRoleController {
    @Resource
    private PortalRoleService portalRoleService;

    @PostMapping("/save")
    public ResultVO<Boolean> save(@RequestBody PortalRole role) {
        boolean result = portalRoleService.save(role);
        return ResultVO.suc(result);
    }

    @PostMapping("/delete")
    public ResultVO<Boolean> delete(@RequestBody PortalRole role) {
        boolean result = portalRoleService.removeById(role.getId());
        return ResultVO.suc(result);
    }

    @PostMapping("/update")
    public ResultVO<Boolean> update(@RequestBody PortalRole role) {
        boolean result = portalRoleService.updateById(role);
        return ResultVO.suc(result);
    }

    @PostMapping("/list")
    public ResultVO<List<PortalRole>> list() {
        List<PortalRole> list = portalRoleService.list();
        return ResultVO.suc(list);
    }
} 