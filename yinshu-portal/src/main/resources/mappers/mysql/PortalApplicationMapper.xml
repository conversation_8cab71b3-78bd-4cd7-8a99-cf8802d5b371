<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.portal.dao.PortalApplicationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yinshu.portal.entity.PortalApplication">
        <id column="id" property="id" />
        <result column="app_name" property="appName" />
        <result column="app_icon" property="appIcon" />
        <result column="auth_code" property="authCode" />
        <result column="auth_psw" property="authPsw" />
        <result column="app_home_url" property="appHomeUrl" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />

        <result column="update_time" property="updateTime" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="remark" property="remark" />

    </resultMap>

    <select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.portal.entity.PortalApplication">
        SELECT * FROM portal_application
        <where>
            <if test="params.appName != null and params.appName != ''">
                AND app_name LIKE CONCAT('%', #{params.appName}, '%')
            </if>
            <if test="params.status != null">
                AND status = #{params.status}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
    <select id="getByAuthCode" resultType="com.yinshu.portal.entity.PortalApplication">
        SELECT * FROM portal_application
        WHERE auth_code = #{authCode} and status = 1
    </select>

</mapper>