<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.portal.dao.PortalAppTicketMapper">
    <resultMap id="BaseResultMap" type="com.yinshu.portal.entity.PortalAppTicket">
        <id column="id" property="id" />
        <result column="ticket" property="ticket" />
        <result column="app_id" property="appId" />
        <result column="status" property="status" />
        <result column="use_time" property="useTime" />
        <result column="create_time" property="createTime" />
        <result column="creator" property="creator" />
    </resultMap>
    <select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.portal.entity.PortalAppTicket">
        SELECT * FROM portal_app_ticket
        <where>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="appId != null">
                AND app_id = #{appId}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
    <select id="selectInfoByAppIdAndTicket" resultType="com.yinshu.portal.entity.PortalAppTicket">
        SELECT * FROM portal_app_ticket
        where  status = 0 and  app_id = #{appId} and ticket = #{ticket}
    </select>
</mapper> 