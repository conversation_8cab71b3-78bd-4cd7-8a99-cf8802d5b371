# tocc-admin-boot


## 快速开始

忻州交通运行监测平台后端。

## 获取文件

使用命令行获取文件:
```
git clone -b dev http://192.168.3.235/xz-tocc/yxjc.git
```

## 项目结构
```text
tocc-admin-root/
    |--tocc-all-system-common  # 所有业务系统的公用依赖
    |--xinzhou-toms            # 运行监测系统
    |--xinzhou-tdss            # 辅助决策系统
    |--xinzhou-mcu             # 运行监测系统-摄像头模块
    |--yinshu-fast-excel-util  # excel工具
    |--tocc-admin-all-starter  # 所有系统的入口启动程序
    |--yinshu-common           # 通用配置、工具、拦截器等
    |--yinshu-sys              # MVC、Entities、以及一些配置
```

## 安装

请确保您的设备已经预置安装 JDK 1.8、Maven、以及 IntelliJ IDEA。
- [ ] 使用 IDEA 打开项目目录。
- [ ] 下载 Maven 依赖。
- [ ] 您可根据个人喜好进行项目配置，但不要将 `.idea` 目录上传到云端。

## 使用

- 点击调试模式启动项目
- 编辑程序来完成你的提案
- 调试你的程序
- 提交你的贡献

## 贡献
- [ ] 创建、修改、或删除文件来完成你的提案
- [ ] 将提案添加到工作树中
  ```shell
  git add tocc
  git add .
  # ...
  ```
- [ ] 创建一个 commit
  ```shell
  git commit
  # 或者直接添加备注
  git commit -m 'feat: 实现用户管理模块'
  ```
- [ ] 推送到云端
    ```shell
    git push
    ```

## 取得支持

有问题可以在 Gitlab 提交 issue，或者直接在公司群沟通。

## 路线

如果有要好点子要发布，可以在这里列出：

## 与团队协作

- [ ] [邀请团队成员协作](https://docs.gitlab.com/ee/user/project/members/)
- [ ] [创建一个新的 merge request](https://docs.gitlab.com/ee/user/project/merge_requests/creating_merge_requests.html)
- [ ] [从 merge requests 自动关闭 issues](https://docs.gitlab.com/ee/user/project/issues/managing_issues.html#closing-issues-automatically)
- [ ] [启用 merge request 审核](https://docs.gitlab.com/ee/user/project/merge_requests/approvals/)
- [ ] [Pipeline 成功时自动合并](https://docs.gitlab.com/ee/user/project/merge_requests/merge_when_pipeline_succeeds.html)

## 测试与部署

使用 Gitlab 内置的持续集成（CI.

- [ ] [开始使用 GitLab CI/CD](https://docs.gitlab.com/ee/ci/quick_start/index.html)
- [ ] [Analyze your code for known vulnerabilities with Static Application Security Testing(SAST)](https://docs.gitlab.com/ee/user/application_security/sast/)
- [ ] [Deploy to Kubernetes, Amazon EC2, or Amazon ECS using Auto Deploy](https://docs.gitlab.com/ee/topics/autodevops/requirements.html)
- [ ] [Use pull-based deployments for improved Kubernetes management](https://docs.gitlab.com/ee/user/clusters/agent/)
- [ ] [Set up protected environments](https://docs.gitlab.com/ee/ci/environments/protected_environments.html)

***
