<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.sys.dao.MenuDao">

    <resultMap id="BaseResultMap" type="com.yinshu.sys.entity.Menu">
        <id column="id" property="id" />
        <result column="parent_id" property="parentId" />
        <result column="menu_name" property="menuName" />
        <result column="menu_icon" property="menuIcon" />
        <result column="menu_sort" property="menuSort" />
        <result column="menu_url" property="menuUrl" />
        <result column="component" property="component" />
        <result column="perms" property="perms" />
        <result column="status" property="status" />
        <result column="is_visible" property="isVisible" />
        <result column="menu_type" property="menuType" />
        <collection property="persList" ofType="com.yinshu.sys.entity.MenuPermission">
			<id column="permission_id" property="id" />
			<result column="action_name" property="actionName" />
        	<result column="action_url" property="actionUrl" />
        	<result column="menu_id" property="menuId" />
		</collection>
    </resultMap>
    
    <select id="queryPageList" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select * from s_menu 
		<where>
			<if test="param.keyword != null and param.keyword != '' ">
				and menu_name like concat(concat('%',#{param.keyword}),'%')
			</if>
			<if test="param.parentId != null and param.parentId != '' ">
				and parent_id = #{param.parentId}
			</if>
			<if test="param.plevel != null and param.plevel == 1 ">
				and parent_id is null or parent_id = ''
			</if>
			<if test="param.status != null and param.status != '' ">
				and status = #{param.status}
			</if>
		</where>
		order by menu_sort
	</select>

	<select id="queryList" resultMap="BaseResultMap">
		select * from s_menu order by MENU_SORT
	</select>

	<!-- 自定义查询方法 -->
	<select id="findById" parameterType="java.lang.String" resultMap="BaseResultMap">
		select a.*,b.id permission_id, b.action_name, b.action_url, b.menu_id
		from s_menu a left join s_menu_permission b  on a.id = b.menu_id 
		where a.id = #{id}
		order by b.create_time
	</select>

	<select id="queryMenuPermissionTreeList" resultMap="BaseResultMap">
		select a.*,b.id permission_id, b.action_name, b.action_url
		from s_menu a left join s_menu_permission b  on a.id = b.menu_id
		where a.status = '1'
		order by a.menu_sort
	</select>
</mapper>
