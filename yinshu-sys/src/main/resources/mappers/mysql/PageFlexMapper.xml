<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.sys.dao.PageFlexDao">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.yinshu.sys.entity.PageFlex">
		<id column="id" property="id" />
		<result column="page_name" property="pageName" />
		<result column="page_code" property="pageCode" />
		<result column="page_type" property="pageType" />
		<result column="col_num" property="colNum" />
		<result column="row_height" property="rowHeight" />
		<result column="width" property="width" />
		<result column="height" property="height" />
		<result column="draggable" property="draggable" />
		<result column="resizable" property="resizable" />
		<result column="page_params" property="pageParams" />
		<result column="remark" property="remark" />
		<result column="sort" property="sort" />
		<result column="status" property="status" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
        id, page_name, page_code, page_type, col_num, row_height, width, height, draggable, resizable, page_params, remark, sort, status
    </sql>


	<select id="queryList" resultMap="BaseResultMap" parameterType="com.yinshu.sys.entity.PageFlex">
		select <include refid="Base_Column_List"></include>
		from s_page_flex
		<where>
			<if test="entity.id != null and entity.id != '' ">
				and id = #{entity.id}
			</if>
			<if test="entity.pageName != null and entity.pageName != '' ">
				and page_name = #{entity.pageName}
			</if>
			<if test="entity.pageCode != null and entity.pageCode != '' ">
				and page_code = #{entity.pageCode}
			</if>
			<if test="entity.pageType != null and entity.pageType != '' ">
				and page_type = #{entity.pageType}
			</if>
			<if test="entity.colNum != null and entity.colNum != '' ">
				and col_num = #{entity.colNum}
			</if>
			<if test="entity.rowHeight != null and entity.rowHeight != '' ">
				and row_height = #{entity.rowHeight}
			</if>
			<if test="entity.width != null and entity.width != '' ">
				and width = #{entity.width}
			</if>
			<if test="entity.height != null and entity.height != '' ">
				and height = #{entity.height}
			</if>
			<if test="entity.draggable != null and entity.draggable != '' ">
				and draggable = #{entity.draggable}
			</if>
			<if test="entity.resizable != null and entity.resizable != '' ">
				and resizable = #{entity.resizable}
			</if>
			<if test="entity.pageParams != null and entity.pageParams != '' ">
				and page_params = #{entity.pageParams}
			</if>
			<if test="entity.remark != null and entity.remark != '' ">
				and remark = #{entity.remark}
			</if>
			<if test="entity.sort != null and entity.sort != '' ">
				and sort = #{entity.sort}
			</if>
			<if test="entity.status != null and entity.status != '' ">
				and status = #{entity.status}
			</if>
		</where>
		order by sort
	</select>

	<!-- 分页查询  -->
	<select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.sys.entity.PageFlex">
		select <include refid="Base_Column_List"></include>
		from s_page_flex
		<where>
			<if test="entity.queryKeyword != null and entity.queryKeyword != '' ">
				(
				<trim prefix="" prefixOverrides="or">
					or page_name like concat(concat('%', #{entity.queryKeyword}), '%')
					or page_code like concat(concat('%', #{entity.queryKeyword}), '%')
					or page_params like concat(concat('%', #{entity.queryKeyword}), '%')
					or remark like concat(concat('%', #{entity.queryKeyword}), '%')
				</trim>
				)
			</if>
		</where>
		order by sort
	</select>

	<select id="check" resultMap="BaseResultMap" parameterType="com.yinshu.sys.entity.PageComponent">
		select <include refid="Base_Column_List"></include>
		from s_page_flex
		<where>
			<if test="id != null and id != '' ">
				AND id != #{id}
			</if>
			AND (page_code = #{pageCode})
		</where>
		order by sort
	</select>

	<select id="getMaxSort" resultType="Integer" >
		select max(sort) max_sort from s_page_flex
	</select>

</mapper>