<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.sys.dao.MenuPermissionDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yinshu.sys.entity.MenuPermission">
        <id column="id" property="id" />
        <result column="menu_id" property="menuId" />
        <result column="action_type" property="actionType" />
        <result column="action_name" property="actionName" />
        <result column="action_url" property="actionUrl" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, menu_id, action_type, action_name, action_url, create_time, create_user, update_time
    </sql>


	<!-- 分页查询  -->
	<select id="queryPageList" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select * from s_menu_permission 
		<where>
			<if test="param.keyword != null and param.keyword != '' ">
				
			</if>
		</where>
	</select>
	
	<select id="queryList" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select * from s_menu_permission 
		<where>
			<if test="param.id != null and param.id != '' ">
				and id = #{param.id}
			</if>
			<if test="param.menuId != null and param.menuId != '' ">
				and menu_id = #{param.menuId}
			</if>
			<if test="param.actionType != null and param.actionType != '' ">
				and action_type = #{param.actionType}
			</if>
			<if test="param.actionName != null and param.actionName != '' ">
				and action_name = #{param.actionName}
			</if>
			<if test="param.actionUrl != null and param.actionUrl != '' ">
				and action_url = #{param.actionUrl}
			</if>
		</where>
		order by create_time
	</select>


</mapper>