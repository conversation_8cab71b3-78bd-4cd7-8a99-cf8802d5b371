<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.sys.dao.RoleDao">

    <resultMap id="BaseResultMap" type="com.yinshu.sys.entity.Role">
        <id column="id" property="id" />
		<result column="role_code" property="roleCode" />
		<result column="role_name" property="roleName" />
		<!-- <result column="unitid" property="unitid" /> -->
		<result column="create_time" property="createTime" />
		<result column="role_description" property="roleDescription" />
        <!-- <association property="unit" javaType="com.yinshu.sys.entity.Unit">
			<id column="UNITID" property="id" />
			<result column="UNAME" property="uname" />
			<result column="PARENTID" property="parentid" />
			<result column="UCODE" property="ucode" />
			<result column="USORT" property="usort" />
	        <result column="MCU" property="mcu" />
	        <result column="LOCATION" property="location" />
		</association> -->
    </resultMap>
    
    <select id="queryPageList" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select r.*,u.uname from s_role r left join s_unit u on u.id=r.unitid
		<where>
			<if test="param.keyword != null and param.keyword != '' ">
				role_name like concat(concat('%',#{param.keyword}),'%')
			</if>
			<if test="param.unitid != null and param.unitid != '' ">
				and unitid = #{param.unitid}
			</if>
			 <if test="param.unitpath != null and param.unitpath != ''">
				and unitpath like concat(concat('%',#{param.unitpath}),'%')
			</if>
		</where>
		order by create_time
	</select>

	<select id="queryParentIdIsNull" resultType="java.lang.String">
		select id from s_unit where PARENTID is null
	</select>
</mapper>
