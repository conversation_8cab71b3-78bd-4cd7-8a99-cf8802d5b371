<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.sys.dao.DictionaryDao">
    <resultMap id="BaseResultMap" type="com.yinshu.sys.entity.Dictionary">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="classify" jdbcType="VARCHAR" property="classify" />
        <result column="dic_name" jdbcType="VARCHAR" property="dicName" />
        <result column="dic_code" jdbcType="VARCHAR" property="dicCode" />
        <result column="parent_code" jdbcType="VARCHAR" property="parentCode" />
        <result column="dic_sort" jdbcType="DECIMAL" property="dicSort" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />

        <result column="children_num" property="childrenNum" />
    </resultMap>
    
    <select id="queryNormalList" resultMap="BaseResultMap" parameterType="com.yinshu.sys.entity.Dictionary">
        select * from s_dictionary
        <where>
            <if test="classify != null and classify != '' ">
                AND classify = #{classify}
            </if>
            <if test="dicCode != null and dicCode != '' ">
                AND dic_code = #{dicCode}
            </if>
            <if test="parentCode != null and parentCode != '' ">
                AND parent_code = #{parentCode}
            </if>
            <if test="status != null and status != '' ">
                AND status = #{status}
            </if>
            <if test="queryKeyword != null and queryKeyword != '' ">
                AND (
                dic_name like concat(concat('%', #{entity.queryKeyword}), '%')
                or dic_code like concat(concat('%', #{entity.queryKeyword}), '%')
                )
            </if>
        </where>
        order by classify, dic_sort
    </select>

    <select id="queryList" resultMap="BaseResultMap" parameterType="com.yinshu.sys.entity.Dictionary">
        select id, classify, dic_name, dic_code, parent_code, dic_sort, status, remark, create_time, update_time,
        (select count(id) children_num from s_dictionary dict
        where dict.parent_code = s_dictionary.dic_code) AS children_num
        from s_dictionary
        <where>
            <if test="classify != null and classify != '' ">
                AND classify = #{classify}
            </if>
            <if test="dicCode != null and dicCode != '' ">
                AND dic_code = #{dicCode}
            </if>
            <choose>
                <when test="parentCode != null and parentCode != '' ">
                    AND parent_code = #{parentCode}
                </when>
                <otherwise>
                    AND parent_code is null
                </otherwise>
            </choose>
            <if test="status != null and status != '' ">
                AND status = #{status}
            </if>
            <if test="queryKeyword != null and queryKeyword != '' ">
                AND (
                dic_name like concat(concat('%', #{entity.queryKeyword}), '%')
                or dic_code like concat(concat('%', #{entity.queryKeyword}), '%')
                )
            </if>
        </where>
        order by classify, dic_sort
    </select>

    <select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.sys.entity.Dictionary">
        select id, classify, dic_name, dic_code, parent_code, dic_sort, status, remark, create_time,
        update_time
        from s_dictionary
        <where>
            <if test="entity.classify != null and entity.classify != '' ">
                AND classify = #{entity.classify}
            </if>
            <choose>
                <when test="entity.parentCode != null and entity.parentCode != '' ">
                    AND parent_code = #{entity.parentCode}
                </when>
                <otherwise>
                    AND parent_code is null
                </otherwise>
            </choose>
            <if test="entity.status != null and entity.status != '' ">
                AND status = #{entity.status}
            </if>
            <if test="entity.queryKeyword != null and entity.queryKeyword != '' ">
                AND (
                dic_name like concat(concat('%', #{entity.queryKeyword}), '%')
                or dic_code like concat(concat('%', #{entity.queryKeyword}), '%')
                )
            </if>
        </where>
        order by classify, dic_sort
    </select>

    <select id="queryTreeAllList" resultMap="BaseResultMap" parameterType="com.yinshu.sys.entity.Dictionary">
        select id, classify, dic_name, dic_code, parent_code, dic_sort, status, remark, create_time,
        update_time
        from s_dictionary
        <where>
            <if test="classify != null and classify != '' ">
                AND classify = #{classify}
            </if>
            <if test="status != null and status != '' ">
                AND status = #{status}
            </if>
        </where>
        order by classify, dic_sort
    </select>

    <select id="check" resultMap="BaseResultMap" parameterType="com.yinshu.sys.entity.Dictionary">
        select id, classify, dic_name, dic_code, parent_code, dic_sort, status, remark, create_time,
        update_time
        from s_dictionary
        <where>
            <if test="id != null and id != '' ">
                AND id != #{id}
            </if>
            AND dic_code = #{dicCode}
        </where>
        order by classify, dic_sort
    </select>

    <update id="updateByParentCode">
        update s_dictionary
        set parent_code = #{newParentCode,jdbcType=VARCHAR}
        where parent_code = #{oldParentCode}
    </update>

    <delete id="removeByParentCode"  parameterType="com.yinshu.sys.entity.Dictionary">
        delete from s_dictionary where parent_code = #{parentCode}
    </delete>

    <select id="getMaxSort" resultType="Integer" parameterType="com.yinshu.sys.entity.Dictionary">
        select max(dic_sort) max_sort
        from s_dictionary
        <where>
            <if test="classify != null and classify != '' ">
                AND classify = #{classify}
            </if>
            <choose>
                <when test="parentCode != null and parentCode != '' ">
                    AND parent_code = #{parentCode}
                </when>
                <otherwise>
                    AND parent_code is null
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="getByDicCodeSingle" resultMap="BaseResultMap" parameterType="String">
        select id, classify, dic_name, dic_code, parent_code, dic_sort, status, remark, create_time, update_time
        from s_dictionary
        where dic_code = #{dicCode}
        limit 1
    </select>
</mapper>
