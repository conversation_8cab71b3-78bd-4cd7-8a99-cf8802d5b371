<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.sys.dao.UnitDao">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.yinshu.sys.entity.Unit">
		<id column="ID" property="id" />
		<result column="uname" property="uname" />
		<result column="parent_id" property="parentId" />
		<result column="ucode" property="ucode" />
		<result column="usort" property="usort" />
		<result column="unit_path" property="unitPath" />
		<result column="create_time" property="createTime" />

	</resultMap>

	<select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.sys.entity.Unit">
		select * from s_unit
		<where>
			<if test="param.keyword != null and param.keyword != '' ">
				AND UNAME like concat(concat('%',#{param.keyword}),'%')
			</if>
			<if test="param.uname != null and param.uname != '' ">
				AND uname like concat(concat('%',#{param.uname}),'%')
			</if>
 			<if test="param.parentId != null and param.parentId != '' ">
				AND parent_id = #{param.parentId}
			</if>
			<!--<if test="param.topId!=null and param.topId">
				AND UNITPATH like concat(concat('%',#{param.topId}),'%')
			</if> -->
		</where>
		order by usort
	</select>

 	<select id="queryList" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select * from s_unit
		<where>
			<if test="param.ucode != null and param.ucode != '' ">
				AND UCODE = #{param.ucode}
			</if>
			<if test="param.parentid != null and param.parentid != ''">
				AND PARENTID is null
			</if>
			<if test="param.topId!=null and param.topId">
				AND UNITPATH like concat(concat('%',#{param.topId}),'%')
			</if>
			<if test="param.type!=null and param.type != ''">
				AND TYPE = #{param.type}
			</if>
		</where>
		order by usort
	</select> 
	
	<select id="queryTreeList" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select id, uname, parent_id from s_unit
		<where>
			<if test="param.ucode != null and param.ucode != '' ">
				AND ucode = #{param.ucode}
			</if>
			<if test="param.parentId != null and param.parentId != ''">
				AND parent_id is null
			</if>
<!-- 			<if test="param.topId!=null and param.topId">
				AND unit_path like concat(concat('%',#{param.topId}),'%')
			</if> -->
		</where>
		order by usort
	</select> 
	
	
	
</mapper>
