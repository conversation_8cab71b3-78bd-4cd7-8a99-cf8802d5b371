<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.sys.dao.JobTaskDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yinshu.sys.entity.JobTask">
        <id column="id" property="id" />
        <result column="task_name" property="taskName" />
        <result column="cron_expression" property="cronExpression" />
        <result column="bean_name" property="beanName" />
        <result column="method_name" property="methodName" />
        <result column="status" property="status" />
        <result column="description" property="description" />
        <result column="task_execute_flag" property="taskExecuteFlag" />
		<result column="task_error_info" property="taskErrorInfo" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_name, cron_expression, bean_name, method_name, status, description, task_execute_flag, task_error_info, create_time, update_time
    </sql>


	<select id="queryList" resultMap="BaseResultMap" parameterType="com.yinshu.sys.entity.JobTask">
		select <include refid="Base_Column_List"></include>
		from s_job_task
		<where>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.taskName != null and entity.taskName != '' ">
					and task_name = #{entity.taskName}
				</if>
				<if test="entity.cronExpression != null and entity.cronExpression != '' ">
					and cron_expression = #{entity.cronExpression}
				</if>
				<if test="entity.beanName != null and entity.beanName != '' ">
					and bean_name = #{entity.beanName}
				</if>
				<if test="entity.methodName != null and entity.methodName != '' ">
					and method_name = #{entity.methodName}
				</if>
				<if test="entity.status != null and entity.status != '' ">
					and status = #{entity.status}
				</if>
				<if test="entity.description != null and entity.description != '' ">
					and description = #{entity.description}
				</if>
				<if test="entity.taskExecuteFlag != null and entity.taskExecuteFlag != '' ">
					and task_execute_flag = #{entity.taskExecuteFlag}
				</if>
				<if test="entity.taskErrorInfo != null and entity.taskErrorInfo != '' ">
					and task_error_info = #{entity.taskErrorInfo}
				</if>
				<if test="entity.createTime != null and entity.createTime != '' ">
					and create_time = #{entity.createTime}
				</if>
				<if test="entity.updateTime != null and entity.updateTime != '' ">
					and update_time = #{entity.updateTime}
				</if>
		</where>
	</select>

	<!-- 分页查询  -->
	<select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.sys.entity.JobTask">
		select <include refid="Base_Column_List"></include>
		from s_job_task
		<where>
			<if test="entity.keyword != null and entity.keyword != '' ">
				(
				<trim prefix="" prefixOverrides="or">
						or task_name like concat(concat('%', #{entity.keyword}), '%')
						or cron_expression like concat(concat('%', #{entity.keyword}), '%')
						or bean_name like concat(concat('%', #{entity.keyword}), '%')
						or method_name like concat(concat('%', #{entity.keyword}), '%')
						or description like concat(concat('%', #{entity.keyword}), '%')
						or task_execute_flag like concat(concat('%', #{entity.keyword}), '%')
				</trim>
				)
			</if>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.taskName != null and entity.taskName != '' ">
					and task_name = #{entity.taskName}
				</if>
				<if test="entity.cronExpression != null and entity.cronExpression != '' ">
					and cron_expression = #{entity.cronExpression}
				</if>
				<if test="entity.beanName != null and entity.beanName != '' ">
					and bean_name = #{entity.beanName}
				</if>
				<if test="entity.methodName != null and entity.methodName != '' ">
					and method_name = #{entity.methodName}
				</if>
				<if test="entity.status != null and entity.status != '' ">
					and status = #{entity.status}
				</if>
				<if test="entity.description != null and entity.description != '' ">
					and description = #{entity.description}
				</if>
				<if test="entity.taskExecuteFlag != null and entity.taskExecuteFlag != '' ">
					and task_execute_flag = #{entity.taskExecuteFlag}
				</if>
				<if test="entity.createTime != null and entity.createTime != '' ">
					and create_time = #{entity.createTime}
				</if>
				<if test="entity.updateTime != null and entity.updateTime != '' ">
					and update_time = #{entity.updateTime}
				</if>
		</where>
		order by create_time desc
	</select>



</mapper>