<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.sys.dao.PageComponentDao">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.yinshu.sys.entity.PageComponent">
		<id column="id" property="id" />
		<result column="comp_name" property="compName" />
		<result column="comp_code" property="compCode" />
		<result column="comp_path" property="compPath" />
		<result column="comp_image" property="compImage" />
		<result column="example_params" property="exampleParams" />
		<result column="example_datas" property="exampleDatas" />
		<result column="example_events" property="exampleEvents" />
		<result column="remark" property="remark" />
		<result column="sort" property="sort" />
		<result column="status" property="status" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
        id, comp_name, comp_code, comp_path, comp_image, example_params, example_datas, example_events, remark, sort, status
    </sql>


	<select id="queryList" resultMap="BaseResultMap" parameterType="com.yinshu.sys.entity.PageComponent">
		select <include refid="Base_Column_List"></include>
		from s_page_component
		<where>
			<if test="entity.id != null and entity.id != '' ">
				and id = #{entity.id}
			</if>
			<if test="entity.compName != null and entity.compName != '' ">
				and comp_name = #{entity.compName}
			</if>
			<if test="entity.compCode != null and entity.compCode != '' ">
				and comp_code = #{entity.compCode}
			</if>
			<if test="entity.compPath != null and entity.compPath != '' ">
				and comp_path = #{entity.compPath}
			</if>
			<if test="entity.compImage != null and entity.compImage != '' ">
				and comp_image = #{entity.compImage}
			</if>
			<if test="entity.exampleParams != null and entity.exampleParams != '' ">
				and example_params = #{entity.exampleParams}
			</if>
			<if test="entity.exampleDatas != null and entity.exampleDatas != '' ">
				and example_datas = #{entity.exampleDatas}
			</if>
			<if test="entity.exampleEvents != null and entity.exampleEvents != '' ">
				and example_events = #{entity.exampleEvents}
			</if>
			<if test="entity.remark != null and entity.remark != '' ">
				and remark = #{entity.remark}
			</if>
			<if test="entity.sort != null and entity.sort != '' ">
				and sort = #{entity.sort}
			</if>
			<if test="entity.status != null and entity.status != '' ">
				and status = #{entity.status}
			</if>
			<if test="entity.status != null and entity.status != '' ">
				and status = #{entity.status}
			</if>
		</where>
		order by sort
	</select>

	<!-- 分页查询  -->
	<select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.sys.entity.PageComponent">
		select <include refid="Base_Column_List"></include>
		from s_page_component
		<where>
			<if test="entity.queryKeyword != null and entity.queryKeyword != '' ">
				(
				<trim prefix="" prefixOverrides="or">
					or comp_name like concat(concat('%', #{entity.queryKeyword}), '%')
					or comp_code like concat(concat('%', #{entity.queryKeyword}), '%')
					or comp_path like concat(concat('%', #{entity.queryKeyword}), '%')
					or comp_image like concat(concat('%', #{entity.queryKeyword}), '%')
					or example_params like concat(concat('%', #{entity.queryKeyword}), '%')
					or example_datas like concat(concat('%', #{entity.queryKeyword}), '%')
					or example_events like concat(concat('%', #{entity.queryKeyword}), '%')
					or remark like concat(concat('%', #{entity.queryKeyword}), '%')
				</trim>
				)
			</if>
		</where>
		order by sort
	</select>

	<select id="check" resultMap="BaseResultMap" parameterType="com.yinshu.sys.entity.PageComponent">
		select <include refid="Base_Column_List"></include>
		from s_page_component
		<where>
			<if test="id != null and id != '' ">
				AND id != #{id}
			</if>
			AND (comp_code = #{compCode})
		</where>
		order by sort
	</select>

	<select id="getMaxSort" resultType="Integer" >
		select max(sort) max_sort from s_page_component
	</select>


</mapper>