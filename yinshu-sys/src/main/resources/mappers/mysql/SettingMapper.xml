<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.sys.dao.SettingDao">
  <resultMap id="BaseResultMap" type="com.yinshu.sys.entity.Setting">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="parm_name" jdbcType="VARCHAR" property="parmName" />
    <result column="parm_code" jdbcType="VARCHAR" property="parmCode" />
    <result column="parm_value" jdbcType="VARCHAR" property="parmValue" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="set_type" jdbcType="VARCHAR" property="setType" />
  </resultMap>

  <select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.sys.entity.Setting">
    select id, parm_name, parm_code, parm_value, remark, sort, status,set_type
    from s_setting
    <where>
      <if test="entity.status != null">
        AND status = #{entity.status}
      </if>
      <if test="entity.queryKeyword != null and entity.queryKeyword != '' ">
        AND (
        parm_name like concat(concat('%', #{entity.queryKeyword}), '%')
        or parm_code like concat(concat('%', #{entity.queryKeyword}), '%')
        or parm_value like concat(concat('%', #{entity.queryKeyword}), '%')
        or set_type like concat(concat('%', #{entity.queryKeyword}), '%')
        )
      </if>
    </where>
    order by sort
  </select>

  <select id="queryList" resultMap="BaseResultMap" parameterType="com.yinshu.sys.entity.Setting">
    select id, parm_name, parm_code, parm_value, remark, sort, status, set_type
    from s_setting
    <where>
      <if test="entity.status != null">
        AND status = #{entity.status}
      </if>
      <if test="entity.parmCode != null and entity.parmCode != '' ">
        AND parm_code = #{entity.parmCode}
      </if>
      <if test="entity.setType != null and entity.setType != '' ">
        AND set_type = #{entity.setType}
      </if>
      <if test="entity.queryKeyword != null and entity.queryKeyword != '' ">
        AND (
        parm_name like concat(concat('%', #{entity.queryKeyword}), '%')
        or parm_code like concat(concat('%', #{entity.queryKeyword}), '%')
        or parm_value like concat(concat('%', #{entity.queryKeyword}), '%')
        or set_type like concat(concat('%', #{entity.queryKeyword}), '%')
        )
      </if>
    </where>
    order by sort
  </select>

  <select id="check" resultMap="BaseResultMap" parameterType="com.yinshu.sys.entity.Setting">
    select id, parm_name, parm_code, parm_value, remark, sort, status,set_type
    from s_setting
    <where>
      <if test="id != null and id != '' ">
        AND id != #{id}
      </if>
      AND (parm_name = #{parmName} or parm_code = #{parmCode})
    </where>
    order by sort
  </select>

  <select id="getMaxSort" resultType="Integer" >
    select max(sort) max_sort from s_setting
  </select>

</mapper>