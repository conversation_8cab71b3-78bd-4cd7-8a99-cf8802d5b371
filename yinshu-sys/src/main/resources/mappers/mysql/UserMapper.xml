<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.sys.dao.UserDao">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.yinshu.sys.entity.User">
		<id column="id" property="id" />
		<result column="user_name" property="userName" />
		<result column="login_name" property="loginName" />
		<result column="password" property="password" />
		<result column="unit_id" property="unitId" />
		<result column="create_time" property="createTime" />
		<association property="unit" javaType="com.yinshu.sys.entity.Unit">
			<id column="unit_id" property="id" />
			<result column="uname" property="uname" />
		</association>
	</resultMap>

	<select id="queryPageList" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select a.*, b.uname from s_user a left join s_unit b on a.unit_id = b.id 
		<where>
			<if test="param.loginName != null and param.loginName != '' ">
				and login_name = #{param.loginName}
			</if>
			<if test="param.userName != null and param.userName != '' ">
				and user_name = #{param.userName}
			</if>
			<if test="param.unitId != null and param.unitId != '' ">
				and unit_id = #{param.unitId}
			</if>
		</where>
		order by create_time
	</select>
	
	<select id="queryList" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select a.*, b.uname from s_user a left join s_unit b on a.unit_id = b.id
		<where>
			<if test="param.loginName != null and param.loginName != '' ">
				and login_name = #{param.loginName}
			</if>
			<if test="param.unitId != null and param.unitId != '' ">
				and unit_id = #{param.unitId}
			</if>
			<if test="param.existIds != null and param.existIds.size != 0">
				or a.id in
				<foreach collection="param.existIds" item="id" separator="," open="(" close=")">
					#{id}
				</foreach>
			</if>
		</where>
	</select>
	
	<!-- 查询未授权的用户 -->
	<select id="queryUnAuthPageList" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select a.*, b.uname from s_user a left join s_unit b on a.unit_id = b.id 
		<where>
			<if test="param.unitId != null and param.unitId != '' ">
				and unit_id = #{param.unitId}
			</if>
			<if test="param.userIds !=null and param.userIds.size != 0">
				and a.id not in
				<foreach collection="param.userIds" item="id" separator="," open="(" close=")">
					#{id}
				</foreach>
			</if>
		</where>
		order by create_time
	</select>
	<update id="updatePassword">
        update s_user
        set password = #{newPassword,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
</mapper>
