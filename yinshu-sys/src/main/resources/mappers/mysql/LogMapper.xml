<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.sys.dao.LogDao">
    <resultMap id="BaseResultMap" type="com.yinshu.sys.entity.Log">
        <result column="id" property="id" />
        <result column="oper_user" property="operUser" />
        <result column="oper_modul" property="operModul" />
        <result column="oper_type" property="operType" />
        <result column="login_ip" property="loginIp" />
        <result column="browser_type" property="browserType" />
        <result column="create_time" property="createTime" />
        <result column="url" property="url" />
        <result column="content" property="content" />
    </resultMap>

    <select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.sys.entity.Log">
        select * from s_log
        <where>
            <if test="entity.startTime != null ">
                AND CREATE_TIME &gt;= #{entity.startTime}
            </if>
            <if test="entity.endTime != null ">
                AND CREATE_TIME &lt;= #{entity.endTime}
            </if>
            <if test="entity.queryKeyword != null and entity.queryKeyword != '' ">
                AND (
                URL like concat(concat('%', #{entity.queryKeyword}), '%')
                or CONTENT like concat(concat('%', #{entity.queryKeyword}), '%')
                )
            </if>
        </where>
        order by CREATE_TIME desc
    </select>

</mapper>
