<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.sys.dao.RoleUserDao">

	<select id="queryPageList" resultType="java.util.Map" parameterType="java.util.HashMap">
		select * from s_role 
		<where>
			<if test="param.keyword != null and param.keyword != '' ">
				ROLE_NAME like concat(concat('%',#{param.keyword}),'%')
			</if>
		</where>
		order by CREATE_TIME
	</select>
	
	<!-- 根据角色或者用戶查询 -->
	<select id="queryList" resultType="java.util.Map" parameterType="java.util.HashMap">
		select t1.*, t3.role_name,t3.role_code,t2.user_name,t3.role_description from s_role_user t1
		left join s_user t2 on t1.user_id = t2.id
		left join s_role t3 on t1.role_id = t3.id
		<where>
			<if test="userId!=null and userId!=''">
				and t1.user_id = #{userId} 
	        </if>
	        <if test="roleId!=null and roleId!=''">
				and t1.role_id = #{roleId} 
	        </if>
        </where>
	</select>
	
	<!-- 根据角色查询所有用户 -->
	<select id="queryUserListByRoleId" resultType="java.util.HashMap">
		 select * from s_user t1 left join s_role_user t2 on t1.id = t2.user_id
		 where t2.role_id = #{roleId} 
	</select>
	
	<!-- 根据角色删除用户 -->
	<select id="removeByRoleId">
		 delete from s_role_user where role_id = #{roleId} 
	</select>
	
	<!-- 删除所属用户 -->
	<delete id="removeByUserIds" parameterType="java.util.List">
		 delete from s_role_user where user_id in
		 <foreach item="userId" collection="list" open="(" separator="," close=")">
            #{userId}
        </foreach>
	</delete>
	
	

</mapper>
