<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.sys.dao.RoleMenuPermissionDao">
  <resultMap id="BaseResultMap" type="com.yinshu.sys.entity.RoleMenuPermission">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="role_menu_id" jdbcType="VARCHAR" property="roleMenuId" />
    <result column="permisson_id" jdbcType="VARCHAR" property="permissonId" />
  </resultMap>

    <delete id="deleteByRoleId" parameterType="String">
        delete from s_role_menu_permission
        where role_menu_id in
        (select id from s_role_menu where role_id = #{roleId} )
    </delete>

    <delete id="deleteByMenuIds" parameterType="java.util.List">
        delete from s_role_menu_permission
        where role_menu_id in
        (select id from s_role_menu where menu_id in
        <foreach collection="idList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>)
    </delete>

  <delete id="deleteByRoleMenuIds" parameterType="java.util.List">
        delete from s_role_menu_permission
        where role_menu_id in
      <foreach collection="idList" item="item" open="(" separator="," close=")">
          #{item}
      </foreach>
  </delete>
</mapper>