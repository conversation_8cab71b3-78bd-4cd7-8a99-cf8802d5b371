<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.sys.dao.PageLayoutDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yinshu.sys.entity.PageLayout">
        <id column="id" property="id" />
        <result column="x" property="x" />
        <result column="y" property="y" />
        <result column="w" property="w" />
        <result column="h" property="h" />
        <result column="i" property="i" />
        <result column="draggable" property="draggable" />
        <result column="resizable" property="resizable" />
        <result column="static_container" property="staticContainer" />
		<result column="bind_code" property="bindCode" />
		<result column="bind_style" property="bindStyle" />
		<result column="bind_params" property="bindParams" />
		<result column="bind_datas" property="bindDatas" />
		<result column="bind_events" property="bindEvents" />
        <result column="page_flex_id" property="pageFlexId" />
        <result column="page_component_id" property="pageComponentId" />
		<association property="pageComponent" javaType="com.yinshu.sys.entity.PageComponent">
			<id column="comp_id" property="id" />
			<result column="comp_name" property="compName" />
			<result column="comp_code" property="compCode" />
			<result column="comp_path" property="compPath" />
			<result column="example_params" property="exampleParams" />
			<result column="example_datas" property="exampleDatas" />
			<result column="example_events" property="exampleEvents" />
			<result column="remark" property="remark" />
			<result column="sort" property="sort" />
			<result column="status" property="status" />
		</association>
    </resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
        id, x, y, w, h, i, draggable, resizable, static_container, bind_code, bind_style, bind_params, bind_datas, bind_events, page_flex_id, page_component_id
    </sql>


	<select id="queryList" resultMap="BaseResultMap" parameterType="com.yinshu.sys.entity.PageLayout">
		select t1.*,
		t2.id as comp_id, t2.comp_name, t2.comp_code, t2.comp_path,
		t2.example_params, t2.example_datas, t2.example_events
		from s_page_layout t1 LEFT JOIN s_page_component t2
		on t1.page_component_id = t2.id
		<where>
				<if test="entity.id != null and entity.id != '' ">
					and t1.id = #{entity.id}
				</if>
				<if test="entity.x != null and entity.x != '' ">
					and x = #{entity.x}
				</if>
				<if test="entity.y != null and entity.y != '' ">
					and y = #{entity.y}
				</if>
				<if test="entity.w != null and entity.w != '' ">
					and w = #{entity.w}
				</if>
				<if test="entity.h != null and entity.h != '' ">
					and h = #{entity.h}
				</if>
				<if test="entity.i != null and entity.i != '' ">
					and i = #{entity.i}
				</if>
				<if test="entity.draggable != null and entity.draggable != '' ">
					and draggable = #{entity.draggable}
				</if>
				<if test="entity.resizable != null and entity.resizable != '' ">
					and resizable = #{entity.resizable}
				</if>
				<if test="entity.staticContainer != null and entity.staticContainer != '' ">
					and static_container = #{entity.staticContainer}
				</if>
				<if test="entity.bindCode != null and entity.bindCode != '' ">
					and bind_code = #{entity.bindCode}
				</if>
				<if test="entity.bindStyle != null and entity.bindStyle != '' ">
					and bind_style = #{entity.bindStyle}
				</if>
				<if test="entity.bindParams != null and entity.bindParams != '' ">
					and bind_params = #{entity.bindParams}
				</if>
				<if test="entity.bindDatas != null and entity.bindDatas != '' ">
					and bind_datas = #{entity.bindDatas}
				</if>
				<if test="entity.bindEvents != null and entity.bindEvents != '' ">
					and bind_events = #{entity.bindEvents}
				</if>
				<if test="entity.pageFlexId != null and entity.pageFlexId != '' ">
					and page_flex_id = #{entity.pageFlexId}
				</if>
				<if test="entity.pageComponentId != null and entity.pageComponentId != '' ">
					and page_component_id = #{entity.pageComponentId}
				</if>
		</where>
		order by i asc
	</select>

	<!-- 分页查询  -->
	<select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.sys.entity.PageLayout">
		select <include refid="Base_Column_List"></include>
		from s_page_layout
		<where>
			<if test="entity.queryKeyword != null and entity.queryKeyword != '' ">
				(
				<trim prefix="" prefixOverrides="or">
					or bind_code like concat(concat('%', #{entity.queryKeyword}), '%')
					or bind_style like concat(concat('%', #{entity.queryKeyword}), '%')
					or bind_params like concat(concat('%', #{entity.queryKeyword}), '%')
					or bind_datas like concat(concat('%', #{entity.queryKeyword}), '%')
					or bind_events like concat(concat('%', #{entity.queryKeyword}), '%')
				</trim>
				)
			</if>
		</where>
	</select>



</mapper>