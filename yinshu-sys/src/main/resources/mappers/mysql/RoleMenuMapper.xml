<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.sys.dao.RoleMenuDao">

	<resultMap id="MenuResultMap" type="com.yinshu.sys.entity.Menu">
		<id column="id" property="id" />
        <result column="parent_id" property="parentId" />
        <result column="menu_name" property="menuName" />
        <result column="menu_icon" property="menuIcon" />
        <result column="menu_sort" property="menuSort" />
        <result column="menu_url" property="menuUrl" />
        <result column="component" property="component" />
        <result column="perms" property="perms" />
        <result column="status" property="status" />
        <result column="is_visible" property="isVisible" />
        <result column="menu_type" property="menuType" />
        <collection property="persList" ofType="com.yinshu.sys.entity.MenuPermission">
			<id column="permission_id" property="id" />
			<result column="action_name" property="actionName" />
        	<result column="action_url" property="actionUrl" />
		</collection>
<!-- 		<collection property="roleMenuPermissionList" ofType="com.yinshu.sys.entity.RoleMenuPermission">
			<id column="permission_id" property="id" />
			<result column="action_name" property="actionName" />
        	<result column="action_url" property="actionUrl" />
		</collection> -->
	</resultMap>

	<resultMap id="RoleMenuResultMap" type="com.yinshu.sys.entity.RoleMenu">
		<id column="id" jdbcType="VARCHAR" property="id" />
		<result column="role_id" jdbcType="VARCHAR" property="roleId" />
		<result column="menu_id" jdbcType="VARCHAR" property="menuId" />
		<result column="perms" jdbcType="VARCHAR" property="perms" />
		<collection property="permissionList" ofType="com.yinshu.sys.entity.RoleMenuPermission">
			<id column="role_menu_permission_id" property="id" />
			<result column="role_menu_id" property="roleMenuId" />
			<result column="permisson_id" property="permissonId" />
		</collection>
	</resultMap>

	
	<!-- 根据角色查询所有菜单 -->
	<select id="queryMenuListByRoleId" resultType="java.util.HashMap">
		 select t1.*, t2.id role_menu_id from s_menu t1 left join s_role_menu t2 on t1.id = t2.menu_id
		 where t2.role_id = #{roleId} 
		 order by t1.menu_sort
	</select>

	<!-- 根据角色查询所有菜单对应关系, 包含菜单权限标识表 -->
	<select id="queryRoleMenusByRoleId" resultMap="RoleMenuResultMap">
		select t1.*,
		t2.id as role_menu_permission_id, t2.role_menu_id, t2.permisson_id
		from s_role_menu t1 LEFT JOIN s_role_menu_permission t2
		on t1.id = t2.role_menu_id
		where t1.role_id = #{roleId}
	</select>
	
	<!-- 根据角色查询所有菜单,支持多角色 -->
	<select id="queryMenuListByRoles" resultType="java.util.HashMap" resultMap="MenuResultMap">
 		 <!--select distinct t3.*, t4.id permission_id, t4.action_name, t4.action_url from s_role_menu t1 
		 left join s_role_menu_permission t2 on t2.role_menu_id = t1.id
		 left join s_menu t3 on t1.menu_id = t3.id
		 left join s_menu_permission t4 on t2.permisson_id = t4.id
		 where t1.role_id in
		 <foreach collection="list" item="role" index="index" open="(" close=")" separator=",">
		  	#{role.id}
		</foreach>
		 order by t3.menu_sort-->
		 
  		 select * from (
			select t3.*, t1.id role_menu_id from s_role_menu t1, s_menu t3 where t1.menu_id = t3.id and t1.role_id in 
			<foreach collection="list" item="role" index="index" open="(" close=")" separator=",">
			  	#{role.id}
			</foreach>
		 ) t1
		 left join s_role_menu_permission t2 on t2.role_menu_id = t1.role_menu_id
		 left join s_menu_permission t4 on t2.permisson_id = t4.id
		 order by menu_sort;
	</select>
	
	<!-- 根据角色删除菜单 -->
	<select id="removeByRoleId">
		 delete from s_role_menu where role_id = #{roleId} 
	</select>
	
	<!-- 根据菜单ID删除 -->
	<select id="removeByMenuIds" parameterType="java.lang.String">
		 delete from s_role_menu where menu_id in 
		 <foreach collection="array" open="(" close=")" separator="," item="id">
		 	#{id}
		 </foreach>
	</select>
	

</mapper>
