<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.sys.dao.FileUploadDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yinshu.sys.entity.FileUpload">
        <result column="id" property="id" />
        <result column="f_id" property="fId" />
        <result column="file_name" property="fileName" />
        <result column="file_path" property="filePath" />
        <result column="file_type" property="fileType" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, f_id, file_name, file_path, file_type, create_time, create_user, update_time
    </sql>


	<select id="queryList" resultMap="BaseResultMap" parameterType="com.yinshu.sys.entity.FileUpload">
		select <include refid="Base_Column_List"></include>
		from s_file_upload
		<where>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.fId != null and entity.fId != '' ">
					and f_id = #{entity.fId}
				</if>
				<if test="entity.fileName != null and entity.fileName != '' ">
					and file_name = #{entity.fileName}
				</if>
				<if test="entity.filePath != null and entity.filePath != '' ">
					and file_path = #{entity.filePath}
				</if>
				<if test="entity.fileType != null and entity.fileType != '' ">
					and file_type = #{entity.fileType}
				</if>
				<if test="entity.createTime != null and entity.createTime != '' ">
					and create_time = #{entity.createTime}
				</if>
				<if test="entity.createUser != null and entity.createUser != '' ">
					and create_user = #{entity.createUser}
				</if>
				<if test="entity.updateTime != null and entity.updateTime != '' ">
					and update_time = #{entity.updateTime}
				</if>
		</where>
	</select>

	<!-- 分页查询  -->
	<select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.sys.entity.FileUpload">
		select <include refid="Base_Column_List"></include>
		from s_file_upload
		<where>
			<if test="entity.keyword != null and entity.keyword != '' ">
				(
				<trim prefix="" prefixOverrides="or">
						or file_name like concat(concat('%', #{entity.keyword}), '%')
						or file_path like concat(concat('%', #{entity.keyword}), '%')
						or file_type like concat(concat('%', #{entity.keyword}), '%')
						or create_user like concat(concat('%', #{entity.keyword}), '%')
				</trim>
				)
			</if>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.fId != null and entity.fId != '' ">
					and f_id = #{entity.fId}
				</if>
				<if test="entity.fileName != null and entity.fileName != '' ">
					and file_name = #{entity.fileName}
				</if>
				<if test="entity.filePath != null and entity.filePath != '' ">
					and file_path = #{entity.filePath}
				</if>
				<if test="entity.fileType != null and entity.fileType != '' ">
					and file_type = #{entity.fileType}
				</if>
				<if test="entity.createTime != null and entity.createTime != '' ">
					and create_time = #{entity.createTime}
				</if>
				<if test="entity.createUser != null and entity.createUser != '' ">
					and create_user = #{entity.createUser}
				</if>
				<if test="entity.updateTime != null and entity.updateTime != '' ">
					and update_time = #{entity.updateTime}
				</if>
		</where>
		order by create_time desc
	</select>

    <!-- 根据业务ID删除对应的附件 -->
	<delete id="removeByFids" parameterType="java.lang.String">
        delete from s_file_upload where f_id in 
        <foreach collection="array" open="(" close=")" separator="," item="id">
		 	#{id}
		 </foreach>
    </delete>


</mapper>