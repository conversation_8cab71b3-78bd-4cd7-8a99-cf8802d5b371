<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.sys.dao.SettingDmDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yinshu.sys.entity.SettingDm">
        <id column="ID" property="id" />
        <result column="PARM_NAME" property="parmName" />
        <result column="PARM_CODE" property="parmCode" />
        <result column="PARM_VALUE" property="parmValue" />
        <result column="REMARK" property="remark" />
        <result column="SORT" property="sort" />
        <result column="STATUS" property="status" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, PARM_NAME, PARM_CODE, PARM_VALUE, REMARK, SORT, STATUS
    </sql>


	<select id="queryList" resultMap="BaseResultMap" parameterType="com.yinshu.sys.entity.SettingDm">
		select <include refid="Base_Column_List"></include>
		from S_SETTING_DM
		<where>
				<if test="entity.id != null and entity.id != '' ">
					and ID = #{entity.id}
				</if>
				<if test="entity.parmName != null and entity.parmName != '' ">
					and PARM_NAME = #{entity.parmName}
				</if>
				<if test="entity.parmCode != null and entity.parmCode != '' ">
					and PARM_CODE = #{entity.parmCode}
				</if>
				<if test="entity.parmValue != null and entity.parmValue != '' ">
					and PARM_VALUE = #{entity.parmValue}
				</if>
				<if test="entity.remark != null and entity.remark != '' ">
					and REMARK = #{entity.remark}
				</if>
				<if test="entity.sort != null and entity.sort != '' ">
					and SORT = #{entity.sort}
				</if>
				<if test="entity.status != null and entity.status != '' ">
					and STATUS = #{entity.status}
				</if>
		</where>
		order by SORT
	</select>

	<!-- 分页查询  -->
	<select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.sys.entity.SettingDm">
		select <include refid="Base_Column_List"></include>
		from S_SETTING_DM
		<where>
			<if test="entity.queryKeyword != null and entity.queryKeyword != '' ">
				(
				<trim prefix="" prefixOverrides="or">
						or ID like concat(concat('%', #{entity.queryKeyword}), '%')
						or PARM_NAME like concat(concat('%', #{entity.queryKeyword}), '%')
						or PARM_CODE like concat(concat('%', #{entity.queryKeyword}), '%')
						or PARM_VALUE like concat(concat('%', #{entity.queryKeyword}), '%')
						or REMARK like concat(concat('%', #{entity.queryKeyword}), '%')
				</trim>
				)
			</if>
		</where>
		order by SORT
	</select>

	<select id="check" resultMap="BaseResultMap" parameterType="com.yinshu.sys.entity.Setting">
		select <include refid="Base_Column_List"></include>
		from S_SETTING_DM
		<where>
			<if test="id != null and id != '' ">
				AND ID != #{id}
			</if>
			AND (PARM_NAME = #{parmName} or PARM_CODE = #{parmCode})
		</where>
		order by SORT
	</select>

	<select id="getMaxSort" resultType="Integer" >
    select max(SORT) MAX_SORT from S_SETTING_DM
  </select>


</mapper>