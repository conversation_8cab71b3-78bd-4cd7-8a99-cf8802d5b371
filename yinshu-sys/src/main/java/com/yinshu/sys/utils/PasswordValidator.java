package com.yinshu.sys.utils;

import java.util.regex.Pattern;

/**
 * 强密码策略校验器工具类
 */
public final class PasswordValidator {

    /**
     * 密码策略正则表达式 (已移除开头和结尾锚点):
     * 该模式用于查找一个满足所有条件的子字符串。
     *
     * (?=.*[a-z])         # 至少一个小写字母
     * (?=.*[A-Z])         # 至少一个大写字母
     * (?=.*[0-9])         # 至少一个数字
     * (?=.*[...])         # 至少一个特殊字符
     * .{12,}              # 总长度至少12位
     */
    private static final String PASSWORD_PATTERN_NO_ANCHORS =
            "(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[\\^$*.\\[\\]{}()?\\\"!@#%&/\\\\,><':;|_~`+= \\-]).{12,}";

    private static final Pattern PATTERN = Pattern.compile(PASSWORD_PATTERN_NO_ANCHORS);

    private PasswordValidator() {} // 私有构造，防止实例化

    /**
     * 验证密码是否符合预设的强密码策略
     * @param password 待验证的密码
     * @return true 如果符合策略，否则 false
     */
    public static boolean isValid(final String password) {
        if (password == null) {
            return false;
        }
        return PATTERN.matcher(password).matches();
    }
}
