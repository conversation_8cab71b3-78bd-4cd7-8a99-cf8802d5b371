package com.yinshu.sys.utils;

import com.yinshu.sys.entity.SessionUser;
import lombok.experimental.UtilityClass;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * @Author: c
 * @Date: 2024/10/22/11:02
 * @Description:
 */
@UtilityClass
public class SessionUserUtils {
    public static SessionUser getSessionUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Object obj = authentication.getPrincipal();
        SessionUser sessionUser = (SessionUser) obj;
        return sessionUser;
    }
}
