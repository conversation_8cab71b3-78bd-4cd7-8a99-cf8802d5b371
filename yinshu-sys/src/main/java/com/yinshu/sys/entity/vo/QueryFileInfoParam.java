package com.yinshu.sys.entity.vo;

import com.yinshu.sys.entity.FileInfo;
import com.yinshu.utils.PageParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * 查询文件信息 参数
 *
 * <AUTHOR>
 * @date 2024/12/30
 */

@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryFileInfoParam extends PageParam<FileInfo> {

    /**
     * 文件名
     */
    private String name;

    /**
     * 文件类型
     */
    private Integer type;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate date;
}
