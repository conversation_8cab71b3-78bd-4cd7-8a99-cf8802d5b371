package com.yinshu.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 文件范围
 *
 * <AUTHOR>
 * @date 2024/12/27
 */
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("S_FILE_USER")
public class FileUser {

    /**
     * Bound Id
     */
    @TableField("ID")
    private Long id;

    /**
     * 文件 ID
     */
    @TableField("FILE_ID")
    private Long fileId;

    /**
     * 用户 ID
     */
    @TableField("USER_ID")
    private String userId;
}
