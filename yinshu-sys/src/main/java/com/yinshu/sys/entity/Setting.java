package com.yinshu.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yinshu.utils.PageParam;

/**
 *  系统设置表
 */
@TableName(value = "s_setting")
public class Setting extends PageParam<Setting> {

    private static final long serialVersionUID = 6764754016251990254L;

    @TableId(value = "id", type = IdType.NONE)
    /**
     *   主键
     */
    private String id;

    /**
     *   参数名称
     */
    private String parmName;

    /**
     *   参数标示
     */
    private String parmCode;

    /**
     *   参数值
     */
    private String parmValue;

    /**
     *   备注
     */
    private String remark;

    /**
     *   排序
     */
    private Integer sort;

    /**
     *   状态 0：可用 1：不可用
     */
    private Integer status;

    /**
     *  层级类型 0：系统级别 1：用户级别
     */
    private String setType;

    public String getSetType() {
        return setType;
    }

    public void setSetType(String setType) {
        this.setType = setType;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getParmName() {
        return parmName;
    }

    public void setParmName(String parmName) {
        this.parmName = parmName;
    }

    public String getParmCode() {
        return parmCode;
    }

    public void setParmCode(String parmCode) {
        this.parmCode = parmCode;
    }

    public String getParmValue() {
        return parmValue;
    }

    public void setParmValue(String parmValue) {
        this.parmValue = parmValue;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}