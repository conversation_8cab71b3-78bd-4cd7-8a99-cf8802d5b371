package com.yinshu.sys.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;

import com.yinshu.utils.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

/**
 * 
 * 
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@TableName("s_job_task")
public class JobTask extends PageParam<JobTask> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private String id;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * cron表达式
     */
    private String cronExpression;

    /**
     * spring的bean名称
     */
    private String beanName;

    /**
     * 执行的方法
     */
    private String methodName;

    /**
     * 状态（0-停用，1-启用，2-异常）
     */
    private Integer status;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 执行标志（考虑多项目部署任务不会重复执行）
     */
    private String taskExecuteFlag;

    /**
     * 异常信息
     */
    private String taskErrorInfo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }
    public String getCronExpression() {
        return cronExpression;
    }

    public void setCronExpression(String cronExpression) {
        this.cronExpression = cronExpression;
    }
    public String getBeanName() {
        return beanName;
    }

    public void setBeanName(String beanName) {
        this.beanName = beanName;
    }
    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
    public String getTaskExecuteFlag() {
        return taskExecuteFlag;
    }

    public void setTaskExecuteFlag(String taskExecuteFlag) {
        this.taskExecuteFlag = taskExecuteFlag;
    }

    public String getTaskErrorInfo() {
        return taskErrorInfo;
    }

    public void setTaskErrorInfo(String taskErrorInfo) {
        this.taskErrorInfo = taskErrorInfo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "JobTask{" +
            "id=" + id +
            ", taskName=" + taskName +
            ", cronExpression=" + cronExpression +
            ", beanName=" + beanName +
            ", methodName=" + methodName +
            ", status=" + status +
            ", description=" + description +
            ", taskExecuteFlag=" + taskExecuteFlag +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
        "}";
    }
}