package com.yinshu.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yinshu.utils.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

/**
 * 
 * 页面布局表
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@TableName("s_page_layout")
public class PageLayout extends PageParam<PageLayout> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private String id;

    /**
     * 应位于哪一列
     */
    private Integer x;

    /**
     * 位于哪一行
     */
    private Integer y;

    /**
     * 占多少列
     */
    private Integer w;

    /**
     * 占多少行
     */
    private Integer h;

    /**
     * 元素的唯一标识
     */
    private Integer i;

    /**
     * 是否可拖拽
     */
    private Boolean draggable;

    /**
     * 是否可缩放
     */
    private Boolean resizable;

    /**
     * 是否为静态的
     */
    private Boolean staticContainer;

    /**
     * 绑定别名编码
     */
    private String bindCode;

    /**
     * 绑定样式
     */
    private String bindStyle;

    /**
     * 绑定参数
     */
    private String bindParams;

    /**
     * 绑定数据
     */
    private String bindDatas;

    /**
     * 绑定事件
     */
    private String bindEvents;

    /**
     * 所属页面id
     */
    private String pageFlexId;

    /**
     * 组件id
     */
    private String pageComponentId;

    /**
     * 组件
     */
    @TableField(exist = false)
    private PageComponent pageComponent;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public Integer getX() {
        return x;
    }

    public void setX(Integer x) {
        this.x = x;
    }
    public Integer getY() {
        return y;
    }

    public void setY(Integer y) {
        this.y = y;
    }
    public Integer getW() {
        return w;
    }

    public void setW(Integer w) {
        this.w = w;
    }
    public Integer getH() {
        return h;
    }

    public void setH(Integer h) {
        this.h = h;
    }
    public Integer getI() {
        return i;
    }

    public void setI(Integer i) {
        this.i = i;
    }

    public Boolean getDraggable() {
        return draggable;
    }

    public void setDraggable(Boolean draggable) {
        this.draggable = draggable;
    }

    public Boolean getResizable() {
        return resizable;
    }

    public void setResizable(Boolean resizable) {
        this.resizable = resizable;
    }

    public Boolean getStaticContainer() {
        return staticContainer;
    }

    public void setStaticContainer(Boolean staticContainer) {
        this.staticContainer = staticContainer;
    }

    public String getBindCode() {
        return bindCode;
    }

    public void setBindCode(String bindCode) {
        this.bindCode = bindCode;
    }

    public String getBindStyle() {
        return bindStyle;
    }

    public void setBindStyle(String bindStyle) {
        this.bindStyle = bindStyle;
    }

    public String getBindParams() {
        return bindParams;
    }

    public void setBindParams(String bindParams) {
        this.bindParams = bindParams;
    }

    public String getBindDatas() {
        return bindDatas;
    }

    public void setBindDatas(String bindDatas) {
        this.bindDatas = bindDatas;
    }

    public String getBindEvents() {
        return bindEvents;
    }

    public void setBindEvents(String bindEvents) {
        this.bindEvents = bindEvents;
    }

    public String getPageFlexId() {
        return pageFlexId;
    }

    public void setPageFlexId(String pageFlexId) {
        this.pageFlexId = pageFlexId;
    }

    public String getPageComponentId() {
        return pageComponentId;
    }

    public void setPageComponentId(String pageComponentId) {
        this.pageComponentId = pageComponentId;
    }

    public PageComponent getPageComponent() {
        return pageComponent;
    }

    public void setPageComponent(PageComponent pageComponent) {
        this.pageComponent = pageComponent;
    }

    @Override
    public String toString() {
        return "PageLayout{" +
            "id=" + id +
            ", x=" + x +
            ", y=" + y +
            ", w=" + w +
            ", h=" + h +
            ", i=" + i +
            ", draggable=" + draggable +
            ", resizable=" + resizable +
            ", staticContainer=" + staticContainer +
            ", pageComponentId=" + pageComponentId +
        "}";
    }
}