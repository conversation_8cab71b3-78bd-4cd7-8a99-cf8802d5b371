package com.yinshu.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@TableName(value = "s_role")
@Data
public class Role implements Serializable {

    /**
     * 角色id
     */
    private String id;

    /**
     * 角色代码
     */
    private String roleCode;

    /**
     * 角色名
     */
    private String roleName;

    /**
     * 角色描述
     */
    private String roleDescription;

    /**
     * 权限标识
     */
    @TableField(exist = false)
    private String authority;

    //机构
    @TableField(exist = false)
    private String unitId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 页数
     */
    @TableField(exist = false)
    private int pageNumber;

    /**
     * 每页数量
     */
    @TableField(exist = false)
    private int pageSize;


    /**
     * 数据来源 1=系统内部 0=第三方同步
     */
    private Integer dataSource;
}
