package com.yinshu.sys.entity;

import java.util.Map;

public class PageComponentSource {
    private String pageLayoutId;
    private Map<String, Object> bindParams;
    private Map<String, BindData> bindDatas;
    private Map<String, Object> resultData;

    public String getPageLayoutId() {
        return pageLayoutId;
    }

    public void setPageLayoutId(String pageLayoutId) {
        this.pageLayoutId = pageLayoutId;
    }

    public Map<String, Object> getBindParams() {
        return bindParams;
    }

    public void setBindParams(Map<String, Object> bindParams) {
        this.bindParams = bindParams;
    }

    public Map<String, BindData> getBindDatas() {
        return bindDatas;
    }

    public void setBindDatas(Map<String, BindData> bindDatas) {
        this.bindDatas = bindDatas;
    }

    public Map<String, Object> getResultData() {
        return resultData;
    }

    public void setResultData(Map<String, Object> resultData) {
        this.resultData = resultData;
    }

    public static class BindData {
        private String type;
        private String sql;
        private boolean onlyOne = false;

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getSql() {
            return sql;
        }

        public void setSql(String sql) {
            this.sql = sql;
        }

        public boolean isOnlyOne() {
            return onlyOne;
        }

        public void setOnlyOne(boolean onlyOne) {
            this.onlyOne = onlyOne;
        }
    }

    public enum BindDataTypeEnum {
        SQL("sql");

        private final String typeValue;

        BindDataTypeEnum(String typeValue) {
            this.typeValue = typeValue;
        }

        public String getTypeValue() {
            return typeValue;
        }

        public boolean typeEquals(String bindDataType) {
            return typeValue.equals(bindDataType);
        }

    }

}
