package com.yinshu.sys.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yinshu.utils.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

/**
 * 
 * 文件上传
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@TableName("s_file_upload")
public class FileUpload extends PageParam<FileUpload> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private String id;

    /**
     * 业务外键
     */
    private String fId;

    /**
     * 文件名称
     */
    private String fileName;
    
    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 文件类型 1:图片  2:视频  3:文件
     */
    private String fileType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;
    
	/**
	 * 文件名称(前端名称)
	 */
    @TableField(exist = false)
	private String name;
	
	/**
	 * 文件路径(存储路径)
	 */
    @TableField(exist = false)
	private String path;
	
	/**
	 * 文件路径(前端访问URL)
	 */
    @TableField(exist = false)
	private String url;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getfId() {
        return fId;
    }

    public void setfId(String fId) {
        this.fId = fId;
    }
    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    
    public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	@Override
    public String toString() {
        return "FileUpload{" +
            "id=" + id +
            ", fId=" + fId +
            ", fileName=" + fileName +
            ", filePath=" + filePath +
            ", fileType=" + fileType +
            ", createTime=" + createTime +
            ", createUser=" + createUser +
            ", updateTime=" + updateTime +
        "}";
    }
}