package com.yinshu.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件信息
 *
 * <AUTHOR>
 * @date 2024/12/27
 */
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("S_FILE")
public class FileInfo {

    /**
     * 文件 ID
     */
    @TableField("ID")
    private Long id;

    /**
     * 文件名称
     */
    @TableField("NAME")
    private String name;

    /**
     * 类型
     * 0: 全部
     * 1: 其他
     * 2: 报告
     */
    @TableField("TYPE")
    private Integer type;

    /**
     * 文件描述
     */
    @TableField("DESCRIPTION")
    private String description;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 是否发布
     * 0: 不同步 APP
     * 1: 同步到 APP
     */
    private Boolean published;

    /**
     * 文件 URL
     */
    @TableField("URL")
    private String url;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("UPDATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;


    /**
     * 用户 ID
     */
    @TableField(exist = false)
    private List<String> userIds;

    /**
     * 表单文件对象
     */
    @TableField(exist = false)
    private MultipartFile file;
}
