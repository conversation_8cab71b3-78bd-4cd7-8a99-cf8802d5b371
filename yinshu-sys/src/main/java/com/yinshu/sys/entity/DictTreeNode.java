package com.yinshu.sys.entity;

import java.util.List;

public class DictTreeNode {
    //节点标签
    private String id;
    //节点标签
    private String label;
    //节点内容
    private String value;
    //父节点标识
    private String parent;
    //字典分类
    private String classify;
    //是否为叶子节点
    private Boolean leaf;
    //子节点
    private List<DictTreeNode> children;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getParent() {
        return parent;
    }

    public void setParent(String parent) {
        this.parent = parent;
    }

    public Boolean getLeaf() {
        return leaf;
    }

    public void setLeaf(Boolean leaf) {
        this.leaf = leaf;
    }

    public List<DictTreeNode> getChildren() {
        return children;
    }

    public void setChildren(List<DictTreeNode> children) {
        this.children = children;
    }

    public String getClassify() {
        return classify;
    }

    public void setClassify(String classify) {
        this.classify = classify;
    }
}
