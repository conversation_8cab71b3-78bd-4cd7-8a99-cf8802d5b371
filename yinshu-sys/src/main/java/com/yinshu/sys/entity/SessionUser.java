package com.yinshu.sys.entity;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * 用户登录session实体
 * <AUTHOR>
 *
 */
public class SessionUser implements UserDetails {
	
	private static final long serialVersionUID = 1L;

	/**
	 * 用户
	 */
	private User user;
	
	/**
	 * 角色数组
	 */
	private String[] roleArray;
	
	/**
	 * 系统设置Map
	 */
	private Map<String, String> sysSettingMap;
	
	/**
	 * 角色
	 */
	private List<Role> roleList = new ArrayList<>();
	
	/**
	 * 菜单
	 */
	private List<Menu> menuList = new ArrayList<>();
	
	/**
	 * 菜单下面所有权限
	 */
	private List<String> permissionList = new ArrayList<>();
	
	/**
	 * 权限
	 */
	private List<String> permissions = new ArrayList<>();
	
	
	/**
	 * json格式的菜单结构
	 */
	private List<Map<String, Object>> menuTreeList = new ArrayList<>();
	
	public SessionUser() {}
	
	public SessionUser(User user) {
		this.user = user;
	}
	
	public SessionUser(User user, List<Role> roleList) {
		this.user = user;
		this.roleList = roleList;
	}
	
	public SessionUser(User user, List<Role> roleList, List<Menu> menuList) {
		this.user = user;
		this.roleList = roleList;
		this.menuList = menuList;
		this.permissionList = setMenuPermissionList(menuList);
		List<String> roleCodes = roleList.stream().map(Role::getRoleCode).collect(Collectors.toList());
		this.setRoleArray(roleCodes.toArray(new String[roleCodes.size()]));
	}

	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}
	
	@Override
	@JSONField(serialize=false)
	public Collection<? extends GrantedAuthority> getAuthorities() {
		List<SimpleGrantedAuthority> authorities = new ArrayList<SimpleGrantedAuthority>();
		for(Role role : roleList) {
			SimpleGrantedAuthority simpleGrantedAuthority = new SimpleGrantedAuthority(role.getRoleCode());
			authorities.add(simpleGrantedAuthority);
		}
		return authorities;
	}

	@Override
	public String getPassword() {
		// TODO Auto-generated method stub
		return user.getPassword();
	}

	@Override
	public String getUsername() {
		// TODO Auto-generated method stub
		return user.getUserName();
	}

	@Override
	public boolean isAccountNonExpired() {
		// TODO Auto-generated method stub
		return true;
	}

	@Override
	public boolean isAccountNonLocked() {
		// TODO Auto-generated method stub
		return true;
	}

	@Override
	public boolean isCredentialsNonExpired() {
		// TODO Auto-generated method stub
		return true;
	}

	@Override
	public boolean isEnabled() {
		// TODO Auto-generated method stub
		return true;
	}

	public List<String> getPermissions() {
		return permissions;
	}

	public void setPermissions(List<String> permissions) {
		this.permissions = permissions;
	}

	public List<Role> getRoleList() {
		return roleList;
	}

	public void setRoleList(List<Role> roleList) {
		this.roleList = roleList;
	}

	public List<Menu> getMenuList() {
		return menuList;
	}

	public void setMenuList(List<Menu> menuList) {
		this.menuList = menuList;
	}

	public List<Map<String, Object>> getMenuTreeList() {
		return menuTreeList;
	}

	public void setMenuTreeList(List<Map<String, Object>> menuTreeList) {
		this.menuTreeList = menuTreeList;
	}

	public List<String> getPermissionList() {
		return permissionList;
	}

	public void setPermissionList(List<String> permissionList) {
		this.permissionList = permissionList;
	}
	
	/**
	 * 将所有权限汇聚到一起
	 * @param menuList
	 * @return
	 */
	private List<String> setMenuPermissionList(List<Menu> menuList) {
		List<String> resultList = new ArrayList<>();
		StringBuffer sb = new StringBuffer();
		for(Menu menu : menuList) {
			List<MenuPermission> persList = menu.getPersList();
			for(MenuPermission pers : persList) {
				String menuUrl = menu.getMenuUrl();
				menuUrl = menuUrl == null ? "" : menu.getMenuUrl().split("\\?")[0];
				resultList.add(menuUrl + "/" + pers.getActionUrl());
				sb.append(menuUrl + "/" + pers.getActionUrl()).append(",");
			}
		}
		return resultList;
	}

	public String[] getRoleArray() {
		return roleArray;
	}

	public void setRoleArray(String[] roleArray) {
		this.roleArray = roleArray;
	}

	public Map<String, String> getSysSettingMap() {
		return sysSettingMap;
	}

	public void setSysSettingMap(List<Setting> sysSetting) {
		Map<String, String> maps = sysSetting.stream().collect(Collectors.toMap(Setting::getParmCode, Setting::getParmValue, (key1, key2) -> key2));
		this.sysSettingMap = maps;
	}
}
