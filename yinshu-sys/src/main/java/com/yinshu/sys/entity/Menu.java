package com.yinshu.sys.entity;

import java.io.Serializable;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yinshu.utils.PageParam;

@TableName(value="s_menu")
public class Menu extends PageParam<Menu> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private String id;
    
    /**
     * 父级菜单
     */
    private String parentId;
    
    /**
     * 菜单名称
     */
    private String menuName;
    
    /**
     * 菜单图片
     */
    @TableField("MENU_ICON")
    private String menuIcon;
    
    /**
     * 菜单排序
     */
    @TableField("MENU_SORT")
    private Integer menuSort;
    
    /**
     * 菜单URL
     */
    private String menuUrl;
    
    /**
     * 菜单状态
     */
    private String status;
    
    /**
     * 菜单组件
     */
    private String component;
    
    /**
     * 权限标识
     */
    private String perms;
    
    /**
     * (1:所有都显示  2:只大屏显示)
     * 菜单类型
     */
    private String menuType;
    
    /**
     * 是否可见(1:可见 2: 不可见) 主要是为了权限
     */
    private String isVisible;
    
    /**
     * 对应的权限
     */
    @TableField(exist = false)
    private List<MenuPermission> persList;
    
    /**
     * 角色所拥有的权限
     */
    @TableField(exist = false)
    private List<RoleMenuPermission> roleMenuPermissionList;
    
    /**
     * 是否被选中
     */
    @TableField(exist = false)
    private boolean checked = false;
    
    @TableField(exist = false)
    private String plevel;
    
    /**
     * 子菜单
     */
    @TableField(exist = false)
    private List<Menu> children;

	@TableField(exist = false)
	private String roleId;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	public String getMenuName() {
		return menuName;
	}

	public void setMenuName(String menuName) {
		this.menuName = menuName;
	}

	public Integer getMenuSort() {
		return menuSort;
	}

	public void setMenuSort(Integer menuSort) {
		this.menuSort = menuSort;
	}

	public String getMenuUrl() {
		return menuUrl;
	}

	public void setMenuUrl(String menuUrl) {
		this.menuUrl = menuUrl;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public List<Menu> getChildren() {
		return children;
	}

	public void setChildren(List<Menu> children) {
		this.children = children;
	}

	public boolean isChecked() {
		return checked;
	}

	public void setChecked(boolean checked) {
		this.checked = checked;
	}

	public String getMenuIcon() {
		return menuIcon;
	}

	public void setMenuIcon(String menuIcon) {
		this.menuIcon = menuIcon;
	}

	public String getMenuType() {
		return menuType;
	}

	public void setMenuType(String menuType) {
		this.menuType = menuType;
	}

	public String getComponent() {
		return component;
	}

	public void setComponent(String component) {
		this.component = component;
	}

	public String getPerms() {
		return perms;
	}

	public void setPerms(String perms) {
		this.perms = perms;
	}

	public List<MenuPermission> getPersList() {
		return persList;
	}

	public void setPersList(List<MenuPermission> persList) {
		this.persList = persList;
	}

	public String getPlevel() {
		return plevel;
	}

	public void setPlevel(String plevel) {
		this.plevel = plevel;
	}

	public List<RoleMenuPermission> getRoleMenuPermissionList() {
		return roleMenuPermissionList;
	}

	public void setRoleMenuPermissionList(List<RoleMenuPermission> roleMenuPermissionList) {
		this.roleMenuPermissionList = roleMenuPermissionList;
	}

	public String getIsVisible() {
		return isVisible;
	}

	public void setIsVisible(String isVisible) {
		this.isVisible = isVisible;
	}
}
