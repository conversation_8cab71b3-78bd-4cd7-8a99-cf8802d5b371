package com.yinshu.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.List;

@TableName(value="s_role_menu")
public class RoleMenu implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private String id;

    /**
     * 角色ID
     */
    private String roleId;
    
    /**
     * 菜单ID
     */
    private String menuId;
	/**
	 * 权限标识
	 */
	private String perms;
	/**
	 * 权限数组
	 */
	@TableField(exist = false)
	private List<RoleMenuPermission> permissionList;

    
	/**
	 * 页数
	 */
	@TableField(exist = false)
	private int pageNumber;

	/**
	 * 每页数量
	 */
	@TableField(exist = false)
	private int pageSize;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public int getPageNumber() {
		return pageNumber;
	}

	public void setPageNumber(int pageNumber) {
		this.pageNumber = pageNumber;
	}

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}

	public String getMenuId() {
		return menuId;
	}

	public void setMenuId(String menuId) {
		this.menuId = menuId;
	}

	public String getPerms() {
		return perms;
	}

	public void setPerms(String perms) {
		this.perms = perms;
	}

	public List<RoleMenuPermission> getPermissionList() {
		return permissionList;
	}

	public void setPermissionList(List<RoleMenuPermission> permissionList) {
		this.permissionList = permissionList;
	}
}
