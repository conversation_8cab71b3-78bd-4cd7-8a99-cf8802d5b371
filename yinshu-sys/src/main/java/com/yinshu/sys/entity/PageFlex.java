package com.yinshu.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yinshu.utils.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.List;

/**
 * 
 * 动态页面表
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@TableName("s_page_flex")
public class PageFlex extends PageParam<PageFlex> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private String id;

    /**
     * 页面名称
     */
    private String pageName;

    /**
     * 页面标识
     */
    private String pageCode;

    /**
     * 页面类型（1：栅格布局，2：定位布局）
     */
    private Integer pageType;

    /**
     * 栅格列数
     */
    private Integer colNum;

    /**
     * 栅格行高
     */
    private Integer rowHeight;

    /**
     * 页面宽度
     */
    private Integer width;

    /**
     * 页面高度
     */
    private Integer height;

    /**
     * 是否可拖拽
     */
    private Boolean draggable;

    /**
     * 是否可缩放
     */
    private Boolean resizable;

    /**
     * 页面全局参数
     */
    private String pageParams;

    /**
     * 备注
     */
    private String remark;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态 1：可用 0：不可用
     */
    private Integer status;

    /**
     * 布局容器集合
     */
    @TableField(exist = false)
    private List<PageLayout> pageLayoutList;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getPageName() {
        return pageName;
    }

    public void setPageName(String pageName) {
        this.pageName = pageName;
    }
    public String getPageCode() {
        return pageCode;
    }

    public Integer getPageType() {
        return pageType;
    }

    public void setPageType(Integer pageType) {
        this.pageType = pageType;
    }

    public void setPageCode(String pageCode) {
        this.pageCode = pageCode;
    }
    public Integer getColNum() {
        return colNum;
    }

    public void setColNum(Integer colNum) {
        this.colNum = colNum;
    }
    public Integer getRowHeight() {
        return rowHeight;
    }

    public void setRowHeight(Integer rowHeight) {
        this.rowHeight = rowHeight;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public Boolean getDraggable() {
        return draggable;
    }

    public void setDraggable(Boolean draggable) {
        this.draggable = draggable;
    }

    public Boolean getResizable() {
        return resizable;
    }

    public void setResizable(Boolean resizable) {
        this.resizable = resizable;
    }

    public String getPageParams() {
        return pageParams;
    }

    public void setPageParams(String pageParams) {
        this.pageParams = pageParams;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<PageLayout> getPageLayoutList() {
        return pageLayoutList;
    }

    public void setPageLayoutList(List<PageLayout> pageLayoutList) {
        this.pageLayoutList = pageLayoutList;
    }

    @Override
    public String toString() {
        return "PageFlex{" +
            "id=" + id +
            ", pageName=" + pageName +
            ", pageCode=" + pageCode +
            ", colNum=" + colNum +
            ", rowHeight=" + rowHeight +
            ", remark=" + remark +
            ", sort=" + sort +
            ", status=" + status +
        "}";
    }
}