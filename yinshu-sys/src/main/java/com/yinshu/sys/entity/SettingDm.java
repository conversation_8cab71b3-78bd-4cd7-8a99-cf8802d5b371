package com.yinshu.sys.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.yinshu.utils.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

/**
 * 
 * 系统设置表
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@TableName("S_SETTING_DM")
public class SettingDm extends PageParam<SettingDm> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.NONE)
    private String id;

    /**
     * 参数名称
     */
    @TableField("PARM_NAME")
    private String parmName;

    /**
     * 参数标识
     */
    @TableField("PARM_CODE")
    private String parmCode;

    /**
     * 参数值
     */
    @TableField("PARM_VALUE")
    private String parmValue;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 排序
     */
    @TableField("SORT")
    private Integer sort;

    /**
     * 状态 0：可用 1：不可用
     */
    @TableField("STATUS")
    private Integer status;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getParmName() {
        return parmName;
    }

    public void setParmName(String parmName) {
        this.parmName = parmName;
    }
    public String getParmCode() {
        return parmCode;
    }

    public void setParmCode(String parmCode) {
        this.parmCode = parmCode;
    }
    public String getParmValue() {
        return parmValue;
    }

    public void setParmValue(String parmValue) {
        this.parmValue = parmValue;
    }
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "SettingDm{" +
            "id=" + id +
            ", parmName=" + parmName +
            ", parmCode=" + parmCode +
            ", parmValue=" + parmValue +
            ", remark=" + remark +
            ", sort=" + sort +
            ", status=" + status +
        "}";
    }
}