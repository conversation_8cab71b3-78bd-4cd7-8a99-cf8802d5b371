package com.yinshu.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yinshu.utils.PageParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@TableName(value = "s_user")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class User extends PageParam<User> implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private String id;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 登录账号
     */
    private String loginName;

    /**
     * 用户密码
     */
    private String password;

    /**
     * 机构ID
     */
    private String unitId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 对应机构
     */
    @TableField(exist = false)
    private Unit unit;


    /**
     * 手机号
     */
    private String phone;

    /**
     * 部门ID(组织ID)
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 数据来源 1=系统内部 0=第三方同步
     */
    private Integer dataSource;

    /**
     * 在职/离职 1：在职 2：离职
     */
    private Integer state;

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public Unit getUnit() {
        return unit;
    }

    public void setUnit(Unit unit) {
        this.unit = unit;
    }
}
