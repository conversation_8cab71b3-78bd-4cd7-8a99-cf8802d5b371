package com.yinshu.sys.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yinshu.utils.PageParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName(value = "s_unit")
public class Unit extends PageParam<Unit> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 组织ID
     */
    private String id;

    /**
     * 组织名称
     */
    private String uname;

    /**
     * 上级组织
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String parentId;

    /**
     * 组织代码
     */
    private String ucode;

    /**
     * 排序
     */
    private Integer usort;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * id链路
     */
    private String unitPath;

    /**
     * 摄像头机构
     */
    @TableField("CAMERA_UNIT")
    private String cameraUnit;

    /**
     * 机构类型: Account 集团、单位 Department 部门
     */
    private String type;

    /**
     * 是否是集团 1:是 0:否
     */
    private Boolean isGroup;

    /**
     * 路径
     */
    private String path;

    /**
     * 数据来源 1=系统内部 0=第三方同步
     */
    private Integer dataSource;

}
