package com.yinshu.sys.entity;

import java.util.List;

public class MenuTreeNode {
    //节点标签
    private String id;
    //节点标签
    private String label;
    //节点内容
    private String value;
    //父节点标识
    private String parent;
    //是否为叶子节点
    private Boolean leaf;
    /**
     * 对应的所有权限列表
     */
    private List<MenuPermission> permissionList;
    //子节点
    private List<MenuTreeNode> children;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getParent() {
        return parent;
    }

    public void setParent(String parent) {
        this.parent = parent;
    }

    public Boolean getLeaf() {
        return leaf;
    }

    public void setLeaf(Boolean leaf) {
        this.leaf = leaf;
    }

    public List<MenuTreeNode> getChildren() {
        return children;
    }

    public void setChildren(List<MenuTreeNode> children) {
        this.children = children;
    }

    public List<MenuPermission> getPermissionList() {
        return permissionList;
    }

    public void setPermissionList(List<MenuPermission> permissionList) {
        this.permissionList = permissionList;
    }
}
