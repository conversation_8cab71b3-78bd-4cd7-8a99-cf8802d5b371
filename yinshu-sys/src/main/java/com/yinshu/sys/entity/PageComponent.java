package com.yinshu.sys.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yinshu.utils.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

/**
 *
 * 页面组件表
 *
 * <AUTHOR>
 * @since 2024-09-30
 */
@TableName("s_page_component")
public class PageComponent extends PageParam<PageComponent> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private String id;

    /**
     * 组件名称
     */
    private String compName;

    /**
     * 组件标识
     */
    private String compCode;

    /**
     * 组件路径
     */
    private String compPath;

    /**
     * 组件缩略图
     */
    private String compImage;

    /**
     * 组件参数示例
     */
    private String exampleParams;

    /**
     * 组件数据示例
     */
    private String exampleDatas;

    /**
     * 组件事件示例
     */
    private String exampleEvents;

    /**
     * 备注
     */
    private String remark;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态 1：可用 0：不可用
     */
    private Integer status;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getCompName() {
        return compName;
    }

    public void setCompName(String compName) {
        this.compName = compName;
    }
    public String getCompCode() {
        return compCode;
    }

    public void setCompCode(String compCode) {
        this.compCode = compCode;
    }
    public String getCompPath() {
        return compPath;
    }

    public void setCompPath(String compPath) {
        this.compPath = compPath;
    }
    public String getCompImage() {
        return compImage;
    }

    public void setCompImage(String compImage) {
        this.compImage = compImage;
    }
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getExampleParams() {
        return exampleParams;
    }

    public void setExampleParams(String exampleParams) {
        this.exampleParams = exampleParams;
    }

    public String getExampleDatas() {
        return exampleDatas;
    }

    public void setExampleDatas(String exampleDatas) {
        this.exampleDatas = exampleDatas;
    }

    public String getExampleEvents() {
        return exampleEvents;
    }

    public void setExampleEvents(String exampleEvents) {
        this.exampleEvents = exampleEvents;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "PageComponent{" +
                "id=" + id +
                ", compName=" + compName +
                ", compCode=" + compCode +
                ", compPath=" + compPath +
                ", compImage=" + compImage +
                ", remark=" + remark +
                ", sort=" + sort +
                ", status=" + status +
                "}";
    }
}