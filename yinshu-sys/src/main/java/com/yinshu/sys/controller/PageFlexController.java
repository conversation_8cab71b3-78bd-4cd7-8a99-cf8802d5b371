package com.yinshu.sys.controller;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestMapping;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.*;
import com.yinshu.sys.entity.PageFlex;
import com.yinshu.sys.manager.PageFlexManager;

import org.springframework.web.bind.annotation.RestController;

/**
 *
 * 动态页面表 
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@RestController
@RequestMapping("/api/sys/pageFlex")
public class PageFlexController {

	@Autowired
	private PageFlexManager pageFlexManager;

	
	@GetMapping("/pageList")
    public ResultVO<?> pageList(PageFlex entity) {
		IPage<PageFlex> resultList = pageFlexManager.queryPageList(entity);
		return new ResultVO<>(resultList);
    }
    
    @GetMapping("/queryList")
    public ResultVO<?> queryList(PageFlex entity) {
		List<PageFlex> resultList = pageFlexManager.queryList(entity);
		return new ResultVO<>(resultList);
    }

	@GetMapping("/getById/{id}")
	public ResultVO<?> getById(@PathVariable("id") String id){
	return new ResultVO<>(pageFlexManager.getById(id));
	}

	@GetMapping("/getLayoutById/{id}")
	public ResultVO<?> getLayoutById(@PathVariable("id") String id){
		return new ResultVO<>(pageFlexManager.getLayoutById(id));
	}

	@GetMapping("/getLayoutByPageCode/{pageCode}")
	public ResultVO<?> getLayoutByPageCode(@PathVariable("pageCode") String pageCode){
		return new ResultVO<>(pageFlexManager.getLayoutByPageCode(pageCode));
	}

	@GetMapping("/getNextSort")
	public ResultVO<?> getNextSort(){
		return new ResultVO<>(pageFlexManager.getNextSort());
	}

	@PostMapping("/create")
    public ResultVO<?> create(@RequestBody PageFlex entity){
		if(pageFlexManager.save(entity)){
			return new ResultVO<>(entity);
		}else{
			return new ResultVO<>(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()),
					"新增失败，请检查页面标识的唯一性");
		}
	}
	
	@PutMapping("/update")
    public ResultVO<?> update(@RequestBody PageFlex entity){
		if(pageFlexManager.update(entity)){
			return new ResultVO<>(entity);
		}else{
			return new ResultVO<>(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()),
					"修改失败，请检查页面标识的唯一性");
		}
	}

	@DeleteMapping("/remove/{ids}")
	public ResultVO<?> remove(@PathVariable List<String> ids){
		pageFlexManager.remove(ids);
		return new ResultVO<>(ids);
	}


}
