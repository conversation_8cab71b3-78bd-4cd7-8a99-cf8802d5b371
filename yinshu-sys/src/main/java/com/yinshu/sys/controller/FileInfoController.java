package com.yinshu.sys.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.FileInfo;
import com.yinshu.sys.entity.vo.QueryFileInfoParam;
import com.yinshu.sys.service.FileInfoService;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 报送文件管理
 *
 * <AUTHOR>
 * @date 2024/12/27
 */
@RestController
@RequestMapping("/api/sys/fileInfo")
public class FileInfoController {

    private final FileInfoService fileInfoService;

    public FileInfoController(FileInfoService fileInfoService) {
        this.fileInfoService = fileInfoService;
    }


    @GetMapping("/page")
    public ResultVO<IPage<FileInfo>> page(QueryFileInfoParam param) {
        IPage<FileInfo> result = fileInfoService.page(param);
        return new ResultVO<>(ResultVO.SUCCESS_CODE, result);
    }

    @GetMapping("/detail/{id}")
    public ResultVO<FileInfo> detail(@PathVariable Long id) {
        FileInfo result = fileInfoService.detail(id);
        return new ResultVO<>(ResultVO.SUCCESS_CODE, result);
    }

    @PutMapping("/saveOrUpdate")
    public ResultVO<FileInfo> saveOrUpdate(@ModelAttribute FileInfo fileInfo) {
        boolean result = fileInfoService.saveOrUpdate(fileInfo);
        if (!result) {
            return new ResultVO<>(ResultVO.ERROR_CODE, "保存失败...");
        }

        fileInfo.setFile(null); // 防止解析出错
        return new ResultVO<>(ResultVO.SUCCESS_CODE, fileInfo);
    }

    @DeleteMapping("/{id}")
    public ResultVO<Boolean> remove(@PathVariable Long id) {
        boolean result = fileInfoService.remove(id);
        return new ResultVO<>(ResultVO.SUCCESS_CODE, result);
    }

    @DeleteMapping("/batch/{ids}")
    public ResultVO<Boolean> removeBatch(@PathVariable List<Long> ids) {
        boolean result = fileInfoService.removeBatch(ids);
        return new ResultVO<>(ResultVO.SUCCESS_CODE, result);
    }

    @PutMapping("/publish/{id}")
    public ResultVO<Boolean> publish(@PathVariable Long id) {
        boolean result = fileInfoService.publish(id);
        return new ResultVO<>(ResultVO.SUCCESS_CODE, result);
    }

    @PutMapping("/unpublish/{id}")
    public ResultVO<Boolean> unpublish(@PathVariable Long id) {
        boolean result = fileInfoService.unpublish(id);
        return new ResultVO<>(ResultVO.SUCCESS_CODE, result);
    }

}
