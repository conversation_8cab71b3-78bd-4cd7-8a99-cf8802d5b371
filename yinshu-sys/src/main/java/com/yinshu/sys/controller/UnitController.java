package com.yinshu.sys.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.annotation.OperLog;
import com.yinshu.sys.entity.Unit;
import com.yinshu.sys.manager.UnitManager;
import com.yinshu.utils.ResultVO;

/**
 * 机构管理
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/api/sys/unit")
public class UnitController {
	
	@Autowired
	private UnitManager unitManager;
	
	@GetMapping("/pageList")
	@OperLog(operModul="机构管理", operType="查询")
    public ResultVO<?> pageList(Unit entity) {
    	IPage<Unit> resultList = unitManager.queryPageList(entity);
    	return new ResultVO<>(resultList);
    }
	
	@PostMapping("/create")
	@OperLog(operModul="机构管理", operType="新增")
    public ResultVO<?> create(@RequestBody Unit entity){
		unitManager.save(entity);
		return new ResultVO<>(entity);
	}
	
	@PutMapping("/update")
	@OperLog(operModul="机构管理", operType="修改")
    public ResultVO<?> update(@RequestBody Unit entity){
		unitManager.update(entity);
		return new ResultVO<>(entity);
	}
	
	@PostMapping("/getUnitTree")
    public ResultVO<?> getUnitTree(@RequestBody Map<String, Object> params){
		return new ResultVO<>(unitManager.getJsonTreeList());
	}
	
	
	@GetMapping("/getById/{id}")
    public ResultVO<?> getById(@PathVariable(name = "id", required = true) String id){
		return new ResultVO<>(unitManager.getById(id));
	}
	
	@DeleteMapping("/remove/{id}")
    public ResultVO<?> remove(@PathVariable(name = "id", required = true) String id){
		unitManager.remove(id);
		return new ResultVO<>(id);
	}
	
	@DeleteMapping("/removeAll/{ids}")
	@OperLog(operModul="机构管理", operType="删除")
    public ResultVO<?> removeAll(@PathVariable(name = "ids", required = true) List<String> ids){
		unitManager.remove(ids);
		return new ResultVO<>(ids);
	}
	
}
