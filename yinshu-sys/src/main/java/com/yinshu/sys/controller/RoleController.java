package com.yinshu.sys.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.annotation.OperLog;
import com.yinshu.sys.entity.Role;
import com.yinshu.sys.entity.RoleMenu;
import com.yinshu.sys.entity.RoleUser;
import com.yinshu.sys.entity.vo.RoleMenusVO;
import com.yinshu.sys.manager.RoleManager;
import com.yinshu.sys.manager.RoleMenuManager;
import com.yinshu.sys.manager.RoleUserManager;
import com.yinshu.utils.CommonUtils;
import com.yinshu.utils.ResultVO;
import com.yinshu.utils.UUIDGenerator;

@RestController
@RequestMapping("/api/sys/role")
public class RoleController {
	
	@Autowired
	private RoleManager roleManager;
	
	@GetMapping("/pageList")
//	@PreAuthorize("hasAuthority('test')")
	@OperLog(operModul="角色管理", operType="查询")
    public ResultVO<?> pageList(HttpServletRequest request) {
    	Map<String, Object> params = CommonUtils.getRequestParams(request);
    	IPage<Role> resultList = roleManager.queryPageList(params);
    	return new ResultVO<>(resultList);
    }
	
	@PostMapping("/create")
	@OperLog(operModul="角色管理", operType="新增")
    public ResultVO<?> create(@RequestBody Role entity){
		roleManager.save(entity);
		return new ResultVO<>(entity);
	}
	
	@PutMapping("/update")
	@OperLog(operModul="角色管理", operType="修改")
    public ResultVO<?> update(@RequestBody Role entity){
		roleManager.update(entity);
		return new ResultVO<>(entity);
	}
	
	@GetMapping("/getById/{id}")
    public ResultVO<?> getById(@PathVariable(name = "id", required = true) String id){
		return new ResultVO<>(roleManager.getById(id));
	}
	
	@DeleteMapping("/remove/{id}")
	@OperLog(operModul="角色管理", operType="删除")
    public ResultVO<?> remove(@PathVariable(name = "id", required = true) String id){
		roleManager.remove(id);
		return new ResultVO<>(id);
	}
	
	@DeleteMapping("/removeAll/{ids}")
	@OperLog(operModul="角色管理", operType="删除")
    public ResultVO<?> remove(@PathVariable(name = "ids", required = true) List<String> ids){
		roleManager.remove(ids);
		return new ResultVO<>(ids);
	}
	
	@Autowired
	private RoleUserManager roleUserManager;

	@PostMapping("/roleuser/create")
	public ResultVO<?> create(@RequestBody JSONObject json) {
		String roleId = json.getString("roleId");
		String[] userIds = json.getJSONArray("userIds").toJavaObject(String[].class);
		List<RoleUser> userList = new ArrayList<RoleUser>();
		if (userIds != null) {
			for (String userId : userIds) {
				RoleUser entity = new RoleUser();
				entity.setId(UUIDGenerator.uuid());
				entity.setRoleId(roleId);
				entity.setUserId(userId);
				userList.add(entity);
			}
		}
		roleUserManager.save(roleId, userList);
		return new ResultVO<>(roleId);
	}

	/**
	 * 根据角色查询
	 * 
	 * @param roleId
	 * @return
	 */
	@GetMapping("/roleuser/queryListByRoleId/{roleId}")
	public ResultVO<?> queryListByRoleId(@PathVariable(value = "roleId", required = true) String roleId) {
		List<Map<String, String>> resultList = roleUserManager.queryListByRoleId(roleId);
		return new ResultVO<>(resultList);
	}
	
	@Autowired
	private RoleMenuManager roleMenuManager;
	
	
	@PostMapping("/rolemenu/save")
    public ResultVO<?> save(@RequestBody RoleMenusVO roleMenusVO){
		roleMenuManager.save(roleMenusVO.getRoleId(), roleMenusVO.getRoleMenuList());
		return new ResultVO<>(roleMenusVO.getRoleId());
	}

	@GetMapping("/rolemenu/queryRoleMenusByRoleId/{roleId}")
	public ResultVO<?> queryRoleMenusByRoleId(@PathVariable("roleId") String roleId){
		List<RoleMenu> roleMenus = roleMenuManager.queryRoleMenusByRoleId(roleId);
		return new ResultVO<>(roleMenus);
	}
	
}
