package com.yinshu.sys.controller;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.*;
import com.yinshu.sys.entity.JobTask;
import com.yinshu.sys.manager.JobTaskManager;

import org.springframework.web.bind.annotation.RestController;

/**
 *
 *  
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@RestController
@RequestMapping("/api/sys/jobTask")
public class JobTaskController {

	@Autowired
	private JobTaskManager jobTaskManager;

	
	@GetMapping("/pageList")
    public ResultVO<?> pageList(JobTask entity) {
		IPage<JobTask> resultList = jobTaskManager.queryPageList(entity);
		return new ResultVO<>(resultList);
    }
    
    @GetMapping("/queryList")
    public ResultVO<?> queryList(JobTask entity) {
		List<JobTask> resultList = jobTaskManager.queryList(entity);
		return new ResultVO<>(resultList);
    }

	@GetMapping("/getById/{id}")
	public ResultVO<?> getById(@PathVariable("id") String id){
	return new ResultVO<>(jobTaskManager.getById(id));
	}
    
	@PostMapping("/create")
    public ResultVO<?> create(@RequestBody JobTask entity){
		jobTaskManager.save(entity);
		return new ResultVO<>(entity);
	}
	
	@PutMapping("/update")
    public ResultVO<?> update(@RequestBody JobTask entity){
		jobTaskManager.update(entity);
		return new ResultVO<>(entity);
	}

	@DeleteMapping("/remove/{ids}")
	public ResultVO<?> remove(@PathVariable List<String> ids){
		jobTaskManager.remove(ids);
		return new ResultVO<>(ids);
	}


}
