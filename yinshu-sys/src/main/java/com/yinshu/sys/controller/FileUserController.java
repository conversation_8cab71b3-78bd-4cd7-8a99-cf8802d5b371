package com.yinshu.sys.controller;

import com.yinshu.sys.entity.User;
import com.yinshu.sys.service.FileUserService;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 文件-用户关联 管理
 *
 * <AUTHOR>
 * @date 2024/12/27
 */
@RestController
@RequestMapping("/api/sys/fileUser")
public class FileUserController {

    private final FileUserService fileUserService;

    public FileUserController(FileUserService fileRangeService) {
        this.fileUserService = fileRangeService;
    }

    @GetMapping("/users/{fileId}")
    public ResultVO<List<User>> getUsers(@PathVariable Long fileId) {
        return new ResultVO<>(fileUserService.getUsers(fileId));
    }
}
