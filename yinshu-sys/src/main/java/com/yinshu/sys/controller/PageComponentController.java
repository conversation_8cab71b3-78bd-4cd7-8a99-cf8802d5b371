package com.yinshu.sys.controller;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestMapping;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.*;
import com.yinshu.sys.entity.PageComponent;
import com.yinshu.sys.manager.PageComponentManager;

import org.springframework.web.bind.annotation.RestController;

/**
 *
 * 页面组件表 
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@RestController
@RequestMapping("/api/sys/pageComponent")
public class PageComponentController {

	@Autowired
	private PageComponentManager pageComponentManager;

	
	@GetMapping("/pageList")
    public ResultVO<?> pageList(PageComponent entity) {
		IPage<PageComponent> resultList = pageComponentManager.queryPageList(entity);
		return new ResultVO<>(resultList);
    }
    
    @GetMapping("/queryList")
    public ResultVO<?> queryList(PageComponent entity) {
		List<PageComponent> resultList = pageComponentManager.queryList(entity);
		return new ResultVO<>(resultList);
    }

	@GetMapping("/getById/{id}")
	public ResultVO<?> getById(@PathVariable("id") String id){
	return new ResultVO<>(pageComponentManager.getById(id));
	}

	@GetMapping("/getNextSort")
	public ResultVO<?> getNextSort(){
		return new ResultVO<>(pageComponentManager.getNextSort());
	}

	@PostMapping("/create")
    public ResultVO<?> create(@RequestBody PageComponent entity){
		if(pageComponentManager.save(entity)){
			return new ResultVO<>(entity);
		}else{
			return new ResultVO<>(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()),
					"新增失败，请检查组件标识的唯一性");
		}
	}
	
	@PutMapping("/update")
    public ResultVO<?> update(@RequestBody PageComponent entity){
		if(pageComponentManager.update(entity)){
			return new ResultVO<>(entity);
		}else{
			return new ResultVO<>(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()),
					"修改失败，请检查组件标识的唯一性");
		}
	}

	@DeleteMapping("/remove/{ids}")
	public ResultVO<?> remove(@PathVariable List<String> ids){
		pageComponentManager.remove(ids);
		return new ResultVO<>(ids);
	}


}
