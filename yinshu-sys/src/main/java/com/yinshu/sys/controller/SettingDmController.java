package com.yinshu.sys.controller;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestMapping;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.*;
import com.yinshu.sys.entity.SettingDm;
import com.yinshu.sys.manager.SettingDmManager;

import org.springframework.web.bind.annotation.RestController;

/**
 *
 * 系统设置表 
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@RestController
@RequestMapping("/api/sys/settingDm")
public class SettingDmController {

	@Autowired
	private SettingDmManager settingDmManager;

	
	@GetMapping("/pageList")
    public ResultVO<?> pageList(SettingDm entity) {
		IPage<SettingDm> resultList = settingDmManager.queryPageList(entity);
		return new ResultVO<>(resultList);
    }
    
    @GetMapping("/queryList")
    public ResultVO<?> queryList(SettingDm entity) {
		List<SettingDm> resultList = settingDmManager.queryList(entity);
		return new ResultVO<>(resultList);
    }

	@GetMapping("/getById/{id}")
	public ResultVO<?> getById(@PathVariable("id") String id){
	return new ResultVO<>(settingDmManager.getById(id));
	}

	@GetMapping("/getNextSort")
	public ResultVO<?> getNextSort(){
		return new ResultVO<>(settingDmManager.getNextSort());
	}

	@PostMapping("/create")
    public ResultVO<?> create(@RequestBody SettingDm entity){
		if(settingDmManager.check(entity)){
			settingDmManager.save(entity);
			return new ResultVO<>(entity);
		}else{
			return new ResultVO<>(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()),
					"新增失败，请检查参数名称和标识的唯一性");
		}
	}
	
	@PutMapping("/update")
    public ResultVO<?> update(@RequestBody SettingDm entity){
		if(settingDmManager.check(entity)){
			settingDmManager.update(entity);
			return new ResultVO<>(entity);
		}else{
			return new ResultVO<>(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()),
					"修改失败，请检查参数名称和标识的唯一性");
		}
	}

	@DeleteMapping("/remove/{ids}")
	public ResultVO<?> remove(@PathVariable List<String> ids){
		settingDmManager.remove(ids);
		return new ResultVO<>(ids);
	}


}
