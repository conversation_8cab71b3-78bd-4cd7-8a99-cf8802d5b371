package com.yinshu.sys.controller;

import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.annotation.OperLog;
import com.yinshu.sys.entity.Menu;
import com.yinshu.sys.manager.MenuManager;
import com.yinshu.utils.ResultVO;

@RestController
@RequestMapping("/api/sys/menu")
public class MenuController {
	
	@Autowired
	private MenuManager menuManager;
	
	@PostMapping("/pageList")
    public ResultVO<?> pageList(@RequestBody Menu entity) {
    	IPage<Menu> resultList = menuManager.queryPageList(entity);
    	return new ResultVO<>(resultList);
    }
	
	@RequestMapping("/create")
	@OperLog(operModul="菜单管理", operType="新增")
    public ResultVO<?> create(Menu entity){
		menuManager.save(entity);
		return new ResultVO<>(entity);
	}
	
	@PostMapping("/createAll")
	@OperLog(operModul="菜单管理", operType="新增")
    public ResultVO<?> createAll(@RequestBody Menu entity){
		menuManager.saveAll(entity);
		return new ResultVO<>(entity);
	}
	
	@PutMapping("/update")
	@OperLog(operModul="菜单管理", operType="修改")
    public ResultVO<?> update(@RequestBody Menu entity){
		menuManager.update(entity);
		return new ResultVO<>(entity);
	}
	
	@PostMapping("/getJsonTree")
    public ResultVO<?> getMenuTree(@RequestBody JSONObject json){
		return new ResultVO<>(menuManager.getJsonTreeList());
	}

	@GetMapping("/getMenuPermissionTree")
	public ResultVO<?> getMenuPermissionTree(){
		return new ResultVO<>(menuManager.getMenuPermissionTreeList());
	}
	
	@GetMapping("/getById/{id}")
    public ResultVO<?> getById(@PathVariable(name = "id", required = true) String id){
		return new ResultVO<>(menuManager.findById(id));
	}
	
	@DeleteMapping("/remove/{id}")
	@OperLog(operModul="菜单管理", operType="删除")
    public ResultVO<?> remove(@PathVariable(name = "id", required = true) String id){
		menuManager.remove(id);
		return new ResultVO<>(id);
	}
	
	@DeleteMapping("/removeAll/{ids}")
	@OperLog(operModul="菜单管理", operType="删除")
	public ResultVO<?> removeAll(@PathVariable(name = "ids", required = true) List<String> ids) {
		menuManager.remove(ids);
		return new ResultVO<>(ids);
	}
	
}
