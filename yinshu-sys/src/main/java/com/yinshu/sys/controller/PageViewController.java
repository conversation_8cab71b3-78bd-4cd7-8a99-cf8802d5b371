package com.yinshu.sys.controller;

import com.yinshu.sys.entity.PageComponentSource;
import com.yinshu.sys.manager.PageFlexManager;
import com.yinshu.sys.manager.PageViewManager;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 *
 * 组件数据源
 *
 * <AUTHOR>
 * @since 2024-10-16
 */
@RestController
@RequestMapping("/api/sys/bigScreen/pageView")
public class PageViewController {
	@Autowired
	private PageFlexManager pageFlexManager;

	@Autowired
	private PageViewManager pageViewManager;

	@GetMapping("/getLayoutById/{id}")
	public ResultVO<?> getLayoutById(@PathVariable("id") String id){
		return new ResultVO<>(pageFlexManager.getLayoutById(id));
	}

	@GetMapping("/getLayoutByPageCode/{pageCode}")
	public ResultVO<?> getLayoutByPageCode(@PathVariable("pageCode") String pageCode){
		return new ResultVO<>(pageFlexManager.getLayoutByPageCode(pageCode));
	}

	@PostMapping("/loaderData")
    public ResultVO<?> loaderData(@RequestBody PageComponentSource pageComponentSource) {
		pageViewManager.loaderData(pageComponentSource);
		return new ResultVO<>(pageComponentSource.getResultData());
    }


}
