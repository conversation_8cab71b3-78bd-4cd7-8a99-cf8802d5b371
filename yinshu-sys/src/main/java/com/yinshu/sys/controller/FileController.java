package com.yinshu.sys.controller;

import com.yinshu.annotation.OperLog;
import com.yinshu.sys.config.LocalStorageConfig;
import com.yinshu.utils.ResultVO;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 文件管理
 * 基于 MinIO 的对象存储接口
 * author: <PERSON>, <PERSON>
 * date: 2024-12-12 15:19:12
 */
@RestController
@RequestMapping("/api/sys/file")
public class FileController {

	private final LocalStorageConfig localStorageConfig;

    public FileController(LocalStorageConfig localStorageConfig) {
        this.localStorageConfig = localStorageConfig;
    }

    /**
	 * 文件上传
	 */
	@PostMapping("/upload")
	@OperLog(operModul="", operType="文件上传")
    public ResultVO<?> fileUpload(@RequestParam("files")MultipartFile[] files) {
		StringBuilder filePath = new StringBuilder();

		if (!ObjectUtils.isEmpty(files)) {
			for (MultipartFile file : files) {
				String filepath = localStorageConfig.putObject(file);
				filePath.append(filepath).append(";");
			}
		}
		return new ResultVO<>(filePath.toString());
    }
	
	/**
	 * 文件下载
	 */
	@GetMapping("/download/{filePath}")
	@OperLog(operModul="", operType="文件下载")
    public void fileDownload(@PathVariable(name = "filePath") String filePath, HttpServletResponse response) {
		localStorageConfig.downloadFile(filePath, response);
    }

	@DeleteMapping("/delete/{filePath}")
	@OperLog(operModul = "", operType = "文件删除")
	public ResultVO<?> deleteFile(@PathVariable(name = "filePath") String filePath) {
		boolean result = localStorageConfig.removeObject(filePath);
		return new ResultVO<>(ResultVO.SUCCESS_CODE, "删除成功", result);
	}

}
