package com.yinshu.sys.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.Dictionary;
import com.yinshu.sys.entity.DictTreeNode;
import com.yinshu.sys.manager.DictionaryManager;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/sys/dictionary")
public class DictionaryController {
	
	@Autowired
	private DictionaryManager dictionaryManager;
	
	@GetMapping("/pageList")
    public ResultVO<?> pageList(Dictionary entity) {
		IPage<Dictionary> dictionaryIPage = dictionaryManager.queryPageList(entity);
		return new ResultVO<>(dictionaryIPage);
    }

	@GetMapping("/queryTreeList")
	public ResultVO<?> queryTreeList(Dictionary entity) {
		List<DictTreeNode> dictionaryTreeNodeList = dictionaryManager.queryTreeList(entity);
		return new ResultVO<>(dictionaryTreeNodeList);
	}

	@GetMapping("/queryTreeAllList")
	public ResultVO<?> queryTreeAllList(Dictionary entity) {
		List<DictTreeNode> dictionaryTreeNodeList = dictionaryManager.queryTreeAllList(entity);
		return new ResultVO<>(dictionaryTreeNodeList);
	}

	@GetMapping("/queryList")
	public ResultVO<?> queryList(Dictionary entity) {
		List<Dictionary> dictionaryList = dictionaryManager.queryList(entity);
		return new ResultVO<>(dictionaryList);
	}

	@GetMapping("/getById/{id}")
	public ResultVO<?> getById(@PathVariable("id") String id){
		return new ResultVO<>(dictionaryManager.getById(id));
	}

	@GetMapping("/getNextSort")
	public ResultVO<?> getNextSort(Dictionary entity){
		return new ResultVO<>(dictionaryManager.getNextSort(entity));
	}

	@PostMapping("/create")
    public ResultVO<?> create(@RequestBody Dictionary entity){
		if(dictionaryManager.check(entity)){
			dictionaryManager.save(entity);
			return new ResultVO<>(entity);
		}else{
			return new ResultVO<>(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()),
					"新增失败，请检查字典名称和编码的唯一性");
		}
	}
	
	@PutMapping("/update")
    public ResultVO<?> update(@RequestBody Dictionary entity){
		if(dictionaryManager.check(entity)){
			dictionaryManager.update(entity);
			return new ResultVO<>(entity);
		}else{
			return new ResultVO<>(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()),
					"修改失败，请检查字典名称和编码的唯一性");
		}
	}

	@DeleteMapping("/remove/{ids}")
	public ResultVO<?> remove(@PathVariable List<String> ids){
		dictionaryManager.remove(ids);
		return new ResultVO<>(ids);
	}
	
	/**
	 * 根据字典编码获取列表
	 * @return
	 */
	@GetMapping("/getByDicCode/{code}")
	public ResultVO<?> getByDicCode(@PathVariable(name = "code", required = true) String code){
		List<Dictionary> resultList = dictionaryManager.getByDicCode(code); 
		return new ResultVO<>(resultList);
	}
	
	@GetMapping("/treeByDicCode/{dicCode}")
	public ResultVO<?> treeByDicCode(@PathVariable(value = "dicCode", required = true) String dicCode) {
		DictTreeNode dic = dictionaryManager.treeByDicCode(dicCode);
		return new ResultVO<>(dic);
	}
	
	/**
	 * 根据字典编码获取列表
	 * @return
	 */
	@GetMapping("/getListByDicCode/{code}")
	public ResultVO<?> getListByDicCode(@PathVariable(name = "code", required = true) String code){
		List<Dictionary> resultList = dictionaryManager.getListByDicCode(code); 
		return new ResultVO<>(resultList);
	}
	
}
