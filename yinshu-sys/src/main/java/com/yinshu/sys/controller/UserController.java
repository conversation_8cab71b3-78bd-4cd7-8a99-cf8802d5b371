package com.yinshu.sys.controller;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.annotation.OperLog;
import com.yinshu.exception.RestfulAPIException;
import com.yinshu.sys.entity.ResetPassword;
import com.yinshu.sys.entity.SessionUser;
import com.yinshu.sys.entity.Setting;
import com.yinshu.sys.entity.User;
import com.yinshu.sys.manager.SettingManager;
import com.yinshu.sys.manager.UserManager;
import com.yinshu.sys.security.SecurityUtils;
import com.yinshu.sys.utils.PasswordValidator;
import com.yinshu.utils.RedisCache;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/sys/user")
public class UserController {
    @Resource
    SettingManager settingManager;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private UserManager userManager;
    @Value("${system.redis:true}")
    private Boolean redisFlag;

    @PostMapping("/list")
    public ResultVO<List<User>> list(@RequestBody JSONObject json) {
        List<User> result = userManager.queryList(json);
        return new ResultVO<>(ResultVO.SUCCESS_CODE, result);
    }

    @GetMapping("/pageList")
    //@PreAuthorize("hasAuthority('test')")
    @OperLog(operModul = "用户管理", operType = "查询")
    public ResultVO<?> pageList(User entity) {
        IPage<User> resultList = userManager.queryPageList(entity);
        return new ResultVO<>(resultList);
    }

    /**
     * 查询未授权用户
     */
    @PostMapping("/unAuthPageList")
    public ResultVO<?> queryUnAuthPageList(@RequestBody JSONObject json) {
        Map<String, Object> params = JSON.toJavaObject(json, Map.class);
        IPage<User> resultList = userManager.queryUnAuthPageList(params);
        return new ResultVO<>(resultList);
    }

    @PostMapping("/create")
    @OperLog(operModul = "用户管理", operType = "新增")
    public ResultVO<?> create(@RequestBody User entity) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        String hashedPassword = passwordEncoder.encode(entity.getPassword());
        entity.setPassword(hashedPassword);
        userManager.save(entity);
        return new ResultVO<>(entity);
    }

    @PutMapping("/update")
    @OperLog(operModul = "用户管理", operType = "修改")
    public ResultVO<?> update(@RequestBody User entity) {
//        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
//        String hashedPassword = passwordEncoder.encode(entity.getPassword());
//        entity.setPassword(hashedPassword);
        entity.setPassword(null);
        userManager.update(entity);
        return new ResultVO<>(entity);
    }

    @PostMapping("/updatePassword")
    @OperLog(operModul = "用户管理", operType = "修改密码")
    public ResultVO<?> updatePassword(@RequestBody ResetPassword resetPassword) {
        String oldPassword = resetPassword.getOldPassword();
        String newPassword = resetPassword.getNewPassword();
        SessionUser loginUser = SecurityUtils.getLoginUser();
        String id = loginUser.getUser().getId();
        String userName = loginUser.getUsername();
        String password = loginUser.getPassword();
        if (!SecurityUtils.matchesPassword(oldPassword, password)) {
            return new ResultVO<>(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()),
                    "修改密码失败，旧密码错误");
        }
        if (SecurityUtils.matchesPassword(newPassword, password)) {
            return new ResultVO<>(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()),
                    "新密码不能与旧密码相同");
        }
        if (!PasswordValidator.isValid(newPassword)) {
            return new ResultVO<>(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()),
                    "密码强度校验不通过,请重新输入！");
        }
        newPassword = SecurityUtils.encryptPassword(newPassword);
        if (userManager.updatePassword(id, newPassword) > 0) {
            // 更新缓存用户密码
            loginUser.getUser().setPassword(newPassword);
            if (redisFlag) {
                redisCache.setCacheObject("login:" + userName, loginUser);
            }
            return new ResultVO<>(userName);
        }
        return new ResultVO<>(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()),
                "修改密码异常，请联系管理员");
    }

    @PutMapping("/reset/password/{id}")
    @OperLog(operModul = "用户管理", operType = "重置密码")
    public ResultVO<Boolean> resetPassword(@PathVariable("id") String id) {
        if (StringUtils.isEmpty(id)) {
            throw new RestfulAPIException("用户不能为空");
        }
        User entity = userManager.getById(id);
        if (entity == null) {
            throw new RestfulAPIException("用户不存在");
        }
        Setting setting = settingManager.getByCode("password");
        if (setting == null || StringUtils.isEmpty(setting.getId())) {
            throw new RestfulAPIException("密码配置不存在,请先前往系统设置配置密码!");
        }
        if (setting.getStatus().equals(1)) {
            throw new RestfulAPIException("密码配置不可用,请前往系统设置修改配置状态!");
        }
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        String hashedPassword = passwordEncoder.encode(setting.getParmValue());
        entity.setPassword(hashedPassword);
        int result = userManager.updatePassword(id, entity.getPassword());
        if (redisFlag) {
            Object object = redisCache.getCacheObject("login:" + entity.getUserName());
            if (object != null && object instanceof SessionUser) {
                SessionUser sessionUser = (SessionUser) object;
                if (sessionUser.getUser() != null) {
                    sessionUser.getUser().setPassword(entity.getPassword());
                    redisCache.setCacheObject("login:" + sessionUser.getUsername(), sessionUser);
                }
            }
        }
        return new ResultVO<>(result > 0);
    }


    @GetMapping("/getById/{id}")
    public ResultVO<?> getById(@PathVariable(name = "id", required = true) String id) {
        return new ResultVO<>(userManager.getById(id));
    }

    @DeleteMapping("/remove/{id}")
    @OperLog(operModul = "用户管理", operType = "删除")
    public ResultVO<?> remove(@PathVariable(name = "id", required = true) String id) {
        userManager.remove(id);
        return new ResultVO<>(id);
    }

    @DeleteMapping("/removeAll/{ids}")
    @OperLog(operModul = "用户管理", operType = "删除")
    public ResultVO<?> remove(@PathVariable(name = "ids", required = true) List<String> ids) {
        userManager.remove(ids);
        return new ResultVO<>(ids);
    }

}
