package com.yinshu.sys.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.yinshu.sys.entity.MenuPermission;
import com.yinshu.sys.manager.MenuPermissionManager;
import com.yinshu.utils.CommonUtils;
import com.yinshu.utils.ResultVO;

/**
 *
 *  
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@RestController
@RequestMapping("/api/sys/menu/permission")
public class MenuPermissionController {

	@Autowired
	private MenuPermissionManager menuPermissionManager;
	
    @RequestMapping("getList")
    public ResultVO<?> queryList(HttpServletRequest request) {
    	Map<String, Object> params = CommonUtils.getRequestParams(request);
    	List<MenuPermission> resultList = menuPermissionManager.queryList(params);
    	return new ResultVO<>(resultList);
    }
    
	@RequestMapping("/create")
    public ResultVO<?> create(MenuPermission entity){
		String[] actionNames = entity.getActionName().split(",");
		String[] actionUrls = entity.getActionUrl().split(",");
		for(int i = 0; i < actionNames.length; i++) {
			MenuPermission data = new MenuPermission();
			data.setActionName(actionNames[i]);
			data.setActionUrl(actionUrls[i]);
			data.setMenuId(entity.getMenuId());
			menuPermissionManager.save(data);
		}
		return new ResultVO<>(entity);
	}
	
	@RequestMapping("/update")
    public ResultVO<?> update(MenuPermission entity){
		menuPermissionManager.update(entity);
		return new ResultVO<>(entity);
	}
	
	@RequestMapping("/remove")
    public ResultVO<?> remove(@RequestParam("id[]") String[] ids){
		List<String> idList = Arrays.asList(ids);
		menuPermissionManager.remove(idList);
		return new ResultVO<>(ids);
	}
	
	@RequestMapping("/getById")
    public ResultVO<?> getById(HttpServletRequest request){
		return new ResultVO<>(menuPermissionManager.getById(request.getParameter("id")));
	}

}
