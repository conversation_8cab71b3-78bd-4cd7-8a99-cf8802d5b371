package com.yinshu.sys.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.Log;
import com.yinshu.sys.manager.LogManager;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/sys/log")
public class LogController {
	
	@Autowired
	private LogManager logManager;
	
	@GetMapping("/pageList")
    public ResultVO<?> pageList(Log entity) {
		IPage<Log> logIPage = logManager.queryPageList(entity);
		return new ResultVO<>(logIPage);
    }

	@DeleteMapping("/remove/{ids}")
	public ResultVO<?> remove(@PathVariable List<String> ids){
		logManager.remove(ids);
		return new ResultVO<>(ids);
	}
	
}
