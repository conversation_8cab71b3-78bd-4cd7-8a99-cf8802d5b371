package com.yinshu.sys.controller;

import java.io.File;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.annotation.OperLog;
import com.yinshu.exception.APIException;
import com.yinshu.sys.entity.FileUpload;
import com.yinshu.sys.manager.FileUploadManager;
import com.yinshu.utils.DateUtils;
import com.yinshu.utils.FileUploadUtils;
import com.yinshu.utils.ResultVO;

@RestController
@RequestMapping("/api/file")
public class FileUploadController {
	
	private static Logger logger = LoggerFactory.getLogger(FileUploadController.class);
	
	@Autowired
	private Environment environment;
	
	@Autowired
	private FileUploadManager fileUploadManager;

	@PostMapping("/upload")
	@OperLog(operModul="文件上传", operType="新增")
	public ResultVO<?> imageUpload(HttpServletRequest request) {
		try {
			logger.info("图片上传.start---------->>>");
			String date = DateUtils.getDateTime("yyyyMMdd");
			String bathPath = environment.getProperty("attachment.default-path");
			//String path = bathPath + File.separator + date;
			//String fileName = CommonUtils.uploadFiles(request, path);
			Map<String, String> fileName = FileUploadUtils.uploadFiles(request, bathPath);
//			if(StringUtils.isBlank(fileName)){
//				return new ResultVO<>(null);
//			}
			logger.info("图片上传.end---------->>>" + (date + File.separator + fileName));
			//return new ResultVO<String>(path.replaceAll("\\\\", "/") + "/" + fileName);
			return new ResultVO<>(fileName);
		} catch (Exception e) {
			throw new APIException(e.getMessage());
		}
	}
	
	/**
	 * 根据业务id获取文件，完整的url显示地址
	 * @param entity
	 * @return
	 */
    @GetMapping("/getFileList")
    public ResultVO<?> getFileList(FileUpload entity) {
        List<FileUpload> resultList = fileUploadManager.queryList(entity);
        for(FileUpload file : resultList) {
        	file.setUrl(file.getFilePath() == null ? null : environment.getProperty("system.oss-path") + file.getFilePath().replaceAll("\\\\", "/"));
        }
        return new ResultVO<>(resultList);
    }

	
	@GetMapping("/pageList")
    public ResultVO<?> pageList(FileUpload entity) {
		IPage<FileUpload> resultList = fileUploadManager.queryPageList(entity);
		return new ResultVO<>(resultList);
    }
    
    @GetMapping("/queryList")
    public ResultVO<?> queryList(FileUpload entity) {
		List<FileUpload> resultList = fileUploadManager.queryList(entity);
		return new ResultVO<>(resultList);
    }

	@GetMapping("/getById/{id}")
	public ResultVO<?> getById(@PathVariable("id") String id){
	return new ResultVO<>(fileUploadManager.getById(id));
	}
    
	@PostMapping("/create")
    public ResultVO<?> create(@RequestBody FileUpload entity){
		fileUploadManager.save(entity);
		return new ResultVO<>(entity);
	}
	
	@PutMapping("/update")
    public ResultVO<?> update(@RequestBody FileUpload entity){
		fileUploadManager.update(entity);
		return new ResultVO<>(entity);
	}

	@DeleteMapping("/remove/{ids}")
	public ResultVO<?> remove(@PathVariable List<String> ids){
		fileUploadManager.remove(ids);
		return new ResultVO<>(ids);
	}
}
