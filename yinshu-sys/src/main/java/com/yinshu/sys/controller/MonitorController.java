package com.yinshu.sys.controller;

import com.yinshu.utils.ResultVO;
import com.yinshu.utils.server.Server;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/sys/monitor")
public class MonitorController {
	
	@GetMapping("/getServerInfo")
    public ResultVO<?> getServerInfo()  throws Exception{
		Server server = new Server();
		server.copyTo();
		return new ResultVO<>(server);
    }
	
}
