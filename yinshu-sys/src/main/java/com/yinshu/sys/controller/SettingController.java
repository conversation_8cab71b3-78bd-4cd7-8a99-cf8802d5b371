package com.yinshu.sys.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.annotation.OperLog;
import com.yinshu.sys.entity.Setting;
import com.yinshu.sys.manager.SettingManager;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/sys/setting")
public class SettingController {
	
	@Autowired
	private SettingManager settingManager;
	
	@GetMapping("/pageList")
    public ResultVO<?> pageList(Setting entity) {
		IPage<Setting> logIPage = settingManager.queryPageList(entity);
		return new ResultVO<>(logIPage);
    }

	@GetMapping("/queryList")
	public ResultVO<?> queryList(Setting entity) {
		List<Setting> resultList = settingManager.queryList(entity);
		return new ResultVO<>(resultList);
	}

	@GetMapping("/getById/{id}")
	public ResultVO<?> getById(@PathVariable("id") String id){
		return new ResultVO<>(settingManager.getById(id));
	}

	@GetMapping("/getNextSort")
	public ResultVO<?> getNextSort(){
		return new ResultVO<>(settingManager.getNextSort());
	}

	@PostMapping("/create")
	@OperLog(operModul="系统设置", operType="新增")
	public ResultVO<?> create(@RequestBody Setting entity){
		if(settingManager.check(entity)){
			settingManager.save(entity);
			return new ResultVO<>(entity);
		}else{
			return new ResultVO<>(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()),
					"新增失败，请检查参数名称和标识的唯一性");
		}
	}

	@PutMapping("/update")
	@OperLog(operModul="系统设置", operType="修改")
	public ResultVO<?> update(@RequestBody Setting entity){
		if(settingManager.check(entity)){
			settingManager.update(entity);
			return new ResultVO<>(entity);
		}else{
			return new ResultVO<>(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()),
					"修改失败，请检查参数名称和标识的唯一性");
		}
	}
	
	@DeleteMapping("/remove/{id}")
	@OperLog(operModul="系统设置", operType="删除")
	public ResultVO<?> remove(@PathVariable String id){
		settingManager.remove(id);
		return new ResultVO<>(id);
	}

	@DeleteMapping("/removeAll/{ids}")
	@OperLog(operModul="系统设置", operType="删除")
	public ResultVO<?> removeAll(@PathVariable List<String> ids){
		settingManager.remove(ids);
		return new ResultVO<>(ids);
	}
	
	@GetMapping("/refreshCache")
	public ResultVO<?> refreshCache() {
		return new ResultVO<>(settingManager.refreshCache());
	}
	
    @GetMapping("/getByCode/{code}")
    public ResultVO<Setting> getByCode(@PathVariable("code") String code) {
        return new ResultVO<>(settingManager.getByCode(code));
    }
	
}
