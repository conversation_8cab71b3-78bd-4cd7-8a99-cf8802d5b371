package com.yinshu.sys.config;

import io.minio.*;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * MinIO 工具类
 * @deprecated 已改为本地存储，MinIO 可能过不了信创
 */
@Configuration
public class MinioConfig implements InitializingBean {

    private static final String BUCKET_NAME = "report";

    @Value(value = "${spring.minio.host}")
    private String host;

    @Value(value = "${spring.minio.access-key}")
    private String accessKey;

    @Value(value = "${spring.minio.secret-key}")
    private String secretKey;

    private MinioClient minioClient;


    protected final static Logger logger = LoggerFactory.getLogger(MinioConfig.class);

    @Override
    public void afterPropertiesSet() throws Exception {
        try {
            this.minioClient = MinioClient.builder()
                .endpoint(host)
                .credentials(accessKey, secretKey)
                .build();
        } catch (Exception e) {
            logger.error("minio初始化失败: {}", e.toString());
        }
    }

    /**
     * 上传
     */
    public String putObject(MultipartFile multipartFile) {
        // bucket 不存在，创建
        if (bucketExists(BUCKET_NAME)) {
            try (InputStream inputStream = multipartFile.getInputStream()) {
                // 上传文件的名称
                String fileName = System.currentTimeMillis() + "_" + multipartFile.getOriginalFilename();

                // PutObjectOptions，上传配置(文件大小，内存中文件分片大小)
                minioClient.putObject(PutObjectArgs.builder()
                    .bucket(BUCKET_NAME)
                    .stream(inputStream, multipartFile.getSize(), -1)
                    .object(fileName)
                    .contentType(multipartFile.getContentType())
                    .build());

                return UriUtils.encode(fileName, StandardCharsets.UTF_8);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return "";
    }

    /**
     * 创建桶
     *
     * @param bucketName 桶名称
     */
    public boolean bucketExists(String bucketName) {
        try {
            if (!minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build())) {
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
                minioClient.setBucketPolicy(SetBucketPolicyArgs.builder().bucket(bucketName).build());
            }
            return true;
        } catch (Exception e) {
            logger.error("{} 桶创建失败: {}", bucketName, e.toString());
        }
        return false;
    }

    /**
     * 下载
     *
     * @param fileName 文件名称
     */
    public void downloadFile(String fileName, HttpServletResponse response) {
        try (InputStream inputStream = this.minioClient.getObject(GetObjectArgs
                .builder().bucket(BUCKET_NAME).object(fileName)
                .build())
        ) {

            StatObjectResponse statresponse = minioClient.statObject(StatObjectArgs.builder().bucket(BUCKET_NAME).object(fileName).build());
            String filename= UriUtils.decode(fileName, StandardCharsets.UTF_8);
            response.setContentType(statresponse.contentType());
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION,
                    "attachment;filename=" + URLEncoder.encode(filename.split("_")[1],"UTF-8"));
            IOUtils.copy(inputStream, response.getOutputStream());

        } catch (Exception e) {
            logger.error("下载文件失败: {}", e.toString());
        }
    }

    /**
     * 删除文件
     *
     * @param fileName 文件名称
     * @return 成功删除
     * @throws RuntimeException TODO Unknown
     */
    public boolean removeObject(String fileName) {
        try {
            this.minioClient.removeObject(RemoveObjectArgs.builder().bucket(BUCKET_NAME).object(fileName).build());
            return true;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

   public void putDirObject(String dirName) {
       try {
           if (bucketExists(BUCKET_NAME)) {
               final ObjectWriteResponse response = minioClient.putObject(
                       PutObjectArgs.builder()
                               .bucket(BUCKET_NAME).object(dirName)
                               .stream(new ByteArrayInputStream(new byte[]{}), 0, -1)
                               .build());
           }

       } catch (Exception e) {
           logger.error(e.toString());
       }
   }

}
