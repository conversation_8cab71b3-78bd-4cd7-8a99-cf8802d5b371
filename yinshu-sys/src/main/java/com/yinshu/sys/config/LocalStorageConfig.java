package com.yinshu.sys.config;

import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;

/**
 * 本地存储配置
 *
 * <AUTHOR>
 * @date 2025/03/24
 */
@Component
public class LocalStorageConfig implements InitializingBean {

    private static final String BUCKET_NAME = "attachment";

    /**
     * 文件存储路径
     */
    @Value("${system.profile}")
    private String rootPath;

    protected final static Logger logger = LoggerFactory.getLogger(LocalStorageConfig.class);

    @Override
    public void afterPropertiesSet() throws Exception {
        // 初始化存储根目录
        Path root = Paths.get(rootPath);
        if (!Files.exists(root)) {
            Files.createDirectories(root);
        }
    }

    /**
     * 上传对象
     *
     * @param multipartFile Multipart 文件
     * @return {@link String }
     */
    public String putObject(MultipartFile multipartFile) {
        return putObject(multipartFile, BUCKET_NAME);
    }

    /**
     * 上传对象
     *
     * @param multipartFile Multipart 文件
     * @param bucketName    存储桶名称
     * @return {@link String }
     */
    public String putObject(MultipartFile multipartFile, String bucketName) {
        if (bucketExists(bucketName)) {
            try (InputStream inputStream = multipartFile.getInputStream()) {
                return saveFile(inputStream, multipartFile.getOriginalFilename(), bucketName);
            } catch (Exception e) {
                throw new RuntimeException("文件上传失败", e);
            }
        }
        return "";
    }

    /**
     * 上传对象
     *
     * @param inputStream 输入流
     * @param filename    文件名
     * @return {@link String }
     */
    public String putObject(InputStream inputStream, String filename) {
        return putObject(inputStream, filename, BUCKET_NAME);
    }

    /**
     * 上传对象
     *
     * @param inputStream 输入流
     * @param filename    文件名
     * @param bucketName  存储桶名称
     * @return {@link String }
     */
    public String putObject(InputStream inputStream, String filename, String bucketName) {
        if (bucketExists(bucketName)) {
            return saveFile(inputStream, filename, bucketName);
        }
        return "";
    }

    public String saveFile(InputStream inputStream, String filename, String bucketName) {
        try (InputStream is = inputStream) {
            String saveName = generateFileName(bucketName, filename);
            Path targetPath = getFilePath(bucketName, saveName);

            Files.copy(is, targetPath, StandardCopyOption.REPLACE_EXISTING);
            return UriUtils.encode(saveName, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("文件保存失败", e);
        }
    }

    public String putLocalfilw(File file, String fileName, String bucketName) {
        if (bucketExists(bucketName)) {
            try (InputStream inputStream = new FileInputStream(file)) {
                String newFileName = generateFileName(bucketName, fileName);
                Path targetPath = getFilePath(bucketName, newFileName);

                Files.copy(inputStream, targetPath, StandardCopyOption.REPLACE_EXISTING);
                file.delete();
                return UriUtils.encode(newFileName, StandardCharsets.UTF_8);
            } catch (Exception e) {
                throw new RuntimeException("本地文件上传失败", e);
            }
        }
        return "";
    }

    public boolean bucketExists(String bucketName) {
        try {
            Path bucketPath = Paths.get(rootPath, bucketName);
            if (!Files.exists(bucketPath)) {
                Files.createDirectories(bucketPath);
            }
            return true;
        } catch (Exception e) {
            logger.error("目录创建失败: {}", e.getMessage());
            return false;
        }
    }

    public void downloadFile(String fileName, HttpServletResponse response) {
        try {
            String decodedFileName = UriUtils.decode(fileName, StandardCharsets.UTF_8);
            String[] parts = decodedFileName.split("_");
            String bucketName = parts.length > 2 ? parts[0] : BUCKET_NAME;

            Path filePath = getFilePath(bucketName, decodedFileName);
            if (!Files.exists(filePath)) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
                return;
            }

            response.setContentType(Files.probeContentType(filePath));
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION,
                    "attachment;filename=\"" + UriUtils.encode(getDisplayFileName(parts), StandardCharsets.UTF_8) + "\"");

            try (InputStream is = Files.newInputStream(filePath);
                 OutputStream os = response.getOutputStream()) {
                IOUtils.copy(is, os);
            }
        } catch (Exception e) {
            logger.error("文件下载失败", e);
        }
    }

    public InputStream readFile(String fileName) {
        try {
            String decodedFileName = UriUtils.decode(fileName, StandardCharsets.UTF_8);
            String[] parts = decodedFileName.split("_");
            String bucketName = parts.length > 2 ? parts[0] : BUCKET_NAME;

            return Files.newInputStream(getFilePath(bucketName, decodedFileName));
        } catch (Exception e) {
            throw new RuntimeException("文件读取失败", e);
        }
    }

    public String getFilePath(String fileName) {
        try {
            String decodedFileName = UriUtils.decode(fileName, StandardCharsets.UTF_8);
            String[] parts = decodedFileName.split("_");
            String bucketName = parts.length > 2 ? parts[0] : BUCKET_NAME;

            return Paths.get(rootPath, bucketName, decodedFileName).toString();
        } catch (Exception e) {
            throw new RuntimeException("路径获取失败", e);
        }
    }

    public boolean removeObject(String fileName) {
        try {
            Path filePath = Paths.get(getFilePath(fileName));
            return Files.deleteIfExists(filePath);
        } catch (Exception e) {
            throw new RuntimeException("文件删除失败", e);
        }
    }

    public void putDirObject(String dirName) {
        try {
            Path dirPath = Paths.get(rootPath, BUCKET_NAME, dirName);
            Files.createDirectories(dirPath);
        } catch (Exception e) {
            logger.error("目录创建失败", e);
        }
    }

    // 辅助方法
    private String generateFileName(String bucketName, String originalName) {
        return bucketName + "_" + System.currentTimeMillis() + "_" + originalName;
    }

    private Path getFilePath(String bucketName, String fileName) {
        return Paths.get(rootPath, bucketName, fileName);
    }

    private String getDisplayFileName(String[] parts) {
        return parts.length > 2 ? parts[2] : parts[1];
    }
}