package com.yinshu.sys.http;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.exception.APIException;
import com.yinshu.exception.DvisualTokenException;
import com.yinshu.http.HttpTemplateAbstract;
import com.yinshu.sys.entity.Setting;
import com.yinshu.sys.manager.SettingManager;
import com.yinshu.utils.EncodeUtils;
import com.yinshu.utils.RedisCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.concurrent.TimeUnit;

/**
 * 和大数据平台进行http请求统一封装
 *
 * <AUTHOR>
 */
@Component
public class DvisualHttpTemplate extends HttpTemplateAbstract {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SettingManager settingManager;


    /**
     * post 请求
     *
     * @param url        请求地址
     * @param jsonObject 请求参数 JSON
     * @return JSONObject
     */
    @Override
    public JSONObject post(String url, JSONObject jsonObject) {
        return this.post(url, jsonObject, true);
    }

    /**
     * post 请求
     *
     * @param url        请求地址
     * @param jsonObject 请求参数 JSON
     * @param isRetry    是否重试
     * @return JSONObject
     */
    private JSONObject post(String url, JSONObject jsonObject, boolean isRetry) {
        JSONObject result = new JSONObject();
        Object dvisualAuth = redisCache.getCacheObject("dvisual:auth");
        Setting settingBaseUrl = settingManager.getByCode("dvisual-baseUrl");
        Setting settingAppId = settingManager.getByCode("dvisual-appid");
        Setting settingSecret = settingManager.getByCode("dvisual-secret");

        if (!StringUtils.hasText(settingBaseUrl.getParmValue()) || !StringUtils.hasText(settingAppId.getParmValue()) || !StringUtils.hasText(settingSecret.getParmValue())) {
            throw new APIException("「大数据平台」基础配置不能为空");
        }

        if (dvisualAuth == null) {
            JSONObject tokenJson = new JSONObject();
            tokenJson.put("appid", settingAppId.getParmValue());
            tokenJson.put("time", EncodeUtils.encode(Long.toString(System.currentTimeMillis()), settingSecret.getParmValue()));
            JSONObject tokenResult = super.post(settingBaseUrl.getParmValue() + "/api/auth", tokenJson);
            redisCache.setCacheObject("dvisual:auth", tokenResult, tokenResult.getLong("expire") - 100000, TimeUnit.MILLISECONDS);
            url = convertUrl(url, tokenResult.getString("token"));
        } else {
            JSONObject authJson = (JSONObject) dvisualAuth;
            url = convertUrl(url, authJson.getString("token"));
        }

        try {
            result = super.post(settingBaseUrl.getParmValue() + url, jsonObject);
        } catch (DvisualTokenException e) {
            //重试一次
            if (isRetry) {
                redisCache.deleteObject("dvisual:auth");//如果token过期，删除本地，再次请求
                result = this.post(url, jsonObject, false);
            }
        }
        return result;
    }

    private String convertUrl(String url, String token) {
        if (url.contains("?")) {
            return url + "&token=" + token;
        } else {
            return url + "/" + token;
        }
    }
}
