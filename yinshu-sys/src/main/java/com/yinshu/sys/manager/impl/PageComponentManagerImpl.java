package com.yinshu.sys.manager.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.PageComponent;
import com.yinshu.sys.manager.PageComponentManager;
import com.yinshu.sys.service.PageComponentService;
import com.yinshu.utils.SnowflakeIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 页面组件表 
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@Service
public class PageComponentManagerImpl implements PageComponentManager {

	@Autowired
	private PageComponentService pageComponentService;

	@Autowired
	private SnowflakeIdGenerator snowflakeIdGenerator;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<PageComponent> queryList(PageComponent entity) {
		List<PageComponent> resultList = pageComponentService.queryList(entity);
		return resultList;
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<PageComponent> queryPageList(PageComponent entity) {
		IPage<PageComponent> resultList = pageComponentService.queryPageList(entity);
		return resultList;
	}

	@Override
	public boolean check(PageComponent entity) {
		return pageComponentService.check(entity);
	}

	@Override
	public Integer getNextSort() {
		Integer maxSort = pageComponentService.getMaxSort();
		if(maxSort != null){
			return maxSort+1;
		}else{
			return 1;
		}
	}
	
	/**
	 * 保存
	 * @param entity
	 */
	@Transactional
	public boolean save(PageComponent entity) {
		if(pageComponentService.check(entity)){
			entity.setId(snowflakeIdGenerator.nextIdStr());
			return pageComponentService.save(entity);
		}else{
			return false;
		}
	}
	
	/**
	 * 删除
	 * @param id
	 */
	
	public void remove(String id) {
		pageComponentService.removeById(id);	
	}
	
	/**
	 * 批量删除
	 * @param idList
	 */
	public void remove(List<String> idList) {
		pageComponentService.removeByIds(idList);	
	}
	
	/**
	 * 更新
	 * @param entity
	 */
	@Transactional
	public boolean update(PageComponent entity) {
		if(pageComponentService.check(entity)){
			return pageComponentService.updateById(entity);
		}else{
			return false;
		}
	}
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	public PageComponent getById(String id) {
		return pageComponentService.getById(id);
	}
	
}
