package com.yinshu.sys.manager.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.yinshu.sys.entity.Role;
import com.yinshu.sys.entity.RoleUser;
import com.yinshu.sys.manager.RoleUserManager;
import com.yinshu.sys.service.RoleUserService;
import com.yinshu.utils.UUIDGenerator;

@Transactional
@Component
public class RoleUserManagerImpl implements RoleUserManager {
	
	@Autowired
	private RoleUserService roleUserService;
	
	/**
	 * 保存數據
	 * @param entity
	 */
	public void save(RoleUser entity) {
		entity.setId(UUIDGenerator.uuid());
		roleUserService.save(entity);			
	}
	
	
	/**
	 * 保存数据的时候先删除，要做事务考虑
	 */
	public void save(String roleId, List<RoleUser> entityList) {
		roleUserService.removeByRoleId(roleId);
		roleUserService.saveBatch(entityList);
	}
	
	/**
	 * 保存数据的时候先删除，要做事务考虑
	 */
	public void saveAll(String classify, String roleId, List<RoleUser> list) {
		if("0".equals(classify) || "1".equals(classify)) {//如果是从“全部”的table页过来
			roleUserService.removeByRoleId(roleId);
		}
		for(RoleUser entity : list) {
			entity.setId(UUIDGenerator.uuid());
			roleUserService.save(entity);			
		}
	}
	
	/**
	 * 根据条件查询
	 * @param roleId
	 * @return
	 */
	public List<Map<String, String>> queryList(Map<String, Object> params) {
		return roleUserService.queryList(params);
	}
	
	/**
	 * 根据角色查询
	 * @param roleId
	 * @return
	 */
	public List<Map<String, String>> queryListByRoleId(String roleId) {
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("roleId", roleId);
		return queryList(params);
	}
	
	/**
	 * 根据用户查询对应的所有角色
	 * @param userId
	 * @return
	 */
	@Deprecated
	public List<Map<String, String>> queryRoleListByUserId(String userId) {
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("userId", userId);
		return roleUserService.queryList(params);
	}
	
	/**
	 * 根据用户查询对应的所有角色
	 * @param userId
	 * @return
	 */
	public List<Role> queryRoleByUserId(String userId) {
		List<Role> resultList = new ArrayList<Role>();
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("userId", userId);
		List<Map<String, String>> tempList = roleUserService.queryList(params);
		for(Map<String, String> entry : tempList) {
			Role role = new Role();
			role.setId(entry.get("ROLE_ID"));
			role.setRoleName(entry.get("ROLE_NAME"));
			role.setRoleCode(entry.get("ROLE_CODE"));
			resultList.add(role);
		}
		return resultList;
	}
	
	public void remove(List<String> idList) {
		roleUserService.removeByIds(idList);
	}

}
