package com.yinshu.sys.manager.impl;

import java.util.Map;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.JobTask;
import com.yinshu.sys.service.JobTaskService;
import com.yinshu.sys.manager.JobTaskManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Service;
import com.yinshu.utils.SnowflakeIdGenerator;
import com.yinshu.utils.DateUtils;
import org.springframework.util.MethodInvoker;

import javax.annotation.Resource;

/**
 *  
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Service
public class JobTaskManagerImpl implements JobTaskManager, ApplicationRunner {

	protected final static Logger logger = LoggerFactory.getLogger(JobTaskManagerImpl.class);

	@Autowired
	private JobTaskService jobTaskService;

	@Autowired
	private SnowflakeIdGenerator snowflakeIdGenerator;

	@Resource
	private ApplicationContext applicationContext;

	@Autowired
	private ThreadPoolTaskScheduler taskScheduler;

	/**
	 * 用于获取配置，考虑多项目部署公用一个库任务会重复执行，所以使用此配置判断任务是否能够执行，
	 * 不配置taskExecuteFlag，则默认执行，忽略此判断
	 */
	@Value("#{'${job.task.execute.flag:}'.split(',')}")
	private Set<String> executeFlagSet;

	private static final Map<String, ScheduledFuture<?>> TASK_MAP = new ConcurrentHashMap<>();
	private static final Integer STATUS_STOP = 0;
	private static final Integer STATUS_START = 1;
	private static final Integer STATUS_ERROR = 2;

	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<JobTask> queryList(JobTask entity) {
		List<JobTask> resultList = jobTaskService.queryList(entity);
		return resultList;
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<JobTask> queryPageList(JobTask entity) {
		IPage<JobTask> resultList = jobTaskService.queryPageList(entity);
		return resultList;
	}
	
	/**
	 * 保存
	 * @param entity
	 */
	public void save(JobTask entity) {
		entity.setId(snowflakeIdGenerator.nextIdStr());
		entity.setCreateTime(DateUtils.getNow());
		jobTaskService.save(entity);
		startTask(entity);
	}
	
	/**
	 * 删除
	 * @param id
	 */
	
	public void remove(String id) {
		stopTask(id);
		jobTaskService.removeById(id);	
	}
	
	/**
	 * 批量删除
	 * @param idList
	 */
	public void remove(List<String> idList) {
		if(CollectionUtils.isNotEmpty(idList)){
			idList.forEach(id -> stopTask(id));
			jobTaskService.removeByIds(idList);
		}
	}
	
	/**
	 * 更新
	 * @param entity
	 */
	public void update(JobTask entity) {
		entity.setUpdateTime(DateUtils.getNow());
		if(STATUS_START.equals(entity.getStatus()) && checkTaskExecuteFlag(entity)){
			entity.setTaskErrorInfo("");
		}
		jobTaskService.updateById(entity);
		updateTask(entity);
	}
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	public JobTask getById(String id) {
		return jobTaskService.getById(id);
	}


	//定时任务处理------------------------------------------------------------

	// 启动任务
	public void startTask(JobTask jobTask) {
		if(STATUS_START.equals(jobTask.getStatus()) && checkTaskExecuteFlag(jobTask)){
			try {
				Runnable task = createdTask(jobTask.getBeanName(), jobTask.getMethodName());
				ScheduledFuture<?> future = taskScheduler.schedule(task, new CronTrigger(jobTask.getCronExpression()));
				TASK_MAP.put(jobTask.getId(), future);
			} catch (Exception e) {
				logger.error("jobTask创建失败：", e);
				jobTask.setStatus(STATUS_ERROR);
				jobTask.setTaskErrorInfo(e.getMessage());
				jobTaskService.updateById(jobTask);
				stopTask(jobTask.getId());
			}
		}
	}

	// 停止任务
	public void stopTask(String id) {
		ScheduledFuture<?> future = TASK_MAP.get(id);
		if (future != null) {
			future.cancel(true); // true表示中断正在执行的任务
			TASK_MAP.remove(id);
		}
		logger.info("定时任务停止-id：{}", id);
	}

	// 修改周期（先停止再启动）
	public void updateTask(JobTask jobTask) {
		stopTask(jobTask.getId());
		startTask(jobTask);
	}

	public Runnable createdTask(String beanName, String methodName) throws Exception {
		MethodInvoker invoker = new MethodInvoker();
		invoker.setTargetObject(applicationContext.getBean(beanName));
		invoker.setTargetMethod(methodName);
		invoker.prepare();
		Runnable runnable = () -> {
			try {
				invoker.invoke();
			} catch (Exception e) {
				logger.error("jobTask-"+beanName+"-"+methodName+"-执行出错：", e);
			}
		};
		return runnable;
	}

	public boolean checkTaskExecuteFlag(JobTask jobTask){
		String taskExecuteFlag = jobTask.getTaskExecuteFlag();
		if(StringUtils.isNotBlank(taskExecuteFlag)){
			if(!executeFlagSet.contains(taskExecuteFlag)){
				return false;
			}
		}
		return true;
	}


	@Override
	public void run(ApplicationArguments args) throws Exception {
		List<JobTask> list = jobTaskService.list();
		list.forEach(jobTask -> {
			startTask(jobTask);
		});
	}
}
