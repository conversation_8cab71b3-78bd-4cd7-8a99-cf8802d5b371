package com.yinshu.sys.manager;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.Log;

import java.util.List;


public interface LogManager {
	/**
	 * 分页查询
	 * @param entity
	 * @return
	 */
	IPage<Log> queryPageList(Log entity);

	/**
	 * 新增
	 * @param entity
	 */
	void save(Log entity);
	/**
	 * 单笔删除
	 * @param id
	 */
	void remove(String id);

	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);
}
