package com.yinshu.sys.manager;

import java.util.List;

import com.yinshu.sys.entity.Menu;
import com.yinshu.sys.entity.Role;
import com.yinshu.sys.entity.RoleMenu;

public interface RoleMenuManager {
	
	void save(List<RoleMenu> list);

	void save(String roleId, List<RoleMenu> list);

	/**
	 * 根据菜单ID删除
	 * @param menuIds
	 */
	void removeByMenuIds(String[] menuIds);
	/**
	 * 根据角色查询所有菜单
	 * @param roleId
	 * @return
	 */
	List<Menu> queryMenuByRoleId(String roleId);

	List<RoleMenu> queryRoleMenusByRoleId(String roleId);
	
	/**
	 * 根据角色查询所有菜单,支持多角色
	 * @param roleId
	 * @return
	 */
	List<Menu> queryMenuListByRoles(List<Role> roleIds);
	
}
