package com.yinshu.sys.manager;

import java.util.Map;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.JobTask;

/**
 *  
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
public interface JobTaskManager {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<JobTask> queryList(JobTask entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<JobTask> queryPageList(JobTask entity);
	
	/**
	 * 保存
	 * @param entity
	 */
	void save(JobTask entity);
	
	/**
	 * 删除
	 * @param id
	 */
	
	void remove(String id);
	
	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);
	
	/**
	 * 更新
	 * @param entity
	 */
	void update(JobTask entity);
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	JobTask getById(String id);

}

