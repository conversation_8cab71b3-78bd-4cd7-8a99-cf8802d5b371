package com.yinshu.sys.manager;

import java.util.List;
import java.util.Map;

import com.yinshu.sys.entity.Role;
import com.yinshu.sys.entity.RoleUser;

public interface RoleUserManager {
	
	void remove(List<String> idList);
	
	void save(RoleUser entity);
	
	void save(String roleId, List<RoleUser> list);
	
	/**
	 * 根据条件查询
	 * @param roleId
	 * @return
	 */
	List<Map<String, String>> queryList(Map<String, Object> params);
	
	/**
	 * 根据角色查询
	 * @param roleId
	 * @return
	 */
	List<Map<String, String>> queryListByRoleId(String roleId);
	
	/**
	 * 保存数据的时候先删除，要做事务考虑
	 */
	void saveAll(String classify, String roleId, List<RoleUser> list);
	
	/**
	 * 根据用户查询对应的所有角色
	 * @param userId
	 * @return
	 */
	List<Map<String, String>> queryRoleListByUserId(String userId);
	
	/**
	 * 根据用户查询对应的所有角色
	 * @param userId
	 * @return
	 */
	List<Role> queryRoleByUserId(String userId);
	
}
