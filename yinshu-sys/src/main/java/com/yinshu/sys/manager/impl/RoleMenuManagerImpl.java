package com.yinshu.sys.manager.impl;

import com.yinshu.sys.entity.Menu;
import com.yinshu.sys.entity.Role;
import com.yinshu.sys.entity.RoleMenu;
import com.yinshu.sys.entity.RoleMenuPermission;
import com.yinshu.sys.manager.RoleMenuManager;
import com.yinshu.sys.manager.RoleMenuPermissionManager;
import com.yinshu.sys.service.RoleMenuService;
import com.yinshu.utils.UUIDGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Transactional
@Component
public class RoleMenuManagerImpl implements RoleMenuManager {
	
	@Autowired
	private RoleMenuService roleMenuService;
	@Autowired
	private RoleMenuPermissionManager roleMenuPermissionManager;

	/**
	 * 保存数据的时候先删除，要做事务考虑
	 */
	public void save(List<RoleMenu> list) {
		roleMenuService.removeByRoleId(list.get(0).getRoleId());
		for(RoleMenu entity : list) {
			entity.setId(UUIDGenerator.uuid());
			roleMenuService.save(entity);			
		}
	}
	
	/**
	 * 保存数据的时候先删除，要做事务考虑
	 */
	public void save(String roleId, List<RoleMenu> list) {
		//删除菜单权限标识表
		roleMenuPermissionManager.deleteByRoleId(roleId);
		//删除角色菜单表
		roleMenuService.removeByRoleId(roleId);
		for(RoleMenu entity : list) {
			//插入新的菜单表
			entity.setId(UUIDGenerator.uuid());
			roleMenuService.save(entity);
			//插入新的菜单权限标识表
			List<RoleMenuPermission> permissionList = entity.getPermissionList();
			if(permissionList != null){
				for(RoleMenuPermission permission : permissionList) {
					permission.setRoleMenuId(entity.getId());
					roleMenuPermissionManager.save(permission);
				}
			}
		}
	}

	@Override
	public void removeByMenuIds(String[] menuIds) {
		roleMenuPermissionManager.deleteByMenuIds(Arrays.asList(menuIds));
		roleMenuService.removeByMenuIds(menuIds);
	}


	/**
	 * 根据角色查询所有菜单
	 * @param roleId
	 * @return
	 */
	public List<Menu> queryMenuByRoleId(String roleId) {
		List<Menu> resultList = new ArrayList<Menu>();
		List<Map<String, String>> roleMenuList = roleMenuService.queryMenuListByRoleId(roleId);
		for(Map<String, String> entry : roleMenuList) {
			Menu menu = new Menu();
			menu.setId(entry.get("ID"));
			menu.setMenuName(entry.get("MENU_NAME"));
			menu.setParentId(entry.get("PARENT_ID"));
			menu.setMenuUrl(entry.get("MENU_URL"));
			menu.setMenuIcon(entry.get("MENU_ICON"));
			menu.setChecked(entry.get("ROLE_MENU_ID") != null);
			resultList.add(menu);
		}
		return resultList;
	}

	/**
	 * 根据角色查询所有菜单对应关系
	 * @param roleId
	 * @return
	 */
	@Override
	public List<RoleMenu> queryRoleMenusByRoleId(String roleId) {
		return roleMenuService.queryRoleMenusByRoleId(roleId);
	}

	/**
	 * 根据角色查询所有菜单,支持多角色
	 * @param roleIds
	 * @return
	 */
	public List<Menu> queryMenuListByRoles(List<Role> roleIds) {
		List<Menu> resultList = new ArrayList<Menu>();
//		Map<String, List<Menu>> menuMap = new HashMap<>();
//		List<Map<String, String>> roleMenuList = roleMenuService.queryMenuListByRoles(roleIds);
//		for(Map<String, String> entry : roleMenuList) {
//			Menu menu = new Menu();
//			menu.setId(entry.get("ID"));
//			menu.setMenuName(entry.get("MENU_NAME"));
//			menu.setParentId(entry.get("PARENT_ID"));
//			menu.setMenuUrl(entry.get("MENU_URL"));
//			menu.setMenuIcon(entry.get("MENU_ICON"));
//			menu.setMenuType(entry.get("MENU_TYPE"));
//			resultList.add(menu);
//			if(!StringUtils.isEmpty(menu.getParentId())) {
//				if(menuMap.containsKey(menu.getParentId())) {
//					menuMap.get(menu.getParentId()).add(menu);
//				} else {
//					List<Menu> subItem = new ArrayList<>();
//					subItem.add(menu);
//					menuMap.put(menu.getParentId(), subItem);
//				}
//			}
//		}
//		
//		for(Menu menu : resultList) {
//			if(StringUtils.isEmpty(menu.getParentId()) && menuMap.get(menu.getId()) != null) {
//				List<Menu> tempList = menuMap.get(menu.getId());
//				boolean isExist = false;
//				for(Menu m : tempList) {
//					if(m.getMenuUrl().equals(menu.getMenuUrl())) {
//						menu.setMenuUrl(m.getMenuUrl());
//						isExist = true;
//					}
//				}
//				if(isExist == false) {
//					menu.setMenuUrl(tempList.get(0).getMenuUrl());					
//				}
//			}
//		}
		
		return resultList;
	}

}
