package com.yinshu.sys.manager;

import java.util.Map;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.PageLayout;

/**
 * 页面布局表 
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
public interface PageLayoutManager {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<PageLayout> queryList(PageLayout entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<PageLayout> queryPageList(PageLayout entity);
	
	/**
	 * 保存
	 * @param entity
	 */
	void save(PageLayout entity);

	/**
	 * 批量保存
	 * @param entityList
	 */
	void saveBatch(List<PageLayout> entityList);
	
	/**
	 * 删除
	 * @param id
	 */
	
	void remove(String id);
	
	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);

	/**
	 * 删除
	 * @param idList
	 */

	void removeByPageFlexIds(List<String> idList);
	
	/**
	 * 更新
	 * @param entity
	 */
	void update(PageLayout entity);
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	PageLayout getById(String id);


}

