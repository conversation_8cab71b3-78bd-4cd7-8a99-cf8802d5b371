package com.yinshu.sys.manager.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.yinshu.sys.entity.DictTreeNode;
import com.yinshu.sys.entity.Dictionary;
import com.yinshu.sys.manager.DictionaryManager;
import com.yinshu.sys.service.DictionaryService;
import com.yinshu.utils.DateUtils;
import com.yinshu.utils.SnowflakeIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class DictionaryManagerImpl implements DictionaryManager {
    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;
    @Autowired
    private DictionaryService dictionaryService;


//    @Cacheable(cacheNames="s_dictionary")
    public List<Dictionary> queryList(Dictionary params) {
        return dictionaryService.queryList(params);
    }

    public IPage<Dictionary> queryPageList(Dictionary entity) {
        return dictionaryService.queryPageList(entity);
    }

    public List<DictTreeNode> queryTreeList(Dictionary entity) {
        List<Dictionary> dictionaryList = dictionaryService.queryList(entity);
        List<DictTreeNode> dictionaryTreeNodeList = dictionaryList.stream().map(dictionary -> {
            DictTreeNode treeNode = new DictTreeNode();
            treeNode.setId(dictionary.getId());
            treeNode.setLabel(dictionary.getDicName());
            treeNode.setValue(dictionary.getDicCode());
            treeNode.setClassify(dictionary.getClassify());
            treeNode.setParent(dictionary.getParentCode());
            //判断是否叶子节点
            treeNode.setLeaf(dictionary.getChildrenNum() != null && dictionary.getChildrenNum() == 0);
            return treeNode;
        }).collect(Collectors.toList());
        return dictionaryTreeNodeList;
    }

	/*@Override
	public List<DictTreeNode> queryTreeAllList(Dictionary entity) {
		List<DictTreeNode> dictTreeNodes = this.queryTreeList(entity);
		if(dictTreeNodes.size() > 0){
			for (DictTreeNode node : dictTreeNodes) {
				Dictionary nodeParams = new Dictionary();
				nodeParams.setClassify(entity.getClassify());
				nodeParams.setClassify(entity.getClassify());
				nodeParams.setParentCode(node.getValue());
				List<DictTreeNode> children = this.queryTreeAllList(nodeParams);
				if(children.size() > 0){
					node.setChildren(children);
				}
			}
		}
		return dictTreeNodes;
	}*/

    @Override
    public List<DictTreeNode> queryTreeAllList(Dictionary entity) {
        List<Dictionary> dictionaryList = dictionaryService.queryTreeAllList(entity);
        List<DictTreeNode> dictTreeNodes = this.queryTreeAllList(dictionaryList, entity);
        return dictTreeNodes;
    }

    private List<DictTreeNode> queryTreeAllList(List<Dictionary> allList, Dictionary entity) {
        List<DictTreeNode> dictTreeNodes = this.filterTreeList(allList, entity);
        if (dictTreeNodes.size() > 0) {
            for (DictTreeNode node : dictTreeNodes) {
                Dictionary nodeParams = new Dictionary();
                nodeParams.setClassify(entity.getClassify());
                nodeParams.setParentCode(node.getValue());
                List<DictTreeNode> children = this.queryTreeAllList(allList, nodeParams);
                if (children.size() > 0) {
                    node.setChildren(children);
                }
            }
        }
        return dictTreeNodes;
    }

    private List<DictTreeNode> filterTreeList(List<Dictionary> allList, Dictionary entity) {
        List<Dictionary> dictionaryList = allList.stream()
                .filter(item ->
                        (StringUtils.isEmpty(entity.getClassify()) || StringUtils.equals(item.getClassify(), entity.getClassify()))
                                && (StringUtils.isEmpty(entity.getParentCode()) ? StringUtils.isEmpty(item.getParentCode())
                                : StringUtils.equals(item.getParentCode(), entity.getParentCode()))
                ).collect(Collectors.toList());
        List<DictTreeNode> dictionaryTreeNodeList = dictionaryList.stream().map(dictionary -> {
            DictTreeNode treeNode = new DictTreeNode();
            treeNode.setId(dictionary.getId());
            treeNode.setLabel(dictionary.getDicName());
            treeNode.setValue(dictionary.getDicCode());
            treeNode.setClassify(dictionary.getClassify());
            treeNode.setParent(dictionary.getParentCode());
            treeNode.setLeaf(false);
            return treeNode;
        }).collect(Collectors.toList());
        return dictionaryTreeNodeList;
    }

    @Override
    public boolean check(Dictionary entity) {
        return dictionaryService.check(entity);
    }

    /**
     * 查看
     *
     * @param id
     * @return
     */
    public Dictionary getById(String id) {
        return dictionaryService.getById(id);
    }

    /**
     * 新增
     *
     * @param entity
     */
    @CacheEvict(cacheNames="s_dictionary", allEntries = true)
    public void save(Dictionary entity) {
        entity.setId(snowflakeIdGenerator.nextIdStr());
        Date now = DateUtils.getNow();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        dictionaryService.save(entity);
    }

    /**
     * 修改
     *
     * @param entity
     */
    @CacheEvict(cacheNames="s_dictionary", allEntries = true)
    @Transactional
    public void update(Dictionary entity) {
        if (StringUtils.isNotEmpty(entity.getDicCode())) {
            Dictionary oldDictionary = dictionaryService.getById(entity.getId());
            if (!entity.getDicCode().equals(oldDictionary.getDicCode())) {
                dictionaryService.updateByParentCode(entity.getClassify(), oldDictionary.getDicCode(), entity.getDicCode());
            }
        }
        entity.setUpdateTime(DateUtils.getNow());
        dictionaryService.updateById(entity);
    }

    /**
     * code修改后，需同时修改关联子节点的parentCode
     *
     * @param oldParentCode
     * @param newParentCode
     * @return
     */
    @Override
    public int updateByParentCode(String classify, String oldParentCode, String newParentCode) {
        return dictionaryService.updateByParentCode(classify, oldParentCode, newParentCode);
    }

    /**
     * 单笔删除
     *
     * @param id
     */
    @CacheEvict(cacheNames="s_dictionary", allEntries = true)
    public void remove(String id) {
        this.remove(Collections.singletonList(id));
    }

    /**
     * 批量删除
     *
     * @param idList
     */
    @CacheEvict(cacheNames="s_dictionary", allEntries = true)
    @Transactional
    public void remove(List<String> idList) {
        List<Dictionary> dictionariyList = dictionaryService.listByIds(idList);
        for (Dictionary dictionary : dictionariyList) {
            //递归删除子节点
            removeByParentCode(dictionary);
        }
        dictionaryService.removeByIds(idList);
    }

    /**
     * 根据ParentCode递归删除
     *
     * @param entity
     */
    @Transactional
    @Override
    public void removeByParentCode(Dictionary entity) {
        Dictionary params = new Dictionary();
        params.setClassify(entity.getClassify());
        params.setParentCode(entity.getDicCode());
        List<Dictionary> dictionariyList = dictionaryService.queryList(params);
        if (dictionariyList.size() > 0) {
            for (Dictionary dictionary : dictionariyList) {
                removeByParentCode(dictionary);
            }
        }
        dictionaryService.removeByParentCode(params);
    }

    /**
     * 根据字典编码获取列表
     *
     * @return
     */
    public List<Dictionary> getByDicCode(String dicCode) {
        Dictionary entity = new Dictionary();
        entity.setDicCode(dicCode);
        //List<Dictionary> dictionaryList = dictionaryService.queryList(entity);
        List<Dictionary> dictionaryList = queryList(entity);
        return dictionaryList;
    }

    @Override
    public Integer getNextSort(Dictionary entity) {
        Integer maxSort = dictionaryService.getMaxSort(entity);
        if (maxSort != null) {
            return maxSort + 1;
        } else {
            return 1;
        }

    }

    @Override
    public DictTreeNode treeByDicCode(String dicCode) {
        Dictionary root = dictionaryService.getByDicCodeSingle(dicCode);
        if (root == null) {
            return null;
        }
        return buildTreeNode(root);
    }

    /**
     * @param ids 字典id集合
     * @return java.util.Map<java.lang.String, com.yinshu.sys.entity.Dictionary>
     * <AUTHOR>
     * @description //TODO 批量获取字典
     * @date 2025/7/9 16:01
     **/
    @Override
    public Map<String, Dictionary> getMapByIds(List<String> ids) {
        if (!CollectionUtils.isEmpty(ids)) {
            List<Dictionary> list = dictionaryService.listByIds(ids);
            if (!CollectionUtils.isEmpty(list)) {
                return list.stream().collect(Collectors.toMap(Dictionary::getId, item -> item, (key1, key2) -> key2));
            }
        }
        return Collections.emptyMap();
    }

    private DictTreeNode buildTreeNode(Dictionary dict) {
        DictTreeNode node = new DictTreeNode();
        node.setId(dict.getId());
        node.setLabel(dict.getDicName());
        node.setValue(dict.getDicCode());
        node.setParent(dict.getParentCode());
        node.setClassify(dict.getClassify());

        // 查询所有直接子节点
        Dictionary param = new Dictionary();
        param.setParentCode(dict.getDicCode());
        List<Dictionary> children = dictionaryService.queryList(param);

        if (children != null && !children.isEmpty()) {
            List<DictTreeNode> childNodes = new ArrayList<>();
            for (Dictionary child : children) {
                childNodes.add(buildTreeNode(child));
            }
            node.setChildren(childNodes);
            node.setLeaf(false);
        } else {
            node.setChildren(new ArrayList<>());
            node.setLeaf(true);
        }
        return node;
    }
    
    
    /**
     * 根据字典编码获取列表
     *
     * @return
     */
    @Cacheable(cacheNames="s_dictionary", key="#dicCode")
    public List<Dictionary> getListByDicCode(String dicCode) {
        Dictionary entity = new Dictionary();
        entity.setParentCode(dicCode);
        List<Dictionary> dictionaryList = dictionaryService.queryNormalList(entity);
        return dictionaryList;
    }
    
    
    
    
}
