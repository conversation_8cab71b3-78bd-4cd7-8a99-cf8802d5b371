package com.yinshu.sys.manager.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.Setting;
import com.yinshu.sys.manager.SettingManager;
import com.yinshu.sys.service.SettingService;
import com.yinshu.utils.SnowflakeIdGenerator;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
public class SettingManagerImpl implements SettingManager {
    
    private final SnowflakeIdGenerator snowflakeIdGenerator;
    
    private final SettingService settingService;


    public SettingManagerImpl(SnowflakeIdGenerator snowflakeIdGenerator, SettingService settingService) {
        this.snowflakeIdGenerator = snowflakeIdGenerator;
        this.settingService = settingService;
    }

    /**
     * 分页查询
     * @param entity
     * @return
     */
    @Override
    public IPage<Setting> queryPageList(Setting entity) {
        return settingService.queryPageList(entity);
    }

    @Override
    public List<Setting> queryList(Setting entity) {
        return settingService.queryList(entity);
    }
    
    /**
     * 根据code获取值
     * @param parmCode
     * @return
     */
    @Cacheable(cacheNames="s_setting", key="#parmCode")
    public Setting getByCode(String parmCode) {
		Setting entity = new Setting();
		entity.setParmCode(parmCode);
		//entity.setStatus(0);
		List<Setting> resultList = queryList(entity);
        return !resultList.isEmpty() ? resultList.get(0) : new Setting();
    }
    
//    /**
//     * 根据code获取值
//     * @param parmCode
//     * @return
//     */
//    @Cacheable(cacheNames="s_setting", key="#parmCode + '_' + #status")
//    public Setting getByCode(String parmCode, int status) {
//		Setting entity = new Setting();
//		entity.setParmCode(parmCode);
//		List<Setting> resultList = queryList(entity);
//        return !resultList.isEmpty() ? resultList.get(0) : new Setting();
//    }

    @Override
    public boolean check(Setting entity) {
        return settingService.check(entity);
    }

    @Override
    public Integer getNextSort() {
        Integer maxSort = settingService.getMaxSort();
        if(maxSort != null){
            return maxSort+1;
        }else{
            return 1;
        }
    }

    /**
     * 查看
     * @param id
     * @return
     */
    public Setting getById(String id) {
        return settingService.getById(id);
    }

    /**
     * 新增
     * @param entity
     */
    @CachePut(cacheNames="s_setting", key="#entity.parmCode")
    public Setting save(Setting entity) {
        entity.setId(snowflakeIdGenerator.nextIdStr());
        settingService.save(entity);
        return entity;
    }

    /**
     * 修改
     * @param entity
     */
    @Transactional
    @CachePut(cacheNames="s_setting", key="#entity.parmCode")
    public Setting update(Setting entity) {
        settingService.updateById(entity);

        return entity;
    }

    /**
     * 单笔删除
     * @param id
     */
    @Override
    //@CacheEvict(cacheNames="s_setting", key="#parmCode")
    public void remove(String id) {
        settingService.removeById(id);
    }

    /**
     * 批量删除
     * @param idList
     */
    @Override
    @CacheEvict(cacheNames="s_setting", allEntries=true)
    public void remove(List<String> idList) {
        settingService.removeByIds(idList);
    }
    
    /**
     * 刷新缓存
     */
    @CacheEvict(cacheNames="s_setting", allEntries=true)
    public List<Setting> refreshCache() {
        return settingService.queryList(new Setting());
    }

}
