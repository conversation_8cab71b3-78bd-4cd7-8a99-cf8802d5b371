package com.yinshu.sys.manager.impl;

import com.yinshu.sys.entity.RoleMenuPermission;
import com.yinshu.sys.manager.RoleMenuPermissionManager;
import com.yinshu.sys.service.RoleMenuPermissionService;
import com.yinshu.utils.SnowflakeIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class RoleMenuPermissionManagerImpl implements RoleMenuPermissionManager {
	@Autowired
	private SnowflakeIdGenerator snowflakeIdGenerator;
	@Resource
	private RoleMenuPermissionService roleMenuPermissionService;


	@Override
	public boolean save(RoleMenuPermission entity) {
		entity.setId(snowflakeIdGenerator.nextIdStr());
		return roleMenuPermissionService.save(entity);
	}

	@Override
	public int deleteByRoleId(String roleId) {
		return roleMenuPermissionService.deleteByRoleId(roleId);
	}

	@Override
	public int deleteByMenuIds(List<String> idList) {
		return roleMenuPermissionService.deleteByMenuIds(idList);
	}

	@Override
	public int deleteByRoleMenuIds(List<String> idList) {
		return roleMenuPermissionService.deleteByRoleMenuIds(idList);
	}
}
