package com.yinshu.sys.manager.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.Log;
import com.yinshu.sys.manager.LogManager;
import com.yinshu.sys.service.LogService;
import com.yinshu.utils.DateUtils;
import com.yinshu.utils.SnowflakeIdGenerator;
import com.yinshu.utils.UUIDGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class LogManagerImpl implements LogManager {
    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;
    @Autowired
    private LogService logService;

    /**
     * 分页查询
     * @param entity
     * @return
     */
    @Override
    public IPage<Log> queryPageList(Log entity) {
        return logService.queryPageList(entity);
    }

    /**
     * 新增
     * @param entity
     */
    @Async
    public void save(Log entity) {
        entity.setId(snowflakeIdGenerator.nextIdStr());
        entity.setCreateTime(DateUtils.getNow());
        logService.save(entity);
    }

    /**
     * 单笔删除
     * @param id
     */
    @Override
    public void remove(String id) {
        logService.removeById(id);
    }

    /**
     * 批量删除
     * @param idList
     */
    @Override
    public void remove(List<String> idList) {
        logService.removeByIds(idList);
    }
}
