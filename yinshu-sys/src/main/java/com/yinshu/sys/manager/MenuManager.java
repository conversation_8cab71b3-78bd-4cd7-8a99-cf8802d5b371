package com.yinshu.sys.manager;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.Menu;
import com.yinshu.sys.entity.MenuTreeNode;

public interface MenuManager {
	
	/**
	 * 分页查询
	 * @param params
	 * @return
	 */
	IPage<Menu> queryPageList(Menu entity);

	/**
	 * 保存
	 * @param entity
	 */
	void save(Menu entity);
	
	/**
	 * 保存
	 * @param entity
	 */
	void saveAll(Menu entity);
	
	/**
	 * 删除
	 * @param id
	 */
	
	void remove(String id);
	
	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);
	
	/**
	 * 更新
	 * @param entity
	 */
	void update(Menu entity);
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	Menu getById(String id);
	
	/**
	 * 根据ID获取对象(自定义方法)
	 * @param id
	 * @return
	 */
	Menu findById(String id);
	
	/**
	 * 过滤掉不显示的菜单
	 * @return
	 */
	List<Map<String, Object>> filterJsonTreeList(List<Menu> rootList);
	
	/**
	 * 获取Json格式的树结构
	 * @return
	 */
	List<Map<String, Object>> getJsonTreeList();
	
	/**
	 * 获取Json格式的树结构
	 * @return
	 */
	List<Map<String, Object>> getJsonTreeList(List<Menu> rootList);

	/**
	 * 获取树结构,包含菜单及权限
	 * @return
	 */
	List<MenuTreeNode> getMenuPermissionTreeList();
	
	/**
	 * 保存树结构菜单
	 */
	void saveTree(Menu entity);
	
	/**
	 * 条件查询
	 * @param params
	 * @return
	 */
	List<Menu> queryList(Map<String, Object> params);
	
}
