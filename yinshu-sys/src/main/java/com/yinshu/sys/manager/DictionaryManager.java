package com.yinshu.sys.manager;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.DictTreeNode;
import com.yinshu.sys.entity.Dictionary;

import java.util.List;
import java.util.Map;


public interface DictionaryManager {

	/**
	 * 按条件查询
	 * @param entity
	 * @return
	 */
	List<Dictionary> queryList(Dictionary entity);

	IPage<Dictionary> queryPageList(Dictionary entity);

	List<DictTreeNode> queryTreeList(Dictionary entity);

	List<DictTreeNode> queryTreeAllList(Dictionary entity);

	boolean check(Dictionary entity);

	/**
	 * 查看
	 * @param id
	 * @return
	 */
	Dictionary getById(String id);

	/**
	 * 新增
	 * @param entity
	 */
	void save(Dictionary entity);

	/**
	 * 修改
	 * @param entity
	 */
	void update(Dictionary entity);
	/**
	 * code修改后，需同时修改关联子节点的parentCode
	 * @param oldParentCode
	 * @param newParentCode
	 * @return
	 */
	int updateByParentCode(String classify, String oldParentCode, String newParentCode);

	/**
	 * 单笔删除
	 * @param id
	 */
	void remove(String id);

	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);

	/**
	 * 根据ParentCode递归删除
	 * @param entity
	 */
	void removeByParentCode(Dictionary entity);

	/**
	 * 获取最大排序+1
	 * @param entity
	 * @return
	 */
	Integer getNextSort(Dictionary entity);

	/**
	 * 根据字典编码获取列表
	 * @return
	 */
	List<Dictionary> getByDicCode(String dicCode);

	
	/**
	 * 根据字典编码获取树形列表
	 * @return
	 */
	DictTreeNode treeByDicCode(String dicCode);

	/**
	 * <AUTHOR>
	 * @description //TODO 批量获取字典
	 * @date 2025/7/9 16:01
	 * @param ids 字典id集合
	 * @return java.util.Map<java.lang.String,com.yinshu.sys.entity.Dictionary>
	 **/
	Map<String, Dictionary> getMapByIds(List<String> ids);
	
	/**
	 * 根据字典编码获取列表
	 * @return
	 */
	List<Dictionary> getListByDicCode(String dicCode);
}
