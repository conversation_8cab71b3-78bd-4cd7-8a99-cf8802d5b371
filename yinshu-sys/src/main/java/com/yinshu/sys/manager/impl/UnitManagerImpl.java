package com.yinshu.sys.manager.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.Unit;
import com.yinshu.sys.manager.UnitManager;
import com.yinshu.sys.service.UnitService;
import com.yinshu.utils.DateUtils;
import com.yinshu.utils.UUIDGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;

@Component
public class UnitManagerImpl implements UnitManager {
	
	@Autowired
	private UnitService unitService;

	// 顶级机构ID
	private static String topId;
	
	/**
	 * 分页查询
	 */
	@Override
	public IPage<Unit> queryPageList(Unit entity) {
		return unitService.queryPageList(entity);
	}

	/**
	 * 按条件查询
	 * @param params
	 * @return
	 */
	public List<Unit> queryList(Map<String, Object> params) {
		return unitService.queryList(params);
	}
	
	/**
	 * 获取Json格式的树结构
	 * 
	 * @return
	 */
	public List<Map<String, Object>> getJsonTreeList() {
		Map<String, Object> parma = new HashMap<String, Object>();
		List<Unit> rootList = unitService.queryTreeList(parma);

		/** 先获取一级菜单 */
		List<Unit> menuList = new ArrayList<Unit>();
		for (Unit entity : rootList) {
			if(StringUtils.isEmpty(entity.getParentId())){
				menuList.add(entity);
			}
		}

		/** 在递归获取子菜单 */
		List<Map<String, Object>> listMap = new ArrayList<Map<String, Object>>();
		for (Unit menu : menuList) {
			Map<String, Object> treeMap = new TreeMap<>();
			treeMap.put("value", menu.getId());
			treeMap.put("label", menu.getUname());
			//treeMap.put("ucode", menu.getId());
			treeMap.put("parentId", menu.getParentId());
			List<Map<String, Object>> subList = buildChilTree2(menu, rootList);
			if (subList.size() > 0) {
				treeMap.put("children", subList);
			}
			listMap.add(treeMap);
		}

		if (listMap.size() == 0) {
			Map<String, Object> treeMap = new TreeMap<>();
			treeMap.put("text", "请新增机构");
			listMap.add(treeMap);
		}

		return listMap;
	}
	
	private List<Map<String, Object>> buildChilTree2(Unit pNode, List<Unit> rootList) {
		List<Map<String, Object>> childList = new ArrayList<Map<String, Object>>();
		for (Unit menuNode : rootList) {
			if (!StringUtils.isEmpty((menuNode.getParentId()))) {
				if (menuNode.getParentId().equals(pNode.getId())) {
					Map<String, Object> subMap = new TreeMap<>();
					subMap.put("value", menuNode.getId());
					subMap.put("label", menuNode.getUname());
					//subMap.put("ucode", menuNode.getId());
					subMap.put("parentId", menuNode.getParentId());
					List<Map<String, Object>> subList = buildChilTree2(menuNode, rootList);
					if (subList.size() > 0) {
						subMap.put("children", subList);
					}
					childList.add(subMap);
				}
			}
		}
		return childList;
	}
	
	/**
	 * 查看
	 * @param id
	 * @return
	 */
	public Unit getById(String id) {
		return unitService.getById(id);
	}

	@Override
	public String getById() {
		if (ObjectUtils.isEmpty(topId)) {
			Map<String, Object> parma = new HashMap<>();
			parma.put("parentid", "topId");
			List<Unit> listUnits = unitService.queryList(parma);
			if (!listUnits.isEmpty()) {
				topId = listUnits.get(0).getId();
				return topId;
			} else {
				return null;
			}
		} else {
			return topId;
		}
	}
	
	/**
	 * 新增
	 * @param entity
	 */
	public void save(Unit entity) {
		entity.setId(UUIDGenerator.uuid());
		entity.setCreateTime(DateUtils.getNow());
		entity.setUnitPath(entity.getId());
		if (StringUtils.hasText(entity.getParentId())) {
			Unit parentUnit = this.getById(entity.getParentId());
			if(parentUnit != null) {
				entity.setUnitPath(parentUnit.getUnitPath() + "/" + entity.getId());
			}
		}
		unitService.save(entity);		
	}
	
	/**
	 * 修改
	 * @param entity
	 */
	public void update(Unit entity) {
		entity.setUnitPath(entity.getId());
		if (StringUtils.hasText(entity.getParentId())) {
			Unit parentUnit = this.getById(entity.getParentId());
			if(parentUnit != null) {
				entity.setUnitPath(parentUnit.getUnitPath() + "/" + entity.getId());
			}
		}
		unitService.updateById(entity);	
	}
	
	/**
	 * 单笔删除
	 * @param id
	 */
	public void remove(String id) {
		unitService.removeById(id);
	}
	
	/**
	 * 批量删除
	 * @param idList
	 */
	public void remove(List<String> idList) {
		unitService.removeByIds(idList);
	}

}
