package com.yinshu.sys.manager;

import java.util.Map;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.SettingDm;

/**
 * 系统设置表 
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public interface SettingDmManager {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<SettingDm> queryList(SettingDm entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<SettingDm> queryPageList(SettingDm entity);

	boolean check(SettingDm entity);

	Integer getNextSort();
	
	/**
	 * 保存
	 * @param entity
	 */
	void save(SettingDm entity);
	
	/**
	 * 删除
	 * @param id
	 */
	
	void remove(String id);
	
	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);
	
	/**
	 * 更新
	 * @param entity
	 */
	void update(SettingDm entity);
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	SettingDm getById(String id);

}

