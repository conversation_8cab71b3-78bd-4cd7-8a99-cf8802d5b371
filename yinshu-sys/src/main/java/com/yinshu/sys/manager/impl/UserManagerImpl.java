package com.yinshu.sys.manager.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.User;
import com.yinshu.sys.manager.UserManager;
import com.yinshu.sys.service.UserService;
import com.yinshu.utils.DateUtils;
import com.yinshu.utils.UUIDGenerator;

@Component
public class UserManagerImpl implements UserManager {
	
	@Autowired
	private UserService userService;
	
	/**
	 * 分页查询
	 */
	@Override
	public IPage<User> queryPageList(User entity) {
		return userService.queryPageList(entity);
	}
	
	/**
	 * 条件未授权的用户
	 * @param page
	 * @param params
	 * @return
	 */
	public IPage<User> queryUnAuthPageList(Map<String, Object> params) {
		return userService.queryUnAuthPageList(params);
	}

	/**
	 * 按条件查询
	 * @param params
	 * @return
	 */
	public List<User> queryList(Map<String, Object> params) {
		return userService.queryList(params);
	}
	
	/**
	 * 查看
	 * @param id
	 * @return
	 */
	public User getById(String id) {
		return userService.getById(id);
	}
	
	/**
	 * 新增
	 * @param entity
	 */
	public void save(User entity) {
		entity.setId(UUIDGenerator.uuid());
		entity.setCreateTime(DateUtils.getNow());
		User userByLoginName = getUserByLoginName(entity.getLoginName());
		if (userByLoginName != null) {
			throw new RuntimeException("登录账号:" + entity.getLoginName() + "已存在");
		}
		userService.save(entity);		
	}
	
	/**
	 * 修改
	 * @param entity
	 */
	public void update(User entity) {
		userService.updateById(entity);	
	}
	
	/**
	 * 单笔删除
	 * @param id
	 */
	public void remove(String id) {
		userService.removeById(id);
	}
	
	/**
	 * 批量删除
	 * @param idList
	 */
	public void remove(List<String> idList) {
		userService.removeByIds(idList);
	}
	/**
	 * 更新密码
	 * @param id
	 * @param newPassword
	 * @return
	 */
	@Override
	public int updatePassword(String id, String newPassword) {
		return userService.updatePassword(id, newPassword);
	}
	
	/**
	 * 根据用户名获取用户
	 * @param loginName
	 * @return
	 */
	public User getUserByLoginName(String loginName) {
		if (loginName == null || loginName.isEmpty()) {
			return null;
		}
		Map<String, Object> params = new HashMap<>();
		params.put("loginName", loginName);
		List<User> resultList = userService.queryList(params);
		if (resultList == null || resultList.isEmpty()) {
			return null;
		}
		return resultList.get(0);
	}

}
