package com.yinshu.sys.manager.impl;

import java.util.Map;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.FileUpload;
import com.yinshu.sys.service.FileUploadService;
import com.yinshu.sys.manager.FileUploadManager;
import org.springframework.stereotype.Service;
import com.yinshu.utils.SnowflakeIdGenerator;
import com.yinshu.utils.DateUtils;

/**
 * 文件上传 
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Service
public class FileUploadManagerImpl implements FileUploadManager {

	@Autowired
	private FileUploadService fileUploadService;

	@Autowired
	private SnowflakeIdGenerator snowflakeIdGenerator;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<FileUpload> queryList(FileUpload entity) {
		List<FileUpload> resultList = fileUploadService.queryList(entity);
		return resultList;
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<FileUpload> queryPageList(FileUpload entity) {
		IPage<FileUpload> resultList = fileUploadService.queryPageList(entity);
		return resultList;
	}
	
	/**
	 * 保存
	 * @param entity
	 */
	public void save(FileUpload entity) {
		entity.setId(snowflakeIdGenerator.nextIdStr());
		entity.setCreateTime(DateUtils.getNow());
		fileUploadService.save(entity);
	}
	
	/**
	 * 删除
	 * @param id
	 */
	
	public void remove(String id) {
		fileUploadService.removeById(id);	
	}
	
	/**
	 * 批量删除
	 * @param idList
	 */
	public void remove(List<String> idList) {
		fileUploadService.removeByIds(idList);	
	}
	
	/**
	 * 更新
	 * @param entity
	 */
	public void update(FileUpload entity) {
		entity.setUpdateTime(DateUtils.getNow());
		fileUploadService.updateById(entity);
	}
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	public FileUpload getById(String id) {
		return fileUploadService.getById(id);
	}
	
}
