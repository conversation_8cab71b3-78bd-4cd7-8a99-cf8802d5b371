package com.yinshu.sys.manager;

import java.util.List;

import org.springframework.cache.annotation.CacheEvict;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.Setting;


public interface SettingManager {
	/**
	 * 分页查询
	 * @param entity
	 * @return
	 */
	IPage<Setting> queryPageList(Setting entity);

	/**
	 * 按条件查询
	 * @param entity
	 * @return
	 */
	List<Setting> queryList(Setting entity);
	
	/**
     * 根据code获取值
     * @param parmCode
     * @return
     */
    Setting getByCode(String parmCode);
    
//    /**
//     * 根据code值+状态判断
//     * @param parmCode
//     * @param status
//     * @return
//     */
//    Setting getByCode(String parmCode, int status);

	boolean check(Setting entity);

	Integer getNextSort();

	/**
	 * 查看
	 * @param id
	 * @return
	 */
	Setting getById(String id);

	/**
	 * 新增
	 * @param entity
	 */
	Setting save(Setting entity);

	/**
	 * 修改
	 * @param entity
	 */
	Setting update(Setting entity);
	/**
	 * 单笔删除
	 * @param id
	 */
	void remove(String id);

	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);
	
	/**
     * 刷新缓存
     */
    List<Setting> refreshCache();
}
