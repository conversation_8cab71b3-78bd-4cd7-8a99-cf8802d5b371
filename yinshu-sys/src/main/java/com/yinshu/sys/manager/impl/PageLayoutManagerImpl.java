package com.yinshu.sys.manager.impl;

import java.util.Map;
import java.util.List;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.PageLayout;
import com.yinshu.sys.service.PageLayoutService;
import com.yinshu.sys.manager.PageLayoutManager;
import org.springframework.stereotype.Service;
import com.yinshu.utils.SnowflakeIdGenerator;

/**
 * 页面布局表 
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@Service
public class PageLayoutManagerImpl implements PageLayoutManager {

	@Autowired
	private PageLayoutService pageLayoutService;

	@Autowired
	private SnowflakeIdGenerator snowflakeIdGenerator;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<PageLayout> queryList(PageLayout entity) {
		List<PageLayout> resultList = pageLayoutService.queryList(entity);
		return resultList;
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<PageLayout> queryPageList(PageLayout entity) {
		IPage<PageLayout> resultList = pageLayoutService.queryPageList(entity);
		return resultList;
	}
	
	/**
	 * 保存
	 * @param entity
	 */
	public void save(PageLayout entity) {
		entity.setId(snowflakeIdGenerator.nextIdStr());
		pageLayoutService.save(entity);
	}

	/**
	 * 批量保存
	 * @param entityList
	 */
	@Override
	public void saveBatch(List<PageLayout> entityList) {
		if(entityList != null && entityList.size() > 0){
			for (PageLayout entity: entityList) {
				entity.setId(snowflakeIdGenerator.nextIdStr());
			}
			pageLayoutService.saveBatch(entityList);
		}
	}
	
	/**
	 * 删除
	 * @param id
	 */
	
	public void remove(String id) {
		pageLayoutService.removeById(id);	
	}
	
	/**
	 * 批量删除
	 * @param idList
	 */
	public void remove(List<String> idList) {
		pageLayoutService.removeByIds(idList);	
	}

	@Override
	public void removeByPageFlexIds(List<String> idList) {
		LambdaUpdateChainWrapper<PageLayout> wrapper =
				pageLayoutService.lambdaUpdate().in(PageLayout::getPageFlexId, idList);
		wrapper.remove();
	}

	/**
	 * 更新
	 * @param entity
	 */
	public void update(PageLayout entity) {
		pageLayoutService.updateById(entity);
	}
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	public PageLayout getById(String id) {
		return pageLayoutService.getById(id);
	}


}
