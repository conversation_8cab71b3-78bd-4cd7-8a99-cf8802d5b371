package com.yinshu.sys.manager;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.PageFlex;

import java.util.List;

/**
 * 动态页面表 
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
public interface PageFlexManager {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<PageFlex> queryList(PageFlex entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<PageFlex> queryPageList(PageFlex entity);

	boolean check(PageFlex entity);

	Integer getNextSort();
	
	/**
	 * 保存
	 * @param entity
	 */
	boolean save(PageFlex entity);
	
	/**
	 * 删除
	 * @param id
	 */
	
	void remove(String id);
	
	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);
	
	/**
	 * 更新
	 * @param entity
	 */
	boolean update(PageFlex entity);
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	PageFlex getById(String id);

	PageFlex getLayoutById(String id);

	PageFlex getLayoutByPageCode(String pageCode);

}

