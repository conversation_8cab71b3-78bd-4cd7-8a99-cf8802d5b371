package com.yinshu.sys.manager.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.PageFlex;
import com.yinshu.sys.entity.PageLayout;
import com.yinshu.sys.manager.PageFlexManager;
import com.yinshu.sys.manager.PageLayoutManager;
import com.yinshu.sys.service.PageFlexService;
import com.yinshu.utils.SnowflakeIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 动态页面表 
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@Service
public class PageFlexManagerImpl implements PageFlexManager {

	@Autowired
	private PageFlexService pageFlexService;

	@Autowired
	private PageLayoutManager pageLayoutManager;

	@Autowired
	private SnowflakeIdGenerator snowflakeIdGenerator;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<PageFlex> queryList(PageFlex entity) {
		List<PageFlex> resultList = pageFlexService.queryList(entity);
		return resultList;
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<PageFlex> queryPageList(PageFlex entity) {
		IPage<PageFlex> resultList = pageFlexService.queryPageList(entity);
		return resultList;
	}

	@Override
	public boolean check(PageFlex entity) {
		return pageFlexService.check(entity);
	}

	@Override
	public Integer getNextSort() {
		Integer maxSort = pageFlexService.getMaxSort();
		if(maxSort != null){
			return maxSort+1;
		}else{
			return 1;
		}
	}
	
	/**
	 * 保存
	 * @param entity
	 */
	@Transactional
	public boolean save(PageFlex entity) {
		if(pageFlexService.check(entity)){
			entity.setId(snowflakeIdGenerator.nextIdStr());
			return pageFlexService.save(entity);
		}else{
			return false;
		}
	}
	
	/**
	 * 删除
	 * @param id
	 */
	
	public void remove(String id) {
		pageFlexService.removeById(id);	
	}
	
	/**
	 * 批量删除
	 * @param idList
	 */
	@Transactional
	public void remove(List<String> idList) {
		pageFlexService.removeByIds(idList);
		pageLayoutManager.removeByPageFlexIds(idList);
	}
	
	/**
	 * 更新
	 * @param entity
	 */
	@Transactional
	public boolean update(PageFlex entity) {
		if(pageFlexService.check(entity)){
			List<PageLayout> pageLayoutList = entity.getPageLayoutList();
			if(pageLayoutList != null){
				pageLayoutManager.removeByPageFlexIds(Collections.singletonList(entity.getId()));
				for(PageLayout PageLayout : pageLayoutList){
					PageLayout.setPageFlexId(entity.getId());
				}
				pageLayoutManager.saveBatch(pageLayoutList);
			}
			return pageFlexService.updateById(entity);
		}else{
			return false;
		}
	}
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	public PageFlex getById(String id) {
		PageFlex pageFlex = pageFlexService.getById(id);
		return pageFlex;
	}

	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	public PageFlex getLayoutById(String id) {
		PageFlex pageFlex = pageFlexService.getById(id);
		if(!Integer.valueOf(1).equals(pageFlex.getStatus())){
			return null;
		}
		PageLayout pageLayoutEntity = new PageLayout();
		pageLayoutEntity.setPageFlexId(id);
		List<PageLayout> pageLayoutList = pageLayoutManager.queryList(pageLayoutEntity);
		pageFlex.setPageLayoutList(pageLayoutList);
		return pageFlex;
	}

	/**
	 * 根据pageCode获取对象
	 * @param pageCode
	 * @return
	 */
	public PageFlex getLayoutByPageCode(String pageCode) {
		PageFlex pageFlex = pageFlexService.getByPageCode(pageCode);
		if(!Integer.valueOf(1).equals(pageFlex.getStatus())){
			return null;
		}
		PageLayout pageLayoutEntity = new PageLayout();
		pageLayoutEntity.setPageFlexId(pageFlex.getId());
		List<PageLayout> pageLayoutList = pageLayoutManager.queryList(pageLayoutEntity);
		pageFlex.setPageLayoutList(pageLayoutList);
		return pageFlex;
	}
	
}
