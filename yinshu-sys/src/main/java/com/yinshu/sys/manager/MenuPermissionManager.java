package com.yinshu.sys.manager;

import java.util.Map;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.MenuPermission;

/**
 *  
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
public interface MenuPermissionManager {

	/**
	 * 条件查询
	 * @param params
	 * @return
	 */
	List<MenuPermission> queryList(Map<String, Object> params);

	/**
	 * 条件分页查询
	 * @param params
	 * @return
	 */
	IPage<MenuPermission> queryPageList(Map<String, Object> params);
	
	/**
	 * 保存
	 * @param entity
	 */
	void save(MenuPermission entity);
	
	/**
	 * 删除
	 * @param id
	 */
	
	void remove(String id);
	
	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);
	
	/**
	 * 更新
	 * @param entity
	 */
	void update(MenuPermission entity);
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	MenuPermission getById(String id);

}

