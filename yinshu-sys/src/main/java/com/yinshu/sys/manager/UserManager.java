package com.yinshu.sys.manager;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.User;

public interface UserManager {

	/**
	 * 分页查询
	 * @param params
	 * @return
	 */
	IPage<User> queryPageList(User entity);
	
	/**
	 * 条件未授权的用户
	 * @param page
	 * @param params
	 * @return
	 */
	IPage<User> queryUnAuthPageList(Map<String, Object> params);
	
	/**
	 * 按条件查询
	 * @param params
	 * @return
	 */
	List<User> queryList(Map<String, Object> params);
	
	
	/**
	 * 查看
	 * @param id
	 * @return
	 */
	User getById(String id);
	
	/**
	 * 新增
	 * @param entity
	 */
	void save(User entity);
	
	/**
	 * 修改
	 * @param entity
	 */
	void update(User entity);	
	
	/**
	 * 单笔删除
	 * @param id
	 */
	void remove(String id);
	
	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);

	/**
	 * 更新密码
	 * @param id
	 * @param newPassword
	 * @return
	 */
	int updatePassword(String id, String newPassword);
	
	/**
	 * 根据用户名获取用户
	 * @param loginName
	 * @return
	 */
	User getUserByLoginName(String loginName);
}
