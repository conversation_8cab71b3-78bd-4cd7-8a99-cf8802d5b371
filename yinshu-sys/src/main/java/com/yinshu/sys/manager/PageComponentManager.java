package com.yinshu.sys.manager;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.PageComponent;

import java.util.List;

/**
 * 页面组件表 
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
public interface PageComponentManager {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<PageComponent> queryList(PageComponent entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<PageComponent> queryPageList(PageComponent entity);

	boolean check(PageComponent entity);

	Integer getNextSort();
	
	/**
	 * 保存
	 * @param entity
	 */
	boolean save(PageComponent entity);
	
	/**
	 * 删除
	 * @param id
	 */
	
	void remove(String id);
	
	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);
	
	/**
	 * 更新
	 * @param entity
	 */
	boolean update(PageComponent entity);
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	PageComponent getById(String id);

}

