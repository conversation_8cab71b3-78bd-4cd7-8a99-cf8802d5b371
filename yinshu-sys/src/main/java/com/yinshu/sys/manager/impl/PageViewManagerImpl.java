package com.yinshu.sys.manager.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yinshu.sys.entity.PageComponentSource;
import com.yinshu.sys.entity.PageComponentSource.BindData;
import com.yinshu.sys.entity.PageComponentSource.BindDataTypeEnum;
import com.yinshu.sys.entity.PageLayout;
import com.yinshu.sys.manager.PageViewManager;
import com.yinshu.sys.manager.PageLayoutManager;
import com.yinshu.sys.service.PageViewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import freemarker.cache.StringTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;

import java.io.StringReader;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Component
public class PageViewManagerImpl implements PageViewManager {

    @Autowired
    private PageLayoutManager pageLayoutManager;

    @Autowired
    private PageViewService pageViewService;

    @Override
    public void loaderData(PageComponentSource pageComponentSource) {
        PageLayout pageLayout = pageLayoutManager.getById(pageComponentSource.getPageLayoutId());
        Map<String, Object> bindParamsMap = pageComponentSource.getBindParams();
        String bindDatasJson = pageLayout.getBindDatas();
        Map<String, BindData> bindDatasMap = JSON.parseObject(bindDatasJson, new TypeReference<Map<String, BindData>>(){});
        pageComponentSource.setBindDatas(bindDatasMap);
        Map<String, Object> resultData = new HashMap<>();
        for(Map.Entry<String, BindData> entry :bindDatasMap.entrySet()){
            String dataName = entry.getKey();
            BindData bindData = entry.getValue();
            putResultData(resultData, dataName, bindData, bindParamsMap);

        }
        pageComponentSource.setResultData(resultData);
    }

    private void putResultData(Map<String, Object> resultData, String dataName, BindData bindData, Map<String, Object> bindParamsMap){
        if(BindDataTypeEnum.SQL.typeEquals(bindData.getType())){
            String sql = parseContent(bindData.getSql(), bindParamsMap);
            if(isOnlySelect(sql)){
                List<LinkedHashMap<String, Object>> resultList = pageViewService.onlySelect(sql);
                if(bindData.isOnlyOne()){
                    if(resultList.size() > 0){
                        resultData.put(dataName, resultList.get(0));
                    }else{
                        resultData.put(dataName, new HashMap<>(0));
                    }
                }else{
                    resultData.put(dataName, resultList);
                }
            }
        }
    }

    public static String parseContent(String content, Map<String, Object> dataMap) {
        try {
            String tempName = "template";
            Configuration configuration = new Configuration(Configuration.VERSION_2_3_31);
            configuration.setNumberFormat("#");
            StringTemplateLoader stringLoader = new StringTemplateLoader();
            stringLoader.putTemplate(tempName, content);
            Template template = new Template(tempName, new StringReader(content), configuration);
            StringWriter stringWriter = new StringWriter();
            template.process(dataMap, stringWriter);
            content = stringWriter.toString();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("模板解析失败");
        }
        return content;
    }

    public static boolean isOnlySelect(String sql) {
        String upperCaseSQL = sql.trim().toUpperCase();
        boolean checkFlag = upperCaseSQL.startsWith("SELECT") && !upperCaseSQL.contains("INSERT")
                && !upperCaseSQL.contains("UPDATE") && !upperCaseSQL.contains("DELETE")
                && !upperCaseSQL.contains("CREATE") && !upperCaseSQL.contains("DROP")
                && !upperCaseSQL.contains("TRUNCATE");
        return checkFlag;
    }
}
