package com.yinshu.sys.manager;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.Unit;

import java.util.List;
import java.util.Map;

public interface UnitManager {

	/**
	 * 分页查询
	 * @param params
	 * @return
	 */
	IPage<Unit> queryPageList(Unit entity);
	
	/**
	 * 按条件查询
	 * @param params
	 * @return
	 */
	List<Unit> queryList(Map<String, Object> params);
	
	/**
	 * 获取Json格式的树结构
	 * 
	 * @return
	 */
	List<Map<String, Object>> getJsonTreeList();
	
	
	/**
	 * 查看
	 * @param id
	 * @return
	 */
	Unit getById(String id);

	/**
	 * 查询顶级机构id
	 * @return
	 */
	String getById();

	/**
	 * 新增
	 * @param entity
	 */
	void save(Unit entity);
	
	/**
	 * 修改
	 * @param entity
	 */
	void update(Unit entity);	
	
	/**
	 * 单笔删除
	 * @param id
	 */
	void remove(String id);
	
	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);
}
