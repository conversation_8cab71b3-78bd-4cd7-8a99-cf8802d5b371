package com.yinshu.sys.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.Menu;
import com.yinshu.sys.entity.MenuPermission;
import com.yinshu.sys.entity.MenuTreeNode;
import com.yinshu.sys.manager.MenuManager;
import com.yinshu.sys.manager.RoleMenuManager;
import com.yinshu.sys.service.MenuPermissionService;
import com.yinshu.sys.service.MenuService;
import com.yinshu.utils.DateUtils;
import com.yinshu.utils.UUIDGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Transactional
public class MenuManagerImpl implements MenuManager {

	@Autowired
	private MenuService menuService;
	
	@Autowired
	private MenuPermissionService menuPermissionService;

	@Autowired
	private RoleMenuManager roleMenuManager;
	
	@Override
	public IPage<Menu> queryPageList(Menu entity) {
		return menuService.queryPageList(entity);
	}
	
	public List<Menu> queryList(Map<String, Object> params) {
		QueryWrapper<Menu> queryWrapper = new QueryWrapper<Menu>();
		queryWrapper.orderByAsc("menu_Sort");
		return menuService.list(queryWrapper);
	}
	
	public Menu getById(String id) {
		return menuService.getById(id);
	}
	
	/**
	 * 根据ID获取对象(自定义方法)
	 * @param id
	 * @return
	 */
	public Menu findById(String id) {
		return menuService.findById(id);
	}
	
	public void save(Menu entity) {
		entity.setId(UUIDGenerator.uuid());
		menuService.save(entity);
		//MenuCache.MENU_LIST.add(entity);
	}
	
	public void saveAll(Menu entity) {
		entity.setId(UUIDGenerator.uuid());
		menuService.save(entity);
		converPersList(entity.getPersList(), entity.getId());
		menuPermissionService.saveBatch(entity.getPersList());
		//MenuCache.MENU_LIST.add(entity);
	}
	
	public void update(Menu entity) {
		Date createTime = DateUtils.getNow();
		menuService.updateById(entity);
		menuPermissionService.removeByMap(new HashMap<String, Object>(){{put("menu_id", entity.getId());}});
		for(MenuPermission item : entity.getPersList()) {
			if(!StringUtils.hasText(item.getId())) {
				item.setId(UUIDGenerator.uuid());
			} 
			item.setMenuId(entity.getId());
			item.setCreateTime(createTime);
			menuPermissionService.save(item);
		}
		//MenuCache.updateMenuList(menuService.list());
	}
	
	public void remove(String id) {
		menuService.removeById(id);
		roleMenuManager.removeByMenuIds(new String[]{id});
		menuPermissionService.removeByMap(new HashMap<String, Object>(){{put("menu_id", id);}});
		//MenuCache.updateMenuList(menuService.list());
	}
	
	/**
	 * 删除菜单的同时，也删除角色对应的菜单表
	 */
	public void remove(List<String> idList) {
		menuService.removeByIds(idList);
		roleMenuManager.removeByMenuIds(idList.toArray(new String[idList.size()]));
		for (String id: idList) {
			menuPermissionService.removeByMap(new HashMap<String, Object>(){{put("menu_id", id);}});
		}
		//MenuCache.updateMenuList(menuService.list());
	}
	
	/**
	 * 过滤掉不显示的菜单
	 * @return
	 */
	public List<Map<String, Object>> filterJsonTreeList(List<Menu> rootList) {
		List<Menu> dataList = new ArrayList<Menu>();
		for(Menu entity : rootList) {
			if(entity.getIsVisible() != null && entity.getIsVisible().equals("2")) {
				continue;
			}
			dataList.add(entity);
		}
		return getJsonTreeList(dataList);
		
	}
	
	/**
	 * 获取Json格式的树结构
	 * @return
	 */
	public List<Map<String, Object>> getJsonTreeList() {
		List<Menu> rootList = menuService.queryList();
		return getJsonTreeList(rootList);
	}
	
	
	/**
	 * 获取Json格式的树结构
	 * @return
	 */
	public List<Map<String, Object>> getJsonTreeList(List<Menu> rootList) {

		List<Map<String, Object>> listMap = new ArrayList<Map<String,Object>>();
		
		List<Menu> menuList = new ArrayList<>();
		for(Menu entity : rootList) {
//			if(entity.getIsVisible() != null && entity.getIsVisible().equals("2")) {
//				continue;
//			}
			if(!StringUtils.hasText(entity.getParentId())) {
				menuList.add(entity);/**先获取一级菜单*/
			}
		}
		
		for(Menu menu : menuList) {
			Map<String, Object> treeMap = new TreeMap<>();
			treeMap.put("id", menu.getId());
			treeMap.put("value", menu.getId());
			treeMap.put("label", menu.getMenuName());
			treeMap.put("url", menu.getMenuUrl());
			treeMap.put("icon", menu.getMenuIcon());
			treeMap.put("component", menu.getComponent());
			treeMap.put("menuType", menu.getMenuType());
			List<Map<String, Object>> subList = buildChilTree2(menu, rootList);
			if(subList.size() > 0) {
				treeMap.put("children", subList);		
			}
			listMap.add(treeMap);
		}
		
		if(listMap.size() == 0) {
			Map<String, Object> treeMap = new TreeMap<>();
			// 前端判断先把这注释
			//treeMap.put("label", "菜单为空");
			//listMap.add(treeMap);
		}
		
		return listMap;
	}
	
	
	private List<Map<String, Object>> buildChilTree2(Menu pNode, List<Menu> rootList) {
		List<Map<String, Object>> childList = new ArrayList<Map<String,Object>>();
		for (Menu menuNode : rootList) {
			if(!StringUtils.isEmpty((menuNode.getParentId()))) {
	            if (menuNode.getParentId().equals(pNode.getId())) {
	            	Map<String, Object> subMap = new TreeMap<>();
					subMap.put("id", menuNode.getId());
					subMap.put("value", menuNode.getId());
					subMap.put("label", menuNode.getMenuName());
					subMap.put("url", menuNode.getMenuUrl());
					subMap.put("icon", menuNode.getMenuIcon());
					subMap.put("menuType", menuNode.getMenuType());
					subMap.put("component", menuNode.getComponent());
//					if(menuNode.getIsVisible() != null && menuNode.getIsVisible().equals("2")) {
//						continue;
//					}
					List<Map<String, Object>> subList = buildChilTree2(menuNode, rootList);
					if(subList.size() > 0) {
						subMap.put("children", subList);
					}
					childList.add(subMap);
	            }
	        }
		}
		return childList;
	}
	
/*	private Menu buildChilTree(Menu pNode, List<Menu> rootList) {
		List<Menu> childList = new ArrayList<>();
		for (Menu menuNode : rootList) {
			if(!StringUtils.isEmpty((menuNode.getParentId()))) {
	            if (menuNode.getParentId().equals(pNode.getId())) {
	                childList.add(buildChilTree(menuNode, rootList));
	            }
	        }
		}
		pNode.setChildren(childList);
		return pNode;
	}*/
	
	/**
	 * 保存树结构菜单
	 */
	public void saveTree(Menu entity) {
		entity.setId(UUIDGenerator.uuid());
		menuService.save(entity);		
	}
	
	private void converPersList(List<MenuPermission> persList, String menuId) {
		if(persList.size() > 0) {
			Date createTime = DateUtils.getNow();
			for(MenuPermission item : persList) {
				item.setId(UUIDGenerator.uuid());
				item.setMenuId(menuId);
				item.setCreateTime(createTime);
			}
		}
	}

	/**
	 * 获取树结构,包含菜单及权限
	 * @return
	 */
	@Override
	public List<MenuTreeNode> getMenuPermissionTreeList(){
		List<Menu> allList = menuService.queryMenuPermissionTreeList();
		List<MenuTreeNode> treeList = getMenuPermissionTreeList(allList, null);
		return treeList;
	}

	private List<MenuTreeNode> getMenuPermissionTreeList(List<Menu> allList, String parentId) {
		List<MenuTreeNode> treeNodes = this.filterMenuPermissionTreeList(allList, parentId);
		if(treeNodes.size() > 0){
			for (MenuTreeNode node : treeNodes) {
				List<MenuTreeNode> children = this.getMenuPermissionTreeList(allList, node.getId());
				if(children.size() > 0){
					node.setChildren(children);
				}
			}
		}
		return treeNodes;
	}

	private List<MenuTreeNode> filterMenuPermissionTreeList(List<Menu> allList, String parentId) {
		List<Menu> menuList = allList.stream()
				.filter(item -> StringUtils.isEmpty(parentId) ? StringUtils.isEmpty(item.getParentId())
						: com.baomidou.mybatisplus.core.toolkit.StringUtils.equals(item.getParentId(), parentId))
				.collect(Collectors.toList());
		List<MenuTreeNode> menuTreeNodeList = menuList.stream().map(menu -> {
			MenuTreeNode treeNode = new MenuTreeNode();
			treeNode.setId(menu.getId());
			treeNode.setLabel(menu.getMenuName());
			treeNode.setValue(menu.getId());
			treeNode.setParent(menu.getParentId());
			treeNode.setPermissionList(menu.getPersList());
			treeNode.setLeaf(false);
			return treeNode;
		}).collect(Collectors.toList());
		return menuTreeNodeList;
	}
}
