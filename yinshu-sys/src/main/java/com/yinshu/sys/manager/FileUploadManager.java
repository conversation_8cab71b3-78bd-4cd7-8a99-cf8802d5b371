package com.yinshu.sys.manager;

import java.util.Map;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.FileUpload;

/**
 * 文件上传 
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
public interface FileUploadManager {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<FileUpload> queryList(FileUpload entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<FileUpload> queryPageList(FileUpload entity);
	
	/**
	 * 保存
	 * @param entity
	 */
	void save(FileUpload entity);
	
	/**
	 * 删除
	 * @param id
	 */
	
	void remove(String id);
	
	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);
	
	/**
	 * 更新
	 * @param entity
	 */
	void update(FileUpload entity);
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	FileUpload getById(String id);

}

