package com.yinshu.sys.manager.impl;

import java.util.Map;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.SettingDm;
import com.yinshu.sys.service.SettingDmService;
import com.yinshu.sys.manager.SettingDmManager;
import org.springframework.stereotype.Service;
import com.yinshu.utils.SnowflakeIdGenerator;

/**
 * 系统设置表 
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Service
public class SettingDmManagerImpl implements SettingDmManager {

	@Autowired
	private SettingDmService settingDmService;

	@Autowired
	private SnowflakeIdGenerator snowflakeIdGenerator;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<SettingDm> queryList(SettingDm entity) {
		List<SettingDm> resultList = settingDmService.queryList(entity);
		return resultList;
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<SettingDm> queryPageList(SettingDm entity) {
		IPage<SettingDm> resultList = settingDmService.queryPageList(entity);
		return resultList;
	}

	@Override
	public boolean check(SettingDm entity) {
		return settingDmService.check(entity);
	}

	@Override
	public Integer getNextSort() {
		Integer maxSort = settingDmService.getMaxSort();
		if(maxSort != null){
			return maxSort+1;
		}else{
			return 1;
		}
	}
	
	/**
	 * 保存
	 * @param entity
	 */
	public void save(SettingDm entity) {
		entity.setId(snowflakeIdGenerator.nextIdStr());
		settingDmService.save(entity);
	}
	
	/**
	 * 删除
	 * @param id
	 */
	
	public void remove(String id) {
		settingDmService.removeById(id);	
	}
	
	/**
	 * 批量删除
	 * @param idList
	 */
	public void remove(List<String> idList) {
		settingDmService.removeByIds(idList);	
	}
	
	/**
	 * 更新
	 * @param entity
	 */
	public void update(SettingDm entity) {
		settingDmService.updateById(entity);
	}
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	public SettingDm getById(String id) {
		return settingDmService.getById(id);
	}
	
}
