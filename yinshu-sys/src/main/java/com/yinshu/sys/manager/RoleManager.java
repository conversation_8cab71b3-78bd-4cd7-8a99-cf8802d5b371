package com.yinshu.sys.manager;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.Menu;
import com.yinshu.sys.entity.Role;
import com.yinshu.sys.entity.User;

public interface RoleManager {

	void save(Role entity);
	
	void remove(String id);
	
	void remove(List<String> idList);
	
	void update(Role entity);
	
	IPage<Role> queryPageList(Map<String, Object> params);
	
	Role getById(String id);
	
	List<Role> getList();
	
	/**
	 * 根据角色查询所有用户
	 * @param roleId
	 * @param unitId
	 * @return
	 */
	List<User> queryUserListByRoleId(String roleId, String unitId);
	
	/**
	 * 根据角色查询所有菜单
	 * @param roleId
	 * @return
	 */
	List<Menu> queryMenuListByRoleId(String roleId);
	
}
