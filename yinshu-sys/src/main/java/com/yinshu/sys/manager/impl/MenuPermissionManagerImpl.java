package com.yinshu.sys.manager.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.MenuPermission;
import com.yinshu.sys.manager.MenuPermissionManager;
import com.yinshu.sys.service.MenuPermissionService;
import com.yinshu.utils.DateUtils;
import com.yinshu.utils.UUIDGenerator;

/**
 *  
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Service
public class MenuPermissionManagerImpl implements MenuPermissionManager {

	@Autowired
	private MenuPermissionService menuPermissionService;
	
	/**
	 * 条件查询
	 * @param params
	 * @return
	 */
	public List<MenuPermission> queryList(Map<String, Object> params) {
		List<MenuPermission> resultList = menuPermissionService.queryList(params);
		return resultList;
	}
	
	/**
	 * 条件分页查询
	 * @param params
	 * @return
	 */
	public IPage<MenuPermission> queryPageList(Map<String, Object> params) {
		IPage<MenuPermission> resultList = menuPermissionService.queryPageList(params);
		return resultList;
	}
	
	/**
	 * 保存
	 * @param entity
	 */
	public void save(MenuPermission entity) {
		entity.setId(UUIDGenerator.uuid());
		entity.setCreateTime(DateUtils.getNow());
		menuPermissionService.save(entity);
	}
	
	/**
	 * 删除
	 * @param id
	 */
	
	public void remove(String id) {
		menuPermissionService.removeById(id);	
	}
	
	/**
	 * 批量删除
	 * @param idList
	 */
	public void remove(List<String> idList) {
		menuPermissionService.removeByIds(idList);	
	}
	
	/**
	 * 更新
	 * @param entity
	 */
	public void update(MenuPermission entity) {
		menuPermissionService.updateById(entity);
	}
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	public MenuPermission getById(String id) {
		return menuPermissionService.getById(id);
	}
	
}
