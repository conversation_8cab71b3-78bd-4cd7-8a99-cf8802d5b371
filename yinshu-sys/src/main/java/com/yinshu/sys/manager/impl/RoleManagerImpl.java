package com.yinshu.sys.manager.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.yinshu.sys.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.Menu;
import com.yinshu.sys.entity.Role;
import com.yinshu.sys.entity.User;
import com.yinshu.sys.manager.RoleManager;
import com.yinshu.utils.DateUtils;
import com.yinshu.utils.UUIDGenerator;

@Transactional
@Component
public class RoleManagerImpl implements RoleManager {

	@Autowired
	private RoleService roleService;
	
	@Autowired
	private UserService userService;
	
	@Autowired
	private MenuService menuService;
	
	@Autowired
	private RoleUserService roleUserService;
	
	@Autowired
	private RoleMenuService roleMenurService;

	@Autowired
	private RoleMenuPermissionService roleMenuPermissionService;
	
	@Override
	public IPage<Role> queryPageList(Map<String, Object> params) {
		return roleService.queryPageList(params);
	}
	
	public Role getById(String id) {
		return roleService.getById(id);
	}
	
	public void save(Role entity) {
		entity.setId(UUIDGenerator.uuid());
		entity.setCreateTime(DateUtils.getNow());
		roleService.save(entity);		
	}
	
	public void update(Role entity) {
		entity.setUpdateTime(DateUtils.getNow());
		roleService.updateById(entity);	
	}
	
	public void remove(String id) {
		roleService.removeById(id);
		roleUserService.removeByRoleId(id);
		roleMenuPermissionService.deleteByRoleId(id);
		roleMenurService.removeByRoleId(id);
	}
	
	/**
	 * 删除角色的时候，需要将其下面所有的角色菜单删除
	 */
	public void remove(List<String> idList) {
		roleService.removeByIds(idList);
		for(String roleId : idList) {
			roleUserService.removeByRoleId(roleId);
			roleMenuPermissionService.deleteByRoleId(roleId);
			roleMenurService.removeByRoleId(roleId);
		}
	}
	
	public List<Role> getList() {
		return roleService.list();
	}

	/**
	 * 根据角色查询所有用户
	 * @param roleId
	 * @return
	 */
	public List<User> queryUserListByRoleId(String roleId, String unitId) {
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("roleId", roleId);
		Map<String, Object> userMap = new HashMap<String, Object>();
		List<Map<String, String>> roleUserList = roleUserService.queryList(paramMap);
		for(Map<String, String> entry : roleUserList) {
			userMap.put(entry.get("USER_ID"), entry.get("USER_ID"));
		}
		Map<String, Object> _userMap = new HashMap<String, Object>();
		_userMap.put("unitId", unitId);
		List<User> userList = userService.queryList(_userMap);
		for(User entity : userList){
			if(userMap.containsKey(entity.getId())) {
				//entity.setRoleId(roleId);
			}
		}
		return userList;
	}
	
	/**
	 * 根据角色查询所有菜单
	 * @param roleId
	 * @return
	 */
	public List<Menu> queryMenuListByRoleId(String roleId) {
		Map<String, Object> menuMap = new HashMap<String, Object>();
		List<Map<String, String>> roleMenuList = roleMenurService.queryMenuListByRoleId(roleId);
		for(Map<String, String> entry : roleMenuList) {
			menuMap.put(entry.get("MENU_ID"), entry.get("MENU_ID"));
		}
		List<Menu> menuList = menuService.list();
		for(Menu entity : menuList){
			if(menuMap.containsKey(entity.getId())) {
				entity.setRoleId(roleId);
			}
		}
		return menuList;
	}
}
