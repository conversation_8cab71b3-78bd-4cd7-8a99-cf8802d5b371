package com.yinshu.sys.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.Log;
import com.yinshu.sys.entity.Unit;

public interface UnitDao extends BaseMapper<Unit> {

	/**
	 * 条件分页查询
	 * @param page
	 * @param params
	 * @return
	 */
	IPage<Unit> queryPageList(IPage<Unit> page, @Param("param") Unit entity);
	
	/**
	 * 按条件查询
	 * @param params
	 * @return
	 */
	List<Unit> queryList(Map<String, Object> params);
	
	/**
	 * 排序查询
	 * @param params
	 * @return
	 */
	List<Unit> queryTreeList(@Param("param") Map<String, Object> params);
	
}
