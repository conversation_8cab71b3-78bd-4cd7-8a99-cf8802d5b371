package com.yinshu.sys.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.Menu;
import com.yinshu.sys.entity.Unit;

public interface MenuDao extends BaseMapper<Menu> {

	/**
	 * 条件分页查询
	 * @param page
	 * @param params
	 * @return
	 */
	IPage<Menu> queryPageList(IPage<Menu> page, @Param("param") Menu entity);
	
	/**
	 * 排序查询
	 * @return
	 */
	List<Menu> queryList();
	
	/**
	 * 自定义方法
	 * @param id
	 * @return
	 */
	List<Menu> findById(@Param("id") String id);

	List<Menu> queryMenuPermissionTreeList();
	
}
