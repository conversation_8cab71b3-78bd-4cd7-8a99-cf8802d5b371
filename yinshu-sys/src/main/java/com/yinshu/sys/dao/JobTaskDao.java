package com.yinshu.sys.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.JobTask;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 
 * 
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
public interface JobTaskDao extends BaseMapper<JobTask> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<JobTask> queryList(@Param("entity") JobTask entity);

	/**
	* 条件分页查询
	* @param page
	* @param entity
	* @return
	*/
	IPage<JobTask> queryPageList(IPage<JobTask> page, @Param("entity") JobTask entity);

}
