package com.yinshu.sys.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.MenuPermission;

/**
 * 
 * 
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
public interface MenuPermissionDao extends BaseMapper<MenuPermission> {

	/**
	 * 条件查询
	 * @param params
	 * @return
	 */
	List<MenuPermission> queryList(@Param("param") Map<String, Object> params);

	/**
	 * 条件分页查询
	 * @param page
	 * @param params
	 * @return
	 */
	IPage<MenuPermission> queryPageList(IPage<MenuPermission> page, @Param("param") Map<String, Object> params);
}
