package com.yinshu.sys.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.Dictionary;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DictionaryDao extends BaseMapper<Dictionary> {

    List<Dictionary> queryList(Dictionary entity);

    IPage<Dictionary> queryPageList(IPage<Dictionary> page, @Param("entity") Dictionary entity);

    List<Dictionary> queryTreeAllList(Dictionary entity);

    List<Dictionary> check(Dictionary entity);

    void removeByParentCode(Dictionary entity);


    /**
     * code修改后，需同时修改关联子节点的parentCode
     * @param oldParentCode
     * @param newParentCode
     * @return
     */
    int updateByParentCode(@Param("classify") String classify,
                            @Param("oldParentCode") String oldParentCode,
                            @Param("newParentCode") String newParentCode);

    Integer getMaxSort(Dictionary entity);

    Dictionary getByDicCodeSingle(@Param("dicCode") String dicCode);
    
    List<Dictionary> queryNormalList(Dictionary entity);
}
