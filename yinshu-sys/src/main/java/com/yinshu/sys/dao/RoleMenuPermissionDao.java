package com.yinshu.sys.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yinshu.sys.entity.RoleMenuPermission;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RoleMenuPermissionDao extends BaseMapper<RoleMenuPermission> {

    int deleteByRoleId(@Param("roleId") String roleId);

    int deleteByMenuIds(@Param("idList") List<String> idList);

    int deleteByRoleMenuIds (@Param("idList") List<String> idList);
	
}
