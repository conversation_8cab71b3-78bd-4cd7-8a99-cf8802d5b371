package com.yinshu.sys.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.PageComponent;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 
 * 页面组件表
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
public interface PageComponentDao extends BaseMapper<PageComponent> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<PageComponent> queryList(@Param("entity") PageComponent entity);

	/**
	* 条件分页查询
	* @param page
	* @param entity
	* @return
	*/
	IPage<PageComponent> queryPageList(IPage<PageComponent> page, @Param("entity") PageComponent entity);

	List<PageComponent> check(PageComponent entity);

	Integer getMaxSort();
}
