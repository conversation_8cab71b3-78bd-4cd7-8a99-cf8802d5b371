package com.yinshu.sys.dao;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yinshu.sys.entity.Menu;
import com.yinshu.sys.entity.Role;
import com.yinshu.sys.entity.RoleMenu;

public interface RoleMenuDao extends BaseMapper<RoleMenu> {

	/**
	 * 根据角色查询所有菜单
	 * @param roleId
	 * @return
	 */
	List<Map<String, String>> queryMenuListByRoleId(String roleId);
	
	/**
	 * 根据角色查询所有菜单,支持多角色
	 * @param roleId
	 * @return
	 */
	List<Menu> queryMenuListByRoles(List<Role> roleIds);

	/**
	 * 根据角色查询所有菜单对应关系, 包含菜单权限标识表
	 * @param roleId
	 * @return
	 */
	List<RoleMenu> queryRoleMenusByRoleId(String roleId);
	
	/**
	 * 根据角色删除菜单
	 * @param roleId
	 */
	void removeByRoleId(String roleId);
	
	/**
	 * 根据菜单ID删除
	 * @param roleId
	 */
	void removeByMenuIds(String[] array);
	
}
