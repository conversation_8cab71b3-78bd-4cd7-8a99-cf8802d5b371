package com.yinshu.sys.dao;

import java.util.List;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.SettingDm;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 
 * 系统设置表
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@DS("primary")
public interface SettingDmDao extends BaseMapper<SettingDm> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<SettingDm> queryList(@Param("entity") SettingDm entity);

	/**
	* 条件分页查询
	* @param page
	* @param entity
	* @return
	*/
	IPage<SettingDm> queryPageList(IPage<SettingDm> page, @Param("entity") SettingDm entity);

	List<SettingDm> check(SettingDm entity);

	Integer getMaxSort();
}
