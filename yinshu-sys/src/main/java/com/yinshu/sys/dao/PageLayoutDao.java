package com.yinshu.sys.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.PageLayout;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 
 * 页面布局表
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
public interface PageLayoutDao extends BaseMapper<PageLayout> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<PageLayout> queryList(@Param("entity") PageLayout entity);

	/**
	* 条件分页查询
	* @param page
	* @param entity
	* @return
	*/
	IPage<PageLayout> queryPageList(IPage<PageLayout> page, @Param("entity") PageLayout entity);

}
