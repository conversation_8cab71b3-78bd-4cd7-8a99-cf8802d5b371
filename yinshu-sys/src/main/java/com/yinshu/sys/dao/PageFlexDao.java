package com.yinshu.sys.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.PageFlex;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 
 * 动态页面表
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
public interface PageFlexDao extends BaseMapper<PageFlex> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<PageFlex> queryList(@Param("entity") PageFlex entity);

	/**
	* 条件分页查询
	* @param page
	* @param entity
	* @return
	*/
	IPage<PageFlex> queryPageList(IPage<PageFlex> page, @Param("entity") PageFlex entity);

	List<PageFlex> check(PageFlex entity);

	Integer getMaxSort();
}
