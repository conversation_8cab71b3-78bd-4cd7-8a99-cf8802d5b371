package com.yinshu.sys.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.Setting;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SettingDao extends BaseMapper<Setting> {
    IPage<Setting> queryPageList(IPage<Setting> page, @Param("entity") Setting entity);
    /**
     * 按条件查询
     * @param entity
     * @return
     */
    List<Setting> queryList(@Param("entity") Setting entity);

    List<Setting> check(Setting entity);

    Integer getMaxSort();
}
