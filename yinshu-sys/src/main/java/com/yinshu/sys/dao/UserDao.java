package com.yinshu.sys.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.User;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface UserDao extends BaseMapper<User> {

    /**
     * 条件分页查询
     *
     * @param page
     * @param params
     * @return
     */
    IPage<User> queryPageList(IPage<User> page, @Param("param") User entity);

    /**
     * 条件未授权的用户
     *
     * @param page
     * @param params
     * @return
     */
    IPage<User> queryUnAuthPageList(IPage<User> page, @Param("param") Map<String, Object> params);

    /**
     * 按条件查询
     *
     * @param params
     * @return
     */
    List<User> queryList(@Param("param") Map<String, Object> params);

    /**
     * 更新密码
     *
     * @param id
     * @param newPassword
     * @return
     */
    int updatePassword(@Param("id") String id, @Param("newPassword") String newPassword);

}
