package com.yinshu.sys.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.FileUpload;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 
 * 文件上传
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
public interface FileUploadDao extends BaseMapper<FileUpload> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<FileUpload> queryList(@Param("entity") FileUpload entity);

	/**
	* 条件分页查询
	* @param page
	* @param entity
	* @return
	*/
	IPage<FileUpload> queryPageList(IPage<FileUpload> page, @Param("entity") FileUpload entity);
	
	/**
	 * 批量删除文件
	 * @param fId 业务文件ID
	 */
	void removeByFids(String[] fids);

}
