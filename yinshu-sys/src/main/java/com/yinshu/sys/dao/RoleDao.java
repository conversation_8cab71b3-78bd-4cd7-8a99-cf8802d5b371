package com.yinshu.sys.dao;

import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.Role;

public interface RoleDao extends BaseMapper<Role> {

	/**
	 * 条件分页查询
	 * @param page
	 * @param params
	 * @return
	 */
	IPage<Role> queryPageList(IPage<Role> page, @Param("param") Map<String, Object> params);

	String queryParentIdIsNull();
}
