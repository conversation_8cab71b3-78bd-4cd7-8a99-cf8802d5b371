package com.yinshu.sys.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.RoleUser;

public interface RoleUserDao extends BaseMapper<RoleUser> {

	/**
	 * 条件分页查询
	 * @param page
	 * @param params
	 * @return
	 */
	IPage<RoleUser> queryPageList(IPage<RoleUser> page, @Param("param") Map<String, Object> params);
	
	/**
	 * 查询角色和用户的关系
	 * @param params
	 * @return
	 */
	List<Map<String, String>> queryList(Map<String, Object> params);
	
	/**
	 * 根据用户查询所有角色
	 * @param roleId
	 * @return
	 */
	List<Map<String, String>> queryUserListByRoleId(String roleId);
	
	/**
	 * 根据角色删除用户
	 * @param roleId
	 */
	void removeByRoleId(String roleId);
	
	/**
	 * 根据用户删除
	 * @param roleId
	 */
	void removeByUserIds(List<String> idList);
	
}
