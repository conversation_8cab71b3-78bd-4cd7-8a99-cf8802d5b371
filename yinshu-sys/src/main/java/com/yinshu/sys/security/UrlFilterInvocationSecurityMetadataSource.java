package com.yinshu.sys.security;

import java.util.Collection;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.access.SecurityConfig;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.FilterInvocation;
import org.springframework.security.web.access.intercept.FilterInvocationSecurityMetadataSource;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;

import com.yinshu.sys.entity.SessionUser;

@Component
public class UrlFilterInvocationSecurityMetadataSource implements FilterInvocationSecurityMetadataSource {
	
	private static final Logger logger = LoggerFactory.getLogger(UrlFilterInvocationSecurityMetadataSource.class.getName());

	@Autowired
    private IgnoreUrlsConfig ignoreUrlsConfig;

	@Value("${ws.path:/ws}")
	private String webSocketPath;
	
	@Override
	public Collection<ConfigAttribute> getAllConfigAttributes() {
		return null;
	}

	@Override
	public Collection<ConfigAttribute> getAttributes(Object object) throws IllegalArgumentException {
		String requestUrl = ((FilterInvocation)object).getRequestUrl();
		for(String url : ignoreUrlsConfig.getUrls()) {
			if(requestUrl.contains(url)) {
				return null;
            }
        }
		if(requestUrl.startsWith(webSocketPath)){
			return null;
		}
		logger.info("----------(2)Filter.URL----------[URL]" + requestUrl);
		AntPathMatcher antPathMatcher = new AntPathMatcher();
		String url = requestUrl.contains("/api/") ? requestUrl.substring(4) : requestUrl;
		Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
		if (authentication != null && authentication.getPrincipal() instanceof SessionUser) {
			SessionUser sessionUser = (SessionUser)authentication.getPrincipal();
			url = url.contains("?") ? url.substring(0, url.indexOf("?")) : url;
			for(String str : sessionUser.getPermissionList()) {
				if(url.contains(str) || antPathMatcher.match(str, url) || antPathMatcher.match(str, requestUrl)) {
					return SecurityConfig.createList(sessionUser.getRoleArray());
				}
			}
		}
		return SecurityConfig.createList("UN_AUTH" + "@" + url);
		//return null;
	}

	@Override
	public boolean supports(Class<?> clazz) {
		// TODO Auto-generated method stub
//		AntPathMatcher antPathMatcher = new AntPathMatcher();
		//return FilterInvocation.class.isAssignableFrom(clazz);
		return true;
	}
	
	

}
