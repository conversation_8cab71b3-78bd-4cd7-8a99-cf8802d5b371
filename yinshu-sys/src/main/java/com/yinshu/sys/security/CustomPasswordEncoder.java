package com.yinshu.sys.security;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CustomPasswordEncoder
 * @description TODO 定制密码验证策略，支持数据库密文
 * @date 2025/3/4 15:30
 **/

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

public class CustomPasswordEncoder implements PasswordEncoder {

    private final BCryptPasswordEncoder bcrypt = new BCryptPasswordEncoder();

    @Override
    public String encode(CharSequence rawPassword) {
        // 仍然使用 BCrypt 进行加密
        return bcrypt.encode(rawPassword);
    }

    @Override
    public boolean matches(CharSequence rawPassword, String encodedPassword) {
        // 1. 先尝试匹配 BCrypt 密码
        if (bcrypt.matches(rawPassword, encodedPassword)) {
            return true;
        }
        // 2. 如果 `encodedPassword` 本身是明文，直接比对
        return rawPassword.toString().equals(encodedPassword);
    }
}