package com.yinshu.sys.security;

import java.util.Collection;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.AccessDecisionManager;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Component;

@Component
public class UrlAccessDecisionManager implements AccessDecisionManager {

	private static final Logger logger = LoggerFactory.getLogger(UrlAccessDecisionManager.class.getName());
	
	@Override
	public void decide(Authentication authentication, Object object, Collection<ConfigAttribute> configAttributes)
			throws AccessDeniedException, InsufficientAuthenticationException {
		
		logger.info("----------(3)UrlAccess----------[auth]" + authentication);
		Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();

		for(ConfigAttribute attribute : configAttributes) {
			if(attribute.getAttribute().contains("UN_AUTH")) {
				String[] strs = attribute.getAttribute().split("@");
				throw new AccessDeniedException("URL未被授权：" + strs[1]);
			}
			for (GrantedAuthority authority : authorities) {
                if (authority.getAuthority().equals(attribute.getAttribute())) {
                    return;
                }
            }
			return;
		}
		
		throw new AccessDeniedException("无访问权限");
	}

	@Override
	public boolean supports(ConfigAttribute attribute) {
		// TODO Auto-generated method stub
		return true;
	}

	@Override
	public boolean supports(Class<?> clazz) {
		// TODO Auto-generated method stub
		return true;
	}

}
