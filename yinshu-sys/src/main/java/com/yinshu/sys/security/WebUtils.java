package com.yinshu.sys.security;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.utils.ResultVO;

public class WebUtils
{
    /**
     * 将字符串渲染到客户端
     * @param response 渲染对象
     * @param string 待渲染的字符串
     * @return null
     */
    public static void renderString(HttpServletResponse response, String string) throws IOException {
    	try {
    		response.setContentType("application/json;charset=UTF-8");
            String notLogin = JSONObject.toJSONString(new ResultVO<>("-1", string));
            response.getWriter().write(notLogin);
		} catch (Exception e) {
			e.printStackTrace();
		}
    }
    
    /**
     * 将字符串渲染到客户端
     * @param response 渲染对象
     * @param string 待渲染的字符串
     * @return null
     */
    public static void renderString(HttpServletResponse response,String code, String string) throws IOException {
    	try {
    		response.setContentType("application/json;charset=UTF-8");
            String notLogin = JSONObject.toJSONString(new ResultVO<>(code, string));
            response.getWriter().write(notLogin);
		} catch (Exception e) {
			e.printStackTrace();
		}
    }


    public static void setDownLoadHeader(String filename, HttpServletResponse response) throws UnsupportedEncodingException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fname= URLEncoder.encode(filename,"UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition","attachment; filename="+fname);
    }
}

