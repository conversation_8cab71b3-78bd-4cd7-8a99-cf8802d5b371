package com.yinshu.sys.security;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.jwt.JWTUtil;
import com.yinshu.sys.entity.SessionUser;
import com.yinshu.sys.entity.User;
import com.yinshu.utils.RedisCache;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SessionUserUtils
 * @description TODO session用户工具，建议以后所有获取用户信息的地方都用这个工具类
 * @date 2025/7/16 10:00
 **/
@Component
public class SessionUserUtils {

    public static SessionUserUtils INSTANCE;

    @Resource
    private UserDetailsService userDetailsService;

    @Resource
    private RedisCache redisCache;

    @Resource
    private Environment environment;


    /*
     * <AUTHOR>
     * @description //TODO 获取当前请求的token
     * @date 2025/7/16 15:10
     * @return java.lang.String
     **/
    public static String getCurrentRequestToken() {
        ServletRequestAttributes req = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (req != null) {
            return req.getRequest().getHeader("token");
        }
        return null;
    }


    /**
     * @return com.yinshu.sys.entity.SessionUser
     * <AUTHOR>
     * @description //TODO 获取当前登录用户信息
     * @date 2025/7/16 10:07
     **/
    public static SessionUser getSessionUser() {
        String token = getCurrentRequestToken();
        SessionUser user = null;
        if (token != null) {
            if (StringUtils.isNotBlank(token) && JWTUtil.verify(token) && !JWTUtil.isExpire(token)) {
                String loginName = JWTUtil.getTokenClaim(token, "loginName");
                String redis = INSTANCE.environment.getProperty("system.redis");
                if ("true".equals(redis)) {
                    Object o = INSTANCE.redisCache.getCacheObject("login:" + loginName);
                    if (o != null) {
                        String jsonString = JSONObject.toJSONString(o);
                        o = JSONObject.parseObject(jsonString, SessionUser.class);
                        user = (SessionUser) o;
                        return user;
                    }
                }
                UserDetails userDetails = INSTANCE.userDetailsService.loadUserByUsername(loginName);
                if (userDetails instanceof SessionUser) {
                    user = (SessionUser) userDetails;
                }
            }
        }
        return Optional.ofNullable(user).orElseGet(SessionUser::new);
    }

    /*
     * <AUTHOR>
     * @description //TODO 获取当前登录用户ID
     * @date 2025/7/16 10:08
     * @return java.lang.String
     **/
    public static String getUserId() {
        return Optional.ofNullable(getSessionUser().getUser()).orElseGet(User::new).getId();
    }

    /*
     * <AUTHOR>
     * @description //TODO 获取当前登录用户名称
     * @date 2025/7/16 10:08
     * @return java.lang.String
     **/
    public static String getUsername() {
        return getSessionUser().getUsername();
    }

    @PostConstruct
    public void init() {
        INSTANCE = this;
    }
}
