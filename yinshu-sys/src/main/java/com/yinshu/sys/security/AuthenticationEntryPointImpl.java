package com.yinshu.sys.security;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.HttpStatus;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.yinshu.utils.ResultVO;

@Component
public class AuthenticationEntryPointImpl implements AuthenticationEntryPoint {

	@Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) throws IOException, ServletException {
        ResultVO<String> resultVO = new ResultVO<>(HttpStatus.UNAUTHORIZED.toString(), authException.getMessage());
        WebUtils.renderString(response, JSON.toJSONString(resultVO));
    }
}


