package com.yinshu.sys.security;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.yinshu.utils.ResultVO;

@Component
public class AccessDeniedHandlerImpl implements AccessDeniedHandler {

//    AccessDeniedHandler 授权失败处理器
    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException accessDeniedException) throws IOException, ServletException {
    	String message = accessDeniedException.getMessage();
    	int index = message.indexOf("?");
    	message = index == -1 ? message : message.substring(0, index);
    	ResultVO<String> resultVO = new ResultVO<>(HttpStatus.FORBIDDEN.toString(), message);
    	response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write(JSON.toJSONString(resultVO));
    }
}


