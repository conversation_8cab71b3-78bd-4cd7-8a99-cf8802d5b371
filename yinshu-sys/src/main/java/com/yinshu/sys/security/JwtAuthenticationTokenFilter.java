package com.yinshu.sys.security;

import java.io.IOException;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.jwt.JWTUtil;
import com.yinshu.sys.entity.SessionUser;
import com.yinshu.utils.RedisCache;
import com.yinshu.utils.ResultVO;

/**
 * JWT过滤器
 * <AUTHOR>
 *
 */
@Component
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {
	
	private static final Logger logger = LoggerFactory.getLogger(JwtAuthenticationTokenFilter.class.getName());
	
	@Autowired
    private RedisCache redisCache;
	
	@Autowired
	private Environment environment;
	
	@Autowired
    private IgnoreUrlsConfig ignoreUrlsConfig;
	
	@Autowired
	private UserDetailsService userDetailsService;

    @Override
    protected void doFilterInternal(HttpServletRequest request,  HttpServletResponse response, 
    		FilterChain filterChain) throws ServletException, IOException {
    	
        String url = request.getRequestURI();
    	String token = request.getHeader("token");
    	//支持从参数获取token
		if(token == null){
			token = request.getParameter("token");
		}
    	logger.info("----------(1)jwt.filter----------[URL]" + url + " [jwt.token]" + token);
    	
    	/** 如果是白名单则不拦截 */
    	for(String path : ignoreUrlsConfig.getUrls()) {
			if(url.contains(path)) {
				filterChain.doFilter(request, response);
	            return;
            }
        }

		if(!StringUtils.hasText(token)) {
			WebUtils.renderString(response, "401", "Token不能为空");
			return;
		}

    	
        /** 1、判断token是否有效和过期 */
    	if(!JWTUtil.verify(token) || JWTUtil.isExpire(token)) {
    		if(url.contains("/api/logout")) {
    			filterChain.doFilter(request, response);
                return;
    		}
    		WebUtils.renderString(response, "401", "Token过期或无效，请重新登录");
    		return;
    	}

        try {
        	/**2、根据token中的userID获取用户对象(从redis中获取节约时间)*/
        	SessionUser sessionUser = null;
        	String loginName = JWTUtil.getTokenClaim(token, "loginName");
        	String redis = environment.getProperty("system.redis");
        	if(redis.equals("true")) {
        		String redisKey = "login:" + loginName;
        		JSONObject jsonObject = redisCache.getCacheObject(redisKey);
        		if(jsonObject == null) {
        			throw new RuntimeException("用户信息为空，请重新登录");
        		}
        		sessionUser = jsonObject.toJavaObject(SessionUser.class);
        	} else {
        		sessionUser = (SessionUser)userDetailsService.loadUserByUsername(loginName);
        	}
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(sessionUser, null, sessionUser.getAuthorities());
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            filterChain.doFilter(request, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            response.setContentType("application/json;charset=UTF-8");
            String notLogin = JSONObject.toJSONString(new ResultVO<>("-1", e.getMessage()));
            response.getWriter().write(notLogin);

        }
        
    } 
}


