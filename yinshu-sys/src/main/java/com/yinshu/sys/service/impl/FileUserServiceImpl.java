package com.yinshu.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.sys.dao.FileRangeMapper;
import com.yinshu.sys.entity.FileUser;
import com.yinshu.sys.entity.User;
import com.yinshu.sys.service.FileUserService;
import com.yinshu.sys.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 文件-用户关联服务 实现
 *
 * <AUTHOR>
 * @date 2024/12/27
 */
@Service
public class FileUserServiceImpl extends ServiceImpl<FileRangeMapper, FileUser> implements FileUserService {

    @Autowired
    private UserService userService;

    @Override
    public List<User> getUsers(Long fileId) {
        List<FileUser> bounds = this.list(lambdaQuery().getWrapper().eq(FileUser::getFileId, fileId));
        List<String> userIds = bounds.stream().map(FileUser::getUserId).collect(Collectors.toList());
        return userService.list(new LambdaQueryWrapper<User>().in(User::getId, userIds));
    }
}
