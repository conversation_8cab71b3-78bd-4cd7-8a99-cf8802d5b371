package com.yinshu.sys.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.PageLayout;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 页面布局表 
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
public interface PageLayoutService extends IService<PageLayout> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<PageLayout> queryList(PageLayout entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<PageLayout> queryPageList(PageLayout entity);

}
