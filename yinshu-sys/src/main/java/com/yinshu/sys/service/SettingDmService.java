package com.yinshu.sys.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.SettingDm;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 系统设置表 
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public interface SettingDmService extends IService<SettingDm> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<SettingDm> queryList(SettingDm entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<SettingDm> queryPageList(SettingDm entity);

	boolean check(SettingDm entity);

	Integer getMaxSort();

}
