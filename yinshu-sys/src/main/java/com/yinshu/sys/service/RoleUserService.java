package com.yinshu.sys.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yinshu.sys.entity.RoleUser;

public interface RoleUserService extends IService<RoleUser> {
	
	IPage<RoleUser> queryPageList(Map<String, Object> params);
	
	List<Map<String, String>> queryList(Map<String, Object> params);
	
	/**
	 * 根据角色查询所有用户
	 * @param roleId
	 * @return
	 */
	List<Map<String, String>> queryUserListByRoleId(String roleId);
	
	/**
	 * 根据角色删除用户
	 * @param roleId
	 */
	void removeByRoleId(String roleId);
	
	/**
	 * 根据用户删除
	 * @param roleId
	 */
	void removeByUserIds(List<String> idList);

}
