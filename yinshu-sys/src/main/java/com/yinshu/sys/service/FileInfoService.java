package com.yinshu.sys.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yinshu.sys.entity.FileInfo;
import com.yinshu.sys.entity.vo.QueryFileInfoParam;

import java.util.List;

/**
 * 报送文件服务
 *
 * <AUTHOR>
 * @date 2024/12/27
 */
public interface FileInfoService extends IService<FileInfo> {

    /**
     * 分页查询
     *
     * @param param 参数
     * @return {@link IPage }<{@link FileInfo }>
     */
    IPage<FileInfo> page(QueryFileInfoParam param);

    /**
     * 获取详情
     *
     * @param id 文件 id
     * @return {@link FileInfo }
     */
    FileInfo detail(Long id);

    /**
     * 保存或更新
     *
     * @param data 数据
     * @return boolean
     */
    boolean saveOrUpdate(FileInfo data);

    /**
     * 删除
     *
     * @param id 文件 ID
     */
    boolean remove(Long id);

    /**
     * 批量删除
     *
     * @param ids 文件 ID 列表
     * @return boolean
     */
    boolean removeBatch(List<Long> ids);

    /**
     * 发布
     *
     * @param id 文件 ID
     * @return boolean
     */
    boolean publish(Long id);

    /**
     * 取消发布
     *
     * @param id 文件 ID
     * @return boolean
     */
    boolean unpublish(Long id);

    /**
     * 每日同步
     */
    void syncDaily();
}
