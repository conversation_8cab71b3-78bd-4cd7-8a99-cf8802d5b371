package com.yinshu.sys.service.impl;

import java.util.List;
import java.util.Map;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.sys.dao.UnitDao;
import com.yinshu.sys.entity.Unit;
import com.yinshu.sys.service.UnitService;

@Service
@DS("primary")
public class UnitServiceImpl extends ServiceImpl<UnitDao, Unit> implements UnitService {

	@Autowired
	private UnitDao unitDao;
	
	public IPage<Unit> queryPageList(Unit entity) {
		IPage<Unit> resultList = unitDao.queryPageList(entity.toPage(), entity);
		return resultList;
	}
	
	/**
	 * 按条件查询
	 * @param params
	 * @return
	 */
	public List<Unit> queryList(Map<String, Object> params) {
		return unitDao.queryList(params);
	}
	
	/**
	 * 排序查询
	 * @param params
	 * @return
	 */
	public List<Unit> queryTreeList(Map<String, Object> params) {
		return unitDao.queryTreeList(params);
	}
	
}
