package com.yinshu.sys.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yinshu.sys.entity.Menu;
import com.yinshu.sys.entity.Role;
import com.yinshu.sys.entity.RoleMenu;

public interface RoleMenuService extends IService<RoleMenu> {
	
	/**
	 * 根据角色查询所有菜单
	 * @param roleId
	 * @return
	 */
	List<Map<String, String>> queryMenuListByRoleId(String roleId);

	/**
	 * 根据角色查询所有菜单对应关系
	 * @param roleId
	 * @return
	 */
	List<RoleMenu> queryRoleMenusByRoleId(String roleId);
	
	/**
	 * 根据角色删除菜单
	 * @param roleId
	 */
	void removeByRoleId(String roleId);
	
	/**
	 * 根据菜单ID删除
	 * @param menuIds
	 */
	void removeByMenuIds(String[] menuIds);
	
	/**
	 * 根据角色查询所有菜单,支持多角色
	 * @param roleIds
	 * @return
	 */
	List<Menu> queryMenuListByRoles(List<Role> roleIds);

}
