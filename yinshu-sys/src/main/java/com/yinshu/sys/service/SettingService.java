package com.yinshu.sys.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yinshu.sys.entity.Setting;

import java.util.List;

public interface SettingService extends IService<Setting> {
	
	IPage<Setting> queryPageList(Setting entity);

	/**
	 * 按条件查询
	 * @param entity
	 * @return
	 */
	List<Setting> queryList(Setting entity);

	boolean check(Setting entity);

	Integer getMaxSort();
	
}
