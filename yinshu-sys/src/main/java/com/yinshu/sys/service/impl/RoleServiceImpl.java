package com.yinshu.sys.service.impl;

import java.util.Map;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.sys.dao.RoleDao;
import com.yinshu.sys.entity.Role;
import com.yinshu.sys.service.RoleService;

@Service
@DS("syncDb")
public class RoleServiceImpl extends ServiceImpl<RoleDao, Role> implements RoleService {

	@Autowired
	RoleDao roleDao;

	public IPage<Role> queryPageList(Map<String, Object> params) {
		Page<Role> page = new Page<>(Integer.parseInt(params.get("pageNumber").toString()), Integer.parseInt(params.get("pageSize").toString()));
		IPage<Role> resultList = roleDao.queryPageList(page, params);
		return resultList;
	}

	@Override
	public String queryParentIdIsNull() {
		return roleDao.queryParentIdIsNull();
	}
}
