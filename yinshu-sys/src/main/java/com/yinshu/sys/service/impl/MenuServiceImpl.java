package com.yinshu.sys.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.sys.dao.MenuDao;
import com.yinshu.sys.entity.Menu;
import com.yinshu.sys.entity.Unit;
import com.yinshu.sys.service.MenuService;

@Service
public class MenuServiceImpl extends ServiceImpl<MenuDao, Menu> implements MenuService {

	@Autowired
	private MenuDao menuDao;

	public IPage<Menu> queryPageList(Menu entity) {
		IPage<Menu> resultList = menuDao.queryPageList(entity.toPage(), entity);
		return resultList;
	}
	
	/**
	 * 排序查询
	 * @return
	 */
	public List<Menu> queryList() {
		return menuDao.queryList();
	}
	
	/**
	 * 自定义方法
	 * @param id
	 * @return
	 */
	public Menu findById(String id) {
		List<Menu> resultList = menuDao.findById(id);
		if(resultList.size() == 0 || resultList.size() > 1) {
			throw new RuntimeException();
		}
		return resultList.get(0);
	}

	@Override
	public List<Menu> queryMenuPermissionTreeList() {
		return menuDao.queryMenuPermissionTreeList();
	}
}
