package com.yinshu.sys.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinshu.sys.entity.SettingDm;
import com.yinshu.sys.dao.SettingDmDao;
import com.yinshu.sys.service.SettingDmService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 系统设置表 
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Service
public class SettingDmServiceImpl extends ServiceImpl<SettingDmDao, SettingDm> implements SettingDmService {

	
	@Autowired
	private SettingDmDao settingDmDao;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<SettingDm> queryList(SettingDm entity) {
		return settingDmDao.queryList(entity);
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<SettingDm> queryPageList(SettingDm entity) {
		return settingDmDao.queryPageList(entity.toPage(), entity);
	}

	@Override
	public boolean check(SettingDm entity) {
		List<SettingDm> settingList = settingDmDao.check(entity);
        return settingList.size() <= 0;
    }

	@Override
	public Integer getMaxSort() {
		return settingDmDao.getMaxSort();
	}
}
