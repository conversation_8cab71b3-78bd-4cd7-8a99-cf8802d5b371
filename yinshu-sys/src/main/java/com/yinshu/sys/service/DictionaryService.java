package com.yinshu.sys.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yinshu.sys.entity.Dictionary;

import java.util.List;

public interface DictionaryService extends IService<Dictionary> {
    List<Dictionary> queryList(Dictionary entity);

    IPage<Dictionary> queryPageList(Dictionary entity);

    List<Dictionary> queryTreeAllList(Dictionary entity);
    /**
     * 检查字典名称和code的唯一性
     * @param entity
     * @return
     */
    boolean check(Dictionary entity);
    /**
     * code修改后，需同时修改关联子节点的parentCode
     * @param oldParentCode
     * @param newParentCode
     * @return
     */
    int updateByParentCode(String classify, String oldParentCode, String newParentCode);


    void removeByParentCode(Dictionary entity);

    Integer getMaxSort(Dictionary entity);

    
	Dictionary getByDicCodeSingle(String dicCode);
	
	List<Dictionary> queryNormalList(Dictionary entity);
	
}
