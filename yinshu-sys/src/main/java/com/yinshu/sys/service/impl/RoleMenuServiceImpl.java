package com.yinshu.sys.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.sys.dao.RoleMenuDao;
import com.yinshu.sys.entity.Menu;
import com.yinshu.sys.entity.Role;
import com.yinshu.sys.entity.RoleMenu;
import com.yinshu.sys.service.RoleMenuService;

@Service
public class RoleMenuServiceImpl extends ServiceImpl<RoleMenuDao, RoleMenu> implements RoleMenuService {

	@Autowired
	private RoleMenuDao roleMenuDao;

	/**
	 * 根据角色查询所有菜单
	 * @param roleId
	 * @return
	 */
	public List<Map<String, String>> queryMenuListByRoleId(String roleId) {
		return roleMenuDao.queryMenuListByRoleId(roleId);
	}

	@Override
	public List<RoleMenu> queryRoleMenusByRoleId(String roleId) {
		List<RoleMenu> roleMenuList = roleMenuDao.queryRoleMenusByRoleId(roleId);
		return roleMenuList;
	}

	/**
	 * 根据角色删除菜单
	 * @param roleId
	 */
	public void removeByRoleId(String roleId) {
		roleMenuDao.removeByRoleId(roleId);
	}
	
	/**
	 * 根据菜单ID删除
	 * @param menuIds
	 */
	public void removeByMenuIds(String[] menuIds) {
		roleMenuDao.removeByMenuIds(menuIds);
	}
	
	/**
	 * 根据角色查询所有菜单,支持多角色
	 * @return
	 */
	public List<Menu> queryMenuListByRoles(List<Role> roleIds) {
		return roleMenuDao.queryMenuListByRoles(roleIds);
	}
	
}
