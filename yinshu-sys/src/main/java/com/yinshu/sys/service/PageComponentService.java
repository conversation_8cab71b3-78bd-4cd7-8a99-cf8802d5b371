package com.yinshu.sys.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yinshu.sys.entity.PageComponent;

import java.util.List;

/**
 * 页面组件表 
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
public interface PageComponentService extends IService<PageComponent> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<PageComponent> queryList(PageComponent entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<PageComponent> queryPageList(PageComponent entity);

	boolean check(PageComponent entity);

	Integer getMaxSort();

}
