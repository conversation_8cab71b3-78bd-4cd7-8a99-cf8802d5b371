package com.yinshu.sys.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.MenuPermission;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 *  
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
public interface MenuPermissionService extends IService<MenuPermission> {

	/**
	 * 条件查询
	 * @param params
	 * @return
	 */
	List<MenuPermission> queryList(Map<String, Object> params);

	/**
	 * 条件分页查询
	 * @param params
	 * @return
	 */
	IPage<MenuPermission> queryPageList(Map<String, Object> params);

}
