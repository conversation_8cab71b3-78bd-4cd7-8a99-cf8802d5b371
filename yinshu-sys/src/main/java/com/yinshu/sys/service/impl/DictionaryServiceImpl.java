package com.yinshu.sys.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.sys.dao.DictionaryDao;
import com.yinshu.sys.entity.Dictionary;
import com.yinshu.sys.service.DictionaryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.List;

@Service
public class DictionaryServiceImpl extends ServiceImpl<DictionaryDao, Dictionary> implements DictionaryService {
    @Resource
    private DictionaryDao dictionaryDao;

    @Override
    public List<Dictionary> queryList(Dictionary entity) {
        return dictionaryDao.queryList(entity);
    }

    @Override
    public IPage<Dictionary> queryPageList(Dictionary entity) {
        return dictionaryDao.queryPageList(entity.toPage(), entity);
    }

    @Override
    public List<Dictionary> queryTreeAllList(Dictionary entity) {
        return dictionaryDao.queryTreeAllList(entity);
    }

    @Override
    public boolean check(Dictionary entity) {
        List<Dictionary> dictionaryList = dictionaryDao.check(entity);
        return dictionaryList.size() <= 0;
    }

    /**
     * code修改后，需同时修改关联子节点的parentCode
     * @param oldParentCode
     * @param newParentCode
     * @return
     */
    @Override
    public int updateByParentCode(String classify, String oldParentCode, String newParentCode) {
        return dictionaryDao.updateByParentCode(classify, oldParentCode, newParentCode);
    }

    @Override
    public void removeByParentCode(Dictionary entity) {
        dictionaryDao.removeByParentCode(entity);
    }

    @Override
    public Integer getMaxSort(Dictionary entity) {
        return dictionaryDao.getMaxSort(entity);
    }


    @Override
    public Dictionary getByDicCodeSingle(String dicCode) {
        return dictionaryDao.getByDicCodeSingle(dicCode);
    }
    
    
    public List<Dictionary> queryNormalList(Dictionary entity) {
        return dictionaryDao.queryList(entity);
    }

}
