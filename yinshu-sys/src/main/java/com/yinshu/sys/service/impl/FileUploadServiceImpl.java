package com.yinshu.sys.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.sys.dao.FileUploadDao;
import com.yinshu.sys.entity.FileUpload;
import com.yinshu.sys.service.FileUploadService;
import com.yinshu.utils.DateUtils;
import com.yinshu.utils.SnowflakeIdGenerator;
import com.yinshu.utils.StringHelper;

/**
 * 文件上传 
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Service
public class FileUploadServiceImpl extends ServiceImpl<FileUploadDao, FileUpload> implements FileUploadService {

	@Autowired
	private SnowflakeIdGenerator snowflakeIdGenerator;
	
	@Autowired
	private FileUploadDao fileUploadDao;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<FileUpload> queryList(FileUpload entity) {
		return fileUploadDao.queryList(entity);
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<FileUpload> queryPageList(FileUpload entity) {
		return fileUploadDao.queryPageList(entity.toPage(), entity);
	}
	
	/**
	 * 批量保存文件
	 * @param fId 业务文件ID
	 * @param fileUploadList
	 */
	public void saveBatchAll(String fId, List<FileUpload> fileUploadList) {
		Date dateTime = DateUtils.getNow();
		if (null != fileUploadList && !fileUploadList.isEmpty()) {
			for(FileUpload fileUpload : fileUploadList) {
				fileUpload.setFileName(fileUpload.getName().lastIndexOf(".") != -1 ? fileUpload.getName().substring(0, fileUpload.getName().lastIndexOf(".")) : fileUpload.getName());
				fileUpload.setFileType(StringHelper.getFileType(fileUpload.getName()));
				fileUpload.setId(snowflakeIdGenerator.nextIdStr());
				fileUpload.setfId(fId);
				fileUpload.setCreateTime(dateTime);
			}
		}
		this.saveBatch(fileUploadList);
	}
	
	/**
	 * 批量保存文件(不区分文件类型)
	 * @param fId 业务文件ID
	 * @param fileUploadList
	 */
	public void updateBatchAll(String fId, List<FileUpload> fileUploadList) {
		updateBatchAll(fId, null, fileUploadList);
	}
	
	/**
	 * 批量保存文件(区分文件类型)
	 * @param fId 业务文件ID
	 * @param fileUploadList
	 */
	public void updateBatchAll(String fId, String fileType, List<FileUpload> fileUploadList) {
		if(fileUploadList.size() == 0) {
			Map<String,Object> columnMap  = new HashMap<>();
			columnMap.put("f_id", fId);
			if(StringUtils.hasText(fileType)) {
				columnMap.put("file_type", fileType);
			}
			fileUploadDao.deleteByMap(columnMap);
			return;
		}
		
		/**1、如果上传文件的ID为空，则新增*/
		List<FileUpload> addFileList = new ArrayList<>();
		Map<String, String> uploadFileMap = new HashMap<>();
		
		for(FileUpload fileUpload : fileUploadList) {
			uploadFileMap.put(fileUpload.getId(), fileUpload.getId());
			if(!StringUtils.hasText(fileUpload.getId())) {
				FileUpload file = new FileUpload();
				file.setFileName(fileUpload.getName().lastIndexOf(".") != -1 ? fileUpload.getName().substring(0, fileUpload.getName().lastIndexOf(".")) : fileUpload.getName());
				file.setFilePath(fileUpload.getFilePath());
				file.setFileType(StringHelper.getFileType(fileUpload.getName()));
				file.setId(snowflakeIdGenerator.nextIdStr());
				file.setCreateTime(DateUtils.getNow());
				file.setfId(fId);
				addFileList.add(file);
			}
		}
		
		/**2、 如果上传的文件ID在已有的不存，表示已经删除了*/
		List<FileUpload> removeFileList = new ArrayList<>();
		FileUpload params = new FileUpload();
		params.setfId(fId);
		if(StringUtils.hasText(fileType)) {
			params.setFileType(fileType);	
		}
		List<FileUpload> resultFileList = queryList(params);
		for(FileUpload FileUpload : resultFileList) {
			if(!uploadFileMap.containsKey(FileUpload.getId())) {
				removeFileList.add(FileUpload);
			}
		}
		this.removeByIds(removeFileList);
		this.saveBatch(addFileList);
	}
	
	/**
	 * 批量删除文件
	 * @param fId 业务文件ID
	 */
	public void removeByFids(String[] fids) {
		fileUploadDao.removeByFids(fids);
	}
	
}
