package com.yinshu.sys.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yinshu.sys.entity.PageFlex;

import java.util.List;

/**
 * 动态页面表 
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
public interface PageFlexService extends IService<PageFlex> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<PageFlex> queryList(PageFlex entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<PageFlex> queryPageList(PageFlex entity);

	PageFlex getByPageCode(String pageCode);

	boolean check(PageFlex entity);

	Integer getMaxSort();

}
