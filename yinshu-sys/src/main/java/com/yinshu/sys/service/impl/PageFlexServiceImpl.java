package com.yinshu.sys.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.sys.dao.PageFlexDao;
import com.yinshu.sys.entity.PageFlex;
import com.yinshu.sys.service.PageFlexService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 动态页面表 
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@Service
public class PageFlexServiceImpl extends ServiceImpl<PageFlexDao, PageFlex> implements PageFlexService {

	
	@Autowired
	private PageFlexDao pageFlexDao;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<PageFlex> queryList(PageFlex entity) {
		return pageFlexDao.queryList(entity);
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<PageFlex> queryPageList(PageFlex entity) {
		return pageFlexDao.queryPageList(entity.toPage(), entity);
	}

	@Override
	public PageFlex getByPageCode(String pageCode) {
		PageFlex pageFlex = lambdaQuery().eq(PageFlex::getPageCode, pageCode).one();
		return pageFlex;
	}

	@Override
	public boolean check(PageFlex entity) {
		List<PageFlex> entityList = pageFlexDao.check(entity);
        return entityList.size() <= 0;
    }

	@Override
	public Integer getMaxSort() {
		return pageFlexDao.getMaxSort();
	}
}
