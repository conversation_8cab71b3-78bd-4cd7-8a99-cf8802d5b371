package com.yinshu.sys.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yinshu.sys.entity.FileUpload;

/**
 * 文件上传 
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
public interface FileUploadService extends IService<FileUpload> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<FileUpload> queryList(FileUpload entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<FileUpload> queryPageList(FileUpload entity);
	
	/**
	 * 批量保存文件
	 * @param fId 业务文件ID
	 * @param fileUploadList
	 */
	void saveBatchAll(String fId, List<FileUpload> fileUploadList);
	
	/**
	 * 批量修改文件
	 * @param fId 业务文件ID
	 * @param fileUploadList
	 */
	void updateBatchAll(String fId, List<FileUpload> fileUploadList);
	
	/**
	 * 批量保存文件(区分文件类型)
	 * @param fId 业务文件ID
	 * @param fileUploadList
	 */
	void updateBatchAll(String fId, String fileType, List<FileUpload> fileUploadList);
	
	/**
	 * 批量删除文件
	 * @param fId 业务文件ID
	 */
	void removeByFids(String[] fids);

}
