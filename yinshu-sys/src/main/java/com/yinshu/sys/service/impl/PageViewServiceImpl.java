package com.yinshu.sys.service.impl;

import com.yinshu.sys.dao.PageViewDao;
import com.yinshu.sys.service.PageViewService;
import com.yinshu.utils.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.List;


@Service
public class PageViewServiceImpl implements PageViewService {

	
	@Autowired
	private PageViewDao pageViewDao;

	@Override
	public List<LinkedHashMap<String, Object>> onlySelect(String onlySelectSql) {
		List<LinkedHashMap<String, Object>> resultMaps = pageViewDao.onlySelect(onlySelectSql);
		return CommonUtils.underlineToCamel(resultMaps);
	}
}
