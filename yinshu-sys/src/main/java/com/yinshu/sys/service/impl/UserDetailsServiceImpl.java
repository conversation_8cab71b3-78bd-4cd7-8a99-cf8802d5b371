package com.yinshu.sys.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import com.yinshu.sys.dao.RoleMenuDao;
import com.yinshu.sys.dao.RoleUserDao;
import com.yinshu.sys.dao.UserDao;
import com.yinshu.sys.entity.Menu;
import com.yinshu.sys.entity.Role;
import com.yinshu.sys.entity.SessionUser;
import com.yinshu.sys.entity.User;

/**
 * Spring 安全框架默认实现的接口
 * <AUTHOR>
 *
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

	@Autowired
	private UserDao userDao;
	
	@Autowired
	private RoleUserDao roleUserDao;
	
	@Autowired
	private RoleMenuDao roleMenuDao;

    @Override
    public UserDetails loadUserByUsername(String loginName) throws UsernameNotFoundException {
		Map<String, Object> paramsMap = new HashMap<>();
		paramsMap.put("loginName", loginName);
        List<User> userList = userDao.queryList(paramsMap);
        if (userList.size() == 0) {
            throw new RuntimeException("当前用户不存在");
        }
        User user = userList.get(0);
        List<Role> roleList = queryRoleListByUserId(user.getId());/**1: 查询用户对应的角色*/
        List<Menu> menuResultList = queryMenuListByRoles(roleList);/**2: 查询用户对应的菜单*/
        SessionUser sessionUser = new SessionUser(user, roleList, menuResultList);
        return sessionUser;
    }
    
    /**
     * 根据角色获取菜单
     * @return
     */
    private List<Role> queryRoleListByUserId(String userId) {
    	List<Role> roleList = new ArrayList<>();
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("userId", userId);
        List<Map<String, String>> userRoleList = roleUserDao.queryList(paramsMap);
        for(Map<String, String> entry : userRoleList) {
        	Role role = new Role();
        	role.setId(entry.get("role_id"));
        	role.setRoleCode(entry.get("role_code"));
        	role.setRoleName(entry.get("role_name"));
//        	role.setId(entry.get("ROLE_ID"));
//        	role.setRoleCode(entry.get("ROLE_CODE"));
//        	role.setRoleName(entry.get("ROLE_NAME"));
//            role.setRoleDescription(entry.get("ROLE_DESCRIPTION"));
        	roleList.add(role);
        }
        return roleList;
    	
    }
    
    /**
     * 根据角色获取菜单
     * @return
     */
    private List<Menu> queryMenuListByRoles(List<Role> roleList) {
    	List<Menu> menuList = new ArrayList<>();
    	if(roleList.size() > 0) {
    		menuList = roleMenuDao.queryMenuListByRoles(roleList);
    	}
    	return menuList;
    	
    }
   
}
