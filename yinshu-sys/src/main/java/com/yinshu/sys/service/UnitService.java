package com.yinshu.sys.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yinshu.sys.entity.Log;
import com.yinshu.sys.entity.Unit;

public interface UnitService extends IService<Unit> {
	
	/**
	 * 分页查询
	 * @param params
	 * @return
	 */
	IPage<Unit> queryPageList(Unit entity);
	
	/**
	 * 按条件查询
	 * @param params
	 * @return
	 */
	List<Unit> queryList(Map<String, Object> params);
	
	/**
	 * 排序查询
	 * @param params
	 * @return
	 */
	List<Unit> queryTreeList(Map<String, Object> params);
	
}
