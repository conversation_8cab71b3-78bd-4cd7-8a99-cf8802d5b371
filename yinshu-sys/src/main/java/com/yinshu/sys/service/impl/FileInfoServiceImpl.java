package com.yinshu.sys.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.sys.config.LocalStorageConfig;
import com.yinshu.sys.dao.FileInfoMapper;
import com.yinshu.sys.entity.FileInfo;
import com.yinshu.sys.entity.FileUser;
import com.yinshu.sys.entity.Setting;
import com.yinshu.sys.entity.vo.QueryFileInfoParam;
import com.yinshu.sys.manager.SettingManager;
import com.yinshu.sys.security.SecurityUtils;
import com.yinshu.sys.service.FileInfoService;
import com.yinshu.sys.service.FileUserService;
import com.yinshu.utils.AssertUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriUtils;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class FileInfoServiceImpl extends ServiceImpl<FileInfoMapper, FileInfo> implements FileInfoService {

    private final FileUserService fileUserService;
    private final LocalStorageConfig localStorageConfig;
    private final SettingManager settingManager;
    private final RestTemplate restTemplate;

    public FileInfoServiceImpl(FileUserService fileUserService, LocalStorageConfig localStorageConfig, SettingManager settingManager, RestTemplate restTemplate) {
        this.fileUserService = fileUserService;
        this.localStorageConfig = localStorageConfig;
        this.settingManager = settingManager;
        this.restTemplate = restTemplate;
    }

    @Override
    public IPage<FileInfo> page(QueryFileInfoParam param) {
        LambdaQueryWrapper<FileInfo> queryWrapper = lambdaQuery().getWrapper()
                .like(!ObjectUtils.isEmpty(param.getName()), FileInfo::getName, param.getName())
                .like(!ObjectUtils.isEmpty(param.getKeyword()), FileInfo::getName, param.getKeyword())
                .or().like(!ObjectUtils.isEmpty(param.getKeyword()), FileInfo::getDescription, param.getKeyword())
                .eq(param.getType() != null && param.getType() != 0, FileInfo::getType, param.getType());

        // Rental isolation
        String userId = SecurityUtils.getUserId();
        if (!SecurityUtils.isAdmin(userId)) {
            // Select allowed fileIds
            List<Long> fileIds = fileUserService.list(new LambdaQueryWrapper<FileUser>()
                    .eq(FileUser::getUserId, userId))
                    .stream()
                    .map(FileUser::getFileId)
                    .collect(Collectors.toList());
            queryWrapper.in(!ObjectUtils.isEmpty(fileIds), FileInfo::getId, fileIds);
        }

        LocalDate beginTime = param.getDate();
        if (!ObjectUtils.isEmpty(beginTime)) {
            queryWrapper.ge(FileInfo::getCreateTime, beginTime).
                lt(FileInfo::getCreateTime, beginTime.plusDays(1));
        }


        return this.page(param.toPage(), queryWrapper);
    }

    @Override
    public FileInfo detail(Long id) {
        FileInfo result = this.getById(id);

        // Query user ids
        List<FileUser> bounds = fileUserService.list(new LambdaQueryWrapper<FileUser>().eq(
                FileUser::getFileId, id
        ));
        result.setUserIds(bounds.stream().map(FileUser::getUserId).collect(Collectors.toList()));

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdate(FileInfo data) {
        // Save file
        MultipartFile file = data.getFile();
        if (!ObjectUtils.isEmpty(file)) {
            String filepath = localStorageConfig.putObject(file);
            data.setUrl(filepath);
        }

        // Sync update_time
        data.setUpdateTime(LocalDateTime.now());

        // Save or update data
        boolean result = super.saveOrUpdate(data);

        // Bind users
        List<FileUser> existedBounds = fileUserService.list(
                new LambdaQueryWrapper<FileUser>()
                        .eq(FileUser::getFileId, data.getId())
        );

        // Remove binds dropped
        List<String> userIds = data.getUserIds();
        List<Long> deletedBoundIds = existedBounds.stream()
                .filter(bound -> !userIds.contains(bound.getUserId()))
                .map(FileUser::getId)
                .collect(Collectors.toList());
        if (!deletedBoundIds.isEmpty()) fileUserService.removeByIds(deletedBoundIds);

        // Insert binds selected
        if (!ObjectUtils.isEmpty(userIds)) {
            List<FileUser> bounds2Save = userIds.stream()
                    .filter(id -> existedBounds.stream()
                            .noneMatch(bound -> bound.getUserId().equals(id)))
                    .map(id -> new FileUser().setFileId(data.getId()).setUserId(id))
                    .collect(Collectors.toList());
            if (!bounds2Save.isEmpty()) fileUserService.saveBatch(bounds2Save);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean remove(Long id) {
        boolean result = this.removeById(id);
        fileUserService.remove(new LambdaQueryWrapper<FileUser>().eq(FileUser::getFileId, id));
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeBatch(List<Long> ids) {
        boolean result = this.removeBatchByIds(ids);
        fileUserService.remove(new LambdaQueryWrapper<FileUser>().in(FileUser::getFileId, ids));
        return result;
    }

    @Override
    public boolean publish(Long id) {
        FileInfo info = getById(id);
        info.setPublished(true);
        return this.updateById(info);
    }

    @Override
    public boolean unpublish(Long id) {
        FileInfo info = getById(id);
        info.setPublished(false);
        return this.updateById(info);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncDaily() {
        Setting reportPlatform = settingManager.getByCode("reportPlatform");
        AssertUtils.notEmpty(reportPlatform, "报送平台信息获取失败");

        // Get all file infos
        String reportHost = reportPlatform.getParmValue();
        String yesterday = LocalDateTime.now()
                .minusDays(1)
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String url = "http://" + reportHost + "/report/api/report/template/review/getRpoertList/" + yesterday;
        JSONObject resp = restTemplate.getForObject(url, JSONObject.class);
        AssertUtils.notEmpty(resp);

        // Parse data & Download files
        JSONArray data = resp.getJSONArray("data");
        if (ObjectUtils.isEmpty(data)) return;

        LinkedList<FileInfo> files = new LinkedList<>();
        for (int i = 0; i < data.size(); i++) {
            JSONObject item = data.getJSONObject(i);
            // Parse file
            FileInfo fileInfo = new FileInfo()
                    .setName(item.getString("reportName"))
                    .setCreateTime(LocalDateTime.parse(item.getString("createtime"),
                            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                    );
            files.add(fileInfo);

            // Download file
            String fileKey = item.getString("alias");
            String downloadURL = "http://" + reportHost + "/report/api/download/1/" + fileKey;
            try {
                ResponseEntity<byte[]> fileResp = restTemplate.getForEntity(downloadURL, byte[].class);
                String filename = fileResp.getHeaders().getContentDisposition().getFilename();

                if (filename != null) filename = UriUtils.decode(filename, StandardCharsets.UTF_8);
                byte[] body = fileResp.getBody();
                if (body == null) continue; // 单个文件下载失败不影响
                ByteArrayInputStream fileStream = new ByteArrayInputStream(body);

                String fileUrl = localStorageConfig.putObject(fileStream, filename);
                fileInfo.setUrl(fileUrl);
            } catch (RestClientException e) {
                log.error("文件下载失败", e);
            }
        }


        // Save data
        saveBatch(files);
    }
}
