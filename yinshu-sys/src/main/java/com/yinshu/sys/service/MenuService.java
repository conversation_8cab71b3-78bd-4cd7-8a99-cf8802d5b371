package com.yinshu.sys.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yinshu.sys.entity.Menu;
import com.yinshu.sys.entity.Unit;

public interface MenuService extends IService<Menu> {
	
	IPage<Menu> queryPageList(Menu entity);
	
	/**
	 * 排序查询
	 * @return
	 */
	List<Menu> queryList();
	
	/**
	 * 自定义方法
	 * @param id
	 * @return
	 */
	Menu findById(String id);

	List<Menu> queryMenuPermissionTreeList();

}
