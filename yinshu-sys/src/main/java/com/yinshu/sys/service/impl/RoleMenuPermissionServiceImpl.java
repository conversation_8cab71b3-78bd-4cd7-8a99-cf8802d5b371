package com.yinshu.sys.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.sys.dao.RoleMenuPermissionDao;
import com.yinshu.sys.entity.RoleMenuPermission;
import com.yinshu.sys.service.RoleMenuPermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class RoleMenuPermissionServiceImpl extends ServiceImpl<RoleMenuPermissionDao, RoleMenuPermission> implements RoleMenuPermissionService {

	@Resource
	private RoleMenuPermissionDao roleMenuPermissionDao;


	@Override
	public int deleteByRoleId(String roleId) {
		return roleMenuPermissionDao.deleteByRoleId(roleId);
	}

	@Override
	public int deleteByMenuIds(List<String> idList) {
		if(idList != null && idList.size() > 0){
			return roleMenuPermissionDao.deleteByMenuIds(idList);
		}
		return 0;
	}

	@Override
	public int deleteByRoleMenuIds(List<String> idList) {
		if(idList != null && idList.size() > 0){
			return roleMenuPermissionDao.deleteByRoleMenuIds(idList);
		}
		return 0;
	}
}
