package com.yinshu.sys.service.impl;

import java.util.List;
import java.util.Map;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.sys.dao.UserDao;
import com.yinshu.sys.entity.User;
import com.yinshu.sys.service.UserService;

@Service
@DS("primary")
public class UserServiceImpl extends ServiceImpl<UserDao, User> implements UserService {

	@Autowired
	private UserDao userDao;
	
	public IPage<User> queryPageList(User entity) {
		IPage<User> resultList = userDao.queryPageList(entity.toPage(), entity);
		return resultList;
	}
	
	/**
	 * 条件未授权的用户
	 * @param params
	 * @return
	 */
	public IPage<User> queryUnAuthPageList(Map<String, Object> params) {
		Page<User> page = new Page<>(Integer.parseInt(params.get("pageNumber").toString()), Integer.parseInt(params.get("pageSize").toString()));
		IPage<User> resultList = userDao.queryUnAuthPageList(page, params);
		return resultList;
	}
	
	/**
	 * 按条件查询
	 * @param params
	 * @return
	 */
	public List<User> queryList(Map<String, Object> params) {
		return userDao.queryList(params);
	}

	@Override
	public int updatePassword(String id, String newPassword) {
		return userDao.updatePassword(id, newPassword);
	}

}
