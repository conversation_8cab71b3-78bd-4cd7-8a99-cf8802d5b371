package com.yinshu.sys.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.sys.dao.MenuPermissionDao;
import com.yinshu.sys.entity.MenuPermission;
import com.yinshu.sys.service.MenuPermissionService;

/**
 *  
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Service
public class MenuPermissionServiceImpl extends ServiceImpl<MenuPermissionDao, MenuPermission> implements MenuPermissionService {

	
	@Autowired
	private MenuPermissionDao menuPermissionDao;
	
	/**
	 * 条件查询
	 * @param params
	 * @return
	 */
	public List<MenuPermission> queryList(Map<String, Object> params) {
		return menuPermissionDao.queryList(params);
	}
	
	/**
	 * 条件分页查询
	 * @param params
	 * @return
	 */
	public IPage<MenuPermission> queryPageList(Map<String, Object> params) {
		Page<MenuPermission> page = new Page<>(Integer.parseInt(params.get("pageNumber").toString()), Integer.parseInt(params.get("pageSize").toString()));
		IPage<MenuPermission> resultList = menuPermissionDao.queryPageList(page, params);
		return resultList;
	}
}
