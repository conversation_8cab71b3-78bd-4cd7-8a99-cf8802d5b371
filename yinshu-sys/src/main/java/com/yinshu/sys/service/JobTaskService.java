package com.yinshu.sys.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.JobTask;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 *  
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
public interface JobTaskService extends IService<JobTask> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<JobTask> queryList(JobTask entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<JobTask> queryPageList(JobTask entity);

}
