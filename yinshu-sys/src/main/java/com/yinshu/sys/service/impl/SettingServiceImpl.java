package com.yinshu.sys.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.sys.dao.SettingDao;
import com.yinshu.sys.entity.Setting;
import com.yinshu.sys.service.SettingService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class SettingServiceImpl extends ServiceImpl<SettingDao, Setting> implements SettingService {

    @Resource
    private SettingDao settingDao;

    @Override
    public IPage<Setting> queryPageList(Setting entity) {
        return settingDao.queryPageList(entity.toPage(), entity);
    }

    @Override
    public List<Setting> queryList(Setting entity) {
        return settingDao.queryList(entity);
    }

    @Override
    public boolean check(Setting entity) {
        List<Setting> settingList = settingDao.check(entity);
        return settingList.size() <= 0;
    }

    @Override
    public Integer getMaxSort() {
        return settingDao.getMaxSort();
    }
}
