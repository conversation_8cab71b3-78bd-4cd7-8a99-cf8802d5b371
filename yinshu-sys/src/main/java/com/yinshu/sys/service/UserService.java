package com.yinshu.sys.service;

import java.util.List;
import java.util.Map;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yinshu.sys.entity.User;
import org.apache.ibatis.annotations.Param;

public interface UserService extends IService<User> {
	
	IPage<User> queryPageList(User entity);
	
	/**
	 * 条件未授权的用户
	 * @param page
	 * @param params
	 * @return
	 */
	IPage<User> queryUnAuthPageList(Map<String, Object> params);
	
	/**
	 * 按条件查询
	 * @param params
	 * @return
	 */
	List<User> queryList(Map<String, Object> params);

	/**
	 * 更新密码
	 * @param id
	 * @param newPassword
	 * @return
	 */
	int updatePassword(String id, String newPassword);
	
}
