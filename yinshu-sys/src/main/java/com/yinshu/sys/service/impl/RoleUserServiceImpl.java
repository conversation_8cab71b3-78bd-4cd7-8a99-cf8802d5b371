package com.yinshu.sys.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.sys.dao.RoleUserDao;
import com.yinshu.sys.entity.RoleUser;
import com.yinshu.sys.service.RoleUserService;

@Service
public class RoleUserServiceImpl extends ServiceImpl<RoleUserDao, RoleUser> implements RoleUserService {

	@Autowired
	RoleUserDao roleUserDao;

	public IPage<RoleUser> queryPageList(Map<String, Object> params) {
		Page<RoleUser> page = new Page<>(Integer.parseInt(params.get("pageNumber").toString()), Integer.parseInt(params.get("pageSize").toString()));
		IPage<RoleUser> resultList = roleUserDao.queryPageList(page, params);
		return resultList;
	}

	/**
	 * 查询角色和用户的关系
	 * @param params
	 * @return
	 */
	@Override
	public List<Map<String, String>> queryList(Map<String, Object> params) {
		return roleUserDao.queryList(params);
	}
	
	/**
	 * 根据角色查询所有用户
	 * @param roleId
	 * @return
	 */
	public List<Map<String, String>> queryUserListByRoleId(String roleId) {
		return roleUserDao.queryUserListByRoleId(roleId);
	}
	
	/**
	 * 根据角色删除用户
	 * @param roleId
	 */
	public void removeByRoleId(String roleId) {
		roleUserDao.removeByRoleId(roleId);
	}
	
	/**
	 * 根据用户删除
	 * @param roleId
	 */
	public void removeByUserIds(List<String> idList) {
		roleUserDao.removeByUserIds(idList);
	}
	
}
