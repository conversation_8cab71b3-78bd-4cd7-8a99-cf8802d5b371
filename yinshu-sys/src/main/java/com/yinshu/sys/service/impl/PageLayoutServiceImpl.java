package com.yinshu.sys.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinshu.sys.entity.PageLayout;
import com.yinshu.sys.dao.PageLayoutDao;
import com.yinshu.sys.service.PageLayoutService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 页面布局表 
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@Service
public class PageLayoutServiceImpl extends ServiceImpl<PageLayoutDao, PageLayout> implements PageLayoutService {

	
	@Autowired
	private PageLayoutDao pageLayoutDao;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<PageLayout> queryList(PageLayout entity) {
		return pageLayoutDao.queryList(entity);
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<PageLayout> queryPageList(PageLayout entity) {
		return pageLayoutDao.queryPageList(entity.toPage(), entity);
	}
}
