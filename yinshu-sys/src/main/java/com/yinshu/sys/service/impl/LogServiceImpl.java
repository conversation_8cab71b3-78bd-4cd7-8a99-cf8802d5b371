package com.yinshu.sys.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.sys.dao.LogDao;
import com.yinshu.sys.entity.Log;
import com.yinshu.sys.service.LogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class LogServiceImpl extends ServiceImpl<LogDao, Log> implements LogService {

    @Resource
    private LogDao logDao;

    @Override
    public IPage<Log> queryPageList(Log entity) {
        return logDao.queryPageList(entity.toPage(), entity);
    }
}
