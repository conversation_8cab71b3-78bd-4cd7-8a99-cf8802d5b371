package com.yinshu.sys.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinshu.sys.entity.JobTask;
import com.yinshu.sys.dao.JobTaskDao;
import com.yinshu.sys.service.JobTaskService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 *  
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Service
public class JobTaskServiceImpl extends ServiceImpl<JobTaskDao, JobTask> implements JobTaskService {

	
	@Autowired
	private JobTaskDao jobTaskDao;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<JobTask> queryList(JobTask entity) {
		return jobTaskDao.queryList(entity);
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<JobTask> queryPageList(JobTask entity) {
		return jobTaskDao.queryPageList(entity.toPage(), entity);
	}
}
