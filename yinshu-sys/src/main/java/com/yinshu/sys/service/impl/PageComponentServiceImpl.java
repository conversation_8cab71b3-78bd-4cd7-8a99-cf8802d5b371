package com.yinshu.sys.service.impl;

import java.util.List;

import com.yinshu.sys.entity.Setting;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinshu.sys.entity.PageComponent;
import com.yinshu.sys.dao.PageComponentDao;
import com.yinshu.sys.service.PageComponentService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 页面组件表 
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@Service
public class PageComponentServiceImpl extends ServiceImpl<PageComponentDao, PageComponent> implements PageComponentService {

	
	@Autowired
	private PageComponentDao pageComponentDao;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<PageComponent> queryList(PageComponent entity) {
		return pageComponentDao.queryList(entity);
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<PageComponent> queryPageList(PageComponent entity) {
		return pageComponentDao.queryPageList(entity.toPage(), entity);
	}

	@Override
	public boolean check(PageComponent entity) {
		List<PageComponent> entityList = pageComponentDao.check(entity);
        return entityList.size() <= 0;
    }

	@Override
	public Integer getMaxSort() {
		return pageComponentDao.getMaxSort();
	}
}
