package com.yinshu.sys.aop;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;
import com.yinshu.annotation.OperLog;
import com.yinshu.sys.entity.Log;
import com.yinshu.sys.entity.SessionUser;
import com.yinshu.sys.manager.LogManager;

@Aspect
@Component
public class WebLogAspect {
    private static final Logger logger = LoggerFactory.getLogger(WebLogAspect.class.getName());

    /** 自定义线程池用于异步写入日志 */
    private static final ExecutorService customExecutor;

    /** 排除敏感属性字段 */
    private static final SimplePropertyPreFilter filter;

    static{
        filter = new SimplePropertyPreFilter();
        filter.getExcludes().add("password");
        filter.getExcludes().add("oldPassword");
        filter.getExcludes().add("newPassword");
        filter.getExcludes().add("confirmPassword");

        customExecutor = Executors.newFixedThreadPool(8);
    }

    @Autowired
    private LogManager logManager;

//    @Pointcut("execution(public * com.yinshu.*.controller..*.*(..)) " +
//            "&& !execution(public * com.yinshu.sys.controller.LogController.*(..)) ")
    @Pointcut("execution(public * com.yinshu.sys.controller.*.*(..)) || "
    		+ "execution(public * com.yinshu.login.*.*(..)) || "
    		+ "execution(public * com.yinshu.tocc.datasync.controller.*.*(..)) || "
			+ "execution(public * com.yinshu.app.auth.*.*(..)) || "
			+ "execution(public * com.yinshu.tocc.basic.*.*(..)) || "
			+ "execution(public * com.yinshu.tcps.controller.*.*(..)) || "
			+ "execution(public * com.yinshu.app.controller.*.*(..)))")
    public void webLog() {
    	
    }

    @Before("webLog()")
    public void doBefore(JoinPoint joinPoint) throws Throwable {
        try {
        	// 接收到请求，记录请求内容
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            MethodSignature signature = (MethodSignature)joinPoint.getSignature();//从切面织入点处通过反射机制获取织入点处的方法
            Method method = signature.getMethod();//获取切入点所在的方法
            OperLog operLog = method.getAnnotation(OperLog.class);//获取操作
            
            // 记录下请求内容
            /*logger.info("URL : " + request.getRequestURL().toString());
            logger.info("HTTP_METHOD : " + request.getMethod());
            logger.info("IP : " + request.getRemoteAddr());
            logger.info("CLASS_METHOD : " + joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName());
            logger.info("ARGS : " + Arrays.toString(joinPoint.getArgs()));*/

            Log log = new Log();
            log.setOperUser(getUser());
            log.setUrl(request.getRequestURL().toString());
            log.setLoginIp(request.getRemoteAddr());
            log.setContent(parseContent(joinPoint.getArgs()));
            log.setBrowserType(getBrowser(request));
            log.setOperType(request.getMethod());
            if(operLog != null) {
            	log.setOperModul(operLog.operModul()); // 操作模块
            	log.setOperType(operLog.operType()); // 操作类型
            }
            
            if(operLog == null || operLog.operType().equals("ignore")) {
            	return;
            }
            
            logManager.save(log);
            //使用异步保存日志，提升请求效率
            //asyncSaveLog(log);
        } catch (Exception e) {
            logger.error("日志记录异常",e);
        }
    }

    /**
     * 异步保存日志的方法
     * @param log
     * @return
     */
    private CompletableFuture<String> asyncSaveLog(Log log){
        CompletableFuture<String> completableFuture = CompletableFuture.supplyAsync(() -> {
            logManager.save(log);
            return "保存成功";
        }, customExecutor);
        return completableFuture;
    }


    private static String parseContent(Object[] args){
        List<String> filterList = Arrays.asList(args).stream().map(obj ->{
            String objStr =  parseParams(obj);
            return objStr;
        }).collect(Collectors.toList());
        return Arrays.toString(filterList.toArray());
    }

    /**
     *  解析参数
     * @return
     */
    private static String parseParams (Object obj){
        if(obj == null){
            return "";
        }
        String json = "";
        // 过滤掉部分请求参数，方法参数为HttpServletRequest HttpServletResponse MultipartFile BindingResult
        if(obj instanceof HttpServletRequest){
            Map<String, String[]> parameterMap = ((HttpServletRequest) obj).getParameterMap();
            json = JSON.toJSONString(parameterMap, filter);
        }else if(obj instanceof HttpServletResponse || obj instanceof MultipartFile || obj instanceof BindingResult){
            String packageName = obj.getClass().getName();
            json = packageName;
        }else{
            json = JSON.toJSONString(obj, filter);
        }
        return json;
    }

    private static String getBrowser(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        if (userAgent.contains("Firefox")) {
            return "Mozilla Firefox";
        } else if (userAgent.contains("Chrome")) {
            return "Google Chrome";
        } else if (userAgent.contains("Safari")) {
            return "Apple Safari";
        } else if (userAgent.contains("Edge")) {
            return "Microsoft Edge";
        } else if (userAgent.contains("MSIE") || userAgent.contains("Trident")) {
            return "Internet Explorer";
        }
        return "Unknown Browser";
    }

    private static String getUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if(authentication != null){
        	if(authentication.getPrincipal().equals("anonymousUser")) {
        		return null;
        	}
            SessionUser sessionUser = (SessionUser)authentication.getPrincipal();
            if(sessionUser != null){
                String userAccount = sessionUser.getUser().getLoginName();
                return userAccount;
            }
        }
        return null;
    }
/*

    @AfterReturning(returning = "ret", pointcut = "webLog()")
    public void doAfterReturning(Object ret) throws Throwable {
        // 处理完请求，返回内容
        //logger.info("RESPONSE : " + ret);
    }
*/

}
