package com.yinshu.tdss.controller;


import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.tdss.entity.vo.QueryBaseDataParam;
import com.yinshu.tdss.service.FreightService;
import com.yinshu.utils.ResultVO;

/***
 * 危货车营运专题
 * <AUTHOR>
 */
@RestController
@RequestMapping(ApiConstant.API_TDSS_PREFIX + "/freight")
public class FreightController {

    @Autowired
    private FreightService freightService;

    /**
     * 获取企业数据
     * @param query
     * @return
     */
    @PostMapping("/getCompanyData")
    public ResultVO<?> getCompanyData(@RequestBody QueryBaseDataParam query) {
        JSONObject object = freightService.getCompanyData(query);
        return ResultVO.suc(object);
    }

    /**
     * 获取基础指标数据
     * @param query
     * @return
     */
    @PostMapping("/getBasicData")
    public ResultVO<?> getBasicData(@RequestBody QueryBaseDataParam query) {
        JSONObject object = freightService.getBasicData(query);
        return ResultVO.suc(object);
    }

    /**
     * 获取运营指标数据
     * @param query
     * @return
     */
    @PostMapping("/getDailyOpsData")
    public ResultVO<?> getDailyOpsData(@RequestBody QueryBaseDataParam query) {
        Map<String, Object> object = freightService.getDailyOpsData(query);
        return ResultVO.suc(object);
    }



    /**
     * 获取危货运输排名
     * @param query
     * @return
     */
    @PostMapping("/getTruckDailyOps")
    public ResultVO<?> getTruckDailyOps(@RequestBody QueryBaseDataParam query) {
        JSONObject object = freightService.getTruckDailyOps(query);
        return ResultVO.suc(object);
    }

    /****
     * 运营效率指标
     * @param query
     * @return
     */
    @PostMapping("/getCargoHourTrend")
    public ResultVO<?> getCargoHourTrend(@RequestBody QueryBaseDataParam query) {
        JSONObject object = freightService.getCargoHourTrend(query);
        return ResultVO.suc(object);
    }

    /****
     * 危货运输量趋势
     * @param query
     * @return
     */
    @PostMapping("/getTrendTransVolume")
    public ResultVO<?> getTrendTransVolume(@RequestBody QueryBaseDataParam query) {
        JSONObject object = freightService.getTrendTransVolume(query);
        return ResultVO.suc(object);
    }

    /***
     * 报警事件分类
     * @param query
     * @return
     */
    @PostMapping("/getTruckAlarmHour")
    public ResultVO<?> getTruckAlarmHour(@RequestBody QueryBaseDataParam query) {
        JSONObject object = freightService.getTruckAlarmHour(query);
        return ResultVO.suc(object);
    }

    /***
     * 报警事件趋势
     * @param query
     * @return
     */
    @PostMapping("/getTrendAlarmEvents")
    public ResultVO<?> getTrendAlarmEvents(@RequestBody QueryBaseDataParam query) {
        JSONObject object = freightService.getTrendAlarmEvents(query);
        return ResultVO.suc(object);
    }

    /***
     * 获取危货车地图车辆车速定位热力图
     * @param jsonObject
     * @return
     */
    @PostMapping("/getTruckSpeedHeatMap")
    public ResultVO<?> getTruckSpeedHeatMap(@RequestBody JSONObject jsonObject) {
        JSONObject object = freightService.getTruckSpeedHeatMap(jsonObject);
        return ResultVO.suc(object);
    }

    /***
     * 获取危货车地图运单目的地位置密度热力图
     * @param jsonObject
     * @return
     */
    @PostMapping("/getTruckWayBillDestHeatMap")
    public ResultVO<?> getTruckWayBillDestHeatMap(@RequestBody JSONObject jsonObject) {
        JSONObject object = freightService.getTruckWayBillDestHeatMap(jsonObject);
        return ResultVO.suc(object);
    }

    /***
     * 获取危货车地图事故黑点热力图
     * @param jsonObject
     * @return
     */
    @PostMapping("/getTruckAccidentHeatMap")
    public ResultVO<?> getTruckAccidentHeatMap(@RequestBody JSONObject jsonObject) {
        JSONObject object = freightService.getTruckAccidentHeatMap(jsonObject);
        return ResultVO.suc(object);
    }
}
