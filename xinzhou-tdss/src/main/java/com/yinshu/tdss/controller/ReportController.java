package com.yinshu.tdss.controller;

import com.yinshu.common.ApiConstant;
import com.yinshu.tdss.entity.dto.ReportParamDTO;
import com.yinshu.tdss.entity.entity.FzjcReportFile;
import com.yinshu.tdss.service.ReportService;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;

@RestController
@RequestMapping(ApiConstant.API_TDSS_PREFIX + "/report")
public class ReportController {

    @Autowired
    private ReportService reportService;

    /**
     * 巡游车分析报告
     * @return
     */
    @PostMapping("/cruiseReport")
    public ResultVO<?> cruiseReport (@RequestBody ReportParamDTO reportParamDTO){

        reportService.cruiseReport(reportParamDTO);

        return ResultVO.suc(null);
    }

    /**
     * 下载报告
     * @return
     */
    @PostMapping("/downloadReport")
    public ResultVO<?> downloadReport (@RequestBody FzjcReportFile fzjcReportFile){
        HashMap<String, Object> hashMap = reportService.downloadReport(fzjcReportFile);
        return ResultVO.suc(hashMap);
    }
}
