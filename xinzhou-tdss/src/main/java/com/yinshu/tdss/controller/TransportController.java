package com.yinshu.tdss.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.tdss.service.TransportService;
import com.yinshu.utils.ResultVO;

@RestController
@RequestMapping(ApiConstant.API_TDSS_PREFIX + "/transport")
public class TransportController {
    @Autowired
    private TransportService transportService;

    /**
     * 报警事件分类
     * @param query
     * @return
     */
    @PostMapping("/classifyOfAlarmEvents")
    public ResultVO<?> classifyOfAlarmEvents(@RequestBody JSONObject query){
        JSONObject object = transportService.classifyOfAlarmEvents(query);
        return ResultVO.suc(object);
    }

    /**
     * 报警事件分类
     * @param query
     * @return
     */
    @PostMapping("/classifyOfAlarmTrend")
    public ResultVO<?> classifyOfAlarmTrend(@RequestBody JSONObject query){
        JSONObject object = transportService.classifyOfAlarmTrend(query);
        return ResultVO.suc(object);
    }

    /**
     * 忻州市城际客运基础数据
     * @param query
     * @return
     */
    @PostMapping("/basicData")
    public ResultVO<?> basicData(@RequestBody JSONObject query){
        JSONObject object = transportService.basicData(query);
        return ResultVO.suc(object);
    }

    /**
     * 全部企业 ， 还是基础数据但过滤
     * @param query
     * @return
     */
    @PostMapping("/company")
    public ResultVO<?> company(@RequestBody JSONObject query){
        JSONObject object = transportService.company(query);
        return ResultVO.suc(object);
    }

    /**
     * 忻州市城际客运营运数据按日
     * @param query
     * @return
     */
    @PostMapping("/dataByDay")
    public ResultVO<?> dataByDay(@RequestBody JSONObject query){
        JSONObject object = transportService.dataByDay(query);
        return ResultVO.suc(object);
    }

    /**
     * 忻州市城际客运客运站客流量排名按日
     * @param query
     * @return
     */
    @PostMapping("/trafficRanking")
    public ResultVO<?> trafficRanking(@RequestBody JSONObject query){
        JSONObject object = transportService.trafficRanking(query);
        return ResultVO.suc(object);
    }

    /**
     * 忻州市城际客运地图事故黑点热力图
     * @param query
     * @return
     */
    @PostMapping("/blackSpot")
    public ResultVO<?> blackSpot(@RequestBody JSONObject query){
        JSONObject object = transportService.blackSpot(query);
        return ResultVO.suc(object);
    }

    /**
     * 忻州市城际客运客流量趋势按小时
     * @param query
     * @return
     */
    @PostMapping("/trafficTrends")
    public ResultVO<?> trafficTrends(@RequestBody JSONObject query){
        JSONObject object = transportService.trafficTrends(query);
        return ResultVO.suc(object);
    }

    /**
     * 忻州市城际客运地图车辆客流热力图
     * @param query
     * @return
     */
    @PostMapping("/vehicleFlow")
    public ResultVO<?> vehicleFlow(@RequestBody JSONObject query){
        JSONObject object = transportService.vehicleFlow(query);
        return ResultVO.suc(object);
    }

    /**
     * 忻州市城际客运地图车辆车速运力热力图
     * @param query
     * @return
     */
    @PostMapping("/vehicleSpeed")
    public ResultVO<?> vehicleSpeed(@RequestBody JSONObject query){
        JSONObject object = transportService.vehicleSpeed(query);
        return ResultVO.suc(object);
    }
}
