package com.yinshu.tdss.controller;


import com.yinshu.annotation.OperLog;
import com.yinshu.common.ApiConstant;
import com.yinshu.sys.config.LocalStorageConfig;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(ApiConstant.API_TDSS_PREFIX + "/file")
public class FileUtilController {

    @Autowired
    private LocalStorageConfig localStorageConfig;

    /**文件上传*/
    @PostMapping("/upload")
    @OperLog(operModul="文件上传", operType="文件上传")
    public ResultVO<?> fileUpload(@RequestParam("file") MultipartFile[] file) {
        StringBuilder filePath = new StringBuilder();
        if (file != null ) for (MultipartFile fi : file) {
            String filepath = localStorageConfig.putObject(fi);
            filePath.append(filepath).append(";");
        }
        return new ResultVO<>(filePath.toString());
    }


    /**指定一個文件夾 template ， 文件上传*/
    @PostMapping("/template/upload")
    @OperLog(operModul="文件上传", operType="文件上传")
    public ResultVO<?> templatefileUpload(@RequestParam("file")MultipartFile[] file) {
        StringBuilder filePath = new StringBuilder();
        if (file != null) {

            for (MultipartFile fi : file) {
                String filepath = localStorageConfig.putObject(fi,"template");
                filePath.append(filepath).append(";");
            }

        }
        return new ResultVO<>(filePath.toString());
    }

    /**文件下载*/
    @GetMapping("/download/{filePath}")
    @OperLog(operModul="文件下载", operType="文件下载")
    public void fileDownload(@PathVariable(name = "filePath") String filePath, HttpServletResponse response) {
        localStorageConfig.downloadFile(filePath,response);
    }

}
