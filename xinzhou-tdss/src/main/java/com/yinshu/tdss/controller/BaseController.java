package com.yinshu.tdss.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.tdss.service.BaseService;
import com.yinshu.utils.ResultVO;

/***
 * 公共接口
 * @auther zhonglz
 */
@RestController
@RequestMapping(ApiConstant.API_TDSS_PREFIX + "/base")
public class BaseController {

    @Autowired
    private BaseService baseService;

    /**
     * 获取地市行政区划
     * @param query
     * @return
     */
    @PostMapping("/getCountyData")
    public ResultVO<?> getCountyData(@RequestBody JSONObject query) {
        JSONObject object = baseService.getCountyData(query);
        return ResultVO.suc(object);
    }

}
