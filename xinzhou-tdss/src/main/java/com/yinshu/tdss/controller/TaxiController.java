package com.yinshu.tdss.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.tdss.service.TaxiService;
import com.yinshu.utils.ResultVO;

/**
 * 出租车/巡游车 营运专题
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping(ApiConstant.API_TDSS_PREFIX + "/taxi")
public class TaxiController {

    @Autowired
    private TaxiService taxiService;

    /**
     * 忻州市巡游车地图车辆客流热力图
     * @param query
     * @return
     */
    @PostMapping("/vehicleFlow")
    public ResultVO<?> vehicleFlow(@RequestBody JSONObject query) {
        JSONObject object = taxiService.vehicleFlow(query);
        return ResultVO.suc(object);
    }

    /**
     * 忻州市巡游车地图车辆车速运力热力图
     * @param query
     * @return
     */
    @PostMapping("/vehicleSpeed")
    public ResultVO<?> vehicleSpeed(@RequestBody JSONObject query) {
        JSONObject object = taxiService.vehicleSpeed(query);
        return ResultVO.suc(object);
    }

    /**
     * 忻州市巡游车辆企业基础数据
     * @param query
     * @return
     */
    @PostMapping("/basicData")
    public ResultVO<?> basicData(@RequestBody JSONObject query) {
        JSONObject object = taxiService.basicData(query);
        return ResultVO.suc(object);
    }

    /**
     * 忻州市巡游车辆营运指标分析
     * @param query
     * @return
     */
    @PostMapping("/getOperatingSpeed")
    public ResultVO<?> operatorIndex(@RequestBody JSONObject query) {
        JSONObject object = taxiService.getOperatingSpeed(query);
        return ResultVO.suc(object);
    }

    /**
     * 忻州市巡游车辆营运趋势折线图
     * @param query
     * @return
     */
    @PostMapping("/operatingTrendLine")
    public ResultVO<?> operatingTrendLine(@RequestBody JSONObject query){
        JSONObject object = taxiService.operatingTrendLine(query);
        return ResultVO.suc(object);
    }


    /**
     * 忻州市巡游车辆营运趋势柱状图
     * @param query
     * @return
     */
    @PostMapping("/operatingTrendBar")
    public ResultVO<?> operatingTrendBar(@RequestBody JSONObject query){
        JSONObject object = taxiService.operatingTrendBar(query);
        return ResultVO.suc(object);
    }
}
