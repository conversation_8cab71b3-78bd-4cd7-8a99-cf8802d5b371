package com.yinshu.tdss.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.tdss.service.OnlineCarService;
import com.yinshu.utils.ResultVO;

/**
 * 网约车营运专题
 * author: hefei
 */
@RestController
@RequestMapping(ApiConstant.API_TDSS_PREFIX + "/onlineCar")
public class OnlineCarController {

    @Autowired
    private OnlineCarService onlineCarService;
    /**
     * 获取网约营运基础数据
     * @param query
     * @return
     */
    @RequestMapping("/getBasicData")
    public ResultVO<?> getBasicData(@RequestBody JSONObject query) {
        JSONObject object = onlineCarService.getBasicData(query);
        return ResultVO.suc(object);
    }

    /**
     * 获取网约车辆营运指标分析
     * @param query
     * @return
     */
    @RequestMapping("/getOnlineCarOperationIndexAnalysis")
    public ResultVO<?> getOnlineCarOperationIndexAnalysis(@RequestBody JSONObject query) {
        JSONObject object = onlineCarService.getOnlineCarOperationIndexAnalysis(query);
        return ResultVO.suc(object);
    }

    /**
     * 获取网约车辆营运趋势折线图
     * @param query
     * @return
     */
    @RequestMapping("/getOnlineCarZxt")
    public ResultVO<?> getOnlineCarZxt(@RequestBody JSONObject query) {
        JSONObject object = onlineCarService.getOnlineCarZxt(query);
        return ResultVO.suc(object);
    }

    /**
     * 获取网约车辆营运趋势柱状图
     * @param query
     * @return
     */
    @RequestMapping("/getOnlineCarZzt")
    public ResultVO<?> getOnlineCarZzt(@RequestBody JSONObject query) {
        JSONObject object = onlineCarService.getOnlineCarZzt(query);
        return ResultVO.suc(object);
    }

    /**
     * 获取网约车地图车辆车速运力热力图
     * @param query
     * @return
     */
    @RequestMapping("/getOnlineCarMap")
    public ResultVO<?> getOnlineCarMap(@RequestBody JSONObject query) {
        JSONObject object = onlineCarService.getOnlineCarMap(query);
        return ResultVO.suc(object);
    }

    /**
     * 获取网约车地图车辆客流热力图
     * @param query
     * @return
     */
    @RequestMapping("/getOnlineCarKlMap")
    public ResultVO<?> getOnlineCarKlMap(@RequestBody JSONObject query) {
        JSONObject object = onlineCarService.getOnlineCarKlMap(query);
        return ResultVO.suc(object);
    }
}
