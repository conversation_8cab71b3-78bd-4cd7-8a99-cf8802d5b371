package com.yinshu.tdss.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.tdss.service.BusService;
import com.yinshu.utils.ResultVO;

/**
 * 公交车营运专题
 * <AUTHOR>
 */
@RestController
@RequestMapping(ApiConstant.API_TDSS_PREFIX + "/bus")
public class BusController {
    @Autowired
    private BusService busService;

    /**
     * 获取运营指标与运营里程信息
     * @param query
     * @return
     */
    @PostMapping("/getBusOperationIndexAndMileage")
    public ResultVO<?> getBusOperationIndexAndMileage(@RequestBody JSONObject query) {
        JSONObject object = busService.getBusOperationIndexAndMileage(query);
        return ResultVO.suc(object);
    }

    /**
     * 获取公交车基础信息与线路整体指标
     * @param query
     * @return
     */
    @PostMapping("/getBusSpecialTopic")
    public ResultVO<?> getBusSpecialTopic(@RequestBody JSONObject query) {
        JSONObject object = busService.getBusSpecialTopic(query);
        return ResultVO.suc(object);
    }

    /**
     * 获取公交车线路排名
     * @param query
     * @return
     */
    @PostMapping("/getBusRank")
    public ResultVO<?> getBusRank(@RequestBody JSONObject query) {
        JSONObject object = busService.getBusRank(query);
        return ResultVO.suc(object);
    }

    /**
     * 获取公交车运营折线图
     * @param query
     * @return
     */
    @PostMapping("/getBusOperationLineChart")
    public ResultVO<?> getBusOperationLineChart(@RequestBody JSONObject query) {
        JSONObject object = busService.getBusOperationLineChart(query);
        return ResultVO.suc(object);
    }

    /**
     * 公交车运营柱状图
     * @param query
     * @return
     */
    @PostMapping("/getBusOperationBarChart")
    public ResultVO<?> getBusOperationBarChart(@RequestBody JSONObject query) {
        JSONObject object = busService.getBusOperationBarChart(query);
        return ResultVO.suc(object);
    }

    /**
     * 公交车线路指标分析-按小时
     * @param query
     * @return
     */
    @PostMapping("/getBusOperationIndexAnalysisHour")
    public ResultVO<?> getBusOperationIndexAnalysisHour(@RequestBody JSONObject query) {
        JSONObject object = busService.getBusOperationIndexAnalysisHour(query);
        return ResultVO.suc(object);
    }

    /**
     * 公交车线路指标分析-按天
     * @param query
     * @return
     */
    @PostMapping("/getBusOperationIndexAnalysisDay")
    public ResultVO<?> getBusOperationIndexAnalysisDay(@RequestBody JSONObject query) {
        JSONObject object = busService.getBusOperationIndexAnalysisDay(query);
        return ResultVO.suc(object);
    }

    /**
     * 公交车热力图分布
     * @param query
     * @return
     */
    @PostMapping("/getBusOperationHeatMapDistribution")
    public ResultVO<?> getBusOperationHeatMapDistribution(@RequestBody JSONObject query) {
        JSONObject object = busService.getBusOperationHeatMapDistribution(query);
        return ResultVO.suc(object);
    }

    /**
     * 获取公交车所有线路名称
     * @param query
     * @return
     */
    @PostMapping("/getBusLineName")
    public ResultVO<?> getBusLineName(@RequestBody JSONObject query) {
        JSONObject object = busService.getBusLineName(query);
        return ResultVO.suc(object);
    }

    /**
     * 获取公交车所有区县企业
     * @param query
     * @return
     */
    @PostMapping("/getBusDistrictsAndCompanies")
    public ResultVO<?> getBusDistrictsAndCompanies(@RequestBody JSONObject query) {
        JSONObject object = busService.getBusDistrictsAndCompanies(query);
        return ResultVO.suc(object);
    }
}
