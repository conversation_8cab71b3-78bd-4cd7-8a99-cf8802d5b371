package com.yinshu.tdss.common;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RemoteUrlConstants
 * @description TODO 大数据平台api地址
 * @date 2025/6/3 18:12
 **/
public class RemoteUrlConstants {

    /***
     * 基础区县市
     */
    public static final String dim_xinzhou_county = "/api/100430/data.json?";

    /**
     * 巡游车专题
     */
    /** 忻州市巡游车地图车辆客流热力图//ads_xinzhou_taix_hotmap_kenum_analysis */
    public static final String ads_xinzhou_taix_hotmap_kenum_analysis = "/api/100409/data.json?";

    /** 忻州市巡游车地图车辆车速运力热力图//ads_xinzhou_taix_hotmap_vandrunnum_analysis */
    public static final String ads_xinzhou_taix_hotmap_vandrunnum_analysis = "/api/100410/data.json?";

    /** 忻州市巡游车辆企业基础数据//ads_xinzhou_taix_base_info */
    public static final String ads_xinzhou_taix_base_info = "/api/100408/data.json?";

    /** 忻州市巡游车辆营运指标分析//ads_xinzhou_taix_runing_analysis_daily */
    public static final String ads_xinzhou_taix_runing_analysis_daily = "/api/100407/data.json?";

    /** 忻州市巡游车辆营运趋势折线图//ads_xinzhou_taix_runing_analysis_hour_zhe */
    public static final String ads_xinzhou_taix_runing_analysis_hour_zhe = "/api/100411/data.json?";

    /** 忻州市巡游车辆营运趋势柱状图//ads_xinzhou_taix_runing_analysis_hour_zhu */
    public static final String ads_xinzhou_taix_runing_analysis_hour_zhu = "/api/100412/data.json?";


    /**
     * 公交车专题
     */
    /*** 忻州公交车运营指标与运营里程信息//ads_xinzhou_bus_operation_index_and_mileage */
    public static final String ads_xinzhou_bus_operation_index_and_mileage = "/api/100416/data.json?";

    /*** 忻州公交车基础信息与线路整体指标//ads_xinzhou_bus_base_info */
    public static final String ads_xinzhou_bus_base_info = "/api/100417/data.json?";

    /*** 忻州公交车基础信息与线路整体指标-查询相同表，只是查询两个字段{区县,企业} */
    public static final String ads_xinzhou_bus_enterprise_select = "/api/100111/data.json?";

    /*** 忻州市公交线路排名//ads_xinzhou_bus_road_route_rank */
    public static final String ads_xinzhou_bus_road_route_rank = "/api/100414/data.json?";

    /*** 忻州公交车运营折线图//ads_xinzhou_bus_operation_line_chart */
    public static final String ads_xinzhou_bus_operation_line_chart = "/api/100418/data.json?";

    /*** 忻州公交车运营柱状图//ads_xinzhou_bus_operation_bar_chart */
    public static final String ads_xinzhou_bus_operation_bar_chart = "/api/100419/data.json?";

    /*** 忻州公交车线路指标分析-按小时//ads_xinzhou_bus_road_index_analysis_hour */
    public static final String ads_xinzhou_bus_road_index_analysis_hour = "/api/100420/data.json?";

    /*** 忻州公交车线路指标分析-按天//ads_xinzhou_bus_road_index_analysis_day */
    public static final String ads_xinzhou_bus_road_index_analysis_day = "/api/100421/data.json?";

    /*** 忻州公交车线路指标分析-按天-线路名称//ads_xinzhou_bus_road_index_analysis_day,查询相同的表,只是查询一个字段{线路名称} */
    public static final String ads_xinzhou_bus_road_index_analysis_day_xlmc = "/api/100442/data.json?";

    /*** 忻州公交车热力图分布//ads_xinzhou_bus_heat_map_distribution */
    public static final String ads_xinzhou_bus_heat_map_distribution = "/api/100422/data.json?";

    /**
     * 城际客运专题
     */
    /** 忻州市城际客运报警事件分类按小时//ads_xinzhou_intercity_alarm_hour  */
    public static final String ads_xinzhou_intercity_alarm_hour = "/api/100413/data.json?";

    /** 忻州市城际客运基础数据//ads_xinzhou_intercity_basic_data */
    public static final String ads_xinzhou_intercity_basic_data = "/api/100423/data.json?";

    /** 忻州市城际客运营运数据按日//ads_xinzhou_intercity_operation_daily */
    public static final String ads_xinzhou_intercity_operation_daily = "/api/100424/data.json?";

    /** 忻州市城际客运客运站客流量排名按日//ads_xinzhou_intercity_stn_flow_rank_daily */
    public static final String ads_xinzhou_intercity_stn_flow_rank_daily = "/api/100425/data.json?";

    /** 忻州市城际客运地图事故黑点热力图//ads_xz_intercity_acc_blackspot_heatmap */
    public static final String ads_xz_intercity_acc_blackspot_heatmap = "/api/100426/data.json?";

    /** 忻州市城际客运客流量趋势按小时//ads_xz_intercity_flow_trend_hour */
    public static final String ads_xz_intercity_flow_trend_hour = "/api/100427/data.json?";

    /** 忻州市城际客运地图车辆客流热力图//ads_xz_intercity_veh_flow_heatmap */
    public static final String ads_xz_intercity_veh_flow_heatmap = "/api/100428/data.json?";

    /** 忻州市城际客运地图车辆车速运力热力图//ads_xz_intercity_veh_speed_cap_heatmap */
    public static final String ads_xz_intercity_veh_speed_cap_heatmap = "/api/100429/data.json?";


    /***
     * 危货车运营专题
     */
    // 基础指标数据 忻州市危货车基础数据//ads_xinzhou_dangerous_truck_basic_data
    public static  final String ads_xinzhou_dangerous_truck_basic_data = "/api/100415/data.json?";

    // 运营指标数据 忻州市危货车运营数据按日//ads_xinzhou_hazardous_truck_daily_ops
    public static  final String ads_xinzhou_hazardous_truck_daily_ops = "/api/100431/data.json?";

    // 危货运输排名 忻州市危货运输量排名按日//ads_xinzhou_hazard_cargo_daily_rank
    public static final  String ads_xinzhou_hazard_cargo_daily_rank = "/api/100438/data.json?";

    // 运营效率指标 忻州市危货车运输量趋势按小时//ads_xinzhou_hazard_cargo_hour_trend
    public static final  String ads_xinzhou_hazard_cargo_hour_trend = "/api/100439/data.json?";

    // 报警事件 忻州市危货车报警违规事件指标按小时//ads_xinzhou_hazard_truck_alarm_hour
    public static final String ads_xinzhou_hazard_truck_alarm_hour = "/api/100444/data.json?";

//    危货车地图车辆车速定位热力图
    public static final String ads_xinzhou_hazard_truck_speed_heatmap = "/api/100455/data.json?";

//危货车地图运单目的地位置密度热力图
    public static final String ads_xinzhou_hazard_truck_wb_dest_heatmap = "/api/100456/data.json?";

//    危货车地图事故黑点热力图
    public static final String ads_xinzhou_hazard_truck_accident_heatmap = "/api/100457/data.json?";



    /**
     * 网约车专题
     */
    /** 忻州市网约营运基础数据//ads_xinzhou_net_taix_base_info */
    public static final String ads_xinzhou_net_taix_base_info = "/api/100432/data.json?";

    /** 忻州市网约车辆营运指标分析//ads_xinzhou_net_taix_runing_analysis_daily */
    public static final String ads_xinzhou_net_taix_runing_analysis_daily = "/api/100433/data.json?";

    /** 忻州市网约车辆营运趋势折线图//ads_xinzhou_net_taix_runing_analysis_hour_zhe */
    public static final String ads_xinzhou_net_taix_runing_analysis_hour_zhe = "/api/100434/data.json?";

    /** 忻州市网约车辆营运趋势柱状图//ads_xinzhou_net_taix_runing_analysis_hour_zhu */
    public static final String ads_xinzhou_net_taix_runing_analysis_hour_zhu = "/api/100435/data.json?";

    /** 忻州市网约车地图车辆车速运力热力图//ads_xinzhou_net_taix_hotmap_vandrunnum_analysis */
    public static final String ads_xinzhou_net_taix_hotmap_vandrunnum_analysis = "/api/100436/data.json?";

    /** 忻州市网约车地图车辆客流热力图//ads_xinzhou_net_taix_hotmap_kenum_analysis */
    public static final String ads_xinzhou_net_taix_hotmap_kenum_analysis = "/api/100440/data.json?";

    /**
     * 分析报告
     */
    /** 分析报告日报 */
    public static final String ads_xinzhou_notice_runing_info_daily = "/api/100464/data.json?";

    /** 分析报告月报 */
    public static final String ads_xinzhou_notice_runing_info_month = "/api/100465/data.json?";

    /** 分析报告季报 */
    public static final String ads_xinzhou_notice_runing_info_quarter = "/api/100466/data.json?";

    /** 分析报告年报 */
    public static final String ads_xinzhou_notice_runing_info_year = "/api/100467/data.json?";

    /** 忻州公交车营运分析日报//ads_bus_up_info_daily */
    public static final String ads_bus_up_info_daily = "/api/100510/data.json?";
    /** 忻州公交车营运分析月报//ads_bus_up_info_month */
    public static final String ads_bus_up_info_month = "/api/100511/data.json?";
    /** 公交车分析报告季报 */
    public static final String ads_bus_up_info_quarter = "/api/100513/data.json?";
    /** 忻州公交车营运分析年报//ads_bus_up_info_year */
    public static final String ads_bus_up_info_year = "/api/100512/data.json?";

}
