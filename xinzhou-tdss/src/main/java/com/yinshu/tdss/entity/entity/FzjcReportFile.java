package com.yinshu.tdss.entity.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("fzjc_report_file")
public class FzjcReportFile implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value="id")
    private Long id;

    /**
     * 文件类型(公交车、网约车、巡游车)
     */
    private Integer fileType;

    /**
     * 日期类型(日报、月报、季报、年报)
     */
    private Integer dateType;

    /**
     * 时间
     */
    private String fileDateTime;

    /**
     * 季度
     */
    private String quarter;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;


}
