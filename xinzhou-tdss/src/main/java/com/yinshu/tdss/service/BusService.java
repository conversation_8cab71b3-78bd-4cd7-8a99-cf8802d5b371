package com.yinshu.tdss.service;

import com.alibaba.fastjson.JSONObject;

public interface BusService {

    /**
     * 获取运营指标与运营里程信息
     * @param query
     * @return
     */
    JSONObject getBusOperationIndexAndMileage(JSONObject query);

    /**
     * 获取公交车基础信息与线路整体指标
     * @param query
     * @return
     */
    JSONObject getBusSpecialTopic(JSONObject query);

    /**
     * 获取忻州公交车线路排名
     * @param query
     * @return
     */
    JSONObject getBusRank(JSONObject query);

    /**
     * 获取公交车运营折线图
     * @param query
     * @return
     */
    JSONObject getBusOperationLineChart(JSONObject query);

    /**
     * 公交车运营柱状图
     * @param query
     * @return
     */
    JSONObject getBusOperationBarChart(JSONObject query);

    /**
     * 公交车线路指标分析-按小时
     * @param query
     * @return
     */
    JSONObject getBusOperationIndexAnalysisHour(JSONObject query);

    /**
     * 忻州公交车线路指标分析-按天
     * @param query
     * @return
     */
    JSONObject getBusOperationIndexAnalysisDay(JSONObject query);

    /**
     * 公交车热力图分布
     * @param query
     * @return
     */
    JSONObject getBusOperationHeatMapDistribution(JSONObject query);

    /**
     * 获取公交车所有线路名称
     * @param query
     * @return
     */
    JSONObject getBusLineName(JSONObject query);

    /**
     * 获取公交车所有区县企业
     * @param query
     * @return
     */
    JSONObject getBusDistrictsAndCompanies(JSONObject query);
}
