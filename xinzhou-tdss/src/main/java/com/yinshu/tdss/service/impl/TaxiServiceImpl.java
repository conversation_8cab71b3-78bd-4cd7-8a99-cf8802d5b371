package com.yinshu.tdss.service.impl;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.tdss.common.RemoteUrlConstants;
import com.yinshu.tdss.service.TaxiService;

/**
 * 出租车营运专题
 * <AUTHOR>
 *
 */
@Service
public class TaxiServiceImpl implements TaxiService {

    @Resource
    DvisualHttpTemplate template;

    /**
     * 获取营运速度
     * @param query
     * @return
     */
    public JSONObject getOperatingSpeed(JSONObject query) {
        query = getFilterByParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_taix_runing_analysis_daily, query);
        return post;
    }

    @Override
    public JSONObject vehicleFlow(JSONObject query) {
        String dt = query.getString("dt");
        String hour = query.getString("hour");
        if (StringUtils.isNotBlank(dt) && StringUtils.isNotBlank(hour)) {
            query.put("filter", String.format(" dt = '%s' and hour = '%s' ", dt,hour));
        } else if (StringUtils.isNotBlank(hour)) {
            query.put("filter", String.format(" hour = '%s' ", hour));
        } else if (StringUtils.isNotBlank(dt)) {
            query.put("filter", String.format(" dt = '%s' ", dt));
        }
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_taix_hotmap_kenum_analysis, query);
        return post;
    }

    @Override
    public JSONObject vehicleSpeed(JSONObject query) {
        String dt = query.getString("dt");
        String hour = query.getString("hour");
        if (StringUtils.isNotBlank(dt) && StringUtils.isNotBlank(hour)) {
            query.put("filter", String.format(" dt = '%s' and hour = '%s' ", dt,hour));
        } else if (StringUtils.isNotBlank(hour)) {
            query.put("filter", String.format(" hour = '%s' ", hour));
        } else if (StringUtils.isNotBlank(dt)) {
            query.put("filter", String.format(" dt = '%s' ", dt));
        }
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_taix_hotmap_vandrunnum_analysis, query);
        return post;
    }

    @Override
    public JSONObject basicData(JSONObject query) {
        String filter = "";
        if (query.getString("county") == null || query.getString("county").equals("") || query.getString("county").equals("全市"))  {
            filter += "";
        }else{
            filter += " and county = '"+query.getString("county")+"'";
        }
        if (query.getString("company") == null || query.getString("company").equals("") || query.getString("company").equals("全部企业"))  {
            filter += "";
        }else{
            filter += " and id = '"+query.getString("company")+"'";
        }

        if (!ObjectUtil.isEmpty(filter)) {
            filter = filter.replaceFirst("and", "").trim();
            query.put("filter", filter);
        }
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_taix_base_info, query);
        return post;
    }

    @Override
    public JSONObject operatingTrendLine(JSONObject query) {
        query = getFilterByParam(query);
        String filter = query.getString("filter");
        if (query.getString("startTime").equals(query.getString("endTime"))) {
            if (StringUtils.isNotBlank(filter)) {
                filter += " and hour != null ";
            }
        }else {
            if (StringUtils.isNotBlank(filter)) {
                filter += " and hour = null ";
            }
        }
        query.put("filter", filter);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_taix_runing_analysis_hour_zhe, query);
        return post;
    }

    @Override
    public JSONObject operatingTrendBar(JSONObject query) {
        query = getFilterByParam(query);
        String filter = query.getString("filter");
        if (query.getString("startTime").equals(query.getString("endTime"))) {
            if (StringUtils.isNotBlank(filter)) {
                filter += " and hour != null ";
            }
        }else {
            if (StringUtils.isNotBlank(filter)) {
                filter += " and hour = null ";
            }
        }
        query.put("filter", filter);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_taix_runing_analysis_hour_zhu, query);
        return post;
    }

    private static  JSONObject getFilterByParam(JSONObject query) {
        String filter = "";
        // 处理county条件
        if (query.getString("county") == null ||
                query.getString("county").equals("") ||
                query.getString("county").equals("全市")) {
            // 当county为空时，如果company不为空，则添加county != null
            if (!(query.getString("company") == null ||
                    query.getString("company").equals("") ||
                    query.getString("company").equals("全部企业"))) {
                filter += " and county != null ";
            } else {
                filter += " and county = null ";
            }
        } else {
            filter += " and county = '" + query.getString("county") + "'";
        }

        // 处理company条件
        if (query.getString("company") == null ||
                query.getString("company").equals("") ||
                query.getString("company").equals("全部企业")) {
            filter += " and company = null ";
        } else {
            filter += " and company = '" + query.getString("company") + "'";
        }


        if (query.getString("startTime") == null )  {
            filter += "";
        }else{
            filter += " and dt >= '"+ query.getString("startTime")+"'";
        }

        if (query.getString("endTime") == null )  {
            filter += "";
        }else{
            filter += " and dt <= '"+ query.getString("endTime")+"'";
        }

        if (!ObjectUtil.isEmpty(filter)) {
            filter = filter.replaceFirst("and", "").trim();
            query.put("filter", filter);
        }
        return query;
    }

}
