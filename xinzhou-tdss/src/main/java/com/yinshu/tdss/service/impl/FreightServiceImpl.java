package com.yinshu.tdss.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.tdss.common.RemoteUrlConstants;
import com.yinshu.tdss.common.RequestFilterBuilder;
import com.yinshu.tdss.entity.vo.QueryBaseDataParam;
import com.yinshu.tdss.service.FreightService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/***
 * 危货车营运专题接口实现
 * <AUTHOR>
 */
@Service
public class FreightServiceImpl implements FreightService {
    @Resource
    DvisualHttpTemplate template;

    /***
     * 获取基础指标
     * @param query
     * @return
     */
    @Override
    public JSONObject getBasicData(QueryBaseDataParam query) {
        if (ObjectUtil.isEmpty(query)) {
            return null;
        }
        JSONObject param = new JSONObject();
        if (query.getS() != null){
            param.put("s",query.getS());
        }
        if (query.getS() != null){
            param.put("n",query.getN());
        }
        String filter = "";
        /// 第一种情况，啥也没传，就直接传全部企业以及全市
        if ((query.getCompany() == null||query.getCompany().equals("全部企业"))&& (query.getCounty() == null || query.getCounty().equals("全市")))  {
            filter += "and id = '全部企业' and county = '全市'";
        }else{
            ///  要么直接查询全部企业，要么之需要查单独企业，不用拼接全市了
            if (query.getCompany() != null) {
                filter += " and id = '"+query.getCompany()+"'";
            }
            if (query.getCounty() != null && !query.getCounty().equals("全市"))  {
                filter += " and county = '"+query.getCounty()+"'";
                /// 面临一种情况。 例如 传递的是  忻府区 ，全部企业_忻府区 或者 忻府区 ，全部企业 那么就需要统一成 忻府区 ，全部企业_忻府区
                filter = filter.replaceFirst("全部企业", "全部企业_"+query.getCounty()).trim();
                filter = filter.replaceFirst("全部企业_"+query.getCounty()+"_"+query.getCounty(), "全部企业_"+query.getCounty()).trim();
            }
        }





        if (!ObjectUtil.isEmpty(filter)) {
            filter = filter.replaceFirst("and", "").trim();
            param.put("filter", filter);
        }


        System.out.println("param："+param);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_dangerous_truck_basic_data, param);
        return post;
    }

    @Override
    public JSONObject getCompanyData(QueryBaseDataParam query) {
        if (ObjectUtil.isEmpty(query)) {
            return null;
        }
        JSONObject param = new JSONObject();

        if (query.getS() != null){
            param.put("s",query.getS());
        }
        if (query.getS() != null){
            param.put("n",query.getN());
        }

        String filter = "";
        if (query.getCompany() != null) {
            filter += " and id = '"+query.getCompany()+"'";
        }
        if (query.getCounty() == null || query.getCounty().equals("全市"))  {
            filter = "";
        }else{
            filter += " and county = '"+query.getCounty()+"'";
            filter = filter.replaceFirst("全部企业", "全部企业_"+query.getCounty()).trim();
        }
        if (query.getStartTime() == null )  {
            filter += "";
        }else{
            filter += " and dt >= '"+query.getStartTime()+"'";
        }

        if (query.getEndTime() == null )  {
            filter += "";
        }else{
            filter += " and dt <= '"+query.getEndTime()+"'";
        }
        if (!ObjectUtil.isEmpty(filter)) {
            filter = filter.replaceFirst("and", "").trim();
            param.put("filter", filter);
        }
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_dangerous_truck_basic_data, param);
        return post;
    }

    /***
     * 获取运营指标
     * @param query
     * @return
     */
    @Override
    public Map<String, Object> getDailyOpsData(QueryBaseDataParam query) {
        JSONObject param=paramValue(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_hazardous_truck_daily_ops, param);
        if (post == null || !post.containsKey("list")) {
            return post; // 原样返回错误信息或空数据
        }

        Object rawDataInfo = JSONObject.parseObject(JSON.toJSONString(post.get("list")), List.class)
                .stream()
                .findFirst()
                .orElse(null);

        Map<String, Object> dataMap = null;
        if (rawDataInfo instanceof Map<?, ?>) {
            // 情况1：rawDataInfo 本身是 Map
            dataMap = new HashMap<>();
            for (Map.Entry<?, ?> entry : ((Map<?, ?>) rawDataInfo).entrySet()) {
                if (entry.getKey() instanceof String) {
                    dataMap.put((String) entry.getKey(), entry.getValue());
                }
            }
        } else if (rawDataInfo != null) {
            // 情况2：rawDataInfo 是其他类型（如 JSON 对象），转换为 JSON 字符串再解析
            String jsonString = JSON.toJSONString(rawDataInfo);
            dataMap = JSON.parseObject(jsonString, Map.class);
        }

        if (dataMap != null) {
            // 营运车辆
            Integer operatingVehicles = (Integer) dataMap.get("operating_vehicles");
            // 上线车辆
            Integer onlineVehicles = (Integer) dataMap.get("online_vehicles");
            // 出车率的计算。出车率=营运车辆/上线车辆
            // 出车率计算（使用浮点数运算）
            double vehicleDispatchRate = 0.0;
            if (onlineVehicles != null && onlineVehicles > 0 && operatingVehicles != null) {
                vehicleDispatchRate = ((double) operatingVehicles / onlineVehicles) * 100;
            }
            // 格式化结果为两位小数
            String formattedRate = String.format("%.2f", vehicleDispatchRate);
            // 添加出车率（保存为字符串或 BigDecimal 以避免精度丢失）
            dataMap.put("vehicleDispatchRate", formattedRate);
        }

        return dataMap;
    }

    @Override
    public JSONObject getTruckDailyOps(QueryBaseDataParam query) {
        JSONObject param = new JSONObject();
        if (query.getS() != null){
            param.put("s",query.getS());
        }
        if (query.getS() != null){
            param.put("n",query.getN());
        }
        String filter = "";
        if (query.getCompany() == null) {
            filter += " and company = '全部企业'";
        }else{
            filter += " and company = '"+query.getCompany()+"'";
        }
        if (query.getCounty() == null)  {
            filter += " and county = '全市'";
        }else{
            filter += " and county = '"+query.getCounty()+"'";
        }
        if (query.getStartTime() == null )  {
            filter += "";
        }else{
            filter += " and dt >= '"+query.getStartTime()+"'";
        }

        if (query.getEndTime() == null )  {
            filter += "";
        }else{
            filter += " and dt <= '"+query.getEndTime()+"'";
        }

        if (!ObjectUtil.isEmpty(filter)) {
            filter = filter.replaceFirst("and", "").trim();
            param.put("filter", filter);
        }
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_hazard_cargo_daily_rank, param);

        return post;
    }


    @Override
    public JSONObject getCargoHourTrend(QueryBaseDataParam query) {
        JSONObject param=paramValue(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_hazard_cargo_hour_trend, param);
        return post;
    }

    @Override
    public JSONObject getTrendTransVolume(QueryBaseDataParam query) {
        JSONObject param=paramValue(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_hazard_cargo_hour_trend, param);
        return post;
    }


    @Override
    public JSONObject getTruckAlarmHour(QueryBaseDataParam query) {
        JSONObject param=paramValue(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_hazard_truck_alarm_hour, param);
        return post;
    }

    @Override
    public JSONObject getTrendAlarmEvents(QueryBaseDataParam query) {
        JSONObject param=paramValue(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_hazard_truck_alarm_hour, param);
        return post;
    }


    private JSONObject paramValue(QueryBaseDataParam query){
        JSONObject param = new JSONObject();
        if (query.getS() != null){
            param.put("s",query.getS());
        }
        if (query.getS() != null){
            param.put("n",query.getN());
        }
        String filter = "";
        if (query.getCompany() == null) {
            filter += " and company = '全部企业'";
        }else{
            filter += " and company = '"+query.getCompany()+"'";
        }
        if (query.getCounty() == null)  {
            filter += " and county = '全市'";
        }else{
            filter += " and county = '"+query.getCounty()+"'";
        }
        if (query.getStartTime() == null )  {
            filter += "";
        }else{
            filter += " and dt >= '"+query.getStartTime()+"'";
        }

        if (query.getEndTime() == null )  {
            filter += "";
        }else{
            filter += " and dt <= '"+query.getEndTime()+"'";
        }
        if (ObjectUtil.isNotEmpty(query.getStartTime())&&ObjectUtil.isNotEmpty(query.getEndTime())) {
            if (query.getStartTime().equals(query.getEndTime())) {
                if (StringUtils.isNotBlank(filter)) {
                    filter += " and hour != null ";
                }
            }else {
                if (StringUtils.isNotBlank(filter)) {
                    filter += " and hour = null ";
                }
            }
        }

        if (!ObjectUtil.isEmpty(filter)) {
            filter = filter.replaceFirst("and", "").trim();
            param.put("filter", filter);
        }
        return param;
    }


    @Override
    public JSONObject getTruckSpeedHeatMap(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_hazard_truck_speed_heatmap, query);
        return post;
    }

    @Override
    public JSONObject getTruckWayBillDestHeatMap(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_hazard_truck_wb_dest_heatmap, query);
        return post;
    }

    @Override
    public JSONObject getTruckAccidentHeatMap(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_hazard_truck_accident_heatmap, query);
        return post;
    }
}
