package com.yinshu.tdss.service;

import com.alibaba.fastjson.JSONObject;

/**
 * 城际客运营运专题
 * <AUTHOR>
 *
 */
public interface TransportService {


	JSONObject classifyOfAlarmEvents(JSONObject query);

	JSONObject classifyOfAlarmTrend(JSONObject query);

	JSONObject basicData(JSONObject query);

	JSONObject dataByDay(JSONObject query);

	JSONObject trafficRanking(JSONObject query);

	JSONObject blackSpot(JSONObject query);

	JSONObject trafficTrends(JSONObject query);

	JSONObject vehicleFlow(JSONObject query);

	JSONObject vehicleSpeed(JSONObject query);

	JSONObject company(JSONObject query);


}
