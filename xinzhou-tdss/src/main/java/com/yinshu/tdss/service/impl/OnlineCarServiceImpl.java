package com.yinshu.tdss.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.tdss.common.RemoteUrlConstants;
import com.yinshu.tdss.common.RequestFilterBuilder;
import com.yinshu.tdss.service.OnlineCarService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class OnlineCarServiceImpl implements OnlineCarService {
    @Resource
    DvisualHttpTemplate template;

    /**
     * 获取网约营运基础数据
     *
     * @param query
     * @return
     */
    @Override
    public JSONObject getBasicData(JSONObject query) {
//        这个表没有时间,并且企业是id不是county
        query.remove("startDate");
        query.remove("endDate");
        if (query.get("company") != null && query.getString("company").equals("全部企业")) {
            query.remove("company");
        }
        if (query.containsKey("company") && query.get("company") != null) {
            query.put("id", query.get("company"));
            query.remove("company");
        }
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_net_taix_base_info, query);
        return post;
    }

    /**
     * 获取网约车辆营运指标分析
     *
     * @param query
     * @return
     */
    @Override
    public JSONObject getOnlineCarOperationIndexAnalysis(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_net_taix_runing_analysis_daily, query);
        return post;
    }

    @Override
    public JSONObject getOnlineCarZxt(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_net_taix_runing_analysis_hour_zhe, query);
        return post;
    }

    @Override
    public JSONObject getOnlineCarZzt(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_net_taix_runing_analysis_hour_zhu, query);
        return post;
    }

    @Override
    public JSONObject getOnlineCarMap(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_net_taix_hotmap_vandrunnum_analysis, query);
        return post;
    }

    @Override
    public JSONObject getOnlineCarKlMap(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_net_taix_hotmap_kenum_analysis, query);
        return post;
    }
}
