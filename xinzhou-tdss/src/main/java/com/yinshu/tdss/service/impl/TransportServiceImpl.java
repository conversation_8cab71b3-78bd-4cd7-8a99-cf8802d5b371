package com.yinshu.tdss.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.tdss.common.RemoteUrlConstants;
import com.yinshu.tdss.service.TransportService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 城际客运营运专题
 * <AUTHOR>
 *
 */
@Service
public class TransportServiceImpl implements TransportService {

    @Resource
    DvisualHttpTemplate template;

    @Override
    public JSONObject classifyOfAlarmEvents(JSONObject query) {
        query = getFilterByParam(query);
        String filter = query.getString("filter");
        if (StringUtils.isNotEmpty(filter)) {
            filter += " and hour = null";
        }else {
            filter = "hour = null";
        }
        query.put("filter", filter);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_intercity_alarm_hour, query);
        return post;
    }

    @Override
    public JSONObject classifyOfAlarmTrend(JSONObject query) {
        query = getFilterByParam(query);
        String filter = query.getString("filter");
        if (query.getString("startTime").equals(query.getString("endTime"))) {
            if (StringUtils.isNotBlank(filter)) {
                filter += " and hour != null ";
            }
        }else {
            if (StringUtils.isNotBlank(filter)) {
                filter += " and hour = null ";
            }
        }
        query.put("filter", filter);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_intercity_alarm_hour, query);
        return post;
    }


    @Override
    public JSONObject basicData(JSONObject query) {
        String filter = "";
        // 处理county条件
        if (query.getString("county") == null ||
                query.getString("county").equals("") ||
                query.getString("county").equals("全市")) {
            // 当county为空时，如果company不为空，则添加county != null
            if (!(query.getString("company") == null ||
                    query.getString("company").equals("") ||
                    query.getString("company").equals("全部企业"))) {
                filter += " and county != null ";
            } else {
                filter += " and county = null ";
            }
        } else {
            filter += " and county = '" + query.getString("county") + "'";
        }

        // 处理company条件
        if (query.getString("company") == null ||
                query.getString("company").equals("") ||
                query.getString("company").equals("全部企业")) {
            filter += " and company = null ";
        } else {
            filter += " and company = '" + query.getString("company") + "'";
        }
        if (!ObjectUtil.isEmpty(filter)) {
            filter = filter.replaceFirst("and", "").trim();
            query.put("filter", filter);
        }
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_intercity_basic_data, query);
        return post;
    }

    @Override
    public JSONObject dataByDay(JSONObject query) {
        query = getFilterByParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_intercity_operation_daily, query);
        return post;
    }

    @Override
    public JSONObject trafficRanking(JSONObject query) {
        query = getFilterByParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_intercity_stn_flow_rank_daily, query);
        return post;
    }

    @Override
    public JSONObject blackSpot(JSONObject query) {
        String dt = query.getString("dt");
        String hour = query.getString("hour");
        if (StringUtils.isNotBlank(dt) && StringUtils.isNotBlank(hour)) {
            query.put("filter", String.format(" dt = '%s' and hour = '%s' ", dt,hour));
        } else if (StringUtils.isNotBlank(hour)) {
            query.put("filter", String.format(" hour = '%s' ", hour));
        } else if (StringUtils.isNotBlank(dt)) {
            query.put("filter", String.format(" dt = '%s' ", dt));
        }
        JSONObject post = template.post(RemoteUrlConstants.ads_xz_intercity_acc_blackspot_heatmap, query);
        return post;
    }

    @Override
    public JSONObject trafficTrends(JSONObject query) {
        query = getFilterByParam(query);
        String filter = query.getString("filter");
        if (query.getString("startTime").equals(query.getString("endTime"))) {
            if (StringUtils.isNotBlank(filter)) {
                filter += " and hour != null ";
            }
        }else {
            if (StringUtils.isNotBlank(filter)) {
                filter += " and hour = null ";
            }
        }
        query.put("filter", filter);
        JSONObject post = template.post(RemoteUrlConstants.ads_xz_intercity_flow_trend_hour, query);
        return post;
    }



    @Override
    public JSONObject vehicleFlow(JSONObject query) {
        String dt = query.getString("dt");
        String hour = query.getString("hour");
        if (StringUtils.isNotBlank(dt) && StringUtils.isNotBlank(hour)) {
            query.put("filter", String.format(" dt = '%s' and hour = '%s' ", dt,hour));
        } else if (StringUtils.isNotBlank(hour)) {
            query.put("filter", String.format(" hour = '%s' ", hour));
        } else if (StringUtils.isNotBlank(dt)) {
            query.put("filter", String.format(" dt = '%s' ", dt));
        }
        JSONObject post = template.post(RemoteUrlConstants.ads_xz_intercity_veh_flow_heatmap, query);
        return post;
    }

    @Override
    public JSONObject vehicleSpeed(JSONObject query) {
        String dt = query.getString("dt");
        String hour = query.getString("hour");
        if (StringUtils.isNotBlank(dt) && StringUtils.isNotBlank(hour)) {
            query.put("filter", String.format(" dt = '%s' and hour = '%s' ", dt,hour));
        } else if (StringUtils.isNotBlank(hour)) {
            query.put("filter", String.format(" hour = '%s' ", hour));
        } else if (StringUtils.isNotBlank(dt)) {
            query.put("filter", String.format(" dt = '%s' ", dt));
        }
        JSONObject post = template.post(RemoteUrlConstants.ads_xz_intercity_veh_speed_cap_heatmap, query);
        return post;
    }

    /**
     * 全部企业，还是基础数据但是过滤
     * @param query
     * @return
     */
    @Override
    public JSONObject company(JSONObject query) {

        String filter = "";
        if (query.getString("company") != null) {
            filter += " and id = '"+query.getString("company")+"'";
        }
        if (query.getString("county") == null || query.getString("county").equals("全市"))  {
            filter = "";
        }else{
            filter += " and county = '"+query.getString("county")+"'";
            //filter = filter.replaceFirst("全部企业", "全部企业_"+query.getCounty()).trim();
        }
        filter += "and (company != '' and company != null) ";
        if (!ObjectUtil.isEmpty(filter)) {
            filter = filter.replaceFirst("and", "").trim();
            query.put("filter", filter);
        }

        //query.put("filter", " company ！= '' or company != null ");
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_intercity_basic_data, query);
        return post;
    }

    private static  JSONObject getFilterByParam(JSONObject query) {
        String filter = "";
        // 处理county条件
        if (query.getString("county") == null ||
                query.getString("county").equals("") ||
                query.getString("county").equals("全市")) {
            // 当county为空时，如果company不为空，则添加county != null
            if (!(query.getString("company") == null ||
                    query.getString("company").equals("") ||
                    query.getString("company").equals("全部企业"))) {
                filter += " and county != null ";
            } else {
                filter += " and county = null ";
            }
        } else {
            filter += " and county = '" + query.getString("county") + "'";
        }

        // 处理company条件
        if (query.getString("company") == null ||
                query.getString("company").equals("") ||
                query.getString("company").equals("全部企业")) {
            filter += " and company = null ";
        } else {
            filter += " and company = '" + query.getString("company") + "'";
        }

        if (query.getString("startTime") == null )  {
            filter += "";
        }else{
            filter += " and dt >= '"+ query.getString("startTime")+"'";
        }

        if (query.getString("endTime") == null )  {
            filter += "";
        }else{
            filter += " and dt <= '"+ query.getString("endTime")+"'";
        }

        if (!ObjectUtil.isEmpty(filter)) {
            filter = filter.replaceFirst("and", "").trim();
            query.put("filter", filter);
        }
        return query;
    }


}
