package com.yinshu.tdss.service;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.tdss.entity.vo.QueryBaseDataParam;

import java.util.Map;

/***
 * 危货车营运专题接口
 * <AUTHOR>
 */
public interface FreightService {

    /***
     * 获取基础指标数据
     * @param query
     * @return
     */
    JSONObject getBasicData(QueryBaseDataParam query);

    /****
     * 获取企业数据
     * @param query
     * @return
     */
    JSONObject getCompanyData(QueryBaseDataParam query);

    /***
     * 获取运营指标数据
     * @param query
     * @return
     */
    Map<String, Object> getDailyOpsData(QueryBaseDataParam query);

    /***
     * 获取运输量排名
     * @param query
     * @return
     */
    JSONObject getTruckDailyOps(QueryBaseDataParam query);

    /***
     * 运营效率指标
     * @param query
     * @return
     */
    JSONObject getCargoHourTrend(QueryBaseDataParam query);

    /****
     * 危货运输量趋势
     * @param query
     * @return
     */
    JSONObject getTrendTransVolume(QueryBaseDataParam query);

    /***
     * 报警事件分类
     * @param query
     * @return
     */
    JSONObject getTruckAlarmHour(QueryBaseDataParam query);

    /***
     * 报警事件趋势
     * @param query
     * @return
     */
    JSONObject getTrendAlarmEvents(QueryBaseDataParam query);

    JSONObject getTruckSpeedHeatMap(JSONObject jsonObject);

    JSONObject getTruckWayBillDestHeatMap(JSONObject jsonObject);

    JSONObject getTruckAccidentHeatMap(JSONObject jsonObject);
}
