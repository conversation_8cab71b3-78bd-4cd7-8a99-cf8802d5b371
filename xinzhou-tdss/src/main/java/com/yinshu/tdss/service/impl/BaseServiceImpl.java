package com.yinshu.tdss.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.tdss.common.RemoteUrlConstants;
import com.yinshu.tdss.service.BaseService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class BaseServiceImpl implements BaseService {

    @Resource
    DvisualHttpTemplate template;

    @Override
    public JSONObject getCountyData(JSONObject query) {
        JSONObject post = template.post(RemoteUrlConstants.dim_xinzhou_county, query);
        return post;
    }
}
