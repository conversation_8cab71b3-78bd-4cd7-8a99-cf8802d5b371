package com.yinshu.tdss.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.deepoove.poi.XWPFTemplate;
import com.yinshu.sys.config.LocalStorageConfig;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.tdss.common.RemoteUrlConstants;
import com.yinshu.tdss.common.RequestFilterBuilder;
import com.yinshu.tdss.entity.dto.ReportParamDTO;
import com.yinshu.tdss.entity.entity.FzjcReportFile;
import com.yinshu.tdss.service.ReportService;
import com.yinshu.tdss.utils.WordToPdfConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 出租车营运专题
 *
 * <AUTHOR>
 */
@Service
public class ReportServiceImpl implements ReportService {

    @Resource
    DvisualHttpTemplate template;
    @Resource
    private LocalStorageConfig localStorageConfig;

    private static final Logger logger = LoggerFactory.getLogger(WordToPdfConverter.class.getName());

    @Override
    public void cruiseReport(ReportParamDTO reportParamDTO) {
        ClassLoader classLoader = getClass().getClassLoader();
        URL resourceUrl = classLoader.getResource("wordTemplate/CruiseReport.docx");
        if (resourceUrl != null) {
            File resourceFile = new File(resourceUrl.getPath());
            System.out.println("===========模板文件========");
            System.out.println("resourceFile.getAbsoluteFile() = " + resourceFile.getAbsoluteFile());
            System.out.println("resourceFile.exists() = " + resourceFile.exists());
            // 现在可以使用resourceFile进行操作
            try {
                HashMap<String, Object> hashMap = new HashMap<>();

                //hashMap = buildMap(reportParamDTO)

                XWPFTemplate template = XWPFTemplate.compile(resourceFile.getAbsolutePath()).render(hashMap);


                File tempFile = File.createTempFile("CruiseReport" + System.currentTimeMillis(), ".docx");
                System.out.println("===========生成文件========");
                System.out.println("tempFile.getAbsoluteFile() = " + tempFile.getAbsoluteFile());
                template.writeAndClose(new FileOutputStream(tempFile.getAbsoluteFile()));

            } catch (Exception e) {
                e.printStackTrace();
                throw new RuntimeException("文件生成失败，请联系管理员");
            }
        } else {
            throw new RuntimeException("文件生成失败，请联系管理员");
        }
    }

    @Override
    public HashMap<String, Object> downloadReport(FzjcReportFile fzjcReportFile) {

        HashMap<String, Object> resultMap = new HashMap<>();

        String templatePath = "";
        if (fzjcReportFile.getFileType() == 1) {
            templatePath = "wordTemplate/CruiseReport.docx";
        } else if (fzjcReportFile.getFileType() == 2) {
            templatePath = "wordTemplate/NetCarReport.docx";
        } else {
            templatePath = "wordTemplate/BusReport.docx";
        }

        // 使用类路径加载资源流
        try (InputStream templateStream = getClass().getClassLoader().getResourceAsStream(templatePath)) {
            if (templateStream == null) {
                throw new RuntimeException("模板文件不存在，请检查路径：" + templatePath);
            }

            HashMap<String, Object> hashMap = queryData(fzjcReportFile);

            // 处理Q1,Q2,Q3,Q4
            if (fzjcReportFile.getFileDateTime().contains("Q1")) {
                fzjcReportFile.setFileDateTime(fzjcReportFile.getFileDateTime().replace("Q1", "第一季度"));
            } else if (fzjcReportFile.getFileDateTime().contains("Q2")) {
                fzjcReportFile.setFileDateTime(fzjcReportFile.getFileDateTime().replace("Q2", "第二季度"));
            }else if (fzjcReportFile.getFileDateTime().contains("Q3")) {
                fzjcReportFile.setFileDateTime(fzjcReportFile.getFileDateTime().replace("Q3", "第三季度"));
            }else {
                fzjcReportFile.setFileDateTime(fzjcReportFile.getFileDateTime().replace("Q4", "第四季度"));
            }
            if (hashMap.isEmpty()){
                resultMap.put("msg",buildFileType(fzjcReportFile.getFileType())+"营运分析报告，"+fzjcReportFile.getFileDateTime()+"无数据");
                return resultMap;
            }
            hashMap.put("reportType", buildDataType(fzjcReportFile.getDateType()));
            hashMap.put("reportTime", fzjcReportFile.getFileDateTime());

            // 编译模板并渲染数据
            XWPFTemplate template = XWPFTemplate.compile(templateStream).render(hashMap);

            // 创建临时文件
            String prefix = "temp";
            if (fzjcReportFile.getFileType() == 1) {
                prefix = "忻州市巡游出租车营运分析报告";
            } else if (fzjcReportFile.getFileType() == 2) {
                prefix = "忻州市网约出租车营运分析报告";
            }
            File tempFile = File.createTempFile(prefix + System.currentTimeMillis(), ".docx");

            logger.info("===========生成文件========");
            logger.info("word文件路径tempFile.getAbsoluteFile() =  + tempFile.getAbsoluteFile()");

            // 写入文件
            template.writeAndClose(new FileOutputStream(tempFile.getAbsoluteFile()));

            // word转pdf
            File pdfTempFile = File.createTempFile(prefix + System.currentTimeMillis(), ".pdf");
            InputStream inputStream = new FileInputStream(new File(tempFile.getAbsolutePath()));
            WordToPdfConverter.wordToPdf(inputStream, new File(pdfTempFile.getAbsolutePath()));
            logger.info("pdf文件路径pdfTempFile.getAbsolutePath() = " + pdfTempFile.getAbsolutePath());


            String pdfPath = localStorageConfig.putObject(new FileInputStream(new File(pdfTempFile.getAbsolutePath())), pdfTempFile.getName());
            resultMap.put("pdfPath", pdfPath);

            // word 转 图片
            File imageFile = File.createTempFile("temp", ".png");
            WordToPdfConverter.pdfToImagesByStream(new FileInputStream(new File(pdfTempFile.getAbsolutePath())), imageFile, false);
            String imagePath = localStorageConfig.putObject(new FileInputStream(new File(imageFile.getAbsolutePath())), imageFile.getName());
            resultMap.put("imagePath", imagePath);

            tempFile.deleteOnExit();
            pdfTempFile.deleteOnExit();
            imageFile.deleteOnExit();

            return resultMap;


        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("文件生成失败，请联系管理员");
        }
    }

    private HashMap<String, Object> queryData(FzjcReportFile fzjcReportFile) {
        JSONObject query = new JSONObject();
        query.put("type", buildFileType(fzjcReportFile.getFileType()));
        JSONObject result = new JSONObject();
        if (fzjcReportFile.getDateType() == 1) {
            query.put("dt", formattedDate(fzjcReportFile.getFileDateTime()));
            if (fzjcReportFile.getFileType() == 1 || fzjcReportFile.getFileType() == 2) {
                result = postData(RemoteUrlConstants.ads_xinzhou_notice_runing_info_daily, query);
            }else {
                query.remove("type");
                result = postData(RemoteUrlConstants.ads_bus_up_info_daily,query);
            }

        } else if (fzjcReportFile.getDateType() == 2) {
            if (StringUtils.isBlank(fzjcReportFile.getFileDateTime())) {
                LocalDate localDate = LocalDate.now();
                fzjcReportFile.setFileDateTime(localDate.format(DateTimeFormatter.ofPattern("yyyy年MM月")));
            }
            String[] split = fzjcReportFile.getFileDateTime().split("年");
            query.put("year", split[0]);
            String s = split[1].substring(0, split[1].length() - 1); // 去掉“月”字
            int month = Integer.parseInt(s);
            String formattedMonth = String.format("%02d", month); // 补零处理
            query.put("month", formattedMonth);
            if (fzjcReportFile.getFileType() == 1 || fzjcReportFile.getFileType() == 2) {
                result = postData(RemoteUrlConstants.ads_xinzhou_notice_runing_info_month, query);
            }else {
                query.remove("type");
                result = postData(RemoteUrlConstants.ads_bus_up_info_month, query);
            }

        } else if (fzjcReportFile.getDateType() == 3) {
            query.put("quarter ", "0" + fzjcReportFile.getFileDateTime().split("Q")[1]);
            query.put("year  ", fzjcReportFile.getFileDateTime().split("年")[0]);
            if (fzjcReportFile.getFileType() == 1 || fzjcReportFile.getFileType() == 2) {
                result = postData(RemoteUrlConstants.ads_xinzhou_notice_runing_info_quarter, query);
            }else {
                query.remove("type");
                result = postData(RemoteUrlConstants.ads_bus_up_info_quarter, query);
            }

        } else {
            query.put("year", fzjcReportFile.getFileDateTime().substring(0, 4));
            if (fzjcReportFile.getFileType() == 1 || fzjcReportFile.getFileType() == 2) {
                result = postData(RemoteUrlConstants.ads_xinzhou_notice_runing_info_year, query);
            }else {
                query.remove("type");
                result = postData(RemoteUrlConstants.ads_bus_up_info_year, query);
            }

        }

        return buildMap(fzjcReportFile.getFileType(), result);
    }

    private JSONObject postData(String url, JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        String filter = query.get("filter").toString();
        query.put("filter", filter + " limit 1");
        return template.post(url, query);
    }

    private HashMap<String, Object> buildMap(Integer type, JSONObject result) {
        HashMap<String, Object> hashMap = new HashMap<>();
        // 获取 list 数组
        JSONArray list = result.getJSONArray("list");
        if (list != null && !list.isEmpty()) {
            // 取出第一个对象
            JSONObject firstItem = list.getJSONObject(0);

            // 遍历第一个对象的键值对，放入 hashMap
            for (Map.Entry<String, Object> entry : firstItem.entrySet()) {
                hashMap.put(entry.getKey(), entry.getValue());
            }
        }
        if (type == 1) {

        } else if (type == 2) {

        } else {

        }
        return hashMap;
    }

    private String buildFileType(Integer fileType) {
        if (fileType == 1) {
            return "巡游车";
        } else if (fileType == 2) {
            return "网约车";
        } else {
            return "公交车";
        }
    }

    private String buildDataType(Integer dateType) {
        if (dateType == 1) {
            return "日报";
        } else if (dateType == 2) {
            return "月报";
        } else if (dateType == 3) {
            return "季报";
        } else {
            return "年报";
        }
    }

    private String formattedDate(String inputDate){
        // 定义输入和输出的日期格式
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy年M月d日");
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

// 解析并格式化日期
        LocalDate date = LocalDate.parse(inputDate, inputFormatter);
        return date.format(outputFormatter);
    }
}
