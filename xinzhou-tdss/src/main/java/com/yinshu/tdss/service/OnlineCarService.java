package com.yinshu.tdss.service;

import com.alibaba.fastjson.JSONObject;

/**
 * 网约车营运专题
 */
public interface OnlineCarService {
    /**
     * 获取网约营运基础数据
     * @param query
     * @return
     */
    JSONObject getBasicData(JSONObject query);

    /**
     * 获取网约车辆营运指标分析
     * @param query
     * @return
     */
    JSONObject getOnlineCarOperationIndexAnalysis(JSONObject query);

    /**
     * 获取网约车辆营运趋势折线图
     * @param query
     * @return
     */
    JSONObject getOnlineCarZxt(JSONObject query);

    /**
     * 获取网约车辆营运趋势柱状图
     * @param query
     * @return
     */
    JSONObject getOnlineCarZzt(JSONObject query);

    /**
     * 获取网约车地图车辆车速运力热力图
     * @param query
     * @return
     */
    JSONObject getOnlineCarMap(JSONObject query);

    /**
     * 获取网约车地图车辆客流热力图
     * @param query
     * @return
     */
    JSONObject getOnlineCarKlMap(JSONObject query);
}
