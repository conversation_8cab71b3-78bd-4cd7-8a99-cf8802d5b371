package com.yinshu.tdss.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.tdss.common.RemoteUrlConstants;
import com.yinshu.tdss.common.RequestFilterBuilder;
import com.yinshu.tdss.service.BusService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class BusServiceImpl implements BusService {
    @Resource
    DvisualHttpTemplate template;

    /**
     * 获取运营指标与运营里程信息
     *
     * @param query
     * @return
     */
    @Override
    public JSONObject getBusOperationIndexAndMileage(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_bus_operation_index_and_mileage, query);
        return post;
    }

    /**
     * 获取公交车基础信息与线路整体指标
     *
     * @param query
     * @return
     */
    @Override
    public JSONObject getBusSpecialTopic(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_bus_base_info, query);
        return post;
    }

    /**
     * 获取忻州公交车线路排名
     *
     * @param query
     * @return
     */
    @Override
    public JSONObject getBusRank(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_bus_road_route_rank, query);
        return post;
    }

    /**
     * 获取公交车运营折线图
     *
     * @param query
     * @return
     */
    @Override
    public JSONObject getBusOperationLineChart(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        String filter = query.getString("filter");
        if (filter == null) {
            if (query.getString("startDate").equals(query.getString("endDate"))) {
                filter = "hour != null";
            } else {
                filter = "hour == null";
            }
        } else {
            if (query.getString("startDate").equals(query.getString("endDate"))) {
                filter += "and hour != null";
            } else {
                filter += "and hour == null";
            }
        }
        query.put("filter", filter);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_bus_operation_line_chart, query);
        return post;
    }

    /**
     * 公交车运营柱状图
     *
     * @param query
     * @return
     */
    @Override
    public JSONObject getBusOperationBarChart(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        String filter = query.getString("filter");
        if (filter == null) {
            if (query.getString("startDate").equals(query.getString("endDate"))) {
                filter = "hour != null";
            } else {
                filter = "hour == null";
            }
        } else {
            if (query.getString("startDate").equals(query.getString("endDate"))) {
                filter += "and hour != null";
            } else {
                filter += "and hour == null";
            }
        }
        query.put("filter", filter);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_bus_operation_bar_chart, query);
        return post;
    }

    /**
     * 公交车线路指标分析-按小时
     *
     * @param query
     * @return
     */
    @Override
    public JSONObject getBusOperationIndexAnalysisHour(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_bus_road_index_analysis_hour, query);
        return post;
    }

    /**
     * 忻州公交车线路指标分析-按天
     *
     * @param query
     * @return
     */
    @Override
    public JSONObject getBusOperationIndexAnalysisDay(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_bus_road_index_analysis_day, query);
        return post;

    }

    /**
     * 公交车热力图分布
     *
     * @param query
     * @return
     */
    @Override
    public JSONObject getBusOperationHeatMapDistribution(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        // 1. 调用第三方接口获取原始数据
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_bus_heat_map_distribution, query);
        return post;
    }

    /**
     * 获取公交车所有线路名称
     *
     * @param query
     * @return
     */
    @Override
    public JSONObject getBusLineName(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_bus_road_index_analysis_day, query);
        return post;
    }

    /**
     * 获取公交车所有区县企业
     *
     * @param query
     * @return
     */
    @Override
    public JSONObject getBusDistrictsAndCompanies(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_bus_enterprise_select, query);
        return post;
    }

    private void addSampleData(List<Map<String, Object>> list) {
        addPoint(list, 113.53063, 22.281342, 28);
        addPoint(list, 113.531493, 22.27278, 15);
        addPoint(list, 113.533388, 22.289686, 9);
        addPoint(list, 113.537341, 22.240986, 8);
        addPoint(list, 113.554157, 22.242994, 20);
        addPoint(list, 113.56005, 22.244599, 25);
        addPoint(list, 113.590664, 22.255036, 7);
        addPoint(list, 113.594976, 22.254367, 32);
        addPoint(list, 113.597994, 22.250085, 5);
        addPoint(list, 113.51123, 22.24123, 10);
        addPoint(list, 113.511, 22.264123, 20);
        addPoint(list, 113.512, 22.245123, 30);
        addPoint(list, 113.511233, 22.271238, 20);
        addPoint(list, 113.54123, 22.281235, 45);
        addPoint(list, 113.551231, 22.212365, 20);
        addPoint(list, 113.574, 22.281238, 25);
        addPoint(list, 113.543, 22.261201, 7);
        addPoint(list, 113.58123, 22.2123641, 32);
        addPoint(list, 113.566, 22.24268, 5);
        addPoint(list, 113.575, 22.23235, 10);
        addPoint(list, 113.57787, 22.242364, 70);
        addPoint(list, 113.571567, 22.21265, 80);
        addPoint(list, 113.52134, 22.261238, 90);
    }

    private void addPoint(List<Map<String, Object>> list, double lng, double lat, int count) {
        Map<String, Object> map = new HashMap<>();
        map.put("location", lng + "," + lat);
        map.put("num", String.valueOf(count));
        list.add(map);
    }


}
