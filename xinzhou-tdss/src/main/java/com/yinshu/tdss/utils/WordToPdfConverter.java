package com.yinshu.tdss.utils;

import fr.opensagres.poi.xwpf.converter.pdf.PdfConverter;
import fr.opensagres.poi.xwpf.converter.pdf.PdfOptions;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPageMar;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTSectPr;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigInteger;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;

public class WordToPdfConverter {
    private static final Logger logger = LoggerFactory.getLogger(WordToPdfConverter.class.getName());
    public static void wordToPdf(InputStream inputStream, File outputFilePath) {
        try {
            logger.info("开始转换 Word 到 PDF...");
            XWPFDocument document = new XWPFDocument(inputStream);
            PdfOptions pdfOptions = PdfOptions.create();
            OutputStream out = new FileOutputStream(outputFilePath);
            PdfConverter.getInstance().convert(document, out ,pdfOptions);
            out.close();
            logger.info("Word 转 PDF 成功！");
        } catch (Exception e) {
            logger.info("word转pdf失败！");
            logger.error("Word 转 PDF 失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void pageSet(XWPFDocument document, File outputDoc) throws IOException {
        // 2. 设置页面边距（单位：twip）
        CTSectPr sectPr = document.getDocument().getBody().getSectPr();
        if (sectPr == null) {
            sectPr = document.getDocument().getBody().addNewSectPr();
        }
        CTPageMar pageMar = sectPr.isSetPgMar() ? sectPr.getPgMar() : sectPr.addNewPgMar();
        long pageMarginTop = 0;
        long pageMarginBottom = 0;
        pageMar.setTop(BigInteger.valueOf(pageMarginTop));
        pageMar.setBottom(BigInteger.valueOf(pageMarginBottom));
        // 4. 保存文档
        try (FileOutputStream out = new FileOutputStream(outputDoc)) {
            document.write(out);
            out.flush();
            out.getFD().sync();
        } finally {
            document.close();
        }
    }


    /**
     * PDF 转图片（文件流方式）
     */
    public static void pdfToImagesByStream(InputStream inputStream, File image,boolean isCrop) {
        logger.info("图片生成开始...");
        try (PDDocument document = PDDocument.load(inputStream)) {
            Path tempDirectory = Files.createTempDirectory("images");
            PDFRenderer renderer = new PDFRenderer(document);
            int pages = document.getNumberOfPages();

            for (int page = 0; page < pages; ++page) {
                logger.info("正在渲染第 {} 页", page);
                BufferedImage img = renderer.renderImageWithDPI(page, 400, ImageType.RGB);
                String outputFile = tempDirectory.toAbsolutePath() + File.separator + page + ".png";
                ImageIO.write(img, "png", new File(outputFile));
                logger.info(outputFile);
                logger.info("第 {} 页渲染完成", page);
            }

            if(!isCrop){
                // 裁剪图片
                croppingImages(tempDirectory.toString());
            }


            // 合并图片
            mergeImages(tempDirectory.toString(), image);

            System.out.println("tempDirectory.toAbsolutePath() = " + tempDirectory.toAbsolutePath());
            System.out.println("生成后的图片路径："+image.getAbsolutePath());

            // 删除临时文件
            Files.walk(tempDirectory)
                    .sorted((a, b) -> b.toString().length() - a.toString().length())
                    .forEach(p -> {
                        try {
                            Files.deleteIfExists(p);
                        } catch (IOException e) {
                            logger.error("删除临时文件失败: {}" ,e.getMessage());
                        }
                    });

            logger.info("图片生成完成");
        } catch (IOException e) {
            logger.error("PDF 转图片失败: {}" ,e.getMessage());
        }
    }

    /**
     * 图片合并
     */
    public static void mergeImages(String imagesPath, File mergedImageFile) {
        int heightTotal = 0;
        int maxWidth = 0;
        File dir = new File(imagesPath);
        File[] files = dir.listFiles();

        if (files == null || files.length == 0) {
            logger.error("未找到图片文件");
            return;
        }

        try {
            // 按文件名中的数字排序
            Arrays.sort(files, (file1, file2) -> {
                String name1 = file1.getName();
                String name2 = file2.getName();
                int num1 = Integer.parseInt(name1.replaceAll("\\D", ""));
                int num2 = Integer.parseInt(name2.replaceAll("\\D", ""));
                return Integer.compare(num1, num2);
            });

            // 计算总高度和最大宽度
            for (File file : files) {
                BufferedImage image = ImageIO.read(file);
                if (image.getWidth() > maxWidth) {
                    maxWidth = image.getWidth();
                }
                heightTotal += image.getHeight();
            }

            // 创建合并后的图片
            BufferedImage mergedImage = new BufferedImage(maxWidth, heightTotal, BufferedImage.TYPE_INT_RGB);
            int offsetY = 0;

            for (File file : files) {
                BufferedImage image = ImageIO.read(file);
                // 居中对齐图片
                int offsetX = (maxWidth - image.getWidth()) / 2;
                mergedImage.getGraphics().drawImage(image, offsetX, offsetY, null);
                offsetY += image.getHeight();
            }

            // 保存合并后的图片
            ImageIO.write(mergedImage, "jpg", mergedImageFile);
        } catch (IOException e) {
            logger.error("图片合并失败: {}" , e.getMessage());
        }
    }

    /**
     * 裁剪图片
     */
    public static void croppingImages(String imagePath) {
        File folder = new File(imagePath); // 指定文件夹路径
        File[] files = folder.listFiles((dir, name) -> name.toLowerCase().endsWith(".png") || name.toLowerCase().endsWith(".jpg"));

        if (files != null) {
            // 按文件名中的数字顺序排序
            Arrays.sort(files, (file1, file2) -> {
                // 提取文件名中的数字部分
                String num1 = file1.getName().replaceAll("[^0-9]", "");
                String num2 = file2.getName().replaceAll("[^0-9]", "");
                return Integer.compare(Integer.parseInt(num1), Integer.parseInt(num2));
            });
            for (File file : files) {
                try {
                    BufferedImage img = ImageIO.read(file);
                    int width = img.getWidth();
                    int height = img.getHeight();

                    // 根据图片位置决定裁剪方式
                    int cropTop = 260;
                    int cropBottom = height - 260;
                    BufferedImage croppedImg = img.getSubimage(0, cropTop, width, cropBottom - cropTop);
                    File outputfile = new File(imagePath + File.separator + file.getName());
                    ImageIO.write(croppedImg, "png", outputfile);
                    logger.info("Cropped and saved: {} " ,outputfile.getAbsolutePath());
                } catch (IOException e) {
                    logger.error("Cropped and saved: {} ",e.getMessage());
                }
            }
        }
    }


    public static void main(String[] args) throws Exception {
        InputStream inputStream = new FileInputStream(new File("C:\\Users\\<USER>\\Desktop\\wordReadDemo2.docx"));
        WordToPdfConverter.wordToPdf(inputStream,new File("C:\\Users\\<USER>\\Desktop\\222.pdf"));

        File imageFile=File.createTempFile("temp", ".png");
        WordToPdfConverter.pdfToImagesByStream(new FileInputStream(new File("C:\\Users\\<USER>\\Desktop\\222.pdf")),imageFile,true);
    }
}
