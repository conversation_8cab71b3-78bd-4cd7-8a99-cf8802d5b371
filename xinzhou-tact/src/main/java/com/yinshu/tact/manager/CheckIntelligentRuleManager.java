package com.yinshu.tact.manager;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.CheckIntelligentRule;

/**
 * 智能查岗规则表
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
public interface CheckIntelligentRuleManager {

    /**
     * 条件查询
     *
     * @param entity
     * @return
     */
    List<CheckIntelligentRule> queryList(CheckIntelligentRule entity);

    /**
     * 条件分页查询
     *
     * @param entity
     * @return
     */
    IPage<CheckIntelligentRule> queryPageList(CheckIntelligentRule entity);

    /**
     * 保存
     *
     * @param entity
     */
    void save(CheckIntelligentRule entity);

    /**
     * 删除
     *
     * @param id
     */

    void remove(String id);

    /**
     * 批量删除
     *
     * @param idList
     */
    void remove(List<String> idList);

    /**
     * 更新
     *
     * @param entity
     */
    void update(CheckIntelligentRule entity);

    /**
     * 根据ID获取对象
     *
     * @param id
     * @return
     */
    CheckIntelligentRule getById(String id);

    List<String> getAllEnterpriseName();

    default List<CheckIntelligentRule> buildList(List<CheckIntelligentRule> list) {
        list.forEach(item -> {
			item.setCheckCondition("1、近1小时报警处理率低于" + item.getLessThan() + "%;\n2、报警督办率大于" + item.getGreaterThan() + "%");
        });
		return list;
    }
}

