package com.yinshu.tact.manager;

import java.util.Map;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.HiddenDanger;

/**
 * 隐患整改跟踪表（包含发起和完成阶段） 
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
public interface HiddenDangerManager {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<HiddenDanger> queryList(HiddenDanger entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<HiddenDanger> queryPageList(HiddenDanger entity);
	
	/**
	 * 保存
	 * @param entity
	 */
	void save(HiddenDanger entity);
	
	/**
	 * 删除
	 * @param id
	 */
	
	void remove(String id);
	
	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);
	
	/**
	 * 更新
	 * @param entity
	 */
	void update(HiddenDanger entity);
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	HiddenDanger getById(String id);

}

