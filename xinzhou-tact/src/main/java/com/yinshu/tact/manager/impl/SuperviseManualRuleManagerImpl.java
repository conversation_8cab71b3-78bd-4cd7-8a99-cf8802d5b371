package com.yinshu.tact.manager.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.exception.RestfulAPIException;
import com.yinshu.sys.entity.Setting;
import com.yinshu.sys.manager.SettingManager;
import com.yinshu.tact.common.BigDataResultVO;
import com.yinshu.tact.entity.SuperviseManualRule;
import com.yinshu.tact.manager.SuperviseManualRuleManager;
import com.yinshu.tact.service.SuperviseManualRuleService;
import com.yinshu.utils.SnowflakeIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 手动督办规则表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
@Slf4j
public class SuperviseManualRuleManagerImpl implements SuperviseManualRuleManager {

    @Autowired
    private SuperviseManualRuleService superviseManualRuleService;

    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;

    private final static RestTemplate restTemplate = new RestTemplate();

    @Autowired
    private SettingManager settingManager;

    private final static String URL = "/api/ads_road_safe_alarm_supervision/YjmkH33r0OFeGXg9Amc7";

    /**
     * 条件查询
     *
     * @param entity
     * @return
     */
    @Override
    public List<SuperviseManualRule> queryList(SuperviseManualRule entity) {
        List<SuperviseManualRule> resultList = superviseManualRuleService.queryList(entity);
        return resultList;
    }

    /**
     * 条件分页查询
     *
     * @param entity
     * @return
     */
    @Override
    public IPage<SuperviseManualRule> queryPageList(SuperviseManualRule entity) {
        IPage<SuperviseManualRule> resultList = superviseManualRuleService.queryPageList(entity);
        return resultList;
    }

    /**
     * 保存
     *
     * @param entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(SuperviseManualRule entity) {

        entity.setId(snowflakeIdGenerator.nextIdStr());

        superviseManualRuleService.save(entity);

        /**
         * 此处仅做测试用,实际要等待时间再去调用大数据接口,1未处理,2已督办中,3已处理
         */
        this.updateSupervision(entity.getAlarmId(), 2);
    }

    private void checkData(SuperviseManualRule entity) {
        if (StringUtils.isBlank(entity.getAlarmId())) {
            throw new RestfulAPIException("预警信息不能为空");
        }
        if (entity.getLimitMinutes() == null || entity.getLimitMinutes() == 0 || entity.getLimitMinutes() < 0) {
            throw new RestfulAPIException("超时时间有误");
        }
        if (superviseManualRuleService.count(new LambdaQueryWrapper<SuperviseManualRule>()
                .eq(SuperviseManualRule::getAlarmId, entity.getAlarmId())
                .ne(StringUtils.isNotBlank(entity.getId()), SuperviseManualRule::getId, entity.getId())
        ) > 0) {
            throw new RestfulAPIException("该预警规则已存在，请勿重复添加");
        }
    }

    /**
     * 删除
     *
     * @param id
     */
    @Override
    public void remove(String id) {
        superviseManualRuleService.removeById(id);
    }

    /**
     * 批量删除
     *
     * @param idList
     */
    @Override
    public void remove(List<String> idList) {
        superviseManualRuleService.removeByIds(idList);
    }

    /**
     * 更新
     *
     * @param entity
     */
    @Override
    public void update(SuperviseManualRule entity) {
        superviseManualRuleService.updateById(entity);
    }

    /**
     * 根据ID获取对象
     *
     * @param id
     * @return
     */
    @Override
    public SuperviseManualRule getById(String id) {
        return superviseManualRuleService.getById(id);
    }

    /**
     * @param id 告警ID
     * @return com.yinshu.tact.entity.ToccSuperviseManualRule
     * <AUTHOR>
     * @description //TODO 根据告警ID获取对象
     * @date 2025/7/18 13:59
     **/
    @Override
    public SuperviseManualRule getByAlarmId(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        return superviseManualRuleService.getOne(new LambdaQueryWrapper<SuperviseManualRule>()
                .eq(SuperviseManualRule::getAlarmId, id)
                .last("limit 1")
        );
    }

    @Override
    public BigDataResultVO updateSupervision(String id, int process_status) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        Map<String, Object> map = new HashMap<>();
        map.put("process_status", process_status);
        map.put("id", id);
        String str = JSON.toJSONString(map);
        log.info("调用大数据报警督办接口数据{}", str);
        HttpEntity<String> requestEntity = new HttpEntity<>(str, headers);
        Setting settingBaseUrl = settingManager.getByCode("dvisual-baseUrl");
        ResponseEntity<String> response = restTemplate.postForEntity(
                settingBaseUrl.getParmValue() + URL,
                requestEntity,
                String.class
        );
        BigDataResultVO bigDataResultVO = JSONObject.parseObject(response.getBody(), BigDataResultVO.class);
        if ("200".equals(Objects.requireNonNull(bigDataResultVO).getState())) {
            log.info("调用大数据报警督办接口修改成功{}", response);
            return bigDataResultVO;
        } else {
            log.info("调用大数据报警督办接口修改失败{}", response);
            throw new RestfulAPIException("调用大数据报警督办接口修改失败" + bigDataResultVO.getMessage());
        }
    }

}
