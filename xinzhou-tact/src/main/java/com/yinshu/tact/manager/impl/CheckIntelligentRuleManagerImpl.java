package com.yinshu.tact.manager.impl;

import java.util.List;

import com.yinshu.sys.entity.SessionUser;
import com.yinshu.sys.security.SessionUserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.CheckIntelligentRule;
import com.yinshu.tact.service.CheckIntelligentRuleService;
import com.yinshu.tact.manager.CheckIntelligentRuleManager;
import org.springframework.stereotype.Service;
import com.yinshu.utils.SnowflakeIdGenerator;
import com.yinshu.utils.DateUtils;

/**
 * 智能查岗规则表 
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Service
public class CheckIntelligentRuleManagerImpl implements CheckIntelligentRuleManager {

	@Autowired
	private CheckIntelligentRuleService checkIntelligentRuleService;

	@Autowired
	private SnowflakeIdGenerator snowflakeIdGenerator;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<CheckIntelligentRule> queryList(CheckIntelligentRule entity) {
		List<CheckIntelligentRule> resultList = checkIntelligentRuleService.queryList(entity);
		this.buildList(resultList);
		return resultList;
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<CheckIntelligentRule> queryPageList(CheckIntelligentRule entity) {
		IPage<CheckIntelligentRule> resultList = checkIntelligentRuleService.queryPageList(entity);
		this.buildList(resultList.getRecords());
		return resultList;
	}
	
	/**
	 * 保存
	 * @param entity
	 */
	public void save(CheckIntelligentRule entity) {
		entity.setId(snowflakeIdGenerator.nextIdStr());
		entity.setCreateTime(DateUtils.getNow());
		SessionUser sessionUser = SessionUserUtils.getSessionUser();
		entity.setCreateBy(sessionUser.getUsername());
		checkIntelligentRuleService.save(entity);
	}
	
	/**
	 * 删除
	 * @param id
	 */
	
	public void remove(String id) {
		checkIntelligentRuleService.removeById(id);
	}
	
	/**
	 * 批量删除
	 * @param idList
	 */
	public void remove(List<String> idList) {
		checkIntelligentRuleService.removeByIds(idList);
	}
	
	/**
	 * 更新
	 * @param entity
	 */
	public void update(CheckIntelligentRule entity) {
		entity.setUpdateTime(DateUtils.getNow());
		SessionUser sessionUser = SessionUserUtils.getSessionUser();
		entity.setUpdateBy(sessionUser.getUsername());
		checkIntelligentRuleService.updateById(entity);
	}
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	public CheckIntelligentRule getById(String id) {
		return checkIntelligentRuleService.getById(id);
	}

	@Override
	public List<String> getAllEnterpriseName() {
		return checkIntelligentRuleService.getAllEnterpriseName();
	}

}
