package com.yinshu.tact.manager.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.Dictionary;
import com.yinshu.sys.entity.Setting;
import com.yinshu.sys.manager.DictionaryManager;
import com.yinshu.sys.manager.SettingManager;
import com.yinshu.tact.entity.dto.ProjectSafetyDTO;
import com.yinshu.tact.entity.dto.HazardDiscoveryTimeDTO;
import com.yinshu.tact.entity.dto.SafetyQualityDetailDTO;
import com.yinshu.tact.manager.ProjectSafetyManager;
import com.yinshu.tact.service.HiddenDangerService;
import com.yinshu.tact.service.ProjectSafetyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.yinshu.tact.entity.HiddenDanger;
/**
 * 项目安全管理实现类
 * <AUTHOR>
 * @since 2025-07-21
 */
@Service
@Slf4j
public class ProjectSafetyManagerImpl implements ProjectSafetyManager {

    @Autowired
    private ProjectSafetyService projectSafetyService;

    @Autowired
    private HiddenDangerService hiddenDangerService;

    @Autowired
    private SettingManager settingManager;
    /**
     * 查询安全隐患整改列表
     * @param projectSafetyDTO
     * @return
     */
    @Override
    public List<ProjectSafetyDTO> queryProjectSafetyList(ProjectSafetyDTO projectSafetyDTO) {
        return projectSafetyService.selectProjectSafetyList(projectSafetyDTO);
    }

    /**
     * 查询隐患发现时间列表
     * @param hazardDiscoveryTimeDTO
     * @return
     */
    @Override
    public List<HazardDiscoveryTimeDTO> queryHazardDiscoveryTimeList(HazardDiscoveryTimeDTO hazardDiscoveryTimeDTO) {
        return projectSafetyService.selectHazardDiscoveryTimeList(hazardDiscoveryTimeDTO);
    }

    /**
     * 分页查询安全隐患整改列表
     * @param projectSafetyDTO
     * @return
     */
    @Override
    public IPage<ProjectSafetyDTO> queryProjectSafetyPage(ProjectSafetyDTO projectSafetyDTO) {
        IPage<ProjectSafetyDTO> projectSafetyDTOIPage = projectSafetyService.selectProjectSafetyPage(projectSafetyDTO);
        if (CollectionUtils.isEmpty(projectSafetyDTOIPage.getRecords())){
            return projectSafetyDTOIPage;
        }
        List<String> collect = projectSafetyDTOIPage.getRecords().stream().map(ProjectSafetyDTO::getProjectId).collect(Collectors.toList());
        Map<String, List<HiddenDanger>> listMap = hiddenDangerService.lambdaQuery().in(HiddenDanger::getParentId, collect).list()
                .stream().collect(Collectors.groupingBy(HiddenDanger::getParentId));
        projectSafetyDTOIPage.getRecords().forEach(item -> {
            List<HiddenDanger> hiddenDangers = listMap.get(item.getProjectId());
            if (CollectionUtils.isNotEmpty(hiddenDangers)){
//                计算整改完成的数量,存在整改完成才能显示整改记录
                item.setRectificationCount((int) hiddenDangers.stream().filter(hiddenDanger -> hiddenDanger.getStatus() == 2).count());
//                找到最新的整改记录,作为主表状态
                hiddenDangers.sort((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime()));
                item.setStatus(hiddenDangers.get(0).getStatus());
//                整治进度=整改完成数量/隐患总数*100
                item.setRectificationProgress(Math.round(item.getRectificationCount() * 100.0 / item.getHazardCount())+"%");
            }
        });
        return projectSafetyDTOIPage;
    }

    /**
     * 分页查询隐患发现时间列表
     * @param hazardDiscoveryTimeDTO
     * @return
     */
    @Override
    public IPage<HazardDiscoveryTimeDTO> queryHazardDiscoveryTimePage(HazardDiscoveryTimeDTO hazardDiscoveryTimeDTO) {
        return projectSafetyService.selectHazardDiscoveryTimePage(hazardDiscoveryTimeDTO);
    }

    /**
     * 根据ID查询安全质量检查详情
     * @param id 安全质量检查ID
     * @return
     */
    @Override
    public SafetyQualityDetailDTO getSafetyQualityById(String id) {
        return projectSafetyService.selectSafetyQualityById(id);
    }

    /**
     * 根据项目ID查询所有隐患名称
     * @param projectId
     * @return
     */
    @Override
    public List<HazardDiscoveryTimeDTO> getHazardTypeByProjectId(String projectId) {
        try {
//        隐患参数配置
            Setting byCode = settingManager.getByCode("hidden-danger-parameters");
            List<String> codes = Arrays.asList(byCode.getParmValue().split(","));
//        查询可以选择的隐患详情
            List<HazardDiscoveryTimeDTO> list = projectSafetyService.getHazardTypeByProjectId(projectId,codes);
            return list;
        } catch (Exception e) {
            log.error("根据项目ID查询所有隐患名称出错",e);
            return new ArrayList<>();
        }
    }
}
