package com.yinshu.tact.manager;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.dto.ProjectSafetyDTO;
import com.yinshu.tact.entity.dto.HazardDiscoveryTimeDTO;
import com.yinshu.tact.entity.dto.SafetyQualityDetailDTO;

import java.util.List;

/**
 * 项目安全管理接口
 * <AUTHOR>
 * @since 2025-07-21
 */
public interface ProjectSafetyManager {

    /**
     * 查询安全隐患整改列表
     * @param projectSafetyDTO
     * @return
     */
    List<ProjectSafetyDTO> queryProjectSafetyList(ProjectSafetyDTO projectSafetyDTO);

    /**
     * 查询隐患发现时间列表
     * @param hazardDiscoveryTimeDTO
     * @return
     */
    List<HazardDiscoveryTimeDTO> queryHazardDiscoveryTimeList(HazardDiscoveryTimeDTO hazardDiscoveryTimeDTO);

    /**
     * 分页查询安全隐患整改列表
     * @param projectSafetyDTO
     * @return
     */
    IPage<ProjectSafetyDTO> queryProjectSafetyPage(ProjectSafetyDTO projectSafetyDTO);

    /**
     * 分页查询隐患发现时间列表
     * @param hazardDiscoveryTimeDTO
     * @return
     */
    IPage<HazardDiscoveryTimeDTO> queryHazardDiscoveryTimePage(HazardDiscoveryTimeDTO hazardDiscoveryTimeDTO);

    /**
     * 根据ID查询安全质量检查详情
     * @param id 安全质量检查ID
     * @return
     */
    SafetyQualityDetailDTO getSafetyQualityById(String id);

    /**
     * 根据项目ID查询所有隐患名称
     * @param projectId
     * @return
     */
    List<HazardDiscoveryTimeDTO> getHazardTypeByProjectId(String projectId);
}
