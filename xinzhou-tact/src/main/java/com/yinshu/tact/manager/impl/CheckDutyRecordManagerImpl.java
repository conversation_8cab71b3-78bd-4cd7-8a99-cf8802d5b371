package com.yinshu.tact.manager.impl;

import java.util.Map;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.CheckDutyRecord;
import com.yinshu.tact.service.CheckDutyRecordService;
import com.yinshu.tact.manager.CheckDutyRecordManager;
import org.springframework.stereotype.Service;
import com.yinshu.utils.SnowflakeIdGenerator;
import com.yinshu.utils.DateUtils;

/**
 * 查岗记录表 
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Service
public class CheckDutyRecordManagerImpl implements CheckDutyRecordManager {

	@Autowired
	private CheckDutyRecordService checkDutyRecordService;

	@Autowired
	private SnowflakeIdGenerator snowflakeIdGenerator;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<CheckDutyRecord> queryList(CheckDutyRecord entity) {
		List<CheckDutyRecord> resultList = checkDutyRecordService.queryList(entity);
		return resultList;
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<CheckDutyRecord> queryPageList(CheckDutyRecord entity) {
		IPage<CheckDutyRecord> resultList = checkDutyRecordService.queryPageList(entity);
		return resultList;
	}
	
	/**
	 * 保存
	 * @param entity
	 */
	public void save(CheckDutyRecord entity) {
		entity.setId(snowflakeIdGenerator.nextIdStr());
		entity.setCreateTime(DateUtils.getNow());
		checkDutyRecordService.save(entity);
	}
	
	/**
	 * 删除
	 * @param id
	 */
	
	public void remove(String id) {
		checkDutyRecordService.removeById(id);
	}
	
	/**
	 * 批量删除
	 * @param idList
	 */
	public void remove(List<String> idList) {
		checkDutyRecordService.removeByIds(idList);
	}
	
	/**
	 * 更新
	 * @param entity
	 */
	public void update(CheckDutyRecord entity) {
		entity.setUpdateTime(DateUtils.getNow());
		checkDutyRecordService.updateById(entity);
	}
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	public CheckDutyRecord getById(String id) {
		return checkDutyRecordService.getById(id);
	}

	@Override
	public List<String> getAllEnterpriseName() {
		return checkDutyRecordService.getAllEnterpriseName();
	}

	@Override
	public Map<String,String> getCountByEnterpriseNames(List<String> enterpriseNames) {
		List<CheckDutyRecord> resultList = checkDutyRecordService.getCountByEnterpriseNames(enterpriseNames);
		Map<String, String> collect = resultList.stream().collect(Collectors.toMap(CheckDutyRecord::getCheckEnterpriseName, CheckDutyRecord::getCount));
		return collect;
	}

}
