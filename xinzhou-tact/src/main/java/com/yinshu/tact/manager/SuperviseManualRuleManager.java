package com.yinshu.tact.manager;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.common.BigDataResultVO;
import com.yinshu.tact.entity.SuperviseManualRule;

/**
 * 手动督办规则表 
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface SuperviseManualRuleManager {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<SuperviseManualRule> queryList(SuperviseManualRule entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<SuperviseManualRule> queryPageList(SuperviseManualRule entity);
	
	/**
	 * 保存
	 * @param entity
	 */
	void save(SuperviseManualRule entity);
	
	/**
	 * 删除
	 * @param id
	 */
	
	void remove(String id);
	
	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);
	
	/**
	 * 更新
	 * @param entity
	 */
	void update(SuperviseManualRule entity);
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	SuperviseManualRule getById(String id);

	/**
	 * <AUTHOR>
	 * @description //TODO 根据告警ID获取对象
	 * @date 2025/7/18 13:59
	 * @param id 告警ID
	 * @return com.yinshu.tact.entity.ToccSuperviseManualRule
	 **/
	SuperviseManualRule getByAlarmId(String id);

	/**
	 * 修改大数据报警督办表状态
	 * @param id
	 * @param process_status
	 * @return
	 */
	BigDataResultVO updateSupervision(String id, int process_status);
}

