package com.yinshu.tact.manager;

import java.util.Map;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.CheckDutyRecord;

/**
 * 查岗记录表 
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
public interface CheckDutyRecordManager {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<CheckDutyRecord> queryList(CheckDutyRecord entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<CheckDutyRecord> queryPageList(CheckDutyRecord entity);
	
	/**
	 * 保存
	 * @param entity
	 */
	void save(CheckDutyRecord entity);
	
	/**
	 * 删除
	 * @param id
	 */
	
	void remove(String id);
	
	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);
	
	/**
	 * 更新
	 * @param entity
	 */
	void update(CheckDutyRecord entity);
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	CheckDutyRecord getById(String id);

    List<String> getAllEnterpriseName();

	Map<String,String> getCountByEnterpriseNames(List<String> enterpriseNames);
}

