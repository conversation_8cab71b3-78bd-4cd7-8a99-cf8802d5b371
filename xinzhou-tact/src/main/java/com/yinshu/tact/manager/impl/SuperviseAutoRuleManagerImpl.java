package com.yinshu.tact.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.exception.RestfulAPIException;
import com.yinshu.tact.entity.SuperviseAutoRule;
import com.yinshu.tact.manager.SuperviseAutoRuleManager;
import com.yinshu.tact.service.SuperviseAutoRuleService;
import com.yinshu.utils.SnowflakeIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 自动督办规则表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
@Slf4j
public class SuperviseAutoRuleManagerImpl implements SuperviseAutoRuleManager {

    @Autowired
    private SuperviseAutoRuleService superviseAutoRuleService;

    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;

    @Autowired
    private StringRedisTemplate redisTemplate;

    private static final String REDIS_KEY = "alarm:supervise:rules";

    /**
     * 条件查询
     *
     * @param entity
     * @return
     */
    @Override
    public List<SuperviseAutoRule> queryList(SuperviseAutoRule entity) {
        List<SuperviseAutoRule> rules = superviseAutoRuleService.queryList(entity);
        return rules;
    }

    /**
     * 条件分页查询
     *
     * @param entity
     * @return
     */
    @Override
    public IPage<SuperviseAutoRule> queryPageList(SuperviseAutoRule entity) {
        IPage<SuperviseAutoRule> resultList = superviseAutoRuleService.queryPageList(entity);
        return resultList;
    }

    /**
     * 保存
     *
     * @param entity
     */
    @Override
    public void save(SuperviseAutoRule entity) {
        checkData(entity);
        entity.setId(snowflakeIdGenerator.nextIdStr());
        superviseAutoRuleService.save(entity);
        // 更新缓存（异步执行）
        CompletableFuture.runAsync(() ->
                redisTemplate.opsForHash().put(REDIS_KEY, entity.getAlarmTypeName(), entity.getWaitingMinutes())
        );
    }

    private void checkData(SuperviseAutoRule entity) {
        if (StringUtils.isBlank(entity.getAlarmTypeName())) {
            throw new RestfulAPIException("请选择预警类型");
        }
        if (entity.getWaitingMinutes() == null || entity.getWaitingMinutes() <= 0) {
            throw new RestfulAPIException("请填写等待时间");
        }
        if (superviseAutoRuleService.count(new LambdaQueryWrapper<SuperviseAutoRule>()
                .eq(SuperviseAutoRule::getAlarmTypeName, entity.getAlarmTypeName())
                .ne(StringUtils.isNotBlank(entity.getId()), SuperviseAutoRule::getId, entity.getId())
        ) > 0) {
            throw new RestfulAPIException("该预警类型已存在，请勿重复添加");
        }
    }

    /**
     * 删除
     *
     * @param id
     */

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void remove(String id) {
        SuperviseAutoRule rule = superviseAutoRuleService.getById(id);
        if (rule != null) {
            superviseAutoRuleService.removeById(id);

            // 更新缓存（异步执行）
            CompletableFuture.runAsync(() ->
                    redisTemplate.opsForHash().delete(REDIS_KEY, rule.getAlarmTypeName())
            );
        }
    }

    /**
     * 批量删除
     *
     * @param idList
     */
    @Override
    public void remove(List<String> idList) {
        idList.forEach(this::remove);
    }

    /**
     * 更新
     *
     * @param entity
     */
    @Override
    public void update(SuperviseAutoRule entity) {
        checkData(entity);
        superviseAutoRuleService.updateById(entity);
        // 更新缓存（异步执行）
        CompletableFuture.runAsync(() -> {
            if (entity.getWaitingMinutes() != null) {
                redisTemplate.opsForHash().put(REDIS_KEY, entity.getAlarmTypeName(), entity.getWaitingMinutes());
            } else {
                redisTemplate.opsForHash().delete(REDIS_KEY, entity.getAlarmTypeName());
            }
        });
    }

    /**
     * 根据ID获取对象
     *
     * @param id
     * @return
     */
    @Override
    public SuperviseAutoRule getById(String id) {
        return superviseAutoRuleService.getById(id);
    }

}
