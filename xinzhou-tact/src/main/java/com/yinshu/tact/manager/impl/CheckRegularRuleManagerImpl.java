package com.yinshu.tact.manager.impl;

import java.util.List;

import com.yinshu.sys.entity.SessionUser;
import com.yinshu.sys.security.SessionUserUtils;
import com.yinshu.tact.job.RegularCheckJob;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.CheckRegularRule;
import com.yinshu.tact.service.CheckRegularRuleService;
import com.yinshu.tact.manager.CheckRegularRuleManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import com.yinshu.utils.SnowflakeIdGenerator;
import com.yinshu.utils.DateUtils;

/**
 * 定期自动查岗规则表
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Service
public class CheckRegularRuleManagerImpl implements CheckRegularRuleManager {

    @Autowired
    private CheckRegularRuleService checkRegularRuleService;

    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;

    @Autowired
    private RegularCheckJob regularCheckJob;

    /**
     * 条件查询
     *
     * @param entity
     * @return
     */
    public List<CheckRegularRule> queryList(CheckRegularRule entity) {
        List<CheckRegularRule> resultList = checkRegularRuleService.queryList(entity);
        return resultList;
    }

    /**
     * 条件分页查询
     *
     * @param entity
     * @return
     */
    public IPage<CheckRegularRule> queryPageList(CheckRegularRule entity) {
        IPage<CheckRegularRule> resultList = checkRegularRuleService.queryPageList(entity);
        return resultList;
    }

    /**
     * 保存
     *
     * @param entity
     */
    public void save(CheckRegularRule entity) {
        entity.setId(snowflakeIdGenerator.nextIdStr());
        entity.setCreateTime(DateUtils.getNow());
        SessionUser sessionUser = SessionUserUtils.getSessionUser();
        entity.setCreateBy(sessionUser.getUsername());
        checkRegularRuleService.save(entity);
//		同步新增调度器
        regularCheckJob.addScheduledTask(entity);

    }

    /**
     * 删除
     *
     * @param id
     */

    public void remove(String id) {
        checkRegularRuleService.removeById(id);
        //同步删除调度器
        regularCheckJob.removeScheduledTask(id);

    }

    /**
     * 批量删除
     *
     * @param idList
     */
    public void remove(List<String> idList) {
        idList.forEach(this::remove);
    }

    /**
     * 更新
     *
     * @param entity
     */
    public void update(CheckRegularRule entity) {
        entity.setUpdateTime(DateUtils.getNow());
        SessionUser sessionUser = SessionUserUtils.getSessionUser();
        entity.setUpdateBy(sessionUser.getUsername());
        checkRegularRuleService.updateById(entity);
        //同步更新调度器
        regularCheckJob.updateScheduledTask(entity);

    }

    /**
     * 根据ID获取对象
     *
     * @param id
     * @return
     */
    public CheckRegularRule getById(String id) {
        return checkRegularRuleService.getById(id);
    }

    @Override
    public List<String> getAllEnterpriseName() {
        return checkRegularRuleService.getAllEnterpriseName();
    }

}
