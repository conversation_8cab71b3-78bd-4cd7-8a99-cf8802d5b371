package com.yinshu.tact.manager;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.CheckRegularRule;

import java.util.List;

/**
 * 定期自动查岗规则表 
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
public interface CheckRegularRuleManager {

    /**
     * 条件查询
     * @param entity
     * @return
     */
    List<CheckRegularRule> queryList(CheckRegularRule entity);

    /**
     * 条件分页查询
     * @param entity
     * @return
     */
    IPage<CheckRegularRule> queryPageList(CheckRegularRule entity);

    /**
     * 保存
     * @param entity
     */
    void save(CheckRegularRule entity);

    /**
     * 删除
     * @param id
     */
    void remove(String id);

    /**
     * 批量删除
     * @param idList
     */
    void remove(List<String> idList);

    /**
     * 更新
     * @param entity
     */
    void update(CheckRegularRule entity);

    /**
     * 根据ID获取对象
     * @param id
     * @return
     */
    CheckRegularRule getById(String id);

    /**
     * 获取所有企业名称
     * @return
     */
    List<String> getAllEnterpriseName();
}