package com.yinshu.tact.manager.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.yinshu.sys.entity.Dictionary;
import com.yinshu.sys.manager.DictionaryManager;
import com.yinshu.tact.entity.dto.HazardDiscoveryTimeDTO;
import com.yinshu.tact.service.ProjectSafetyService;
import dm.jdbc.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.HiddenDanger;
import com.yinshu.tact.service.HiddenDangerService;
import com.yinshu.tact.manager.HiddenDangerManager;
import org.springframework.stereotype.Service;
import com.yinshu.utils.SnowflakeIdGenerator;
import com.yinshu.utils.DateUtils;

/**
 * 隐患整改跟踪表（包含发起和完成阶段）
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Service
public class HiddenDangerManagerImpl implements HiddenDangerManager {

    @Autowired
    private HiddenDangerService hiddenDangerService;

    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;

    @Autowired
    private ProjectSafetyService projectSafetyService;

    @Autowired
    private DictionaryManager dictionaryManager;

    /**
     * 条件查询
     *
     * @param entity
     * @return
     */
    public List<HiddenDanger> queryList(HiddenDanger entity) {
        List<HiddenDanger> resultList = hiddenDangerService.queryList(entity);
        return resultList;
    }

    /**
     * 条件分页查询
     *
     * @param entity
     * @return
     */
    public IPage<HiddenDanger> queryPageList(HiddenDanger entity) {
        IPage<HiddenDanger> resultList = hiddenDangerService.queryPageList(entity);
//        根据隐患详情id获取隐患详情-隐患名称
        Set<String> collect = resultList.getRecords().stream()
                .map(HiddenDanger::getRectificationItems) // 获取逗号分隔的字符串
                .filter(StringUtils::isNotBlank) // 非空判断（可选）
                .flatMap(str -> Arrays.stream(str.split(","))) // 按逗号分割为多个字符串并展开
                .map(String::trim) // 去除每个字符串两边的空格（可选）
                .collect(Collectors.toSet()); // 收集为 Set 去重
//        隐患参数字典
        Map<String, String> codeMap = dictionaryManager.getListByDicCode("TCPS_Hidden_Danger")
                .stream().collect(Collectors.toMap(Dictionary::getDicCode, Dictionary::getDicName));

        Map<String, String> stringMap = projectSafetyService.listHazardNameByIds(collect)
                .stream().collect(Collectors.toMap(HazardDiscoveryTimeDTO::getId, HazardDiscoveryTimeDTO::getHazardName));
        resultList.getRecords().forEach(item -> {
            String[] split = item.getRectificationItems().split(";");
            List<String> list = new ArrayList<>();
            for (int i = 0; i < split.length; i++) {
                String s = split[i];
                String s1 = stringMap.get(s);
                if (StringUtils.isNotEmpty(s1)) {
                    String s2 = codeMap.get(s1.split("-")[0]);
                    if (StringUtils.isNotEmpty(s2)) {
                        s1 = s2 + "-" + s1.split("-")[1];
                    }
                    list.add(s1);
                }
            }
            item.setRectificationItems(String.join(";", list));
        });
        return resultList;
    }

    /**
     * 保存
     *
     * @param entity
     */
    public void save(HiddenDanger entity) {
        if (StringUtils.isEmpty(entity.getParentId())) {
            throw new RuntimeException("父ID不能为空");
        }
        if (entity.getStatus() == null) {
            throw new RuntimeException("状态不能为空");
        }
        entity.setId(snowflakeIdGenerator.nextIdStr());
        entity.setCreateTime(DateUtils.getNow());
        hiddenDangerService.save(entity);
    }

    /**
     * 删除
     *
     * @param id
     */

    public void remove(String id) {
        hiddenDangerService.removeById(id);
    }

    /**
     * 批量删除
     *
     * @param idList
     */
    public void remove(List<String> idList) {
        hiddenDangerService.removeByIds(idList);
    }

    /**
     * 更新
     *
     * @param entity
     */
    public void update(HiddenDanger entity) {
        entity.setUpdateTime(DateUtils.getNow());
        if (StringUtil.isEmpty(entity.getId())) {
            if (StringUtil.isEmpty(entity.getParentId())) {
                throw new RuntimeException("ID为空,父ID不能为空");
            } else {
                HiddenDanger dto = hiddenDangerService.getByParentId(entity.getParentId());
                entity.setId(dto.getId());
            }
        }
        hiddenDangerService.updateById(entity);
    }

    /**
     * 根据ID获取对象
     *
     * @param id
     * @return
     */
    public HiddenDanger getById(String id) {
        return hiddenDangerService.getById(id);
    }

}
