package com.yinshu.tact.manager;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.SuperviseAutoRule;

/**
 * 自动督办规则表 
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface SuperviseAutoRuleManager {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<SuperviseAutoRule> queryList(SuperviseAutoRule entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<SuperviseAutoRule> queryPageList(SuperviseAutoRule entity);
	
	/**
	 * 保存
	 * @param entity
	 */
	void save(SuperviseAutoRule entity);
	
	/**
	 * 删除
	 * @param id
	 */
	
	void remove(String id);
	
	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);
	
	/**
	 * 更新
	 * @param entity
	 */
	void update(SuperviseAutoRule entity);
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	SuperviseAutoRule getById(String id);

}

