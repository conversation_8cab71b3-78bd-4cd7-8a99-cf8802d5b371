package com.yinshu.tact.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.SuperviseManualRule;
import com.yinshu.tact.dao.SuperviseManualRuleDao;
import com.yinshu.tact.service.SuperviseManualRuleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 手动督办规则表 
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
public class SuperviseManualRuleServiceImpl extends ServiceImpl<SuperviseManualRuleDao, SuperviseManualRule> implements SuperviseManualRuleService {

	
	@Autowired
	private SuperviseManualRuleDao superviseManualRuleDao;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<SuperviseManualRule> queryList(SuperviseManualRule entity) {
		return superviseManualRuleDao.queryList(entity);
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<SuperviseManualRule> queryPageList(SuperviseManualRule entity) {
		return superviseManualRuleDao.queryPageList(entity.toPage(), entity);
	}
}
