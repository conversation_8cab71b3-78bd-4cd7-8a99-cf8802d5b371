package com.yinshu.tact.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.HiddenDanger;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 隐患整改跟踪表（包含发起和完成阶段） 
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
public interface HiddenDangerService extends IService<HiddenDanger> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<HiddenDanger> queryList(HiddenDanger entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<HiddenDanger> queryPageList(HiddenDanger entity);

	/**
	 * 根据父id查询正在整改的隐患
	 * @param parentId
	 * @return
	 */
    HiddenDanger getByParentId(String parentId);
}
