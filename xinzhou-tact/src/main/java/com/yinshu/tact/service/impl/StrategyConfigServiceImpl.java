package com.yinshu.tact.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.yinshu.tact.dao.StrategyConfigMapper;
import com.yinshu.tact.entity.StrategyConfig;
import com.yinshu.tact.entity.VO.StrategyConfigVO;
import com.yinshu.tact.service.StrategyConfigService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class StrategyConfigServiceImpl extends ServiceImpl<StrategyConfigMapper, StrategyConfig> implements StrategyConfigService {

    @Override
    public List<StrategyConfigVO> listByType(Integer type) {
        return baseMapper.listByType(type);
    }

}
