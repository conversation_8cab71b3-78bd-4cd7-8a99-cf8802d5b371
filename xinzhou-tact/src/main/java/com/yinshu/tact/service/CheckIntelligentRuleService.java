package com.yinshu.tact.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.CheckIntelligentRule;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 智能查岗规则表 
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
public interface CheckIntelligentRuleService extends IService<CheckIntelligentRule> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<CheckIntelligentRule> queryList(CheckIntelligentRule entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<CheckIntelligentRule> queryPageList(CheckIntelligentRule entity);

    List<String> getAllEnterpriseName();
}
