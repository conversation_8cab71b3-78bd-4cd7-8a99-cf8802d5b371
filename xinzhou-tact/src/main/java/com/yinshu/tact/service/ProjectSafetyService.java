package com.yinshu.tact.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.dto.ProjectSafetyDTO;
import com.yinshu.tact.entity.dto.HazardDiscoveryTimeDTO;
import com.yinshu.tact.entity.dto.SafetyQualityDetailDTO;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 项目安全服务接口
 * <AUTHOR>
 * @since 2025-07-21
 */
public interface ProjectSafetyService {

    /**
     * 查询安全隐患整改列表
     * @param projectSafetyDTO
     * @return
     */
    List<ProjectSafetyDTO> selectProjectSafetyList(ProjectSafetyDTO projectSafetyDTO);

    /**
     * 查询隐患发现时间列表
     * @param hazardDiscoveryTimeDTO
     * @return
     */
    List<HazardDiscoveryTimeDTO> selectHazardDiscoveryTimeList(HazardDiscoveryTimeDTO hazardDiscoveryTimeDTO);

    /**
     * 分页查询安全隐患整改列表
     * @param projectSafetyDTO
     * @return
     */
    IPage<ProjectSafetyDTO> selectProjectSafetyPage(ProjectSafetyDTO projectSafetyDTO);

    /**
     * 分页查询隐患发现时间列表
     * @param hazardDiscoveryTimeDTO
     * @return
     */
    IPage<HazardDiscoveryTimeDTO> selectHazardDiscoveryTimePage(HazardDiscoveryTimeDTO hazardDiscoveryTimeDTO);

    /**
     * 根据ID查询安全质量检查详情
     * @param id 安全质量检查ID
     * @return
     */
    SafetyQualityDetailDTO selectSafetyQualityById(String id);

    /**
     * 根据项目ID查询所有隐患名称
     * @param projectId
     * @return
     */
    List<HazardDiscoveryTimeDTO> getHazardTypeByProjectId(String projectId, Collection<String> codes);

    /**
     * 根据隐患详情id获取隐患名称
     * @param collect
     * @return
     */
    List<HazardDiscoveryTimeDTO> listHazardNameByIds(Set<String> collect);
}
