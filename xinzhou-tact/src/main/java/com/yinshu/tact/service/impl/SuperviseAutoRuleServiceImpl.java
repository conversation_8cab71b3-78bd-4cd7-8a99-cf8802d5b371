package com.yinshu.tact.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.SuperviseAutoRule;
import com.yinshu.tact.dao.SuperviseAutoRuleDao;
import com.yinshu.tact.service.SuperviseAutoRuleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 自动督办规则表 
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
public class SuperviseAutoRuleServiceImpl extends ServiceImpl<SuperviseAutoRuleDao, SuperviseAutoRule> implements SuperviseAutoRuleService {

	
	@Autowired
	private SuperviseAutoRuleDao superviseAutoRuleDao;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<SuperviseAutoRule> queryList(SuperviseAutoRule entity) {
		return superviseAutoRuleDao.queryList(entity);
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<SuperviseAutoRule> queryPageList(SuperviseAutoRule entity) {
		return superviseAutoRuleDao.queryPageList(entity.toPage(), entity);
	}
}
