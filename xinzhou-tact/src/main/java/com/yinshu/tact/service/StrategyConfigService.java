package com.yinshu.tact.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yinshu.tact.entity.StrategyConfig;
import com.yinshu.tact.entity.VO.StrategyConfigVO;


import java.util.List;

/**
 * 策略配置表 Service 接口
 * <AUTHOR>
 */
public interface StrategyConfigService extends IService<StrategyConfig> {

    /**
     * 根据类型查询策略配置列表
     * @param type 类型(1:区县 2:运营商 3:企业)
     * @return 策略配置列表
     */
    List<StrategyConfigVO> listByType(Integer type);

}
