package com.yinshu.tact.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.SuperviseManualRule;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 手动督办规则表 
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface SuperviseManualRuleService extends IService<SuperviseManualRule> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<SuperviseManualRule> queryList(SuperviseManualRule entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<SuperviseManualRule> queryPageList(SuperviseManualRule entity);

}
