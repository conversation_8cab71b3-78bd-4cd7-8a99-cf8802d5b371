package com.yinshu.tact.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.CheckRegularRule;
import com.yinshu.tact.dao.CheckRegularRuleDao;
import com.yinshu.tact.service.CheckRegularRuleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 定期自动查岗规则表 
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Service
public class CheckRegularRuleServiceImpl extends ServiceImpl<CheckRegularRuleDao, CheckRegularRule> implements CheckRegularRuleService {

	
	@Autowired
	private CheckRegularRuleDao checkRegularRuleDao;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<CheckRegularRule> queryList(CheckRegularRule entity) {
		return checkRegularRuleDao.queryList(entity);
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<CheckRegularRule> queryPageList(CheckRegularRule entity) {
		return checkRegularRuleDao.queryPageList(entity.toPage(), entity);
	}

	@Override
	public List<String> getAllEnterpriseName() {
		return checkRegularRuleDao.getAllEnterpriseName();
	}
}
