package com.yinshu.tact.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.CheckIntelligentRule;
import com.yinshu.tact.dao.CheckIntelligentRuleDao;
import com.yinshu.tact.service.CheckIntelligentRuleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 智能查岗规则表 
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Service
public class CheckIntelligentRuleServiceImpl extends ServiceImpl<CheckIntelligentRuleDao, CheckIntelligentRule> implements CheckIntelligentRuleService {

	
	@Autowired
	private CheckIntelligentRuleDao checkIntelligentRuleDao;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<CheckIntelligentRule> queryList(CheckIntelligentRule entity) {
		return checkIntelligentRuleDao.queryList(entity);
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<CheckIntelligentRule> queryPageList(CheckIntelligentRule entity) {
		return checkIntelligentRuleDao.queryPageList(entity.toPage(), entity);
	}

	@Override
	public List<String> getAllEnterpriseName() {
		return checkIntelligentRuleDao.getAllEnterpriseName();
	}
}
