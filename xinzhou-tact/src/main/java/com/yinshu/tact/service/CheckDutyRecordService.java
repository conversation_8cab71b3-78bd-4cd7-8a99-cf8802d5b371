package com.yinshu.tact.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.CheckDutyRecord;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 查岗记录表 
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
public interface CheckDutyRecordService extends IService<CheckDutyRecord> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<CheckDutyRecord> queryList(CheckDutyRecord entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<CheckDutyRecord> queryPageList(CheckDutyRecord entity);

	/**
	 * 获取所有企业名称
	 * @return
	 */
    List<String> getAllEnterpriseName();

	/**
	 * 获取指定企业名称的条数
	 * @param enterpriseNames
	 * @return
	 */
    List<CheckDutyRecord> getCountByEnterpriseNames(List<String> enterpriseNames);
}
