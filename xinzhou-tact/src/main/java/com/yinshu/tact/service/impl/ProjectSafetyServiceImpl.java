package com.yinshu.tact.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.dao.ProjectSafetyDao;
import com.yinshu.tact.entity.dto.ProjectSafetyDTO;
import com.yinshu.tact.entity.dto.HazardDiscoveryTimeDTO;
import com.yinshu.tact.entity.dto.SafetyQualityDetailDTO;
import com.yinshu.tact.service.ProjectSafetyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 项目安全服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Service
public class ProjectSafetyServiceImpl implements ProjectSafetyService {

    @Autowired
    private ProjectSafetyDao projectSafetyDao;

    /**
     * 查询安全隐患整改列表
     *
     * @param projectSafetyDTO
     * @return
     */
    @Override
    public List<ProjectSafetyDTO> selectProjectSafetyList(ProjectSafetyDTO projectSafetyDTO) {
        return projectSafetyDao.selectProjectSafetyList(projectSafetyDTO);
    }

    /**
     * 查询隐患发现时间列表
     *
     * @param hazardDiscoveryTimeDTO
     * @return
     */
    @Override
    public List<HazardDiscoveryTimeDTO> selectHazardDiscoveryTimeList(HazardDiscoveryTimeDTO hazardDiscoveryTimeDTO) {
        return projectSafetyDao.selectHazardDiscoveryTimeList(hazardDiscoveryTimeDTO);
    }

    /**
     * 分页查询安全隐患整改列表
     *
     * @param projectSafetyDTO
     * @return
     */
    @Override
    public IPage<ProjectSafetyDTO> selectProjectSafetyPage(ProjectSafetyDTO projectSafetyDTO) {
        return projectSafetyDao.selectProjectSafetyPage(projectSafetyDTO.toPage(), projectSafetyDTO);
    }

    /**
     * 分页查询隐患发现时间列表
     *
     * @param hazardDiscoveryTimeDTO
     * @return
     */
    @Override
    public IPage<HazardDiscoveryTimeDTO> selectHazardDiscoveryTimePage(HazardDiscoveryTimeDTO hazardDiscoveryTimeDTO) {
        return projectSafetyDao.selectHazardDiscoveryTimePage(hazardDiscoveryTimeDTO.toPage(), hazardDiscoveryTimeDTO);
    }

    /**
     * 根据ID查询安全质量检查详情
     *
     * @param id 安全质量检查ID
     * @return
     */
    @Override
    public SafetyQualityDetailDTO selectSafetyQualityById(String id) {
        return projectSafetyDao.selectSafetyQualityById(id);
    }

    /**
     * 根据项目ID查询所有隐患名称
     *
     * @param projectId
     * @return
     */
    @Override
    public List<HazardDiscoveryTimeDTO> getHazardTypeByProjectId(String projectId, Collection<String> codes) {
        return projectSafetyDao.getHazardTypeByProjectId(projectId, codes);
    }

    /**
     * 根据隐患详情id获取隐患名称
     * @param collect
     * @return
     */
    @Override
    public List<HazardDiscoveryTimeDTO> listHazardNameByIds(Set<String> collect) {
        return projectSafetyDao.listHazardNameByIds(collect);
    }
}
