package com.yinshu.tact.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinshu.tact.entity.HiddenDanger;
import com.yinshu.tact.dao.HiddenDangerDao;
import com.yinshu.tact.service.HiddenDangerService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 隐患整改跟踪表（包含发起和完成阶段） 
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Service
public class HiddenDangerServiceImpl extends ServiceImpl<HiddenDangerDao, HiddenDanger> implements HiddenDangerService {

	
	@Autowired
	private HiddenDangerDao hiddenDangerDao;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<HiddenDanger> queryList(HiddenDanger entity) {
		return hiddenDangerDao.queryList(entity);
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<HiddenDanger> queryPageList(HiddenDanger entity) {
		return hiddenDangerDao.queryPageList(entity.toPage(), entity);
	}

	@Override
	public HiddenDanger getByParentId(String parentId) {
		return hiddenDangerDao.getByParentId(parentId);
	}
}
