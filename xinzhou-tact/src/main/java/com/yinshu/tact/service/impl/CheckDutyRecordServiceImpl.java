package com.yinshu.tact.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.CheckDutyRecord;
import com.yinshu.tact.dao.CheckDutyRecordDao;
import com.yinshu.tact.service.CheckDutyRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 查岗记录表 
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Service
public class CheckDutyRecordServiceImpl extends ServiceImpl<CheckDutyRecordDao, CheckDutyRecord> implements CheckDutyRecordService {

	
	@Autowired
	private CheckDutyRecordDao checkDutyRecordDao;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<CheckDutyRecord> queryList(CheckDutyRecord entity) {
		return checkDutyRecordDao.queryList(entity);
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<CheckDutyRecord> queryPageList(CheckDutyRecord entity) {
		return checkDutyRecordDao.queryPageList(entity.toPage(), entity);
	}

	@Override
	public List<String> getAllEnterpriseName() {
		return checkDutyRecordDao.getAllEnterpriseName();
	}

	@Override
	public List<CheckDutyRecord> getCountByEnterpriseNames(List<String> enterpriseNames) {
		return checkDutyRecordDao.getCountByEnterpriseNames(enterpriseNames);
	}
}
