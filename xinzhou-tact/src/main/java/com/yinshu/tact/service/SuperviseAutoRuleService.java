package com.yinshu.tact.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.SuperviseAutoRule;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 自动督办规则表 
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface SuperviseAutoRuleService extends IService<SuperviseAutoRule> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<SuperviseAutoRule> queryList(SuperviseAutoRule entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<SuperviseAutoRule> queryPageList(SuperviseAutoRule entity);

}
