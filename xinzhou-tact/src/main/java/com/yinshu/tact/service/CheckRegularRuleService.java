package com.yinshu.tact.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.CheckRegularRule;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 定期自动查岗规则表 
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
public interface CheckRegularRuleService extends IService<CheckRegularRule> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<CheckRegularRule> queryList(CheckRegularRule entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<CheckRegularRule> queryPageList(CheckRegularRule entity);

    List<String> getAllEnterpriseName();
}
