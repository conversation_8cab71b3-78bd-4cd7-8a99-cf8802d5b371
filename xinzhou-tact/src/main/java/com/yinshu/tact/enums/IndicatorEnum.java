package com.yinshu.tact.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/****
 * 指标项枚举类
 * <AUTHOR>
 */
@Getter
public enum IndicatorEnum {

    VEHICLE_OFFLINE_DURATION("vehicleOfflineDuration", "车辆离线时长","car_offline_time"),
    PLATFORM_CONNECTIVITY_RATE("platformConnectivityRate", "平台连通率","unicom_rate"),
    VEHICLE_ONLINE_RATE("vehicleOnlineRate", "车辆入网率","car_network_access_rate"),
    VEHICLE_ONLINE_PERCENTAGE("vehicleOnlinePercentage", "车辆上线率","car_online_rate"),
    TRAJECTORY_INTEGRITY_RATE("trajectoryIntegrityRate", "轨迹完整率","trajectory_rate"),
    DATA_QUALIFICATION_RATE("dataQualificationRate", "数据合格率","qualified_rate"),
    SATELLITE_POSITIONING_DRIFT_RATE("satellitePositioningDriftRate", "卫星定位漂移率","gps_drift_rate"),
    PLATFORM_INSPECTION_RESPONSE_RATE("platformInspectionResponseRate", "平台查岗响应率","platform_check_responsivity"),
    AVERAGE_FATIGUE_DRIVING_DURATION("averageFatigueDrivingDuration", "平均疲劳驾驶时长","fatigue_driving_avg"),
    PROHIBITED_TIME_DRIVING_TIMES("prohibitedTimeDrivingTimes", "禁行时段行驶次数","prohibition_time_num"),
    AVERAGE_SPEEDING_TIMES("averageSpeedingTimes", "平均超速次数","over_speed_avg");

    private final String code;
    private final String name;
    private final String dataName;

    IndicatorEnum(String code, String name, String dataName) {
        this.code = code;
        this.name = name;
        this.dataName = dataName;
    }
    private static final Map<String, String> codeToNameMap = new HashMap<>();

    private static final Map<String, String> nameToDataNameMap = new HashMap<>();

    static {
        for (IndicatorEnum indicator : IndicatorEnum.values()) {
            codeToNameMap.put(indicator.code, indicator.name);
        }
        for (IndicatorEnum indicator : IndicatorEnum.values()) {
            nameToDataNameMap.put(indicator.code, indicator.dataName);
        }
    }


    /**
     * 根据code查找对应的name
     * @param code 枚举code
     * @return 对应的name，如果未找到则返回null
     */
    public static String getNameByCode(String code) {
        return codeToNameMap.get(code);
    }

    /**
     * 根据name查找对应的dataName
     * @param name 枚举name
     * @return 对应的name，如果未找到则返回null
     */
    public static String getDataNameByName(String name) {
        return nameToDataNameMap.get(name);
    }
}
