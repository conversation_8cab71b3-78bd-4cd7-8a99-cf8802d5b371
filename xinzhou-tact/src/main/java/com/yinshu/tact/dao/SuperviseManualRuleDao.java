package com.yinshu.tact.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.SuperviseManualRule;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 
 * 手动督办规则表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface SuperviseManualRuleDao extends BaseMapper<SuperviseManualRule> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<SuperviseManualRule> queryList(@Param("entity") SuperviseManualRule entity);

	/**
	* 条件分页查询
	* @param page
	* @param entity
	* @return
	*/
	IPage<SuperviseManualRule> queryPageList(IPage<SuperviseManualRule> page, @Param("entity") SuperviseManualRule entity);

}
