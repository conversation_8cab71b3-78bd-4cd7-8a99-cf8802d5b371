package com.yinshu.tact.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.HiddenDanger;
import com.yinshu.tact.entity.dto.ProjectSafetyDTO;
import com.yinshu.tact.entity.dto.HazardDiscoveryTimeDTO;
import com.yinshu.tact.entity.dto.SafetyQualityDetailDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface ProjectSafetyDao extends BaseMapper<HiddenDanger> {

    /**
     * 安全隐患整改 list
     *
     * @param projectSafetyDTO
     * @return
     */
    List<ProjectSafetyDTO> selectProjectSafetyList(ProjectSafetyDTO projectSafetyDTO);

    /**
     * 隐患发现时间 list
     *
     * @param hazardDiscoveryTimeDTO
     * @return
     */
    List<HazardDiscoveryTimeDTO> selectHazardDiscoveryTimeList(@Param("entity") HazardDiscoveryTimeDTO hazardDiscoveryTimeDTO);

    /**
     * 安全隐患整改 分页查询
     *
     * @param page   分页参数
     * @param entity 查询参数
     * @return
     */
    IPage<ProjectSafetyDTO> selectProjectSafetyPage(IPage<ProjectSafetyDTO> page, @Param("entity") ProjectSafetyDTO entity);

    /**
     * 隐患发现时间 分页查询
     *
     * @param page   分页参数
     * @param entity 查询参数
     * @return
     */
    IPage<HazardDiscoveryTimeDTO> selectHazardDiscoveryTimePage(IPage<HazardDiscoveryTimeDTO> page, @Param("entity") HazardDiscoveryTimeDTO entity);

    /**
     * 根据ID查询安全质量检查详情
     *
     * @param id 安全质量检查ID
     * @return
     */
    SafetyQualityDetailDTO selectSafetyQualityById(@Param("id") String id);

    /**
     * 根据项目ID查询所有隐患名称
     *
     * @param projectId
     * @return
     */
    List<HazardDiscoveryTimeDTO> getHazardTypeByProjectId(@Param("projectId") String projectId, @Param("codes") Collection<String> codes);

    /**
     * 根据隐患详情id获取隐患名称
     * @param collect
     * @return
     */
    List<HazardDiscoveryTimeDTO> listHazardNameByIds(@Param("collect") Set<String> collect);
}
