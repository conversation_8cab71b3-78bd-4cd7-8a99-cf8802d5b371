package com.yinshu.tact.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.SuperviseAutoRule;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 
 * 自动督办规则表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface SuperviseAutoRuleDao extends BaseMapper<SuperviseAutoRule> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<SuperviseAutoRule> queryList(@Param("entity") SuperviseAutoRule entity);

	/**
	* 条件分页查询
	* @param page
	* @param entity
	* @return
	*/
	IPage<SuperviseAutoRule> queryPageList(IPage<SuperviseAutoRule> page, @Param("entity") SuperviseAutoRule entity);

}
