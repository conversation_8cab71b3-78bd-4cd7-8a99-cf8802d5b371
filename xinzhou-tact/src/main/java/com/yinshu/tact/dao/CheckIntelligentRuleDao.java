package com.yinshu.tact.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.CheckIntelligentRule;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 
 * 智能查岗规则表
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
public interface CheckIntelligentRuleDao extends BaseMapper<CheckIntelligentRule> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<CheckIntelligentRule> queryList(@Param("entity") CheckIntelligentRule entity);

	/**
	* 条件分页查询
	* @param page
	* @param entity
	* @return
	*/
	IPage<CheckIntelligentRule> queryPageList(IPage<CheckIntelligentRule> page, @Param("entity") CheckIntelligentRule entity);

    List<String> getAllEnterpriseName();
}
