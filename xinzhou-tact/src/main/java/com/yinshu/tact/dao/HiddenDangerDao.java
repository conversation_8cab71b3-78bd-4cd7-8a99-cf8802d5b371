package com.yinshu.tact.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.HiddenDanger;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 
 * 隐患整改跟踪表（包含发起和完成阶段）
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
public interface HiddenDangerDao extends BaseMapper<HiddenDanger> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<HiddenDanger> queryList(@Param("entity") HiddenDanger entity);

	/**
	* 条件分页查询
	* @param page
	* @param entity
	* @return
	*/
	IPage<HiddenDanger> queryPageList(IPage<HiddenDanger> page, @Param("entity") HiddenDanger entity);

	/**
	 * 根据父id查询正在整改的隐患
	 * @param parentId
	 * @return
	 */
    HiddenDanger getByParentId(@Param("parentId") String parentId);
}
