package com.yinshu.tact.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.CheckDutyRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 
 * 查岗记录表
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
public interface CheckDutyRecordDao extends BaseMapper<CheckDutyRecord> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<CheckDutyRecord> queryList(@Param("entity") CheckDutyRecord entity);

	/**
	* 条件分页查询
	* @param page
	* @param entity
	* @return
	*/
	IPage<CheckDutyRecord> queryPageList(IPage<CheckDutyRecord> page, @Param("entity") CheckDutyRecord entity);

	/**
	 * 获取所有企业名称
	 * @return
	 */
	List<String> getAllEnterpriseName();

	/**
	 * 获取指每个业名称的的条数
	 * @param enterpriseNames
	 * @return
	 */
    List<CheckDutyRecord> getCountByEnterpriseNames(@Param("enterpriseNames")List<String> enterpriseNames);
}
