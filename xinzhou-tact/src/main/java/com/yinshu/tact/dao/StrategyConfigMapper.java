package com.yinshu.tact.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;


import com.yinshu.tact.entity.StrategyConfig;
import com.yinshu.tact.entity.VO.StrategyConfigVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 策略配置表 Mapper 接口
 */
@Mapper
public interface StrategyConfigMapper extends BaseMapper<StrategyConfig> {


    List<StrategyConfigVO> listByType(Integer type);
}
