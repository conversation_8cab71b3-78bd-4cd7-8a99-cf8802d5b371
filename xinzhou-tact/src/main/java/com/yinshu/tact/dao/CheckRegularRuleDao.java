package com.yinshu.tact.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tact.entity.CheckRegularRule;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 
 * 定期自动查岗规则表
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
public interface CheckRegularRuleDao extends BaseMapper<CheckRegularRule> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<CheckRegularRule> queryList(@Param("entity") CheckRegularRule entity);

	/**
	* 条件分页查询
	* @param page
	* @param entity
	* @return
	*/
	IPage<CheckRegularRule> queryPageList(IPage<CheckRegularRule> page, @Param("entity") CheckRegularRule entity);

    List<String> getAllEnterpriseName();
}
