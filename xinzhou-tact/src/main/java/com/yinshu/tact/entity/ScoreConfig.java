package com.yinshu.tact.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 评分配置表实体类
 */
@Data
@TableName("tiss_score_config")
public class ScoreConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** 指标ID */
    private Integer indicatorId;

    /** 评分等级说明 */
    private String ratingLevel;

    /** 评分 */
    private Integer score;

    /** 左边规则(gt:大于,gte:大于等于,lt:小于,lte:小于等于,eq:等于,neq:不等于) */
    private String leftRule;

    /** 左边规则值 */
    private Integer leftRuleValue;

    /** 右边规则(gt:大于,gte:大于等于,lt:小于,lte:小于等于,eq:等于,neq:不等于) */
    private String rightRule;

    /** 右边规则值 */
    private Integer rightRuleValue;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新时间 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
} 