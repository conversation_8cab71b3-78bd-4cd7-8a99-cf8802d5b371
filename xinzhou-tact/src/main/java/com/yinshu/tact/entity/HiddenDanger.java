package com.yinshu.tact.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yinshu.utils.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * 
 * 隐患整改跟踪表（包含发起和完成阶段）
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@TableName("tocc_hidden_danger")
@Data
public class HiddenDanger extends PageParam<HiddenDanger> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    /**
     * 整改名称
     */
    private String rectificationName;

    /**
     * 整改事项
     */
    private String rectificationItems;

    /**
     * 整改措施：1-约谈教育 2-下发整改通知 3-停业整顿
     */
    private Integer rectificationMeasure;

    /**
     * 整改截止日期
     */
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date rectificationDeadline;

    /**
     * 整改状态：null-待整改 0已超时 1-整改中 2-已完成
     */
    private Integer status;

    /**
     * 完成时间
     */
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date completionTime;

    /**
     * 整改描述
     */
    private String rectificationDescription;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 安全隐患整改ID
     */
    private String parentId;

    /**
     * 整改单
     */
    private String rectificationForm;

    @Override
    public String toString() {
        return "HiddenDanger{" +
            "id=" + id +
            ", rectificationName=" + rectificationName +
            ", rectificationItems=" + rectificationItems +
            ", rectificationMeasure=" + rectificationMeasure +
            ", rectificationDeadline=" + rectificationDeadline +
            ", status=" + status +
            ", completionTime=" + completionTime +
            ", rectificationDescription=" + rectificationDescription +
            ", remark=" + remark +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", createBy=" + createBy +
            ", updateBy=" + updateBy +
            ", parentId=" + parentId +
        "}";
    }
}