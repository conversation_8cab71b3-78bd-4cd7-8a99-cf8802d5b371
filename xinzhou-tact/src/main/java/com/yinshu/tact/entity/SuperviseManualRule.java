package com.yinshu.tact.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yinshu.utils.PageParam;

import java.io.Serializable;

/**
 * 
 * 手动督办规则表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@TableName("tocc_supervise_manual_rule")
public class SuperviseManualRule extends PageParam<SuperviseManualRule> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.NONE)
    private String id;

    /**
     * 关联的报警信息ID
     */
    private String alarmId;

    /**
     * 督办截止时长（分钟）
     */
    private Integer limitMinutes;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getAlarmId() {
        return alarmId;
    }

    public void setAlarmId(String alarmId) {
        this.alarmId = alarmId;
    }
    public Integer getLimitMinutes() {
        return limitMinutes;
    }

    public void setLimitMinutes(Integer limitMinutes) {
        this.limitMinutes = limitMinutes;
    }

    @Override
    public String toString() {
        return "ToccSuperviseManualRule{" +
            "id=" + id +
            ", alarmId=" + alarmId +
            ", limitMinutes=" + limitMinutes +
        "}";
    }
}