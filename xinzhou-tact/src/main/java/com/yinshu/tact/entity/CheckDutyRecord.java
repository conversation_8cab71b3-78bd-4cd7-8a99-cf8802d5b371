package com.yinshu.tact.entity;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yinshu.utils.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * 
 * 查岗记录表
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
@TableName("tocc_check_duty_record")
public class CheckDutyRecord extends PageParam<CheckDutyRecord> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.NONE)
    @ExcelIgnore
    private String id;

    /**
     * 查岗企业名称
     */
    @ExcelIgnore
    private String checkEnterpriseName;

    /**
     * 发起方式（1自动/2智能/3手动）
     */
    @ExcelProperty(value = "发起方式")
    private String initiateType;

    /**
     * 发起人
     */
    @ExcelProperty(value = "发起人")
    private String initiator;

    /**
     * 发起时间
     */
    @ColumnWidth(30)
    @ExcelProperty(value = "发起时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date initiateTime;

    @ExcelIgnore
    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date initiateStartTime;

    @ExcelIgnore
    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date initiateEndTime;

    /**
     * 应答时间
     */
    @ColumnWidth(30)
    @ExcelProperty(value = "应答时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date responseTime;

    /**
     * 查岗应答人
     */
    @ExcelProperty(value = "查岗应答人")
    private String responder;

    /**
     * 应答时长
     */
    @ExcelProperty(value = "应答时长")
    private String responseDuration;

    /**
     * 查岗接收手机号
     */
    @ExcelProperty(value = "查岗接收手机号")
    private String receivePhone;

    /**
     * 查岗应答问题
     */
    @ColumnWidth(30)
    @ExcelProperty(value = "查岗应答问题")
    private String checkContent ;

    /**
     * 查岗应答内容
     */
    @ColumnWidth(30)
    @ExcelProperty(value = "查岗应答内容")
    private String responseContent;

    /**
     * 状态（1正常/2超时等）
     */
    @ExcelProperty(value = "状态")
    private String status;

    /**
     * 创建者
     */
    @ExcelIgnore
    private String createBy;

    /**
     * 创建时间
     */
    @ExcelIgnore
    @JsonFormat(pattern="yyyy-MM-dd HH:mm",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    @ExcelIgnore
    private String updateBy;

    /**
     * 更新时间
     */
    @ExcelIgnore
    @JsonFormat(pattern="yyyy-MM-dd HH:mm",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    @ExcelIgnore
    private String remark;

    @ExcelIgnore
    @TableField(exist = false)
    private String count;


    @Override
    public String toString() {
        return "ToccCheckDutyRecord{" +
            "id=" + id +
            ", checkEnterpriseName=" + checkEnterpriseName +
            ", initiateType=" + initiateType +
            ", initiator=" + initiator +
            ", initiateTime=" + initiateTime +
            ", responseTime=" + responseTime +
            ", responder=" + responder +
            ", responseDuration=" + responseDuration +
            ", receivePhone=" + receivePhone +
            ", responseContent=" + responseContent +
            ", status=" + status +
            ", createBy=" + createBy +
            ", createTime=" + createTime +
            ", updateBy=" + updateBy +
            ", updateTime=" + updateTime +
            ", remark=" + remark +
        "}";
    }
}