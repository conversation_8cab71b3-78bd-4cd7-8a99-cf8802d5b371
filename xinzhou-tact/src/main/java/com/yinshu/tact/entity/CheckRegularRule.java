package com.yinshu.tact.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yinshu.utils.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

/**
 * 
 * 定期自动查岗规则表
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@TableName("tocc_check_regular_rule")
public class CheckRegularRule extends PageParam<CheckRegularRule> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private String id;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 查岗应答人
     */
    private String checkResponder;

    /**
     * 查岗接收手机号
     */
    private String checkReceivePhone;

    /**
     * 查岗内容
     */
    private String checkContent;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 查岗执行时间段
     */
    private String checkExecTimeStr;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }
    public String getCheckResponder() {
        return checkResponder;
    }

    public void setCheckResponder(String checkResponder) {
        this.checkResponder = checkResponder;
    }
    public String getCheckReceivePhone() {
        return checkReceivePhone;
    }

    public void setCheckReceivePhone(String checkReceivePhone) {
        this.checkReceivePhone = checkReceivePhone;
    }
    public String getCheckContent() {
        return checkContent;
    }

    public void setCheckContent(String checkContent) {
        this.checkContent = checkContent;
    }
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    public String getCheckExecTimeStr() {
        return checkExecTimeStr;
    }

    public void setCheckExecTimeStr(String checkExecTimeStr) {
        this.checkExecTimeStr = checkExecTimeStr;
    }

    @Override
    public String toString() {
        return "ToccCheckRegularRule{" +
            "id=" + id +
            ", enterpriseName=" + enterpriseName +
            ", checkResponder=" + checkResponder +
            ", checkReceivePhone=" + checkReceivePhone +
            ", checkContent=" + checkContent +
            ", status=" + status +
            ", createBy=" + createBy +
            ", createTime=" + createTime +
            ", updateBy=" + updateBy +
            ", updateTime=" + updateTime +
            ", remark=" + remark +
            ", checkExecTimeStr=" + checkExecTimeStr +
        "}";
    }
}