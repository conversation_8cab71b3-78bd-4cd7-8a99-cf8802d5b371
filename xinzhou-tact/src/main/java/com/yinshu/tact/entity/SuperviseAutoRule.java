package com.yinshu.tact.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yinshu.utils.PageParam;

import java.io.Serializable;

/**
 * 
 * 自动督办规则表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@TableName("tocc_supervise_auto_rule")
public class SuperviseAutoRule extends PageParam<SuperviseAutoRule> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.NONE)
    private String id;

    /**
     * 报警类型
     */
    private String alarmTypeName;

    /**
     * 自动督办等待时长（分钟）
     */
    private Integer waitingMinutes;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getAlarmTypeName() {
        return alarmTypeName;
    }

    public void setAlarmTypeName(String alarmTypeName) {
        this.alarmTypeName = alarmTypeName;
    }
    public Integer getWaitingMinutes() {
        return waitingMinutes;
    }

    public void setWaitingMinutes(Integer waitingMinutes) {
        this.waitingMinutes = waitingMinutes;
    }

    @Override
    public String toString() {
        return "ToccSuperviseAutoRule{" +
            "id=" + id +
            ", alarmTypeName=" + alarmTypeName +
            ", waitingMinutes=" + waitingMinutes +
        "}";
    }
}