package com.yinshu.tact.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yinshu.utils.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

/**
 * 
 * 智能查岗规则表
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@TableName("tocc_check_intelligent_rule")
public class CheckIntelligentRule extends PageParam<CheckIntelligentRule> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.NONE)
    private String id;

    /**
     * 查岗企业名称
     */
    private String enterpriseName;

    /**
     * 查岗执行条件（如多条规则可存JSON或文本）
     */
    private String checkCondition;

    /**
     * 查岗应答人
     */
    private String responder;

    /**
     * 查岗接收手机号
     */
    private String receivePhone;

    /**
     * 查岗内容
     */
    private String checkContent;

    /**
     * 状态（开启/停用等）
     */
    private String status;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 近一个小时报警处理率低于
     */
    private Double lessThan;

    /**
     * 报警督办率大于
     */
    private Double greaterThan;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }
    public String getCheckCondition() {
        return checkCondition;
    }

    public void setCheckCondition(String checkCondition) {
        this.checkCondition = checkCondition;
    }
    public String getResponder() {
        return responder;
    }

    public void setResponder(String responder) {
        this.responder = responder;
    }
    public String getReceivePhone() {
        return receivePhone;
    }

    public void setReceivePhone(String receivePhone) {
        this.receivePhone = receivePhone;
    }
    public String getCheckContent() {
        return checkContent;
    }

    public void setCheckContent(String checkContent) {
        this.checkContent = checkContent;
    }
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    public Double getLessThan() {
        return lessThan;
    }

    public void setLessThan(Double lessThan) {
        this.lessThan = lessThan;
    }
    public Double getGreaterThan() {
        return greaterThan;
    }

    public void setGreaterThan(Double greaterThan) {
        this.greaterThan = greaterThan;
    }

    @Override
    public String toString() {
        return "ToccCheckIntelligentRule{" +
            "id=" + id +
            ", enterpriseName=" + enterpriseName +
            ", checkCondition=" + checkCondition +
            ", responder=" + responder +
            ", receivePhone=" + receivePhone +
            ", checkContent=" + checkContent +
            ", status=" + status +
            ", createBy=" + createBy +
            ", createTime=" + createTime +
            ", updateBy=" + updateBy +
            ", updateTime=" + updateTime +
            ", remark=" + remark +
            ", lessThan=" + lessThan +
            ", greaterThan=" + greaterThan +
        "}";
    }
}