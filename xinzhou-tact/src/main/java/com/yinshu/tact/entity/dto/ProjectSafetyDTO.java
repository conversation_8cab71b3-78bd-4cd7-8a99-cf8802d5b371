package com.yinshu.tact.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yinshu.utils.PageParam;
import java.io.Serializable;
import java.util.Date;

/**
 * 项目安全DTO - 对应第一张图片的项目列表结构
 * <AUTHOR>
 * @since 2025-07-21
 */
public class ProjectSafetyDTO extends PageParam<ProjectSafetyDTO> implements Serializable {

    /**
     * 序号
     */
    private Integer sequence;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 区县
     */
    private String region;

    /**
     * 立项批文号
     */
    private String approvalDoc;

    /**
     * 工程名称
     */
    private String projectName;

    /**
     * 项目类型
     */
    private String projectType;

    /**
     * 项目位置
     */
    private String projectLocation;

    /**
     * 建设单位
     */
    private String constructionUnit;

    /**
     * 隐患个数
     */
    private Integer hazardCount;

    /**
     * 整改个数
     */
    private Integer rectificationCount;

    /**
     * 整治进度
     */
    private String rectificationProgress;

    public String getRectificationProgress() {
        return rectificationProgress;
    }

    public void setRectificationProgress(String rectificationProgress) {
        this.rectificationProgress = rectificationProgress;
    }

    /**
     * 整改状态：null-待整改 0已超时 1-整改中 2-已完成
     */
    private Integer status;

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType;
    }

    public Integer getRectificationCount() {
        return rectificationCount;
    }

    public void setRectificationCount(Integer rectificationCount) {
        this.rectificationCount = rectificationCount;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getApprovalDoc() {
        return approvalDoc;
    }

    public void setApprovalDoc(String approvalDoc) {
        this.approvalDoc = approvalDoc;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectLocation() {
        return projectLocation;
    }

    public void setProjectLocation(String projectLocation) {
        this.projectLocation = projectLocation;
    }

    public String getConstructionUnit() {
        return constructionUnit;
    }

    public void setConstructionUnit(String constructionUnit) {
        this.constructionUnit = constructionUnit;
    }

    public Integer getHazardCount() {
        return hazardCount;
    }

    public void setHazardCount(Integer hazardCount) {
        this.hazardCount = hazardCount;
    }
}
