package com.yinshu.tact.entity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 策略配置实体类
 * <AUTHOR>
 */
@Data
@TableName("tiss_strategy_config")
public class StrategyConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 类型(1:区县 2:运营商 3:企业)
     */
    private Integer type;

    /**
     * 指标项
     */
    private String indicator;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}