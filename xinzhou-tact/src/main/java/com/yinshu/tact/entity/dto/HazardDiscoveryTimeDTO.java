package com.yinshu.tact.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yinshu.utils.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 隐患详情DTO
 * <AUTHOR>
 * @since 2025-07-21
 */
public class HazardDiscoveryTimeDTO extends PageParam<HazardDiscoveryTimeDTO> implements Serializable {

    /**
     * 序号
     */
    private Integer sequence;

    /**
     * 主键
     */
    private String id;

    /**
     * 项目 ID
     */
    private String projectId;

    /**
     * 隐患发现时间
     */
    @JsonFormat(pattern="yyyy年M月d日",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date hazardDiscoveryTime;

    /**
     * 隐患名称
     */
    private String hazardName;

    /**
     * 隐患类别
     */
    private String hazardCategory;

    /**
     * 隐患级别
     */
    private String hazardLevel;

    /**
     * 隐患描述
     */
    private String hazardDescription;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public Date getHazardDiscoveryTime() {
        return hazardDiscoveryTime;
    }

    public void setHazardDiscoveryTime(Date hazardDiscoveryTime) {
        this.hazardDiscoveryTime = hazardDiscoveryTime;
    }

    public String getHazardName() {
        return hazardName;
    }

    public void setHazardName(String hazardName) {
        this.hazardName = hazardName;
    }

    public String getHazardCategory() {
        return hazardCategory;
    }

    public void setHazardCategory(String hazardCategory) {
        this.hazardCategory = hazardCategory;
    }

    public String getHazardLevel() {
        return hazardLevel;
    }

    public void setHazardLevel(String hazardLevel) {
        this.hazardLevel = hazardLevel;
    }

    public String getHazardDescription() {
        return hazardDescription;
    }

    public void setHazardDescription(String hazardDescription) {
        this.hazardDescription = hazardDescription;
    }
}
