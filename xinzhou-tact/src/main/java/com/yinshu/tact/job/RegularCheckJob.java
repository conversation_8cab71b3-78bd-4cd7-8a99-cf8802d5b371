package com.yinshu.tact.job;

import com.yinshu.tact.entity.CheckDutyRecord;
import com.yinshu.tact.entity.CheckRegularRule;
import com.yinshu.tact.manager.CheckDutyRecordManager;
import com.yinshu.tact.manager.CheckRegularRuleManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

@Component
@Slf4j
public class RegularCheckJob implements ApplicationRunner , DisposableBean {

    @Autowired
    @Lazy
    private CheckRegularRuleManager checkRegularRuleManager;

    @Autowired
    private CheckDutyRecordManager checkDutyRecordManager;

    // 存储每个规则对应的ScheduledFuture，用于动态取消任务
    private Map<String, List<ScheduledFuture<?>>> scheduledFutures = new ConcurrentHashMap<>();

    // 注入任务调度器，使用线程池支持多任务并行
    @Autowired
    private ScheduledExecutorService scheduler;

    // 标记是否已初始化
    private final AtomicBoolean initialized = new AtomicBoolean(false);

    // 应用启动时初始化所有定时任务
    @Override
    public void run(ApplicationArguments args) {
        log.info("初始化定期自动查岗任务");
        refreshAllScheduledTasks();
    }

    // 刷新所有定时任务（可用于动态加载新规则）
    public void refreshAllScheduledTasks() {
        // 确保只初始化一次
        if (!initialized.compareAndSet(false, true)) {
            return;
        }

        try {
            // 清除现有任务
            scheduledFutures.forEach((ruleId, futures) ->
                    futures.forEach(future -> future.cancel(false)));
            scheduledFutures.clear();

            // 获取所有启用的规则
            CheckRegularRule query = new CheckRegularRule();
            query.setStatus("1"); // 1表示启用
            List<CheckRegularRule> ruleList = checkRegularRuleManager.queryList(query);

            // 为每个规则创建定时任务
            for (CheckRegularRule rule : ruleList) {
                scheduleCheckTask(rule);
            }

            log.info("定期自动查岗任务初始化完成，共加载 {} 个规则", ruleList.size());
        } catch (Exception e) {
            log.error("初始化定期自动查岗任务失败", e);
        }
    }

    /**
     * 为指定规则创建定时任务
     * @param rule
     */
    private void scheduleCheckTask(CheckRegularRule rule) {
        List<ScheduledFuture<?>> futures = new ArrayList<>();

        // 解析规则中的时间点字符串
        String[] timePoints = rule.getCheckExecTimeStr().split("、");

        for (String timePoint : timePoints) {
            try {
                // 解析时间点为LocalTime
                LocalTime checkTime = parseTimePoint(timePoint);

                // 计算今天的执行时间点
                LocalDateTime todayExecutionTime = LocalDateTime.of(
                        LocalDate.now(),
                        checkTime
                );

                // 如果今天的执行时间点已过，则安排到明天
                if (todayExecutionTime.isBefore(LocalDateTime.now())) {
                    todayExecutionTime = todayExecutionTime.plusDays(1);
                }

                // 计算距离执行时间的延迟（毫秒）
                long initialDelay = Duration.between(
                        LocalDateTime.now(),
                        todayExecutionTime
                ).toMillis();

                // 创建每天执行一次的定时任务
                ScheduledFuture<?> future = scheduler.scheduleAtFixedRate(
                        () -> executeCheck(rule),
                        initialDelay,
                        Duration.ofDays(1).toMillis(), // 每天执行一次
                        TimeUnit.MILLISECONDS
                );

                futures.add(future);
                log.info("为规则 {} 安排定时任务，时间点: {}, 初始延迟: {}ms",
                        rule.getId(), timePoint, initialDelay);
            } catch (Exception e) {
                log.error("解析时间点失败: {}", timePoint, e);
            }
        }

        // 保存任务引用，用于后续取消
        if (!futures.isEmpty()) {
            scheduledFutures.put(rule.getId(), futures);
        }
    }

    private LocalTime parseTimePoint(String timePoint) {
        // 解析 "HH:mm" 格式的时间点
        String[] parts = timePoint.split(":");
        if (parts.length == 2) {
            int hour = Integer.parseInt(parts[0].trim());
            int minute = Integer.parseInt(parts[1].trim());
            return LocalTime.of(hour, minute);
        }
        throw new IllegalArgumentException("无效的时间点格式: " + timePoint);
    }

    private void executeCheck(CheckRegularRule rule) {
        log.info("执行查岗任务，规则ID：{}，时间点：{}", rule.getId(), rule.getCheckExecTimeStr());

        try {
            // 调用第三方接口查岗
            callThirdPartyApi(rule);

            // 保存查岗记录
            saveCheckDutyRecord(rule);

            log.info("查岗任务执行成功，规则ID：{}", rule.getId());
        } catch (Exception e) {
            log.error("查岗任务执行失败，规则ID：{}", rule.getId(), e);
        }
    }

    private void callThirdPartyApi(CheckRegularRule rule) {
        log.info("调用第三方接口进行查岗开始，规则ID：{}", rule.getId());
        /**
         * to do
         */
        log.info("调用第三方接口进行查岗结束，规则ID：{}", rule.getId());
    }

    private void saveCheckDutyRecord(CheckRegularRule rule) {
        CheckDutyRecord record = new CheckDutyRecord();
        record.setCheckEnterpriseName(rule.getEnterpriseName());//查岗企业名称
        record.setCheckContent(rule.getCheckContent());//查岗内容(问题)
        record.setStatus("1");//正常
        record.setInitiator(rule.getCreateBy());//发起人
        record.setInitiateType("1");//自动发起
        record.setInitiateTime(new Date());//发起时间
        record.setReceivePhone(rule.getCheckReceivePhone());//手机号
        record.setResponder(record.getResponder());
        checkDutyRecordManager.save(record);
    }


    // 新增规则时调用
    public void addScheduledTask(CheckRegularRule rule) {
        if ("1".equals(rule.getStatus())) { // 只处理启用的规则
            scheduleCheckTask(rule);
        }
    }

    // 删除规则时调用
    public void removeScheduledTask(String ruleId) {
        List<ScheduledFuture<?>> futures = scheduledFutures.remove(ruleId);
        if (futures != null) {
            futures.forEach(future -> future.cancel(false));
            log.info("已取消规则 {} 的定时任务", ruleId);
        }
    }

    // 修改规则时调用
    public void updateScheduledTask(CheckRegularRule rule) {
        // 先删除旧任务
        removeScheduledTask(rule.getId());

        // 如果规则是启用状态，则添加新任务
        if ("1".equals(rule.getStatus())) {
            scheduleCheckTask(rule);
        }
    }

    // 应用关闭时清理资源
    @Override
    public void destroy() {
        log.info("正在关闭定期查岗任务...");
        scheduledFutures.forEach((ruleId, futures) ->
                futures.forEach(future -> future.cancel(true)));
        scheduledFutures.clear();
        // 关闭线程池（关键！）
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
        }

        log.info("定期查岗任务已关闭");
    }

    // 提供手动刷新方法
    public void manualRefresh() {
        refreshAllScheduledTasks();
    }
}
