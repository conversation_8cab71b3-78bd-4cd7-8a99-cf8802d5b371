package com.yinshu.tact.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.exception.RestfulAPIException;
import com.yinshu.sys.entity.Setting;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.sys.manager.SettingManager;
import com.yinshu.tact.common.BigDataResultVO;
import com.yinshu.tact.common.RemoteUrlConstants;
import com.yinshu.tact.common.RequestFilterBuilder;
import com.yinshu.tact.entity.SuperviseAutoRule;
import com.yinshu.tact.service.SuperviseAutoRuleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 报警督办定时任务
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "alarm.supervision.enabled", havingValue = "true", matchIfMissing = true)
public class AlarmSupervisionJob {

    @Autowired
    private SuperviseAutoRuleService superviseAutoRuleService;

    @Resource
    private DvisualHttpTemplate template;

    @Autowired
    @Qualifier("alarmAsyncPool")
    private Executor alarmAsyncPool;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private SettingManager settingManager;
    private final static String URL = "/api/ads_road_safe_alarm_supervision/YjmkH33r0OFeGXg9Amc7";
    private final static RestTemplate restTemplate = new RestTemplate();
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final HttpHeaders HTTP_HEADERS = new HttpHeaders();
    private static final int PAGE_SIZE = 200; // 每页处理数量
    private static final String REDIS_PROCESSED_KEY_PREFIX = "alarm:supervision:processed:";

    static {
        HTTP_HEADERS.setContentType(MediaType.APPLICATION_JSON);
    }

    private static final String REDIS_KEY = "alarm:supervise:rules";

    private static final long CACHE_EXPIRE_TIME = 3600; // 1小时

    private static final JSONObject _query;

    static {
        _query = new JSONObject();
        _query.put("s", 0);
        _query.put("n", PAGE_SIZE);
        _query.put("process_status", 1);
    }

    /**
     * 每10分钟执行一次，使用fixedDelay确保上一次执行完成后再开始下一次
     */
    @Scheduled(fixedDelay = 600000)
    public void checkOverWaitTime() {
        log.info("开始执行超时报警检查任务");
        long startTime = System.currentTimeMillis();

        try {
            // 1. 从Redis获取自动督办规则
            Map<Object, Object> waitingTimeMap = redisTemplate.opsForHash().entries(REDIS_KEY);
            Map<String, Integer> ruleMap = new HashMap<>();
            // 2. 如果缓存为空，记录警告
            if (waitingTimeMap.isEmpty()) {
                log.warn("自动督办规则缓存为空，可能首次启动或缓存过期");
                // 查询自动督办规则
                List<SuperviseAutoRule> ruleList = superviseAutoRuleService.queryList(new SuperviseAutoRule());
                ruleMap = ruleList.stream()
                        .collect(Collectors.toMap(
                                SuperviseAutoRule::getAlarmTypeName,
                                SuperviseAutoRule::getWaitingMinutes,
                                (existing, replacement) -> existing // 处理重复键的策略
                        ));
                // 4. 重建缓存（异步执行，避免影响主线程）
                CompletableFuture.runAsync(() -> rebuildRuleCache(ruleList));
            } else {
                // 3. 转换为需要的Map结构
                ruleMap = waitingTimeMap.entrySet().stream()
                        .collect(Collectors.toMap(
                                entry -> entry.getKey().toString(),
                                entry -> Integer.valueOf(entry.getValue().toString())
                        ));
            }
            // 分页处理报警数据
            boolean hasMoreData = true;

            while (hasMoreData) {
                JSONObject jsonObject = getAlarmSupervisionTableData(_query);
                JSONArray list = jsonObject.getJSONArray("list");

                if (list.size() < PAGE_SIZE) {
                    hasMoreData = false;
                }

                log.info("当前共{}条数据", list.size());
                processAlarmList(list, ruleMap);
//                pageNum++;  //不需要++,始终查询第一页,因为异步处理数据,待处理状态一直改变
            }

            log.info("超时报警检查任务执行完成，耗时:{}ms", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("执行超时报警检查任务失败", e);
        }
    }

    private void processAlarmList(JSONArray list, Map<String, Integer> ruleMap) {
        LocalDateTime now = LocalDateTime.now();

        for (int i = 0; i < list.size(); i++) {
            try {
                JSONObject alarm = list.getJSONObject(i);
                String alarmId = alarm.getString("id");

                // 检查是否已处理过
                if (isAlarmProcessed(alarmId)) {
                    continue;
                }

                // 二次校验状态
                String currentStatus = alarm.getString("process_status");
                if (!"1".equals(currentStatus)) {
                    log.debug("报警id:{}状态已变更为{},跳过处理", alarmId, currentStatus);
                    continue;
                }

                // 解析报警时间
                String alarmTimeStr = alarm.getString("alarmtime");
                if (StringUtils.isBlank(alarmTimeStr)) {
                    log.warn("报警时间为空，跳过处理: alarmId={}", alarm.getString("id"));
                    continue;
                }
                LocalDateTime alarmTime = LocalDateTime.parse(alarmTimeStr, DATE_TIME_FORMATTER);

                // 获取该报警类型对应的“等待时长”
                String alarmType = alarm.getString("alarmtype");
                Integer waitMinutes = ruleMap.get(alarmType);

                if (waitMinutes == null) {
                    log.info("报警类型:{}未找到对应的等待时长，请检查规则配置！", alarmType);
                    continue;
                }

                // 计算已等待时长（分钟）
                long elapsedMinutes = ChronoUnit.MINUTES.between(alarmTime, now);

                // 超时判断
                if (elapsedMinutes > waitMinutes) {
                    // 异步处理
                    alarmAsyncPool.execute(() -> processOverdueAlarm(alarmId, alarm));
                }
            } catch (Exception e) {
                log.error("处理报警数据时发生异常", e);
            }
        }
    }

    private void processOverdueAlarm(String alarmId, JSONObject alarm) {
        try {
            // 标记为已处理
            markAlarmAsProcessed(alarmId);

            // 调用接口修改状态
            updateSupervision(alarmId, 2);

            // 发送消息通知
            doSendMessage(alarm);
        } catch (Exception e) {
            log.error("处理超时报警失败，id:{}", alarmId, e);
        }
    }

    private boolean isAlarmProcessed(String alarmId) {
        String redisKey = REDIS_PROCESSED_KEY_PREFIX + alarmId;
        return Boolean.TRUE.equals(redisTemplate.hasKey(redisKey));
    }

    private void markAlarmAsProcessed(String alarmId) {
        String redisKey = REDIS_PROCESSED_KEY_PREFIX + alarmId;
//        10分钟过期
        redisTemplate.opsForValue().set(redisKey, "1", 10, TimeUnit.MINUTES);
    }

    /**
     * 大数据查询报警督办数据
     */
    public JSONObject getAlarmSupervisionTableData(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        if (query.containsKey("filter")) {
            String filter = query.get("filter").toString();
            filter = filter + " order by alarmtime ASC"; // 先处理最早的报警
            query.put("filter", filter);
        }

        JSONObject post = template.post(RemoteUrlConstants.ads_road_safe_alarm_supervision_table, query);
        JSONArray list = post.getJSONArray("list");

        // 计算等待时间
        for (int i = 0; i < list.size(); i++) {
            JSONObject jsonObject = list.getJSONObject(i);
            String alarmtime = jsonObject.getString("alarmtime");

            if (alarmtime != null && !alarmtime.isEmpty()) {
                LocalDateTime localDateTime = LocalDateTime.parse(alarmtime, DATE_TIME_FORMATTER);
                long between = ChronoUnit.MINUTES.between(localDateTime, LocalDateTime.now());
                jsonObject.put("wait_time", between + "分钟");
            }
        }

        return post;
    }

    public BigDataResultVO updateSupervision(String id, int process_status) {
        Map<String, Object> map = new HashMap<>();
        map.put("process_status", process_status);
        map.put("id", id);
        String str = JSON.toJSONString(map);

        log.info("调用大数据报警督办接口数据:{}", str);
        HttpEntity<String> requestEntity = new HttpEntity<>(str, HTTP_HEADERS);
        Setting settingBaseUrl = settingManager.getByCode("dvisual-baseUrl");

        try {
            ResponseEntity<String> response = restTemplate.postForEntity(settingBaseUrl.getParmValue() + URL, requestEntity, String.class);
            BigDataResultVO result = JSONObject.parseObject(response.getBody(), BigDataResultVO.class);

            if ("200".equals(Objects.requireNonNull(result).getState())) {
                log.info("调用大数据报警督办接口修改成功，响应:{}", response);
                return result;
            } else {
                log.error("调用大数据报警督办接口修改失败，响应:{}", response.getBody());
                throw new RestfulAPIException("调用大数据报警督办接口修改失败:" + result.getMessage());
            }
        } catch (Exception e) {
            log.error("调用大数据报警督办接口发生异常，请求数据:{}", str, e);
            throw new RestfulAPIException("调用大数据报警督办接口发生异常:" + e.getMessage());
        }
    }

    /**
     * 发送消息通知
     */
    public void doSendMessage(JSONObject alarm) {
        try {
            log.info("开始发送消息，报警ID:{}", alarm.getString("id"));
            // 实际消息发送逻辑
            log.info("消息发送结束");
        } catch (Exception e) {
            log.error("发送消息通知失败", e);
        }
    }

    private void rebuildRuleCache(List<SuperviseAutoRule> rules) {
        try {
            // 清空旧缓存
            redisTemplate.delete(REDIS_KEY);

            // 批量写入新缓存
            Map<String, String> cacheMap = rules.stream()
                    .collect(Collectors.toMap(
                            SuperviseAutoRule::getAlarmTypeName,
                            rule -> String.valueOf(rule.getWaitingMinutes())
                    ));

            redisTemplate.opsForHash().putAll(REDIS_KEY, cacheMap);

            // 设置过期时间
            redisTemplate.expire(REDIS_KEY, CACHE_EXPIRE_TIME, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("重建自动督办规则缓存失败", e);
        }
    }
}