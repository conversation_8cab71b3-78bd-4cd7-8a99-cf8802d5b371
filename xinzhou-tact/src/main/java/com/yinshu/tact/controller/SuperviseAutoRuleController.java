package com.yinshu.tact.controller;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.common.ApiConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.*;
import com.yinshu.tact.entity.SuperviseAutoRule;
import com.yinshu.tact.manager.SuperviseAutoRuleManager;

import org.springframework.web.bind.annotation.RestController;

/**
 *
 * 自动督办规则表 
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@RestController
@RequestMapping(ApiConstant.API_TACP_PREFIX + "/superviseAutoRule")
public class SuperviseAutoRuleController {

	@Autowired
	private SuperviseAutoRuleManager superviseAutoRuleManager;

	
	@GetMapping("/pageList")
    public ResultVO<?> pageList(SuperviseAutoRule entity) {
		IPage<SuperviseAutoRule> resultList = superviseAutoRuleManager.queryPageList(entity);
		return new ResultVO<>(resultList);
    }
    
    @GetMapping("/queryList")
    public ResultVO<?> queryList(SuperviseAutoRule entity) {
		List<SuperviseAutoRule> resultList = superviseAutoRuleManager.queryList(entity);
		return new ResultVO<>(resultList);
    }

	@GetMapping("/getById/{id}")
	public ResultVO<?> getById(@PathVariable("id") String id){
	return new ResultVO<>(superviseAutoRuleManager.getById(id));
	}
    
	@PostMapping("/create")
    public ResultVO<?> create(@RequestBody SuperviseAutoRule entity){
		superviseAutoRuleManager.save(entity);
		return new ResultVO<>(entity);
	}
	
	@PutMapping("/update")
    public ResultVO<?> update(@RequestBody SuperviseAutoRule entity){
		superviseAutoRuleManager.update(entity);
		return new ResultVO<>(entity);
	}

	@DeleteMapping("/remove/{ids}")
	public ResultVO<?> remove(@PathVariable List<String> ids){
		superviseAutoRuleManager.remove(ids);
		return new ResultVO<>(ids);
	}


}
