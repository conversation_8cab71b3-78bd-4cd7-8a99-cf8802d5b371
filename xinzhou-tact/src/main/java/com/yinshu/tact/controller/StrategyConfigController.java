package com.yinshu.tact.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yinshu.common.ApiConstant;

import com.yinshu.tact.entity.StrategyConfig;
import com.yinshu.tact.entity.VO.StrategyConfigVO;
import com.yinshu.tact.enums.IndicatorEnum;
import com.yinshu.tact.service.StrategyConfigService;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;


/**
 * 策略配置表 Controller
 */
@RestController
@RequestMapping(ApiConstant.API_TACP_PREFIX + "/strategyConfig")
public class StrategyConfigController {

    @Resource
    private StrategyConfigService strategyConfigService;

    /****
     * 根据类型查询指标列表
     * @return
     */
    @PostMapping("/listByType")
    public ResultVO<List<StrategyConfigVO>> listByType(@RequestBody StrategyConfig strategyConfig) {
        return ResultVO.suc(strategyConfigService.listByType(strategyConfig.getType()));
    }

    /****
     * 获取所有指标项枚举
     * @return
     */
    @PostMapping("/listAllIndicators")
    public ResultVO<List<Map<String, String>>> listAllIndicators() {
        List<Map<String, String>> result = new ArrayList<>();
        for (IndicatorEnum indicator : IndicatorEnum.values()) {
            Map<String, String> item = new HashMap<>();
            item.put("code", indicator.getCode());
            item.put("name", indicator.getName());
            result.add(item);
        }
        return ResultVO.suc(result);
    }

    /****
     * 新增策略配置
     * @param strategyConfig 策略配置实体
     * @return 新增结果
     */
    @PostMapping("/save")
    public ResultVO<Boolean> save(@RequestBody StrategyConfig strategyConfig) {
        // 同类型不能存在重复数据
        long count = strategyConfigService.count(new LambdaQueryWrapper<StrategyConfig>()
                .eq(StrategyConfig::getType, strategyConfig.getType())
                .eq(StrategyConfig::getIndicator, strategyConfig.getIndicator())
        );
        if (count > 0) {
            throw new RuntimeException("该指标已存在，不需重复添加");
        }
        boolean result = strategyConfigService.save(strategyConfig);
        return ResultVO.suc(result);
    }

    /****
     * 修改策略配置
     * @param strategyConfig 策略配置实体
     * @return 新增结果
     */
    @PostMapping("/update")
    public ResultVO<Boolean> update(@RequestBody StrategyConfig strategyConfig) {
        strategyConfig.setUpdateTime(new Date());
        boolean result = strategyConfigService.updateById(strategyConfig);
        return ResultVO.suc(result);
    }

    /****
     * 根据ID删除策略配置
     * @param strategyConfig 策略配置
     * @return 删除结果
     */
    @PostMapping("/delete")
    public ResultVO<Boolean> delete(@RequestBody StrategyConfig strategyConfig) {
        boolean result = strategyConfigService.removeById(strategyConfig.getId());
        return ResultVO.suc(result);
    }

    /****
     * 校验某指标下权重总和是否等于100
     * @param strategyConfig 需传递id
     * @return 校验结果
     */
    @PostMapping("/checkWeightSum")
    public ResultVO<String> checkWeightSum(@RequestBody StrategyConfig strategyConfig) {
        List<StrategyConfig> list = strategyConfigService.list(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<StrategyConfig>().eq("type", strategyConfig.getType())
                );
        int sum = 0;
        for (StrategyConfig config : list) {
            if (config.getWeight() != null) {
                sum += config.getWeight();
            }
        }
        if (sum != 100) {
            return ResultVO.suc("所有指标权重总和为" + sum + "%"+ ", 不等于100%，请检查！");
        }
        return ResultVO.suc("权重总和为100%，校验通过！");
    }
}
