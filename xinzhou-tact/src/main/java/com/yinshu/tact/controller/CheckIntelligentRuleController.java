package com.yinshu.tact.controller;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.common.ApiConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.*;
import com.yinshu.tact.entity.CheckIntelligentRule;
import com.yinshu.tact.manager.CheckIntelligentRuleManager;

import org.springframework.web.bind.annotation.RestController;

/**
 *
 * 智能查岗规则表 
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@RestController
@RequestMapping(ApiConstant.API_TACP_PREFIX + "/checkIntelligentRule")
public class CheckIntelligentRuleController {

	@Autowired
	private CheckIntelligentRuleManager checkIntelligentRuleManager;

	/**
	 * 获取全部的企业名称
	 *
	 * @return 实例对象
	 */
	@GetMapping("/getAllEnterpriseName")
	public ResultVO<List<String>> getAllEnterpriseName() {
		List<String> list = checkIntelligentRuleManager.getAllEnterpriseName();
		return ResultVO.suc(list);
	}
	
	@GetMapping("/pageList")
    public ResultVO<?> pageList(CheckIntelligentRule entity) {
		IPage<CheckIntelligentRule> resultList = checkIntelligentRuleManager.queryPageList(entity);
		return new ResultVO<>(resultList);
    }
    
    @GetMapping("/queryList")
    public ResultVO<?> queryList(CheckIntelligentRule entity) {
		List<CheckIntelligentRule> resultList = checkIntelligentRuleManager.queryList(entity);
		return new ResultVO<>(resultList);
    }

	@GetMapping("/getById/{id}")
	public ResultVO<?> getById(@PathVariable("id") String id){
	return new ResultVO<>(checkIntelligentRuleManager.getById(id));
	}
    
	@PostMapping("/create")
    public ResultVO<?> create(@RequestBody CheckIntelligentRule entity){
		checkIntelligentRuleManager.save(entity);
		return new ResultVO<>(entity);
	}
	
	@PutMapping("/update")
    public ResultVO<?> update(@RequestBody CheckIntelligentRule entity){
		checkIntelligentRuleManager.update(entity);
		return new ResultVO<>(entity);
	}

	@DeleteMapping("/remove/{ids}")
	public ResultVO<?> remove(@PathVariable List<String> ids){
		checkIntelligentRuleManager.remove(ids);
		return new ResultVO<>(ids);
	}


}
