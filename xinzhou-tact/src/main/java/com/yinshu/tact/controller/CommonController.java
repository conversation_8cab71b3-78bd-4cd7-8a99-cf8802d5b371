package com.yinshu.tact.controller;

import com.yinshu.common.ApiConstant;
import com.yinshu.sys.entity.Dictionary;
import com.yinshu.sys.manager.DictionaryManager;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(ApiConstant.API_TACP_PREFIX + "/common")
public class CommonController {

    @Autowired
    private DictionaryManager dictionaryManager;

    /**
     * 根据字典编码获取列表
     * @return
     */
    @GetMapping("/getListByDicCode/{code}")
    public ResultVO<?> getListByDicCode(@PathVariable(name = "code", required = true) String code){
        List<Dictionary> resultList = dictionaryManager.getListByDicCode(code);
        return new ResultVO<>(resultList);
    }
}
