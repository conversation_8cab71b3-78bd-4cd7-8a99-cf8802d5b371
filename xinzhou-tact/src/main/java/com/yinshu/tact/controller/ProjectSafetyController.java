package com.yinshu.tact.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.common.ApiConstant;
import com.yinshu.tact.entity.dto.ProjectSafetyDTO;
import com.yinshu.tact.entity.dto.HazardDiscoveryTimeDTO;
import com.yinshu.tact.entity.dto.SafetyQualityDetailDTO;
import com.yinshu.tact.manager.ProjectSafetyManager;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目安全控制器
 * <AUTHOR>
 * @since 2025-07-21
 */
@RestController
@RequestMapping(ApiConstant.API_TACP_PREFIX + "/project-safety")
public class ProjectSafetyController {

    @Autowired
    private ProjectSafetyManager projectSafetyManager;

    /**
     * 查询项目安全列表
     * @param entity 查询参数
     * @return 项目安全列表
     */
    @GetMapping("/queryProjectList")
    public ResultVO<?> queryProjectList(ProjectSafetyDTO entity) {
        List<ProjectSafetyDTO> resultList = projectSafetyManager.queryProjectSafetyList(entity);
        return new ResultVO<>(resultList);
    }

    /**
     * 查询隐患详情列表
     * @param entity 查询参数
     * @return 隐患详情列表
     */
    @GetMapping("/queryHazardDetailList")
    public ResultVO<?> queryHazardDetailList(HazardDiscoveryTimeDTO entity) {
        List<HazardDiscoveryTimeDTO> resultList = projectSafetyManager.queryHazardDiscoveryTimeList(entity);
        return new ResultVO<>(resultList);
    }

    /**
     * 分页查询项目安全列表
     * @param entity 查询参数
     * @return 分页项目安全列表
     */
    @GetMapping("/pageProjectList")
    public ResultVO<?> pageProjectList(ProjectSafetyDTO entity) {
        IPage<ProjectSafetyDTO> resultList = projectSafetyManager.queryProjectSafetyPage(entity);
        return new ResultVO<>(resultList);
    }

    /**
     * 分页查询隐患详情列表
     * @param entity 查询参数
     * @return 分页隐患详情列表
     */
    @GetMapping("/pageHazardDetailList")
    public ResultVO<?> pageHazardDetailList(HazardDiscoveryTimeDTO entity) {
        IPage<HazardDiscoveryTimeDTO> resultList = projectSafetyManager.queryHazardDiscoveryTimePage(entity);
        return new ResultVO<>(resultList);
    }

    /**
     * 根据项目ID查询所有隐患名称
     * @param projectId 项目ID
     * @return 隐患名称数组
     */
    @GetMapping("/getHazardNameByProjectId/{projectId}")
    public ResultVO<?> getHazardNameByProjectId(@PathVariable("projectId") String projectId) {
        List<HazardDiscoveryTimeDTO> list = projectSafetyManager.getHazardTypeByProjectId(projectId);
        return new ResultVO<>(list);
    }

    /**
     * 根据ID查询安全质量检查详情
     * @param id 安全质量检查ID
     * @return 安全质量检查详情
     */
    @GetMapping("/getSafetyQualityById/{id}")
    public ResultVO<?> getSafetyQualityById(@PathVariable("id") String id) {
        SafetyQualityDetailDTO result = projectSafetyManager.getSafetyQualityById(id);
        return new ResultVO<>(result);
    }
}
