package com.yinshu.tact.controller;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.common.ApiConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.*;
import com.yinshu.tact.entity.CheckRegularRule;
import com.yinshu.tact.manager.CheckRegularRuleManager;

import org.springframework.web.bind.annotation.RestController;

/**
 *
 * 定期自动查岗规则表 
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@RestController
@RequestMapping(ApiConstant.API_TACP_PREFIX + "/checkRegularRule")
public class CheckRegularRuleController {

	@Autowired
	private CheckRegularRuleManager checkRegularRuleManager;


	/**
	 * 获取全部的企业名称
	 *
	 * @return 实例对象
	 */
	@GetMapping("/getAllEnterpriseName")
	public ResultVO<List<String>> getAllEnterpriseName() {
		List<String> list = checkRegularRuleManager.getAllEnterpriseName();
		return ResultVO.suc(list);
	}

	@GetMapping("/pageList")
    public ResultVO<?> pageList(CheckRegularRule entity) {
		IPage<CheckRegularRule> resultList = checkRegularRuleManager.queryPageList(entity);
		return new ResultVO<>(resultList);
    }
    
    @GetMapping("/queryList")
    public ResultVO<?> queryList(CheckRegularRule entity) {
		List<CheckRegularRule> resultList = checkRegularRuleManager.queryList(entity);
		return new ResultVO<>(resultList);
    }

	@GetMapping("/getById/{id}")
	public ResultVO<?> getById(@PathVariable("id") String id){
	return new ResultVO<>(checkRegularRuleManager.getById(id));
	}
    
	@PostMapping("/create")
    public ResultVO<?> create(@RequestBody CheckRegularRule entity){
		checkRegularRuleManager.save(entity);
		return new ResultVO<>(entity);
	}
	
	@PutMapping("/update")
    public ResultVO<?> update(@RequestBody CheckRegularRule entity){
		checkRegularRuleManager.update(entity);
		return new ResultVO<>(entity);
	}

	@DeleteMapping("/remove/{ids}")
	public ResultVO<?> remove(@PathVariable List<String> ids){
		checkRegularRuleManager.remove(ids);
		return new ResultVO<>(ids);
	}
}