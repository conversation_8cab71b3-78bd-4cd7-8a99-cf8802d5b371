package com.yinshu.tact.controller;

import com.yinshu.common.ApiConstant;

import com.yinshu.tact.entity.ScoreConfig;
import com.yinshu.tact.entity.StrategyConfig;
import com.yinshu.tact.enums.IndicatorEnum;
import com.yinshu.tact.service.ScoreConfigService;
import com.yinshu.tact.service.StrategyConfigService;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 评分配置表 Controller
 */
@RestController
@RequestMapping(ApiConstant.API_TACP_PREFIX + "/scoreConfig")
public class ScoreConfigController {

    @Resource
    private ScoreConfigService scoreConfigService;

    @Resource
    private StrategyConfigService strategyConfigService;

    /** 新增 */
    @PostMapping("/save")
    public ResultVO<Boolean> save(@RequestBody ScoreConfig scoreConfig) {
        boolean result = scoreConfigService.save(scoreConfig);
        return ResultVO.suc(result);
    }

    /** 根据ID删除 */
    @PostMapping("/delete")
    public ResultVO<Boolean> delete(@RequestBody ScoreConfig scoreConfig) {
        boolean result = scoreConfigService.removeById(scoreConfig.getId());
        return ResultVO.suc(result);
    }

    /** 修改 */
    @PostMapping("/update")
    public ResultVO<Boolean> update(@RequestBody ScoreConfig scoreConfig) {
        boolean result = scoreConfigService.updateById(scoreConfig);
        return ResultVO.suc(result);
    }

    /** 查询全部 */
    @PostMapping("/list")
    public ResultVO<List<ScoreConfig>> list() {
        List<ScoreConfig> list = scoreConfigService.list();
        return ResultVO.suc(list);
    }

    /***
     * 查询根据指标id查询出相对应的评分配置
     * @param scoreConfig
     * @return
     */
    @PostMapping("/listByIndicatorId")
    public ResultVO<List<ScoreConfig>> listByIndicatorId(@RequestBody ScoreConfig scoreConfig) {
        List<ScoreConfig> list = scoreConfigService.list(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<ScoreConfig>().eq("indicator_id", scoreConfig.getIndicatorId())
        );
        return ResultVO.suc(list);
    }

    /**
     * 批量新增评分配置：先根据指标id删除所有评分数据，再批量新增。
     * 新增前自动生成评分等级说明（中文）
     */
    @PostMapping("/batchSave")
    public ResultVO<Boolean> batchSave(@RequestBody List<ScoreConfig> scoreConfigList) {
        if (scoreConfigList == null || scoreConfigList.isEmpty()) {
            return ResultVO.suc(false);
        }
        Integer indicatorId = scoreConfigList.get(0).getIndicatorId();

        StrategyConfig tissStrategyConfig =strategyConfigService.getById(indicatorId);
        // 1. 删除该指标下所有评分配置
        scoreConfigService.remove(
            new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<ScoreConfig>().eq("indicator_id", indicatorId)
        );
        // 2. 生成评分等级说明
        for (ScoreConfig config : scoreConfigList) {
            String left = ruleToText(config.getLeftRule(), config.getLeftRuleValue());
            String right = ruleToText(config.getRightRule(), config.getRightRuleValue());
            String desc = "当前指标："+ IndicatorEnum.getNameByCode(tissStrategyConfig.getIndicator())+" "+left + "且" + right;
            config.setRatingLevel(desc);
            config.setIndicatorId(indicatorId); // 保证indicatorId一致
            config.setCreateTime(new Date());
        }
        // 3. 批量保存
        boolean result = scoreConfigService.saveBatch(scoreConfigList);
        return ResultVO.suc(result);
    }

    /**
     * 校验某指标下评分总和是否等于100
     * @param scoreConfig indicatorId需传递
     * @return 校验结果
     */
    @PostMapping("/checkScoreSum")
    public ResultVO<String> checkScoreSum(@RequestBody ScoreConfig scoreConfig) {
        List<ScoreConfig> list = scoreConfigService.list(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<ScoreConfig>().eq("indicator_id", scoreConfig.getIndicatorId())
        );
        int sum = 0;
        for (ScoreConfig config : list) {
            if (config.getScore() != null) {
                sum += config.getScore();
            }
        }
        if (sum != 100) {
            return ResultVO.suc("评分总和为" + sum + ", 不等于100，请检查！");
        }
        return ResultVO.suc("评分总和为100，校验通过！");
    }

    /**
     * 规则转中文
     */
    private String ruleToText(String rule, Integer value) {
        if (rule == null || value == null) return "";
        switch (rule) {
            case "gt": return "大于" + value;
            case "gte": return "大于等于" + value;
            case "lt": return "小于" + value;
            case "lte": return "小于等于" + value;
            case "eq": return "等于" + value;
            case "neq": return "不等于" + value;
            default: return value + "";
        }
    }
} 