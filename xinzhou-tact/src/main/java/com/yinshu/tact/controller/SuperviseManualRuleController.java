package com.yinshu.tact.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.common.ApiConstant;
import com.yinshu.tact.entity.SuperviseManualRule;
import com.yinshu.tact.manager.SuperviseManualRuleManager;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 手动督办规则表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@RestController
@RequestMapping(ApiConstant.API_TACP_PREFIX + "/superviseManualRule")
public class SuperviseManualRuleController {

    @Autowired
    private SuperviseManualRuleManager superviseManualRuleManager;


    @GetMapping("/pageList")
    public ResultVO<?> pageList(SuperviseManualRule entity) {
        IPage<SuperviseManualRule> resultList = superviseManualRuleManager.queryPageList(entity);
        return new ResultVO<>(resultList);
    }

    @GetMapping("/queryList")
    public ResultVO<?> queryList(SuperviseManualRule entity) {
        List<SuperviseManualRule> resultList = superviseManualRuleManager.queryList(entity);
        return new ResultVO<>(resultList);
    }

    @GetMapping("/getById/{id}")
    public ResultVO<?> getById(@PathVariable("id") String id) {
        return new ResultVO<>(superviseManualRuleManager.getById(id));
    }

    @GetMapping("/getByAlarmId/{id}")
    public ResultVO<?> getByAlarmId(@PathVariable("id") String id) {
        return new ResultVO<>(superviseManualRuleManager.getByAlarmId(id));
    }

    @PostMapping("/create")
    public ResultVO<?> create(@RequestBody SuperviseManualRule entity) {
        superviseManualRuleManager.save(entity);
        return new ResultVO<>(entity);
    }

    @PutMapping("/update")
    public ResultVO<?> update(@RequestBody SuperviseManualRule entity) {
        superviseManualRuleManager.update(entity);
        return new ResultVO<>(entity);
    }

    @DeleteMapping("/remove/{ids}")
    public ResultVO<?> remove(@PathVariable List<String> ids) {
        superviseManualRuleManager.remove(ids);
        return new ResultVO<>(ids);
    }


}
