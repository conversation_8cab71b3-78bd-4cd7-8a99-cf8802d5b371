package com.yinshu.tact.controller;

import java.io.File;
import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.common.ApiConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.*;
import com.yinshu.tact.entity.HiddenDanger;
import com.yinshu.tact.manager.HiddenDangerManager;

import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 隐患整改跟踪表（包含发起和完成阶段）
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@RestController
@RequestMapping(ApiConstant.API_TACP_PREFIX + "/hiddenDanger")
public class HiddenDangerController {

    @Autowired
    private HiddenDangerManager hiddenDangerManager;

    // 从配置文件读取上传目录
    @Value("${attachment.default-path}")
    private String uploadPath;

    @Value("${system.oss-path}")
    private String path;

    @GetMapping("/pageList")
    public ResultVO<?> pageList(HiddenDanger entity) {
        IPage<HiddenDanger> resultList = hiddenDangerManager.queryPageList(entity);
        return new ResultVO<>(resultList);
    }

    @GetMapping("/queryList")
    public ResultVO<?> queryList(HiddenDanger entity) {
        List<HiddenDanger> resultList = hiddenDangerManager.queryList(entity);
        return new ResultVO<>(resultList);
    }

    @GetMapping("/getById/{id}")
    public ResultVO<?> getById(@PathVariable("id") String id) {
        return new ResultVO<>(hiddenDangerManager.getById(id));
    }

    @PostMapping("/create")
    public ResultVO<?> create(@RequestBody HiddenDanger entity) {
        hiddenDangerManager.save(entity);
        return new ResultVO<>(entity);
    }

    @PutMapping("/update")
    public ResultVO<?> update(@RequestBody HiddenDanger entity) {
        hiddenDangerManager.update(entity);
        return new ResultVO<>(entity);
    }

    @DeleteMapping("/remove/{ids}")
    public ResultVO<?> remove(@PathVariable List<String> ids) {
        hiddenDangerManager.remove(ids);
        return new ResultVO<>(ids);
    }

    // 上传接口（新增临时文件标记）
    @PostMapping("/upload")
    public ResultVO<?> upload(@RequestParam("file") MultipartFile file) {
        try {
            String path = uploadPath + "/rectificationForm";
            // 创建目录（兼容Windows和Linux）
            File dir = new File(path);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 生成唯一文件名
            String fileName = UUID.randomUUID() + "-" + file.getOriginalFilename();
            Path filePath = Paths.get(path, fileName);

            // 保存文件
            file.transferTo(filePath);

            // 返回结果（包含临时文件标记和访问路径）
            Map<String, Object> result = new HashMap<>();
            result.put("filePath", path + "/" + fileName); // 访问路径,暂时废弃
            result.put("tempFile", true); // 标记为临时文件
            result.put("fileName", fileName); // 存储原始文件名

            return new ResultVO<>(result);
        } catch (Exception e) {
            return new ResultVO<>("500", "上传失败: " + e.getMessage(), null);
        }
    }

    // 删除临时文件接口
    @DeleteMapping("/deleteTempFile")
    public ResultVO<?> deleteTempFile(@RequestParam("fileName") String fileName) {
        try {
            String path = uploadPath + "/rectificationForm";
            Path filePath = Paths.get(path, fileName);
            if (Files.exists(filePath)) {
                Files.delete(filePath);
                return new ResultVO<>("文件删除成功");
            }
            return new ResultVO<>("404", "文件不存在", null);
        } catch (IOException e) {
            return new ResultVO<>("500", "删除失败: " + e.getMessage(), null);
        }
    }

    /**
     * 获取图片访问路径
     *
     * @return
     */
    @GetMapping("/getBaseImageUrl")
    public ResultVO<String> getBaseImageUrl() {
        String s = path + "rectificationForm/";
        return new ResultVO<>(s);
    }

}
