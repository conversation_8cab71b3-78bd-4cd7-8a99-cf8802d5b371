package com.yinshu.tact.controller;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.yinshu.common.ApiConstant;
import com.yinshu.fast.excel.util.ExcelUtils;
import com.yinshu.jwt.JWTUtil;
import com.yinshu.utils.RedisCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.*;
import com.yinshu.tact.entity.CheckDutyRecord;
import com.yinshu.tact.manager.CheckDutyRecordManager;

import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 查岗记录表
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@RestController
@RequestMapping(ApiConstant.API_TACP_PREFIX + "/checkDutyRecord")
public class CheckDutyRecordController {

    @Autowired
    private CheckDutyRecordManager checkDutyRecordManager;

    @Autowired
    private RedisCache redisCache;


    @GetMapping("/pageList")
    public ResultVO<?> pageList(CheckDutyRecord entity) {
        IPage<CheckDutyRecord> resultList = checkDutyRecordManager.queryPageList(entity);
        return new ResultVO<>(resultList);
    }

    @GetMapping("/queryList")
    public ResultVO<?> queryList(CheckDutyRecord entity) {
        List<CheckDutyRecord> resultList = checkDutyRecordManager.queryList(entity);
        return new ResultVO<>(resultList);
    }

    @PostMapping("/exportList")
    public ResultVO<?> exportList(@RequestBody CheckDutyRecord entity) {
        List<CheckDutyRecord> resultList = checkDutyRecordManager.queryList(entity);
        resultList.forEach(item -> {
            item.setStatus(item.getStatus().equals("1") ? "正常" : "超时");
            item.setInitiateType(item.getInitiateType().equals("1") ? "自动" : item.getInitiateType().equals("2") ? "智能" : "手动");
        });
        ExcelUtils.exportExcelSheet(CheckDutyRecord.class, resultList);
        return new ResultVO<>(resultList);
    }


    /**
     * 获取全部的企业名称
     *
     * @return 实例对象
     */
    @GetMapping("/getAllEnterpriseName")
    public ResultVO<List<String>> getAllEnterpriseName() {
        List<String> list = checkDutyRecordManager.getAllEnterpriseName();
        return ResultVO.suc(list);
    }

    @GetMapping("/getById/{id}")
    public ResultVO<?> getById(@PathVariable("id") String id) {
        return new ResultVO<>(checkDutyRecordManager.getById(id));
    }

    @PostMapping("/create")
    public ResultVO<?> create(@RequestBody CheckDutyRecord entity, HttpServletRequest request) {
        String token = request.getHeader("token");
        entity.setInitiator(JWTUtil.getTokenClaim(token, "loginName"));
        entity.setInitiateTime(new Date());
        entity.setStatus("1");
        /** 验证码校验*/
        if (StringUtils.isBlank(entity.getId())) {
            throw new RuntimeException("验证码不能为空");
        }

////     从 Redis 获取答案
//		String storedAnswer = redisCache.getCacheObject("kaptcha:" + entity.getId());
//// 校验完成后删除验证码（防止重复使用）
//		redisCache.deleteObject("kaptcha:" + entity.getId());
        checkDutyRecordManager.save(entity);
        return new ResultVO<>(entity);
    }

    @PutMapping("/update")
    public ResultVO<?> update(@RequestBody CheckDutyRecord entity) {
        checkDutyRecordManager.update(entity);
        return new ResultVO<>(entity);
    }

    @DeleteMapping("/remove/{ids}")
    public ResultVO<?> remove(@PathVariable List<String> ids) {
        checkDutyRecordManager.remove(ids);
        return new ResultVO<>(ids);
    }

    //	查询每个企业各有多少条记录
    @PostMapping("getCountByEnterpriseNames")
    public ResultVO<Map<String, String>> getCountByEnterpriseNames(@RequestBody List<String> enterpriseNames) {
        return new ResultVO<>(checkDutyRecordManager.getCountByEnterpriseNames(enterpriseNames));
    }


}
