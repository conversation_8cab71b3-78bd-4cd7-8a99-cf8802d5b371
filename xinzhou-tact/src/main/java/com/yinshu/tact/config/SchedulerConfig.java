package com.yinshu.tact.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

@Configuration
public class SchedulerConfig implements WebMvcConfigurer {
    @Bean
    public ScheduledExecutorService scheduledExecutorService() {
        // 根据需要调整线程池大小
        return Executors.newScheduledThreadPool(10);
    }
}
