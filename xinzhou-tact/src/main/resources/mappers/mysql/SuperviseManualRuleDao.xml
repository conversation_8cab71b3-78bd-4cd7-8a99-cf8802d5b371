<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.tact.dao.SuperviseManualRuleDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yinshu.tact.entity.SuperviseManualRule">
        <id column="id" property="id" />
        <result column="alarm_id" property="alarmId" />
        <result column="limit_minutes" property="limitMinutes" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, alarm_id, limit_minutes
    </sql>


	<select id="queryList" resultMap="BaseResultMap" parameterType="com.yinshu.tact.entity.SuperviseManualRule">
		select <include refid="Base_Column_List"></include>
		from tocc_supervise_manual_rule
		<where>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.alarmId != null and entity.alarmId != '' ">
					and alarm_id = #{entity.alarmId}
				</if>
				<if test="entity.limitMinutes != null and entity.limitMinutes != '' ">
					and limit_minutes = #{entity.limitMinutes}
				</if>
		</where>
	</select>

	<!-- 分页查询  -->
	<select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.tact.entity.SuperviseManualRule">
		select <include refid="Base_Column_List"></include>
		from tocc_supervise_manual_rule
		<where>
			<if test="entity.keyword != null and entity.keyword != '' ">
				(
				<trim prefix="" prefixOverrides="or">
				</trim>
				)
			</if>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.alarmId != null and entity.alarmId != '' ">
					and alarm_id = #{entity.alarmId}
				</if>
				<if test="entity.limitMinutes != null and entity.limitMinutes != '' ">
					and limit_minutes = #{entity.limitMinutes}
				</if>
		</where>
	</select>



</mapper>