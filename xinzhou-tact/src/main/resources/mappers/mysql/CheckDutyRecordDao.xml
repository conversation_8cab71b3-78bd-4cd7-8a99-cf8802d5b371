<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.tact.dao.CheckDutyRecordDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yinshu.tact.entity.CheckDutyRecord">
        <id column="id" property="id" />
        <result column="check_enterprise_name" property="checkEnterpriseName" />
        <result column="initiate_type" property="initiateType" />
        <result column="initiator" property="initiator" />
        <result column="initiate_time" property="initiateTime" />
        <result column="response_time" property="responseTime" />
        <result column="responder" property="responder" />
        <result column="response_duration" property="responseDuration" />
        <result column="receive_phone" property="receivePhone" />
        <result column="response_content" property="responseContent" />
        <result column="status" property="status" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, check_enterprise_name, initiate_type, initiator, initiate_time, response_time, responder, response_duration, receive_phone, response_content, status, create_by, create_time, update_by, update_time, remark
    </sql>


	<select id="queryList" resultMap="BaseResultMap" parameterType="com.yinshu.tact.entity.CheckDutyRecord">
		select <include refid="Base_Column_List"></include>
		from tocc_check_duty_record
		<where>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.checkEnterpriseName != null and entity.checkEnterpriseName != '' ">
					and check_enterprise_name = #{entity.checkEnterpriseName}
				</if>
				<if test="entity.initiateType != null and entity.initiateType != '' ">
					and initiate_type = #{entity.initiateType}
				</if>
				<if test="entity.initiator != null and entity.initiator != '' ">
					and initiator = #{entity.initiator}
				</if>
				<if test="entity.initiateTime != null ">
					and initiate_time = #{entity.initiateTime}
				</if>
				<if test="entity.initiateStartTime != null ">
					and initiate_time &gt;= #{entity.initiateStartTime}
				</if>
				<if test="entity.initiateEndTime != null ">
					and initiate_time &lt;= #{entity.initiateEndTime}
				</if>
				<if test="entity.responseTime != null ">
					and response_time = #{entity.responseTime}
				</if>
				<if test="entity.responder != null and entity.responder != '' ">
					and responder = #{entity.responder}
				</if>
				<if test="entity.responseDuration != null and entity.responseDuration != '' ">
					and response_duration = #{entity.responseDuration}
				</if>
				<if test="entity.receivePhone != null and entity.receivePhone != '' ">
					and receive_phone = #{entity.receivePhone}
				</if>
				<if test="entity.responseContent != null and entity.responseContent != '' ">
					and response_content = #{entity.responseContent}
				</if>
				<if test="entity.status != null and entity.status != '' ">
					and status = #{entity.status}
				</if>
				<if test="entity.createBy != null and entity.createBy != '' ">
					and create_by = #{entity.createBy}
				</if>
				<if test="entity.createTime != null ">
					and create_time = #{entity.createTime}
				</if>
				<if test="entity.updateBy != null and entity.updateBy != '' ">
					and update_by = #{entity.updateBy}
				</if>
				<if test="entity.updateTime != null  ">
					and update_time = #{entity.updateTime}
				</if>
				<if test="entity.remark != null and entity.remark != '' ">
					and remark = #{entity.remark}
				</if>
		</where>
	</select>

	<!-- 分页查询  -->
	<select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.tact.entity.CheckDutyRecord">
		select <include refid="Base_Column_List"></include>
		from tocc_check_duty_record
		<where>
			<if test="entity.keyword != null and entity.keyword != '' ">
				(
				<trim prefix="" prefixOverrides="or">
						or check_enterprise_name like concat(concat('%', #{entity.keyword}), '%')
						or initiate_type like concat(concat('%', #{entity.keyword}), '%')
						or initiator like concat(concat('%', #{entity.keyword}), '%')
						or responder like concat(concat('%', #{entity.keyword}), '%')
						or response_duration like concat(concat('%', #{entity.keyword}), '%')
						or receive_phone like concat(concat('%', #{entity.keyword}), '%')
						or response_content like concat(concat('%', #{entity.keyword}), '%')
						or status like concat(concat('%', #{entity.keyword}), '%')
						or create_by like concat(concat('%', #{entity.keyword}), '%')
						or update_by like concat(concat('%', #{entity.keyword}), '%')
						or remark like concat(concat('%', #{entity.keyword}), '%')
				</trim>
				)
			</if>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.checkEnterpriseName != null and entity.checkEnterpriseName != '' ">
					and check_enterprise_name = #{entity.checkEnterpriseName}
				</if>
				<if test="entity.initiateType != null and entity.initiateType != '' ">
					and initiate_type = #{entity.initiateType}
				</if>
				<if test="entity.initiator != null and entity.initiator != '' ">
					and initiator = #{entity.initiator}
				</if>
				<if test="entity.initiateTime != null ">
					and initiate_time = #{entity.initiateTime}
				</if>
				<if test="entity.initiateStartTime != null ">
					and initiate_time &gt;= #{entity.initiateStartTime}
				</if>
				<if test="entity.initiateEndTime != null ">
					and initiate_time &lt;= #{entity.initiateEndTime}
				</if>
				<if test="entity.responseTime != null ">
					and response_time = #{entity.responseTime}
				</if>
				<if test="entity.responder != null and entity.responder != '' ">
					and responder = #{entity.responder}
				</if>
				<if test="entity.responseDuration != null and entity.responseDuration != '' ">
					and response_duration = #{entity.responseDuration}
				</if>
				<if test="entity.receivePhone != null and entity.receivePhone != '' ">
					and receive_phone = #{entity.receivePhone}
				</if>
				<if test="entity.responseContent != null and entity.responseContent != '' ">
					and response_content = #{entity.responseContent}
				</if>
				<if test="entity.status != null and entity.status != '' ">
					and status = #{entity.status}
				</if>
				<if test="entity.createBy != null and entity.createBy != '' ">
					and create_by = #{entity.createBy}
				</if>
				<if test="entity.createTime != null ">
					and create_time = #{entity.createTime}
				</if>
				<if test="entity.updateBy != null and entity.updateBy != '' ">
					and update_by = #{entity.updateBy}
				</if>
				<if test="entity.updateTime != null ">
					and update_time = #{entity.updateTime}
				</if>
				<if test="entity.remark != null and entity.remark != '' ">
					and remark = #{entity.remark}
				</if>
		</where>
		order by create_time desc
	</select>
    <select id="getAllEnterpriseName" resultType="java.lang.String">
		select distinct check_enterprise_name from tocc_check_duty_record
	</select>
	<select id="getCountByEnterpriseNames" resultType="com.yinshu.tact.entity.CheckDutyRecord">
		select check_enterprise_name,count(1) as count from tocc_check_duty_record where check_enterprise_name in
		<foreach item="item" collection="enterpriseNames" index="index" open="(" separator="," close=")">
			#{item}
		</foreach>
		 group by check_enterprise_name
	</select>


</mapper>