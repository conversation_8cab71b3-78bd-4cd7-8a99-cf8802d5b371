<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.tact.dao.ProjectSafetyDao">

    <resultMap id="ProjectSafetyMap" type="com.yinshu.tact.entity.dto.ProjectSafetyDTO">
        <result property="sequence" column="sequence"/>
        <result property="region" column="region"/>
        <result property="projectId" column="project_id"/>
        <result property="approvalDoc" column="approval_doc"/>
        <result property="projectName" column="project_name"/>
        <result property="projectType" column="project_type"/>
        <result property="projectLocation" column="project_location"/>
        <result property="constructionUnit" column="construction_unit"/>
        <result property="hazardCount" column="hazard_count"/>
    </resultMap>


    <resultMap id="HazardDiscoveryTimeMap" type="com.yinshu.tact.entity.dto.HazardDiscoveryTimeDTO">
        <result property="sequence" column="sequence"/>
        <result property="id" column="id"/>
        <result property="hazardDiscoveryTime" column="hazard_discovery_time"/>
        <result property="projectId" column="project_id"/>
        <result property="hazardName" column="hazard_name"/>
        <result property="hazardCategory" column="hazard_category"/>
        <result property="hazardLevel" column="hazard_level"/>
        <result property="hazardDescription" column="hazard_description"/>
    </resultMap>

    <select id="selectProjectSafetyList" resultMap="ProjectSafetyMap">
        SELECT
        ROW_NUMBER() OVER (ORDER BY p.id) as sequence,
        p.region,
        p.id as project_id,
        p.approval_doc,
        p.project_name,
        p.project_location,
        p.legal_unit as construction_unit,
        COUNT(sq.id) as hazard_count
        FROM
        tcps_project p
        LEFT JOIN
        tcps_safety_quality sq
        ON
        p.id = sq.project_id
        <where>
            <if test="region != null and region != ''">
                AND p.region LIKE CONCAT('%', #{region}, '%')
            </if>
            <if test="approvalDoc != null and approvalDoc != ''">
                AND p.approval_doc LIKE CONCAT('%', #{approvalDoc}, '%')
            </if>
            <if test="projectName != null and projectName != ''">
                AND p.project_name LIKE CONCAT('%', #{projectName}, '%')
            </if>
        </where>
        GROUP BY p.id, p.region, p.approval_doc, p.project_name, p.project_location, p.legal_unit
        ORDER BY p.id
    </select>

    <select id="selectProjectSafetyPage" resultMap="ProjectSafetyMap">
        SELECT
        ROW_NUMBER() OVER (ORDER BY p.id) as sequence,
        p.region,
        p.id as project_id,
        p.approval_doc,
        p.project_name,
        p.project_location,
        p.legal_unit as construction_unit,
        COUNT(sq.id) as hazard_count,
        dict.dic_name as project_type
        FROM
        tcps_project p
        LEFT JOIN tcps_safety_quality sq ON p.id = sq.project_id
        left join s_dictionary dict on dict.parent_code = 'TCPS_PRO_TYPE' and dict.dic_code = p.project_type
        <where>
            <if test="entity.region != null and entity.region != ''">
                AND p.region LIKE CONCAT('%', #{entity.region}, '%')
            </if>
            <if test="entity.approvalDoc != null and entity.approvalDoc != ''">
                AND p.approval_doc LIKE CONCAT('%', #{entity.approvalDoc}, '%')
            </if>
            <if test="entity.projectName != null and entity.projectName != ''">
                AND p.project_name LIKE CONCAT('%', #{entity.projectName}, '%')
            </if>
        </where>
        GROUP BY p.id, p.region, p.approval_doc, p.project_name, p.project_location, p.legal_unit,dict.dic_name
        ORDER BY hazard_count
    </select>


    <select id="selectHazardDiscoveryTimeList" resultMap="HazardDiscoveryTimeMap">
        SELECT
        sq.id,
        ROW_NUMBER() OVER (ORDER BY sq.create_time DESC) as sequence,
        sq.create_time as hazard_discovery_time,
        CONCAT(sq.hazard_type, '-', sq.hazard_level) as hazard_name,
        sq.hazard_type as hazard_category,
        sq.hazard_level,
        sq.remark as hazard_description
        FROM
        tcps_safety_quality sq
        LEFT JOIN
        tcps_project p ON p.id = sq.project_id
        WHERE 1=1
        <if test="entity.hazardLevel != null and entity.hazardLevel != ''">
            AND sq.hazard_level = #{entity.hazardLevel}
        </if>
        <if test="entity.hazardCategory != null and entity.hazardCategory != ''">
            AND sq.hazard_type = #{entity.hazardCategory}
        </if>
        ORDER BY sq.create_time DESC
    </select>

    <select id="selectHazardDiscoveryTimePage" resultMap="HazardDiscoveryTimeMap">
        SELECT
        sq.id,
        ROW_NUMBER() OVER (ORDER BY sq.create_time DESC) as sequence,
        sq.create_time as hazard_discovery_time,
        CONCAT(dict.dic_name, '-', sq.hazard_level) as hazard_name,
        dict.dic_name as hazard_category,
        sq.hazard_level,
        sq.remark as hazard_description
        FROM
        tcps_safety_quality sq
        LEFT JOIN
        tcps_project p ON p.id = sq.project_id
        left join s_dictionary dict on dict.parent_code = 'TCPS_Hidden_Danger' and dict.dic_code = sq.hazard_type
        WHERE 1=1
        <if test="entity.hazardLevel != null and entity.hazardLevel != ''">
            AND sq.hazard_level = #{entity.hazardLevel}
        </if>
        <if test="entity.projectId != null and entity.projectId != ''">
            AND sq.project_id = #{entity.projectId}
        </if>
        <if test="entity.hazardCategory != null and entity.hazardCategory != ''">
            AND sq.hazard_type = #{entity.hazardCategory}
        </if>
        ORDER BY sq.create_time DESC
    </select>

    <resultMap id="SafetyQualityDetailMap" type="com.yinshu.tact.entity.dto.SafetyQualityDetailDTO">
        <result property="id" column="id"/>
        <result property="projectId" column="project_id"/>
        <result property="projectName" column="project_name"/>
        <result property="bidSection" column="bid_section"/>
        <result property="checkType" column="check_type"/>
        <result property="supervisorUnit" column="supervisor_unit"/>
        <result property="supervisorName" column="supervisor_name"/>
        <result property="supervisorPhone" column="supervisor_phone"/>
        <result property="constructUnit" column="construct_unit"/>
        <result property="constructName" column="construct_name"/>
        <result property="constructPhone" column="construct_phone"/>
        <result property="safetyName" column="safety_name"/>
        <result property="safetyPhone" column="safety_phone"/>
        <result property="quality" column="quality"/>
        <result property="checkObject" column="check_object"/>
        <result property="hazardLevel" column="hazard_level"/>
        <result property="hazardType" column="hazard_type"/>
        <result property="pictures" column="pictures"/>
        <result property="videos" column="videos"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createUser" column="create_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="selectSafetyQualityById" resultMap="SafetyQualityDetailMap">
        SELECT
        sq.id,
        sq.project_id,
        p.project_name,
        sq.bid_section,
        sq.check_type,
        sq.supervisor_unit,
        sq.supervisor_name,
        sq.supervisor_phone,
        sq.construct_unit,
        sq.construct_name,
        sq.construct_phone,
        sq.safety_name,
        sq.safety_phone,
        sq.quality,
        sq.check_object,
        sq.hazard_level,
        sq.hazard_type,
        sq.pictures,
        sq.videos,
        sq.remark,
        sq.create_time,
        sq.create_user,
        sq.update_time
        FROM
        tcps_safety_quality sq
        LEFT JOIN
        tcps_project p ON p.id = sq.project_id
        WHERE sq.id = #{id}
    </select>
    <select id="getHazardTypeByProjectId" resultMap="HazardDiscoveryTimeMap">
        SELECT
            sq.id,
            CONCAT(sq.hazard_type, '-', sq.hazard_level) as hazard_name
        FROM
            tcps_safety_quality sq
        <where>
            sq.project_id = #{projectId}
            <if test="codes != null and codes.size() > 0">
                AND sq.hazard_type IN
                <foreach item="item" collection="codes" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="listHazardNameByIds" resultMap="HazardDiscoveryTimeMap">
        SELECT
            sq.id,
            CONCAT(sq.hazard_type, '-', sq.hazard_level) as hazard_name
        FROM
            tcps_safety_quality sq
        WHERE
            sq.id IN
            <foreach item="item" collection="collect" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
    </select>
</mapper>
