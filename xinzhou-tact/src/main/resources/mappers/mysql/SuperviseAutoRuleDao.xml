<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.tact.dao.SuperviseAutoRuleDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yinshu.tact.entity.SuperviseAutoRule">
        <id column="id" property="id" />
        <result column="alarm_type_name" property="alarmTypeName" />
        <result column="waiting_minutes" property="waitingMinutes" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, alarm_type_name, waiting_minutes
    </sql>


	<select id="queryList" resultMap="BaseResultMap" parameterType="com.yinshu.tact.entity.SuperviseAutoRule">
		select <include refid="Base_Column_List"></include>
		from tocc_supervise_auto_rule
		<where>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.alarmTypeName != null and entity.alarmTypeName != '' ">
					and alarm_type_name = #{entity.alarmTypeName}
				</if>
				<if test="entity.waitingMinutes != null and entity.waitingMinutes != '' ">
					and waiting_minutes = #{entity.waitingMinutes}
				</if>
		</where>
	</select>

	<!-- 分页查询  -->
	<select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.tact.entity.SuperviseAutoRule">
		select <include refid="Base_Column_List"></include>
		from tocc_supervise_auto_rule
		<where>
			<if test="entity.keyword != null and entity.keyword != '' ">
				(
				<trim prefix="" prefixOverrides="or">
						or alarm_type_name like concat(concat('%', #{entity.keyword}), '%')
				</trim>
				)
			</if>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.alarmTypeName != null and entity.alarmTypeName != '' ">
					and alarm_type_name = #{entity.alarmTypeName}
				</if>
				<if test="entity.waitingMinutes != null and entity.waitingMinutes != '' ">
					and waiting_minutes = #{entity.waitingMinutes}
				</if>
		</where>
	</select>



</mapper>