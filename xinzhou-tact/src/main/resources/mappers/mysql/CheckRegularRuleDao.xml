<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.tact.dao.CheckRegularRuleDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yinshu.tact.entity.CheckRegularRule">
        <id column="id" property="id" />
        <result column="enterprise_name" property="enterpriseName" />
        <result column="check_responder" property="checkResponder" />
        <result column="check_receive_phone" property="checkReceivePhone" />
        <result column="check_content" property="checkContent" />
        <result column="status" property="status" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
        <result column="check_exec_time_str" property="checkExecTimeStr" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, enterprise_name, check_responder, check_receive_phone, check_content, status, create_by, create_time, update_by, update_time, remark, check_exec_time_str
    </sql>


	<select id="queryList" resultMap="BaseResultMap" parameterType="com.yinshu.tact.entity.CheckRegularRule">
		select <include refid="Base_Column_List"></include>
		from tocc_check_regular_rule
		<where>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.enterpriseName != null and entity.enterpriseName != '' ">
					and enterprise_name = #{entity.enterpriseName}
				</if>
				<if test="entity.checkResponder != null and entity.checkResponder != '' ">
					and check_responder = #{entity.checkResponder}
				</if>
				<if test="entity.checkReceivePhone != null and entity.checkReceivePhone != '' ">
					and check_receive_phone = #{entity.checkReceivePhone}
				</if>
				<if test="entity.checkContent != null and entity.checkContent != '' ">
					and check_content = #{entity.checkContent}
				</if>
				<if test="entity.status != null and entity.status != '' ">
					and status = #{entity.status}
				</if>
				<if test="entity.createBy != null and entity.createBy != '' ">
					and create_by = #{entity.createBy}
				</if>
				<if test="entity.createTime != null and entity.createTime != '' ">
					and create_time = #{entity.createTime}
				</if>
				<if test="entity.updateBy != null and entity.updateBy != '' ">
					and update_by = #{entity.updateBy}
				</if>
				<if test="entity.updateTime != null and entity.updateTime != '' ">
					and update_time = #{entity.updateTime}
				</if>
				<if test="entity.remark != null and entity.remark != '' ">
					and remark = #{entity.remark}
				</if>
				<if test="entity.checkExecTimeStr != null and entity.checkExecTimeStr != '' ">
					and check_exec_time_str = #{entity.checkExecTimeStr}
				</if>
		</where>
	</select>

	<!-- 分页查询  -->
	<select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.tact.entity.CheckRegularRule">
		select <include refid="Base_Column_List"></include>
		from tocc_check_regular_rule
		<where>
			<if test="entity.keyword != null and entity.keyword != '' ">
				(
				<trim prefix="" prefixOverrides="or">
						or enterprise_name like concat(concat('%', #{entity.keyword}), '%')
						or check_responder like concat(concat('%', #{entity.keyword}), '%')
						or check_receive_phone like concat(concat('%', #{entity.keyword}), '%')
						or check_content like concat(concat('%', #{entity.keyword}), '%')
						or status like concat(concat('%', #{entity.keyword}), '%')
						or create_by like concat(concat('%', #{entity.keyword}), '%')
						or update_by like concat(concat('%', #{entity.keyword}), '%')
						or remark like concat(concat('%', #{entity.keyword}), '%')
						or check_exec_time_str like concat(concat('%', #{entity.keyword}), '%')
				</trim>
				)
			</if>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.enterpriseName != null and entity.enterpriseName != '' ">
					and enterprise_name = #{entity.enterpriseName}
				</if>
				<if test="entity.checkResponder != null and entity.checkResponder != '' ">
					and check_responder = #{entity.checkResponder}
				</if>
				<if test="entity.checkReceivePhone != null and entity.checkReceivePhone != '' ">
					and check_receive_phone = #{entity.checkReceivePhone}
				</if>
				<if test="entity.checkContent != null and entity.checkContent != '' ">
					and check_content = #{entity.checkContent}
				</if>
				<if test="entity.status != null and entity.status != '' ">
					and status = #{entity.status}
				</if>
				<if test="entity.createBy != null and entity.createBy != '' ">
					and create_by = #{entity.createBy}
				</if>
				<if test="entity.createTime != null and entity.createTime != '' ">
					and create_time = #{entity.createTime}
				</if>
				<if test="entity.updateBy != null and entity.updateBy != '' ">
					and update_by = #{entity.updateBy}
				</if>
				<if test="entity.updateTime != null and entity.updateTime != '' ">
					and update_time = #{entity.updateTime}
				</if>
				<if test="entity.remark != null and entity.remark != '' ">
					and remark = #{entity.remark}
				</if>
				<if test="entity.checkExecTimeStr != null and entity.checkExecTimeStr != '' ">
					and check_exec_time_str = #{entity.checkExecTimeStr}
				</if>
		</where>
		order by create_time desc
	</select>
    <select id="getAllEnterpriseName" resultType="java.lang.String">
		select distinct enterprise_name from tocc_check_regular_rule
	</select>


</mapper>