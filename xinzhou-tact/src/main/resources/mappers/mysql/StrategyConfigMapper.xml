<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.tact.dao.StrategyConfigMapper">
    <resultMap id="BaseResultMap" type="com.yinshu.tact.entity.VO.StrategyConfigVO">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="type" jdbcType="INTEGER" property="type" />
        <result column="indicator" jdbcType="VARCHAR" property="indicator" />
        <result column="weight" jdbcType="INTEGER" property="weight" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="rating_level" jdbcType="VARCHAR" property="ratingLevel" />
        <result column="score" jdbcType="INTEGER" property="score" />
    </resultMap>
    <select id="listByType" resultMap="BaseResultMap">
        SELECT
            t.id,
            t.`type`,
            t.indicator,
            t.weight,
            t.create_time,
            t.update_time,
            t1.rating_level,
            t1.score
        FROM tiss_strategy_config t left join tiss_score_config t1 on t.id = t1.indicator_id
        WHERE t.type = #{type} order by t.id desc
    </select>

</mapper>