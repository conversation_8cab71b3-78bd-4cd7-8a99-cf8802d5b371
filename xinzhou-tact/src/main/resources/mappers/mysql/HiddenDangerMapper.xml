<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.tact.dao.HiddenDangerDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yinshu.tact.entity.HiddenDanger">
        <id column="id" property="id"/>
        <result column="rectification_name" property="rectificationName"/>
        <result column="rectification_items" property="rectificationItems"/>
        <result column="rectification_measure" property="rectificationMeasure"/>
        <result column="rectification_deadline" property="rectificationDeadline"/>
        <result column="status" property="status"/>
        <result column="completion_time" property="completionTime"/>
        <result column="rectification_description" property="rectificationDescription"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="parent_id" property="parentId"/>
        <result column="rectification_form" property="rectificationForm"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, rectification_name, rectification_items, rectification_measure, rectification_deadline,
        status, completion_time, rectification_description, remark, create_time, update_time, create_by, update_by,
        parent_id, rectification_form
    </sql>


    <select id="queryList" resultMap="BaseResultMap" parameterType="com.yinshu.tact.entity.HiddenDanger">
        select
        <include refid="Base_Column_List"></include>
        from tocc_hidden_danger
        <where>
            <if test="entity.id != null and entity.id != '' ">
                and id = #{entity.id}
            </if>
            <if test="entity.rectificationName != null and entity.rectificationName != '' ">
                and rectification_name = #{entity.rectificationName}
            </if>
            <if test="entity.rectificationItems != null and entity.rectificationItems != '' ">
                and rectification_items = #{entity.rectificationItems}
            </if>
            <if test="entity.rectificationMeasure != null and entity.rectificationMeasure != '' ">
                and rectification_measure = #{entity.rectificationMeasure}
            </if>
            <if test="entity.rectificationDeadline != null and entity.rectificationDeadline != '' ">
                and rectification_deadline = #{entity.rectificationDeadline}
            </if>
            <if test="entity.status != null">
                and status = #{entity.status}
            </if>
            <if test="entity.completionTime != null">
                and completion_time = #{entity.completionTime}
            </if>
            <if test="entity.rectificationDescription != null and entity.rectificationDescription != '' ">
                and rectification_description = #{entity.rectificationDescription}
            </if>
            <if test="entity.remark != null and entity.remark != '' ">
                and remark = #{entity.remark}
            </if>
            <if test="entity.createTime != null">
                and create_time = #{entity.createTime}
            </if>
            <if test="entity.updateTime != null">
                and update_time = #{entity.updateTime}
            </if>
            <if test="entity.createBy != null and entity.createBy != '' ">
                and create_by = #{entity.createBy}
            </if>
            <if test="entity.updateBy != null and entity.updateBy != '' ">
                and update_by = #{entity.updateBy}
            </if>
            <if test="entity.parentId != null and entity.parentId != '' ">
                and parent_id = #{entity.parentId}
            </if>
            <if test="entity.rectificationForm != null and entity.rectificationForm != '' ">
                and rectification_form = #{entity.rectificationForm}
            </if>
        </where>
    </select>

    <!-- 分页查询  -->
    <select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.tact.entity.HiddenDanger">
        select
        <include refid="Base_Column_List"></include>
        from tocc_hidden_danger
        <where>
            <if test="entity.keyword != null and entity.keyword != '' ">
                (
                <trim prefix="" prefixOverrides="or">
                    or rectification_name like concat(concat('%', #{entity.keyword}), '%')
                    or rectification_items like concat(concat('%', #{entity.keyword}), '%')
                    or rectification_description like concat(concat('%', #{entity.keyword}), '%')
                    or remark like concat(concat('%', #{entity.keyword}), '%')
                    or create_by like concat(concat('%', #{entity.keyword}), '%')
                    or update_by like concat(concat('%', #{entity.keyword}), '%')
                    or rectification_form like concat(concat('%', #{entity.keyword}), '%')
                </trim>
                )
            </if>
            <if test="entity.id != null and entity.id != '' ">
                and id = #{entity.id}
            </if>
            <if test="entity.rectificationName != null and entity.rectificationName != '' ">
                and rectification_name = #{entity.rectificationName}
            </if>
            <if test="entity.rectificationItems != null and entity.rectificationItems != '' ">
                and rectification_items = #{entity.rectificationItems}
            </if>
            <if test="entity.rectificationMeasure != null and entity.rectificationMeasure != '' ">
                and rectification_measure = #{entity.rectificationMeasure}
            </if>
            <if test="entity.rectificationDeadline != null and entity.rectificationDeadline != '' ">
                and rectification_deadline = #{entity.rectificationDeadline}
            </if>
            <if test="entity.status != null and entity.status != '' ">
                and status = #{entity.status}
            </if>
            <if test="entity.completionTime != null">
                and completion_time = #{entity.completionTime}
            </if>
            <if test="entity.rectificationDescription != null and entity.rectificationDescription != '' ">
                and rectification_description = #{entity.rectificationDescription}
            </if>
            <if test="entity.remark != null and entity.remark != '' ">
                and remark = #{entity.remark}
            </if>
            <if test="entity.createTime != null">
                and create_time = #{entity.createTime}
            </if>
            <if test="entity.updateTime != null">
                and update_time = #{entity.updateTime}
            </if>
            <if test="entity.createBy != null and entity.createBy != '' ">
                and create_by = #{entity.createBy}
            </if>
            <if test="entity.updateBy != null and entity.updateBy != '' ">
                and update_by = #{entity.updateBy}
            </if>
            <if test="entity.parentId != null and entity.parentId != '' ">
                and parent_id = #{entity.parentId}
            </if>
            <if test="entity.rectificationForm != null and entity.rectificationForm != '' ">
                and rectification_form = #{entity.rectificationForm}
            </if>
        </where>
        order by create_time desc
    </select>
    <select id="getByParentId" resultType="com.yinshu.tact.entity.HiddenDanger">
        select
        <include refid="Base_Column_List"></include>
        from tocc_hidden_danger
        where parent_id = #{parentId} and status != '2'
        order by create_time desc
        limit 1
    </select>



</mapper>