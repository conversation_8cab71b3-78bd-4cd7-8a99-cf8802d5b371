<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.mcu.dao.ChannelGroupMapper">

    <resultMap id="BaseResultMap" type="com.yinshu.mcu.entity.ChannelGroup">
            <id property="id" column="ID" jdbcType="INTEGER"/>
            <result property="channelCode" column="CHANNELCODE" jdbcType="VARCHAR"/>
            <result property="mcId" column="MCID" jdbcType="INTEGER"/>
            <result property="name" column="NAME" jdbcType="VARCHAR"/>
            <result property="parentId" column="PARENTID" jdbcType="VARCHAR"/>
            <result property="businessGroupId" column="BUSINESSGROUPID" jdbcType="VARCHAR"/>
            <result property="syncTime" column="SYNCTIME" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,CHANNELCODE,MCID,
        NAME,PARENTID,BUSINESSGROUPID,
        SYNCTIME
    </sql>
</mapper>
