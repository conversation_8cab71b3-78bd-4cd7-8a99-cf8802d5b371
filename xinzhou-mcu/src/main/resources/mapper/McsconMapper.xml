<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.mcu.dao.McsconMapper">

    <resultMap id="BaseResultMap" type="com.yinshu.mcu.entity.Mcscon">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="plateName" column="PLATENAME" jdbcType="VARCHAR"/>
            <result property="platCode" column="PLATCODE" jdbcType="VARCHAR"/>
            <result property="port" column="PORT" jdbcType="INTEGER"/>
            <result property="username" column="USERNAME" jdbcType="VARCHAR"/>
            <result property="password" column="PASSWORD" jdbcType="VARCHAR"/>
            <result property="type" column="TYPE" jdbcType="VARCHAR"/>
            <result property="plateIp" column="PLATEIP" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,PLATENAME,PLATCODE,
        PORT,USERNAME,PASSWORD,
        TYPE,PLATEIP
    </sql>
</mapper>
