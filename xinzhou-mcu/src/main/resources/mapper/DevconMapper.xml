<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.mcu.dao.DevconMapper">

	<resultMap id="BaseResultMap" type="com.yinshu.mcu.entity.Devcon">
		<id property="id" column="ID" jdbcType="INTEGER" />
		<result property="channelCode" column="CHANNELCODE" jdbcType="VARCHAR" />
		<result property="mcId" column="MCID" jdbcType="VARCHAR" />
		<result property="name" column="NAME" jdbcType="VARCHAR" />
		<result property="owner" column="OWNER" jdbcType="VARCHAR" />
		<result property="civilCode" column="CIVILCODE" jdbcType="VARCHAR" />
		<result property="parentId" column="PARENTID" jdbcType="VARCHAR" />
		<result property="status" column="STATUS" jdbcType="INTEGER" />
		<result property="longitude" column="LONGITUDE" jdbcType="DOUBLE" />
		<result property="latitude" column="LATITUDE" jdbcType="DOUBLE" />
		<result property="businessGroupId" column="BUSINESSGROUPID" jdbcType="VARCHAR" />
		<result property="syncTime" column="SYNCTIME" jdbcType="TIMESTAMP" />
	</resultMap>

	<sql id="Base_Column_List">
		ID,CHANNELCODE,MCID,
		NAME,OWNER,CIVILCODE,
		PARENTID,STATUS,LONGITUDE,
		LATITUDE,BUSINESSGROUPID,SYNCTIME
	</sql>

	<select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.mcu.entity.Devcon">
		select NAME, LONGITUDE, LATITUDE from Devcon
		<where>
			
		</where>
	</select>

</mapper>
