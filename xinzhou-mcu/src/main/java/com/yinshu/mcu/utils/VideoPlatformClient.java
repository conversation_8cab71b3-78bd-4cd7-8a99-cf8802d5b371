package com.yinshu.mcu.utils;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

@Component
public class VideoPlatformClient {
    private  String appKey="49356182573c57878238214a07c8f01c";
    private  String secretKey="0596d607450bf4a00ac3d7c90fea74f61816dd45";
    private  String baseUrl="http://183.203.217.212:28080/";
   //private  String baseUrl="http://220.194.168.183:7080/";
    // 生成签名 [[PDF第2页]]
    private String generateSign(Map<String, String> params) {
        // 添加AppKey到参数列表
        params.put("AppKey", appKey);
        Map<String, String> sortedMap = new TreeMap<>(params);
        StringBuilder paramBuilder = new StringBuilder();
        for (Map.Entry<String, String> entry : sortedMap.entrySet()) {
            if (entry.getValue() != null) {  // 忽略空值参数
                paramBuilder.append(entry.getKey()).append(entry.getValue());
            }
        }
        paramBuilder.append("SecretKey").append(secretKey);
        return DigestUtils.shaHex(paramBuilder.toString());
    }

    // 查询摄像枪列表
    public String getCameraList(String apiVersion) throws Exception {
        Map<String, String> params = new HashMap<>();
        String sign = generateSign(params);

        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet request = new HttpGet(baseUrl + "/api/cameraList?AppKey=" + appKey + "&Sign=" + sign);

        // 设置API版本头 [[PDF第5页]]
        if (apiVersion != null) {
            request.setHeader("ApiVersion", apiVersion);
        }

        return executeRequest(httpClient, request);
    }

    // 获取视频播放地址
    public String getVideoUrl(String cameraId, Integer videoType) {
        Map<String, String> params = new HashMap<>();
        params.put("CameraID", cameraId);
        if (videoType != null) params.put("VideoType", videoType.toString());

        String sign = generateSign(params);

        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet request = new HttpGet(baseUrl + "/api/getVideoUrl?AppKey="
                + appKey + "&Sign=" + sign + "&CameraID=" + cameraId
                + (videoType != null ? "&VideoType=" + videoType : ""));

        return executeRequest(httpClient, request);
    }

    // 获取摄像枪在线状态
    public String getCameraStatus() throws Exception {
        Map<String, String> params = new HashMap<>();
        String sign = generateSign(params);

        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet request = new HttpGet(baseUrl + "/api/getCameraStatus?AppKey="
                + appKey + "&Sign=" + sign);

        return executeRequest(httpClient, request);
    }

    // 获取所有类型视频播放地址
    public String getAllTypeVideoUrl(String cameraId, Integer videoType) throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("CameraID", cameraId);
        if (videoType != null) params.put("VideoType", videoType.toString());

        String sign = generateSign(params);

        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet request = new HttpGet(baseUrl + "/api/getAllTypeVideoUrl?AppKey="
                + appKey + "&Sign=" + sign + "&CameraID=" + cameraId
                + (videoType != null ? "&VideoType=" + videoType : ""));

        return executeRequest(httpClient, request);
    }

    // 获取授权摄像枪截图（POST请求）
    public String getCameraSnapshot(String cameraIds) throws Exception {
        Map<String, String> params = new HashMap<>();
        if (cameraIds != null) params.put("CameraID", cameraIds);

        String sign = generateSign(params);

        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost request = new HttpPost(baseUrl + "/api/getCameraSnapshot");

        // 构建JSON请求体
        JSONObject jsonBody = new JSONObject();
        jsonBody.put("AppKey", appKey);
        jsonBody.put("Sign", sign);
        if (cameraIds != null) jsonBody.put("CameraID", cameraIds);

        StringEntity entity = new StringEntity(jsonBody.toString(), "UTF-8");
        entity.setContentType("application/json");
        request.setEntity(entity);

        return executeRequest(httpClient, request);
    }

    // 通用请求执行方法
    private String executeRequest(CloseableHttpClient httpClient, HttpGet request) {
        try (CloseableHttpResponse response = httpClient.execute(request)) {

                return EntityUtils.toString(response.getEntity());
            } catch (IOException e) {
                return e.getMessage();
            }

    }

    private String executeRequest(CloseableHttpClient httpClient, HttpPost request) throws Exception {
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            return EntityUtils.toString(response.getEntity());
        }
    }

    public static void main(String[] args) {
        try {
            VideoPlatformClient client = new VideoPlatformClient();

            String baseUrl = "https://example.com";

            // 1. 查询摄像枪列表
            String cameraList = client.getCameraList(null);
            System.out.println("摄像枪列表：" + cameraList);

            // 2. 获取视频播放地址
            String videoUrl = client.getVideoUrl("04e639fa-060c-11ea-b158-00ffc0c2e0ae", 0);
            System.out.println("视频地址：" + videoUrl);

            // 3. 获取摄像枪状态
            String cameraStatus = client.getCameraStatus();
            System.out.println("摄像枪状态：" + cameraStatus);

            // 4. 获取所有类型视频地址
            String allTypeUrl = client.getAllTypeVideoUrl("04e639fa-060c-11ea-b158-00ffc0c2e0ae", 1);
            System.out.println("所有视频地址：" + allTypeUrl);

            // 5. 获取截图
            String snapshot = client.getCameraSnapshot("04e639fa-060c-11ea-b158,04e639fa-060c-11ea-b159");
            System.out.println("截图结果：" + snapshot);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }



}
