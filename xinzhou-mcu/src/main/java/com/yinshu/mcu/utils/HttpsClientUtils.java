package com.yinshu.mcu.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.utils.DateUtils;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 数据同步请求
 * 2020-09-27
 * lfh
 *
 */
@Component
public class HttpsClientUtils {
	
	private final static Logger logger = LoggerFactory.getLogger(HttpsClientUtils.class);
	
	@Autowired
	private RestTemplate restTemplate;
	
	private final String AppKey = "a1d18ee4f71d8346e1aebb4a";
	private final String Secret = "cc30441fab74875760d6a8b3";
	
	
	public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        
        // 设置连接超时时间（单位：毫秒）
        requestFactory.setConnectTimeout(3000);
        // 设置读取超时时间（单位：毫秒）
        requestFactory.setReadTimeout(3000);
        
        return new RestTemplate(requestFactory);
    }

	/**
	 * 发送post请求json数据
	 * @param url
	 * @param json
	 * @return
	 */
	public Map<String, Object> doPost(String url, String json) {
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
			headers.add("Accept", MediaType.APPLICATION_JSON_VALUE.toString());
			HttpEntity<String> formEntity = new HttpEntity<String>(json, headers);
			ResponseEntity<Map> responseMap = restTemplate().postForEntity(url, formEntity, Map.class);
			return responseMap.getBody();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			Map<String, Object> resultMap = new HashMap<String, Object>();
			resultMap.put("9999", e.getMessage());
			return resultMap;
		}
		
	}
	
	/**
	 * 发送post请求Map数据
	 * @param url
	 * @param map
	 * @return
	 */
	public void doPost(String url, Map<String, Object> map) {
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
		headers.add("Accept", MediaType.APPLICATION_JSON_VALUE.toString());
		HttpEntity<Map<String,Object>> formEntity = new HttpEntity<Map<String,Object>>(map,headers);
		restTemplate().postForEntity(url, formEntity, String.class);
	}
	
	/**
	 * 使用post发送表单数据
	 * @param url TODO
	 * @param form TODO
	 * @return TODO
	 */
	public Map<String, Object> doPost(String url, MultiValueMap<String, Object> form) {
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.MULTIPART_FORM_DATA);
			headers.add("Accept", MediaType.APPLICATION_JSON_VALUE.toString());
			HttpEntity<MultiValueMap<String,Object>> formEntity = new HttpEntity<MultiValueMap<String,Object>>(form,headers);
			ResponseEntity<Map> responseMap = restTemplate.postForEntity(url, formEntity, Map.class);
			return responseMap.getBody();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			Map<String, Object> resultMap = new HashMap<String, Object>();
			resultMap.put("rspCode", "-1");
			resultMap.put("rspMsg", e.getMessage());
			return resultMap;
		}
	}
	
	/**
	 * 推送极光消息
	 * @param json
	 * @return
	 */
	public Map<String, Object> jPush(String json){
		try {
			
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
			headers.add("Accept", MediaType.APPLICATION_JSON_VALUE);
			String Authorization = "Basic " + Base64.encodeBase64String((AppKey+":"+Secret).getBytes());
			headers.add("Authorization", Authorization);
			HttpEntity<String> formEntity = new HttpEntity<String>(json, headers);
			ResponseEntity<Map> responseMap = restTemplate.postForEntity("https://api.jpush.cn/v3/push", formEntity, Map.class);
			return responseMap.getBody();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			Map<String, Object> resultMap = new HashMap<String, Object>();
			resultMap.put("rspCode", "-1");
			resultMap.put("rspMsg", e.getMessage());
			return resultMap;
		}
		
	}
	
	/**
	 * 使用post发送表单数据
	 * @param url
	 * @param map
	 * @return
	 */
	public ResultData<?> uploadFile(String url, String fileName, String filePath) {
		try {
			long start = System.currentTimeMillis();
			logger.info("url:{}, file:{}, start:{}", url, filePath + fileName, DateUtils.getDateTime());
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.MULTIPART_FORM_DATA);
		    FileSystemResource fileSystemResource = new FileSystemResource(filePath);
	        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
	        params.add(fileName, fileSystemResource);
			HttpEntity<MultiValueMap<String,Object>> formEntity = new HttpEntity<MultiValueMap<String,Object>>(params, headers);
			String response = restTemplate.postForObject(url, formEntity, String.class);
			JSONObject jsonObject = JSON.parseObject(response);
			logger.info("times:{}, result:{}", (System.currentTimeMillis() - start), jsonObject.toString());
			if(!jsonObject.get("ret").equals("200")) {
				JSONObject data = jsonObject.getJSONObject("data");
				return new ResultData<String>(data.getString("file"));
			}
			return new ResultData<>(jsonObject.getString("ret"), jsonObject.getString("msg"));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return new ResultData<>("99", e.getMessage());
		}
	}
	
	/**
	 * 使用post发送表单数据
	 * @param url
	 * @param map
	 * @return
	 */
	public ResultData<?> doPostByForm(String url, Map<String, Object> formData) {
		try {
			long start = System.currentTimeMillis();
			logger.info("url:{}, params:{}", url, formData.toString());
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
			headers.add("uid", "admin");
			MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();
			for(Map.Entry<String, Object> entry : formData.entrySet()) {
				params.add(entry.getKey(), entry.getValue());
			}
			HttpEntity<MultiValueMap<String,Object>> formEntity = new HttpEntity<MultiValueMap<String,Object>>(params, headers);
			String response = restTemplate.postForObject(url, formEntity, String.class);
			JSONObject jsonObject = JSON.parseObject(response);
			logger.info("times:{}, result:{}", (System.currentTimeMillis() - start), jsonObject.toString());
			
			/**假如是短信发送返回数据*/
			if(formData.containsKey("templateId")) {
				JSONArray jsonData = jsonObject.getJSONArray("data");
				return new ResultData<Object>(jsonData);
			}
			
			/**天地阳光http接口返回*/
			if(!jsonObject.get("ret").equals("200")) {
				JSONObject jsonData = jsonObject.getJSONObject("data");
				//if(jsonData.getString("code").equals("0")) {
					return new ResultData<Object>(jsonData);
				//} else {
					//return new ResultData<>(jsonData.getString("code"), jsonData.getString("msg"));
				//}
			}
			return new ResultData<>(jsonObject.getString("ret"), jsonObject.getString("msg"));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return new ResultData<>("99", e.getMessage());
		}
	}
	
}
