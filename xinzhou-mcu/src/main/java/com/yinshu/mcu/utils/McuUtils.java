package com.yinshu.mcu.utils;

import org.springframework.util.ObjectUtils;

public class McuUtils {

    public static boolean isIpAddress(String ip) {
        boolean b1 = ip.matches("([1-9]|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])(\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])){3}");
        return b1;
    }


    /** 给请求添加头信息
     *
     * @param type          操作菜单
     * @param action        操作动作
     * @param transactionid 消息会话id
     * @param content
     * @return
     */
    public static String addHeader(String type, String action, String transactionid, String content) {
        String requeString = null;
        if (!(ObjectUtils.isEmpty(type) || ObjectUtils.isEmpty(action) ||
                ObjectUtils.isEmpty(transactionid) || ObjectUtils.isEmpty(content))) {
            requeString = "Type:" + type + "\r\n" + "Action: " + action + "\r\n" + "Device: viosys-ds" + "\r\n"
                    + "Transactionid:" + transactionid + "\r\n" + "Content:\r\n" + "<body>\r\n" + content
                    + "\r\n</body>";
        }
        return requeString;
    }

}
