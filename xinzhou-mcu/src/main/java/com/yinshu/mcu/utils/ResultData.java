package com.yinshu.mcu.utils;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ResultData<T> {
	/**
     * 状态码，比如1000代表响应成功
     */
    private String code;
    /**
     * 响应信息，用来说明响应情况
     */
    private String msg;
    /**
     * 响应的具体数据
     */
    private T data;

    public ResultData(T data) {
        this("0", "success", data);
    }

    public ResultData(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

}
