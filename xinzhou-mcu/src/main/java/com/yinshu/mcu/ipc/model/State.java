package com.yinshu.mcu.ipc.model;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className State
 * @description TODO
 * @date 2025/6/13 09:30
 **/
@Data
public class State {
    /**
     * ipc鉴权用户名
     **/
    private String username;
    /**
     * ipc鉴权密码
     **/
    private String password;
    /**
     * ipc协议类型（rtsp/onvif）
     **/
    private String type;
    /**
     * ipc外观类型（HD Dome/HD Half-Dome/HD Bullet/Controllable Ipc
     **/
    private String appearance;
    /**
     * 设备状态，init（初始化）、working（正常）、unauth（未认证）、disconnect（未连接）、undispatch（未分配）
     **/
    private String deviceState;
    /**
     * 通道状态
     **/
    private List<ChannelState> channelState;
}