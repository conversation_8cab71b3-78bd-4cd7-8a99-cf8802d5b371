package com.yinshu.mcu.ipc.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ChannelState
 * @description TODO 通道状态
 * @date 2025/6/13 09:30
 **/
@Data
public class ChannelState {
    /**
     * 转发状态，connecting（连接中）、working（正常）、exception（异常）、disable（未开启）、timeout（超时）
     **/
    private String relayState;
    /**
     * 录像状态，connecting（连接中）、working（正常）、warning（警告）、error（异常）、disable（未开启）、timeout（超时）
     **/
    private String recordState;
    /**
     * 转发url
     **/
    private String relayUrl;
    /**
     * websocket直播url
     **/
    private String wsUrl;
}