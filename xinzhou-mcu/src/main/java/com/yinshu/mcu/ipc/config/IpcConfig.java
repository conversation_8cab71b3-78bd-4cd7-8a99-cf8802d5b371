package com.yinshu.mcu.ipc.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className IpcConfig
 * @description TODO ipc 配置
 * @date 2025/6/13 09:41
 **/
@Component
@ConfigurationProperties("ipc")
@Data
public class IpcConfig {

    /**
     * 接口调用前缀 http://x.x.x.x:8080/
     **/
    private String url = "http://***************:7070/guidao/rest/v3/cluster";

}
