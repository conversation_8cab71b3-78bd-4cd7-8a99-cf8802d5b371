package com.yinshu.mcu.ipc.query;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RecordQueryRequest
 * @description TODO RecordQueryRequest
 * @date 2025/6/13 09:53
 **/

import lombok.Data;

import java.io.Serializable;

@Data
public class RecordQueryRequest implements Serializable {
    /**
     * ipcuuid
     **/
    private String uuid;
    /**
     * 录像开始时间
     **/
    private String startTime;
    /**
     * 录像结束时间
     **/
    private String endTime;
    /**
     * 聚合类型，可选值none（单独,只适用非分布式存储）、day（按天聚合）、month（按月聚合）、all（全部聚合），
     **/
    private String aggregation = "all";
    /**
     * 查询缓存，默认false，该开关只适用非分布式存储
     **/
    private Boolean cache;
    /**
     * 只查询锁定录像，默认false，该开关只适用非分布式存储
     **/
    private Boolean onlyLock;
    /**
     * 是否同步查询灾备录像，默认false
     **/
    private Boolean copyback;
    /**
     * 只查询告警录像，默认false
     **/
    private Boolean alarm;
}