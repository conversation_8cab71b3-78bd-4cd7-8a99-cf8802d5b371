package com.yinshu.mcu.ipc.utiils;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.mcu.ipc.config.IpcConfig;
import com.yinshu.mcu.ipc.query.RecordQueryRequest;
import com.yinshu.mcu.ipc.response.IpcResponse;
import com.yinshu.mcu.ipc.response.RtspRecordResponse;
import com.yinshu.sys.manager.SettingManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TrackUtils
 * @description TODO 轨道交通请求utils
 * @date 2025/6/13 09:05
 **/
@Component
public class TrackRequestUtils {

    private static TrackRequestUtils INSTANCE;
    @Resource
    IpcConfig config;

    @Autowired
    private SettingManager settingManager;


    /**
     * @param conditionKey   筛选项名
     * @param conditionValue 筛选项值
     * @return com.yinshu.mcu.ipc.response.IpcResponse
     * <AUTHOR>
     * @description //TODO 获取ipc列表
     * @date 2025/6/13 09:50
     **/
    public static IpcResponse getIpcs(String conditionKey, String conditionValue) {
        String url = INSTANCE.getUrl() + "/ipcs";
        if (StringUtils.isNotEmpty(conditionKey) && StringUtils.isNotEmpty(conditionValue)) {
            url = url + "?conditionKey=" + conditionKey + "&conditionValue=" + conditionValue;
        }
        String body = HttpRequest.get(url)
                .execute().body();
        return JSON.parseObject(body, IpcResponse.class);
    }

    /**
     * @param request 查询参数
     * @return com.yinshu.mcu.ipc.response.RtspRecordResponse
     * <AUTHOR>
     * @description //TODO 获取录像列表
     * @date 2025/6/13 09:58
     **/
    public static RtspRecordResponse getRtspRecord(RecordQueryRequest request) {
        JSONObject object = JSONObject.parseObject(JSONObject.toJSONString(request));
        Map<String, Object> map = object.getInnerMap();
        String url = INSTANCE.getUrl() + "/record/" + request.getUuid();
        String params = map.entrySet().stream()
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
        if (!params.isEmpty()) {
            url = url + "?" + params;
        }
        String body = HttpRequest.get(url)
                .execute().body();
        return JSON.parseObject(body, RtspRecordResponse.class);
    }

    /**
     * @return com.yinshu.mcu.ipc.response.IpcResponse
     * <AUTHOR>
     * @description //TODO 获取ipc列表
     * @date 2025/6/13 09:50
     **/
    public static IpcResponse getIpcs() {
        return getIpcs(null, null);
    }

    /*
     * <AUTHOR>
     * @description //TODO 获取ipc列表
     * @date 2025/6/13 09:45
     * @return java.lang.String
     **/
    public String getUrl() {
        String url = INSTANCE.settingManager.getByCode("ipc-url").getParmValue();
        if (url == null) {
            url = INSTANCE.config.getUrl();
        }
        return url;
    }

    @PostConstruct
    public void init() {
        INSTANCE = this;
    }
}
