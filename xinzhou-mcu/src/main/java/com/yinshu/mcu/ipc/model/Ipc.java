package com.yinshu.mcu.ipc.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className Ipc
 * @description TODO ipc信息
 * @date 2025/6/13 09:30
 **/
@Data
public class Ipc {
    /**
     * ipc uuid
     **/
    private String uuid;
    /**
     * ipc名称
     **/
    private String name;
    /**
     * ipc ip
     **/
    private String ip;
    /**
     * ipc厂商，可选值HIK（海康）、DAHUA（大华）、YUSHI（宇视）、AXIS（安迅士）、HUAWEI（华为）、OTHER（其他）
     **/
    private String vendor;
    /**
     * ipc所属节点ip
     **/
    private String nodeIp;
    /**
     * ipc录像天数
     **/
    private int recordExpiry;
    /**
     * ipc预估码流大小，单位Mbps
     **/
    private int preStreamSize;
    /**
     * 状态信息
     **/
    private State state;
}