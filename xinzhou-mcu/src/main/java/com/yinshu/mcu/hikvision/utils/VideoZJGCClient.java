package com.yinshu.mcu.hikvision.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.hikvision.artemis.sdk.constant.Constants;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class VideoZJGCClient {
    /**
     * API网关的后端服务上下文为：/artemis
     */
    private static final String ARTEMIS_PATH = "/artemis";
    private static String appKey = "26586509";
    private static String secretKey = "j05eBgQR4zq7PoCmLw4N";
    /**
     * 请根据技术支持提供的实际的平台IP/端口和API网关中的合作方信息更换static静态块中的三个参数.
     * [1 host]
     * host格式为IP：Port，如********:443
     * 当使用https协议调用接口时，IP是平台（nginx）IP，Port是https协议的端口；
     * 当使用http协议调用接口时，IP是artemis服务的IP，Port是artemis服务的端口（默认9016）。
     * [2 appKey和appSecret]
     * 请按照技术支持提供的合作方Key和合作方Secret修改
     * appKey：合作方Key
     * appSecret：合作方Secret
     * 调用前看清接口传入的是什么，是传入json就用doPostStringArtemis方法，是表单提交就用doPostFromArtemis方法
     */
    private static String host = "***************:1443";

    /**
     * 根据需求调整超时时间
     */
    static {
        //连接超时时间
        Constants.DEFAULT_TIMEOUT = 10000;
        //读取超时时间
        Constants.SOCKET_TIMEOUT = 60000;
    }


    /*
     * <AUTHOR>
     * @description //TODO 获取所有摄像头列表
     * @date 2025/5/27 11:51
     * @return com.alibaba.fastjson.JSONArray
     **/
    public static JSONArray callAllPostApiGetCamera() {
        /**
         * https://ip:port/artemis/api/resource/v1/regions
         * 过查阅AI Cloud开放平台文档或网关门户的文档可以看到分页获取区域列表的定义,这是一个POST请求的Rest接口, 入参为JSON字符串，接口协议为https。
         * ArtemisHttpUtil工具类提供了doPostStringArtemis调用POST请求的方法，入参可传JSON字符串, 请阅读开发指南了解方法入参，没有的参数可传null
         */
        ArtemisConfig config = new ArtemisConfig();
        config.setHost(host);
        config.setAppKey(appKey);
        config.setAppSecret(secretKey);

        final String getCamsApi = ARTEMIS_PATH + "/api/resource/v1/cameras";
        Integer pageNo = 1, pageSize = 100;
        // post请求Form表单参数
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("treeCode", "0");
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", getCamsApi);
            }
        };
        JSONArray result = new JSONArray();
        while (true) {
            try {
                paramMap.put("pageNo", pageNo);
                paramMap.put("pageSize", pageSize);
                String body = JSON.toJSON(paramMap).toString();
                String json = ArtemisHttpUtil.doPostStringArtemis(config, path, body, null, null, "application/json");
                JSONObject object = JSONObject.parseObject(json);
                JSONObject data = object.getJSONObject("data");
                JSONArray list = data.getJSONArray("list");
                result.addAll(list);
                if (list.isEmpty() || list.size() >= data.getInteger("total")) {
                    break;
                }
                pageNo++;
            } catch (Exception e) {
                break;
            }
        }

        // 拿到了结果数据，还需要获取真实的状态数据
        if (!result.isEmpty()) {
            Map<String, JSONObject> cameraNameMap = new HashMap<>();
            List<String> codes = new ArrayList<>();
            for (int i = 0; i < result.size(); i++) {
                JSONObject obj = result.getJSONObject(i);
                codes.add(obj.getString("cameraIndexCode"));
                cameraNameMap.putIfAbsent(obj.getString("cameraIndexCode"), obj);
            }
            JSONArray array = callPostApiGetOnlineStateByCameraIndexCodes(codes);

            Map<String, Integer> nameStateMap = new HashMap<>();
            for (int i = 0; i < array.size(); i++) {
                JSONObject obj = array.getJSONObject(i);
                nameStateMap.put(obj.getString("indexCode"), obj.getInteger("online"));
            }
            for (String key : cameraNameMap.keySet()) {
                if (nameStateMap.containsKey(key)) {
                    cameraNameMap.get(key).put("status", nameStateMap.get(key));
                }
            }
        }
        return result;
    }

    /*
     * <AUTHOR>
     * @description //TODO 获取摄像头的播放地址
     * @date 2025/5/27 16:32
     * @param code 摄像头编号
     * @return com.alibaba.fastjson.JSONObject
     **/
    public static JSONObject callPostApiGetVideoUrlByCameraIndexCode(String code) {
        ArtemisConfig config = new ArtemisConfig();
        config.setHost(host);
        config.setAppKey(appKey);
        config.setAppSecret(secretKey);

        final String getCamsApi = ARTEMIS_PATH + "/api/video/v2/cameras/previewURLs";
        Map<String, String> paramMap = new HashMap<String, String>();
        paramMap.put("cameraIndexCode", code);
        paramMap.put("protocol", "hls");
        String body = JSON.toJSON(paramMap).toString();
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", getCamsApi);
            }
        };
        String json = null;
        try {
            json = ArtemisHttpUtil.doPostStringArtemis(config, path, body, null, null, "application/json");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return JSONObject.parseObject(json);
    }

    /*
     * <AUTHOR>
     * @description //TODO 获取制定资源类型的区域
     * @date 2025/5/27 16:32
     * @param type 资源类型
     * @return com.alibaba.fastjson.JSONObject
     **/
    public static JSONArray getAreasByResourceType(String type) {
        ArtemisConfig config = new ArtemisConfig();
        config.setHost(host);
        config.setAppKey(appKey);
        config.setAppSecret(secretKey);

        final String getCamsApi = ARTEMIS_PATH + "/api/irds/v2/region/nodesByParams";
        Integer pageNo = 1, pageSize = 100;
        // post请求Form表单参数
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("resourceType", type);
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", getCamsApi);
            }
        };
        JSONArray result = new JSONArray();
        while (true) {
            try {
                paramMap.put("pageNo", pageNo);
                paramMap.put("pageSize", pageSize);
                String body = JSON.toJSON(paramMap).toString();
                String json = ArtemisHttpUtil.doPostStringArtemis(config, path, body, null, null, "application/json");
                JSONObject object = JSONObject.parseObject(json);
                JSONObject data = object.getJSONObject("data");
                JSONArray list = data.getJSONArray("list");
                result.addAll(list);
                if (list.isEmpty() || list.size() >= data.getInteger("total")) {
                    break;
                }
                pageNo++;
            } catch (Exception e) {
                break;
            }
        }
        return result;
    }

    /*
     * <AUTHOR>
     * @description //TODO 获取摄像头的播放地址
     * @date 2025/5/27 16:32
     * @param code 摄像头编号
     * @return com.alibaba.fastjson.JSONObject
     **/
    public static JSONArray callPostApiGetOnlineStateByCameraIndexCodes(List<String> code) {
        ArtemisConfig config = new ArtemisConfig();
        config.setHost(host);
        config.setAppKey(appKey);
        config.setAppSecret(secretKey);
        final String getCamsApi = ARTEMIS_PATH + "/api/nms/v1/online/camera/get";
        JSONArray result = new JSONArray();
        // 对code进行拆分，500一组
        for (int i = 0; i < code.size(); i += 500) {
            List<String> subList = code.subList(i, Math.min(i + 500, code.size()));
            Integer pageNo = 1, pageSize = 500;
            // post请求Form表单参数
            Map<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("indexCodes", subList);
            Map<String, String> path = new HashMap<String, String>(2) {
                {
                    put("https://", getCamsApi);
                }
            };
            while (true) {
                try {
                    paramMap.put("pageNo", pageNo);
                    paramMap.put("pageSize", pageSize);
                    String body = JSON.toJSON(paramMap).toString();
                    String json = ArtemisHttpUtil.doPostStringArtemis(config, path, body, null, null, "application/json");
                    JSONObject object = JSONObject.parseObject(json);
                    JSONObject data = object.getJSONObject("data");
                    JSONArray list = data.getJSONArray("list");
                    result.addAll(list);
                    if (list.isEmpty() || list.size() >= data.getInteger("total")) {
                        break;
                    }
                    pageNo++;
                } catch (Exception e) {
                    break;
                }
            }
        }
        return result;
    }

    public static void main(String[] args) throws Exception {
        JSONArray area = getAreasByResourceType("camera");
        System.out.println("区域信息:");
        System.out.println(area);

        System.out.println("摄像头信息:");
        JSONArray objects = callAllPostApiGetCamera();
        System.out.println(objects);

        if (!objects.isEmpty()) {
            System.out.println("第一个摄像头播放地址:");
            JSONObject jsonObject = objects.getJSONObject(0);
            JSONObject result = callPostApiGetVideoUrlByCameraIndexCode(jsonObject.getString("cameraIndexCode"));
            System.out.println(result);
        }

    }
}
