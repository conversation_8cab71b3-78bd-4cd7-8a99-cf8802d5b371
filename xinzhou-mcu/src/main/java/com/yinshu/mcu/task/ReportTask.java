//package com.yinshu.mcu.task;
//
//import com.yinshu.sys.service.FileInfoService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.scheduling.annotation.Scheduled;
//
///**
// * 报送平台任务
// *
// * <AUTHOR>
// */
//@Configuration
//@Slf4j
//public class ReportTask {
//
//    private final FileInfoService fileInfoService;
//
//    public ReportTask(FileInfoService fileInfoService) {
//        this.fileInfoService = fileInfoService;
//    }
//
//    //    @Scheduled(cron = "*/10 * * * * *")
//    @Scheduled(cron = "0 0 2 * * *")
//    public void syncReportDaily() {
//        fileInfoService.syncDaily();
//    }
//}
