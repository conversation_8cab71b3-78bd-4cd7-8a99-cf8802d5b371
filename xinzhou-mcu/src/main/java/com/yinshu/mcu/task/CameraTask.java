//package com.yinshu.mcu.task;
//
//import com.yinshu.mcu.entity.GetCameraVO;
//import com.yinshu.mcu.service.ChannelGroupService;
//import com.yinshu.mcu.service.DevconService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.scheduling.annotation.Scheduled;
//
//import javax.annotation.Resource;
//
///**
// * 监控任务
// *
// * <AUTHOR>
// */
//@Configuration
//@Slf4j
//public class CameraTask {
//
//    private final DevconService devconService;
//    private final ChannelGroupService groupService;
//
//    @Resource
//    McuConfig config;
//
//
//    public CameraTask(DevconService devconService, ChannelGroupService groupService) {
//        this.devconService = devconService;
//        this.groupService = groupService;
//    }
//
//    /*
//     * <AUTHOR>
//     * @description //TODO 同步mcu摄像头
//     * @date 2025/5/27 14:35
//     **/
//    @Scheduled(cron = "0 0 2 * * *")
//    public void syncCameras() {
//        try {
//            devconService.syncCameraList(new GetCameraVO());
//            devconService.syncHikiCameraList();
//            devconService.syncIpcCameras();
//        } catch (Exception e) {
//            log.error("同步监控失败", e);
//        }
//    }
//
//
//    @Scheduled(cron = "0 0 * * * ?")
//    public void syncCamerasStatus() {
//        if (config.getEnableSyncStatus()) {
//            try {
//                devconService.syncCameraStatus();
//                devconService.syncHikiCamerasStatus();
//                devconService.syncIpcCameraStatus();
//            } catch (Exception e) {
//                log.error("同步监控状态失败", e);
//            }
//        }
//    }
//
//    @Scheduled(cron = "0 0 2 * * *")
//    public void syncGroups() {
//        try {
//            groupService.syncGroups();
//        } catch (Exception e) {
//            log.error("同步监控组失败", e);
//        }
//    }
//
//    @Scheduled(cron = "0 5 3 * * *")
//    public void syncJKCamera() {
//        try {
//            devconService.syncJKCamera(new GetCameraVO());
//        } catch (Exception e) {
//            log.error("同步监控组失败", e);
//        }
//    }
//}
