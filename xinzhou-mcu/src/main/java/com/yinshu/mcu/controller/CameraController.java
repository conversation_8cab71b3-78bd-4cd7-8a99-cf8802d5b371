package com.yinshu.mcu.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.mcu.entity.Devcon;
import com.yinshu.mcu.entity.GetCameraVO;
import com.yinshu.mcu.entity.QueryDevicesParam;
import com.yinshu.mcu.ipc.query.RecordQueryRequest;
import com.yinshu.mcu.ipc.response.RtspRecordResponse;
import com.yinshu.mcu.service.CameraService;
import com.yinshu.mcu.service.DevconService;
import com.yinshu.mcu.utils.VideoPlatformClient;
import com.yinshu.utils.ResultVO;

/**
 * 监控管理
 *
 * <AUTHOR> Bear
 * @date 2024/12/13
 */
@RestController
@RequestMapping("/api/mcu/camera")
public class CameraController {

    private static final Logger logger = LoggerFactory.getLogger(CameraController.class);

    private final DevconService devconService;
    @Autowired
    private CameraService cameraService;
    @Autowired
    private VideoPlatformClient videoPlatformClient;

    public CameraController(DevconService devconService) {
        this.devconService = devconService;
    }

    @GetMapping("/page")
    public ResultVO<IPage<Devcon>> getCamera(QueryDevicesParam param) {
        IPage<Devcon> result = devconService.page(param);
        return new ResultVO<>(result);
    }

    @GetMapping("/getByNameLike/{name}")
    public ResultVO<List<Devcon>> getByNameLike(@PathVariable(name = "name", required = true) String name) {
        return new ResultVO<>(devconService.list(new LambdaQueryWrapper<Devcon>()
                .like(Devcon::getName, name)));
    }

    @GetMapping("/getById/{id}")
    public ResultVO<?> getById(@PathVariable(name = "id", required = true) String id) {
        return new ResultVO<>(devconService.getById(id));
    }

    @PostMapping("/sync")
    public ResultVO<?> sync(GetCameraVO params) {
        try {
            devconService.syncCameraList(params);
            devconService.syncHikiCameraList();
            devconService.syncIpcCameras();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new ResultVO<>(ResultVO.ERROR_CODE, e.getMessage());
        }
        return new ResultVO<>(null);
    }

    @PostMapping("/sync/syncState")
    public ResultVO<?> syncState(GetCameraVO params) {
        try {
            devconService.syncCameraStatus();
            devconService.syncHikiCamerasStatus();
            devconService.syncIpcCameraStatus();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new ResultVO<>(ResultVO.ERROR_CODE, e.getMessage());
        }
        return new ResultVO<>(null);
    }

    @PostMapping("/getIpcVideoUrl")
    public ResultVO<RtspRecordResponse> getIpcVideoUrl(@RequestBody RecordQueryRequest request) {
        return new ResultVO<>(cameraService.getIpcVideoUrl(request));
    }

    @GetMapping("/getVideoUrl/{id}/{videoType}")
    public ResultVO<String> getVideoUrl(@PathVariable(name = "id", required = true) String id, @PathVariable(name = "videoType", required = false) int videoType) {

        return new ResultVO<>(cameraService.getVideoUrl(id, videoType));
    }

    @GetMapping("/getVideoUrl/{id}")
    public ResultVO<String> getVideoUrl(@PathVariable(name = "id", required = true) String id) {

        return new ResultVO<>(cameraService.getVideoUrl(id, -1));
    }

    @PostMapping("/jksync")
    public ResultVO<?> jksync(GetCameraVO params) {
        try {
            devconService.syncJKCamera(new GetCameraVO());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new ResultVO<>(ResultVO.ERROR_CODE, e.getMessage());
        }
        return new ResultVO<>(null);
    }


    @PostMapping("/tree")
    public ResultVO<?> tree(@RequestBody JSONObject params) {
        return new ResultVO<>(devconService.tree(params));
    }

}
