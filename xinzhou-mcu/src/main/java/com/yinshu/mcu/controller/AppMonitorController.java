package com.yinshu.mcu.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.annotation.OperLog;
import com.yinshu.common.ApiConstant;
import com.yinshu.mcu.entity.*;
import com.yinshu.mcu.service.DevconService;
import com.yinshu.mcu.service.McuCameraService;
import com.yinshu.mcu.service.McuMeetingService;
import com.yinshu.mcu.service.MonitorService;
import com.yinshu.utils.ResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping(ApiConstant.API_TOMS_PREFIX +"/api/app/v1/monitor")
public class AppMonitorController {

    private static final Logger logger = LoggerFactory.getLogger(AppMonitorController.class);
    private final MonitorService monitorService;
    private final McuCameraService mcuCameraService;
    private final DevconService devconService;

    public AppMonitorController(MonitorService monitorService, McuCameraService mcuCameraHandler, McuMeetingService mcuMeetingService, DevconService devconService) {
        this.monitorService = monitorService;
        this.mcuCameraService = mcuCameraHandler;
        this.devconService = devconService;
    }

    /**
     * 查询组织
     *
     * @param param 参数
     * @return {@link ResultVO }<{@link IPage }<{@link ChannelGroup }>>
     */
    @GetMapping("/org/query")
    @OperLog(operModul="系统操作", operType="查询组织列表")
    public ResultVO<AppPageResult<ChannelGroup>> queryOrganizes(QueryOrganizesParam param) {
        // 暂时不显示轨道交通数据
        param.setExcludeChannelId(Collections.singletonList("train"));
        IPage<ChannelGroup> result = monitorService.queryOrganizes(param);
        return new ResultVO<>(AppPageResult.fromMyBatisPlus(result));
    }

    /**
     * 查询设备
     *
     * @param param 参数
     * @return {@link ResultVO }<{@link AppPageResult }<{@link Devcon }>>
     */
    @GetMapping("/devices/query")
    @OperLog(operModul = "系统操作", operType = "查询监控资源列表")
    public ResultVO<AppPageResult<Devcon>> queryDevices(QueryDevicesParam param) {
        // 暂时不显示轨道交通数据
        param.setExcludeDataFrom(Collections.singletonList(3));
        IPage<Devcon> result = devconService.page(param);
        return new ResultVO<>(AppPageResult.fromMyBatisPlus(result));
    }


    /**
     * 摄像头云台控制
     *
     * @param request 请求
     * @return Map
     */
    @GetMapping("/ptzAction")
    public Map<String, Object> ptzAction(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<>();

        try {
            Map<String, String> ptzMap = mcuCameraService.ptzAction(request.getParameter("action"),
                request.getParameter("status"),
                request.getParameter("partyNumber"),
                request.getParameter("step"),
                request.getParameter("ptzAddress"));

            if (!ptzMap.get("code").equals("OK")) {
                resultMap.put("msg", ptzMap.get("code"));
            } else {
                resultMap.put("code", "000000");
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            resultMap.put("msg", e instanceof NullPointerException ?
                "java.lang.NullPointerException" : e.getMessage());
        }

        return resultMap;
    }
}
