package com.yinshu.mcu.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.mcu.entity.ChannelGroup;
import com.yinshu.mcu.query.ChannelGroupQuery;
import com.yinshu.mcu.service.ChannelGroupService;
import com.yinshu.mcu.vo.ChannelGroupVO;
import com.yinshu.utils.ResultVO;

/**
 * 通道分组管理
 *
 * <AUTHOR>
 * @date 2024/12/18
 */
@RestController
@RequestMapping("/api/mcu/channelGroup")
public class ChannelGroupController {

    public static final Logger logger = LoggerFactory.getLogger(ChannelGroupController.class);

    private final ChannelGroupService channelGroupService;

    public ChannelGroupController(ChannelGroupService channelGroupService) {
        this.channelGroupService = channelGroupService;
    }

    @PostMapping("/sync")
    public ResultVO<?> sync() {
        try {
            channelGroupService.syncGroups();
        } catch (Exception e) {
            return new ResultVO<>(ResultVO.ERROR_CODE, e.getMessage());
        }

        return new ResultVO<>(ResultVO.SUCCESS_CODE);
    }

    @GetMapping("/page")
    public ResultVO<IPage<ChannelGroup>> getCamera(ChannelGroupQuery param) {
        IPage<ChannelGroup> result = channelGroupService.page(param);
        return new ResultVO<>(result);
    }

    @GetMapping("/getById/{id}")
    public ResultVO<?> getById(@PathVariable(name = "id", required = true) String id) {
        return new ResultVO<>(channelGroupService.getById(id));
    }


    @PostMapping("/tree")
    public ResultVO<?> tree() {
        List<ChannelGroupVO> list = channelGroupService.tree();
        return new ResultVO<>(list);
    }


    @GetMapping("/name/tree/{name}")
    public ResultVO<?> nameTree(@PathVariable("name") String name) {
        List<ChannelGroupVO> list = channelGroupService.nameTree(name);
        return new ResultVO<>(list);
    }
}
