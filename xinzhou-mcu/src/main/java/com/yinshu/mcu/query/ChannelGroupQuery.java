package com.yinshu.mcu.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ChannelGroupQuery
 * @description TODO
 * @date 2025/5/20 10:37
 **/
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ChannelGroupQuery implements Serializable {

    /**
     * 关键字
     */
    private String keyword;


    /**
     * 组织 ID
     */
    private String id;

    /**
     * 设备通道编码
     */
    private String channelCode;

    /**
     * 平台 ID
     */
    private String mcId;

    /**
     * 组织名称
     */
    private String name;

    /**
     * 父组织名称
     */
    private String parentId;

    private String businessGroupId;

    private Integer page = 1;

    private Integer pageSize = 10;

    /**
     * 同步时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime syncTime;

}
