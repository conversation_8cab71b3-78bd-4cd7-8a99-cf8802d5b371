package com.yinshu.mcu.vo;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CameraTreeVO
 * @description TODO 摄像头树数据
 * @date 2025/6/24 16:18
 **/
@Data
public class CameraTreeVO {

    /**
     * 同时兼容
     */
    private String id;

    /**
     * 树节点名称
     */
    private String label;

    /**
     * 树类型 “group” ｜ “camera”
     */
    private String treeType;


    /**
     * 桩号名称
     */
    @TableField(exist = false)
    private String turnName;

    /**
     * 通道号
     */
    @TableField(value = "CHANNELCODE")
    private String channelCode;
    /**
     * 平台 ID
     */
    @TableField(value = "MCID", jdbcType = JdbcType.VARCHAR)
    private String mcId;
    /**
     * 通道名称
     */
    @TableField(value = "NAME")
    private String name;
    /**
     * 坐标系 02-高德 09-百度 84-原始GPS
     */
    @TableField(value = "OWNER", updateStrategy = FieldStrategy.ALWAYS)
    private String owner;
    /**
     * 行政分组 ID
     */
    @TableField(value = "CIVILCODE")
    private String civilCode;
    /**
     * 平台 code / 业务分组 code
     */
    @TableField(value = "PARENTID")
    private String parentId;
    /**
     * 状态
     * 0: 离线; 1: 在线
     */
    @TableField(value = "STATUS")
    private Integer status;

    /**
     * 经度
     */
    @TableField(value = "LONGITUDE")
    private Double longitude;
    /**
     * 纬度
     */
    @TableField(value = "LATITUDE")
    private Double latitude;
    /**
     * 业务分组
     */
    @TableField(value = "BUSINESSGROUPID")
    private String businessGroupId;
    /**
     * 同步时间
     */
    @TableField(value = "SYNCTIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime syncTime;
    /**
     * IP 地址
     */
    @TableField("IP")
    private String ip;
    /**
     * 区域名称
     */
    @TableField(value = "AREA")
    private String area;
    /**
     * 与关联点之间的距离
     */
    @TableField(exist = false)
    private Double distance;
    /**
     * 业务分组类型
     **/
    @TableField(exist = false)
    private String type;

    /**
     * 数据来源 1=mcu 2=海康威视 3=tri_Net_vcs
     */
    @TableField(value = "DATA_FROM")
    private Integer dataFrom;

    /**
     * 叶子节点可能没有
     */
    private List<CameraTreeVO> children;

}
