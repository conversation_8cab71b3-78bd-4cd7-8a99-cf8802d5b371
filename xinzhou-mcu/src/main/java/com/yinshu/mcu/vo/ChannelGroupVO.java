package com.yinshu.mcu.vo;

import com.yinshu.mcu.entity.ChannelGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 分组
 */
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ChannelGroupVO extends ChannelGroup implements Serializable {

    private List<ChannelGroupVO> children;

}