package com.yinshu.mcu.entity;


import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 分页结果
 *
 * <AUTHOR>
 * @date 2024/11/27
 */
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class AppPageResult<T> {

    /**
     * 总数
     */
    private long total;
    /**
     * 页面大小
     */
    private long pageSize;
    /**
     * 当前页码
     */
    private long curPage;
    /**
     * 数据列表
     */
    private List<T> list;

    /**
     * 从 MyBatis Plus 的 IPage 转换
     *
     * @param page 分页参数
     * @return {@link AppPageResult }<{@link T }>
     */
    public static <T> AppPageResult<T> fromMyBatisPlus(IPage<T> page) {
        return new AppPageResult<>(page.getTotal(),
            page.getSize(),
            page.getCurrent(),
            page.getRecords()
        );
    }

}
