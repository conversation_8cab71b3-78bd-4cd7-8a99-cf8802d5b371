package com.yinshu.mcu.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * Query 组织参数
 *
 * <AUTHOR>
 * @date 2024/11/26
 */
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QueryOrganizesParam extends AppPageParam {

    /**
     * 组织编码
     */
    private String id;


    /**
     * 排除显示的channelId
     * **/
    private List<String> excludeChannelId;

}
