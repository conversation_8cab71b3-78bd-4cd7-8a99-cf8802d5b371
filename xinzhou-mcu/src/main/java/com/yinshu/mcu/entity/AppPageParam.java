package com.yinshu.mcu.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * APP 分页查询参数
 *
 * <AUTHOR>
 * @date 2024/11/26
 */
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class AppPageParam {

    private static final Integer DEFAULT_PAGE_NUMBER = 1;
    private static final Integer DEFAULT_PAGE_SIZE = 10;

    private Integer curPage = DEFAULT_PAGE_NUMBER;

    private Integer pageSize = DEFAULT_PAGE_SIZE;

}
