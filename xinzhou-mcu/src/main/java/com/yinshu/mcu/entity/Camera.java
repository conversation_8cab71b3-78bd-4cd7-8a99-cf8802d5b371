package com.yinshu.mcu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 交控监控
 * @TableName CAMERA
 */
@TableName(value ="CAMERA")
@Data
public class Camera implements Serializable {
    /**
     * 设备 ID
     */
    private String id;

    /**
     * 监控名称
     */
    private String cameraName;

    /**
     * 状态
     * 0: 离线; 1: 在线
     */
    private Integer status;

    /**
     * 经度
     */
    private Double tLng;

    /**
     * 纬度
     */
    private Double tLat;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}