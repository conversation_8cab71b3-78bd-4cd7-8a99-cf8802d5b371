package com.yinshu.mcu.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 设备查询参数
 *
 * <AUTHOR>
 * @date 2024/11/27
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryDevicesParam extends AppPageParam {

    private String id;

    // 按坐标范围查找
    /**
     * 纬度
     */
    private Float latitude;
    /**
     * 经度
     */
    private Float longitude;
    /**
     * 半径
     */
    private Float radius;
    /**
     * 范围内由近到远记录数限制，用于redis查找
     */
    private long radiusLimit = 20000;
    /**
     * 单位
     */
    private String unit = "km";
    /**
     * 关键字
     */
    private String keyword;

    /**
     * 状态
     */
    private String status;

    /**
     * 摄像头分类名称
     */
    private String type;

    /**
     * 从系统配置读取的key
     */
    private String settingKey;


    private List<Integer> excludeDataFrom;



}
