package com.yinshu.mcu.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 平台
 * McsCon
 */
@TableName(value ="MCSCON")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class Mcscon implements Serializable {
    /**
     * 
     */
    @TableId(value = "ID")
    private String id;

    /**
     * 平台名称
     */
    @TableField(value = "PLATENAME")
    private String plateName;

    /**
     * 平台号码/摄像头号码
     */
    @TableField(value = "PLATCODE")
    private String platCode;

    /**
     * 
     */
    @TableField(value = "PORT")
    private Integer port;

    /**
     * 
     */
    @TableField(value = "USERNAME")
    private String username;

    /**
     * 
     */
    @TableField(value = "PASSWORD")
    private String password;

    /**
     * 设备类型，0:前端设备（摄像头、执法仪等）; 1:下级平台; 2:上级平台
     */
    @TableField(value = "TYPE")
    private String type;

    /**
     * 
     */
    @TableField(value = "PLATEIP")
    private String plateIp;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}