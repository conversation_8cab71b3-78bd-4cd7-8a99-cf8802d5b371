package com.yinshu.mcu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 分组
 */
@TableName(value ="CHANNEL_GROUP")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ChannelGroup implements Serializable {
    /**
     * 组织 ID
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private String id;

    /**
     * 设备通道编码
     */
    @TableField(value = "CHANNELCODE")
    private String channelCode;

    /**
     * 平台 ID
     */
    @TableField(value = "MCID")
    private String mcId;

    /**
     * 组织名称
     */
    @TableField(value = "NAME")
    private String name;

    /**
     * 父组织名称
     */
    @TableField(value = "PARENTID")
    private String parentId;

    /**
     * 
     */
    @TableField(value = "BUSINESSGROUPID")
    private String businessGroupId;

    /**
     * 同步时间
     */
    @TableField(value = "SYNCTIME")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime syncTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}