package com.yinshu.mcu.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 通道
 *
 * @TableName DEVCON
 */
@TableName(value = "DEVCON")
@AllArgsConstructor
@Accessors(chain = true)
@NoArgsConstructor
@Data
public class Devcon implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 通道 ID
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;
    /**
     * 通道号
     */
    @TableField(value = "CHANNELCODE")
    private String channelCode;
    /**
     * 平台 ID
     */
    @TableField(value = "MCID", jdbcType = JdbcType.VARCHAR)
    private String mcId;
    /**
     * 通道名称
     */
    @TableField(value = "NAME")
    private String name;
    /**
     * 坐标系 02-高德 09-百度 84-原始GPS
     */
    @TableField(value = "OWNER", updateStrategy = FieldStrategy.ALWAYS)
    private String owner;
    /**
     * 行政分组 ID
     */
    @TableField(value = "CIVILCODE")
    private String civilCode;
    /**
     * 平台 code / 业务分组 code
     */
    @TableField(value = "PARENTID")
    private String parentId;
    /**
     * 状态
     * 0: 离线; 1: 在线
     */
    @TableField(value = "STATUS")
    private Integer status;

    /**
     * 经度
     */
    @TableField(value = "LONGITUDE")
    private Double longitude;
    /**
     * 纬度
     */
    @TableField(value = "LATITUDE")
    private Double latitude;
    /**
     * 业务分组
     */
    @TableField(value = "BUSINESSGROUPID")
    private String businessGroupId;
    /**
     * 同步时间
     */
    @TableField(value = "SYNCTIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime syncTime;
    /**
     * IP 地址
     */
    @TableField("IP")
    private String ip;
    /**
     * 区域名称
     */
    @TableField(value = "AREA")
    private String area;
    /**
     * 与关联点之间的距离
     */
    @TableField(exist = false)
    private Double distance;
    /**
     * 业务分组类型
     **/
    @TableField(exist = false)
    private String type;

    /**
     * 数据来源 1=mcu 2=海康威视 3=tri_Net_vcs
     */
    @TableField(value = "DATA_FROM")
    private Integer dataFrom;

}