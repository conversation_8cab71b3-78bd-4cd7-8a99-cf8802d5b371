package com.yinshu.mcu.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.mcu.entity.ChannelGroup;
import com.yinshu.mcu.entity.QueryOrganizesParam;

/**
 * Monitor 服务
 *
 * <AUTHOR>
 * @date 2024/11/26
 */
public interface MonitorService {


    /**
     * 查询组织
     *
     * @param param 参数
     * @return {@link IPage }<{@link ChannelGroup }>
     */
    IPage<ChannelGroup> queryOrganizes(QueryOrganizesParam param);

}
