package com.yinshu.mcu.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinshu.mcu.entity.GetCameraVO;
import com.yinshu.mcu.service.McuMeetingService;
import com.yinshu.mcu.utils.McuUtils;
import com.yinshu.sys.entity.Setting;
import com.yinshu.sys.manager.SettingManager;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetAddress;
import java.net.Socket;
import java.net.SocketTimeoutException;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Service
public class McuMeetingServiceImpl implements McuMeetingService {

    private static final int FETCH_ALL_PAGE_SIZE = 100; // TODO This value could be customized
    private static final Logger logger = LoggerFactory.getLogger(McuMeetingServiceImpl.class);
    private final SettingManager settingManager;

    public McuMeetingServiceImpl(SettingManager settingManager) {
        this.settingManager = settingManager;
    }

    /**
     * 给请求添加头信息
     *
     * @param type          操作菜单
     * @param action        操作动作
     * @param transactionId 消息会话id
     * @param content       TODO
     * @return TODO
     */
    public static String addHeader(String type, String action, String transactionId, String content) {
        String requeString = null;
        if (!(ObjectUtils.isEmpty(type) || ObjectUtils.isEmpty(action) || ObjectUtils.isEmpty(transactionId)
                || ObjectUtils.isEmpty(content))) {
            requeString = "Type:" + type + "\r\n" + "Action: " + action + "\r\n" + "Device: viosys-ds" + "\r\n"
                    + "Transactionid:" + transactionId + "\r\n" + "Content:\r\n" + "<body>\r\n" + content
                    + "\r\n</body>";
        }
        return requeString;
    }

    protected Socket getSocket(String ptzAddress) throws Exception {
        try {
            if (ObjectUtils.isEmpty(ptzAddress)) {
                Setting mcuServerSetting = settingManager.getByCode("mcuServer");

                ptzAddress = mcuServerSetting.getParmValue();
                if (ObjectUtils.isEmpty(ptzAddress)) {
                    throw new RuntimeException("请配置mcu服务器地址和端口！");
                }
            }
            String[] strs = ptzAddress.split("_");
            String ip = strs[0];
            int port = Integer.parseInt(strs[1]);
            //logger.info("McuSocket=========================>>>>IP:" + ip + "&Port:" + port);

            Socket socket = null;
            if (McuUtils.isIpAddress(ip)) {
                socket = new Socket(ip, port);
            } else {
                InetAddress netAddress = InetAddress.getByName(ip);
                socket = new Socket(netAddress, port);
            }
            socket.setSoTimeout(55000);
            //logger.info("McuSocket=========================>>>>连接成功");
            return socket;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        }
    }

    private String sendMcuMessage2(String paramsStr, String ptzAddress) throws Exception {
        Socket socket = null;
        InputStream inputStream;
        OutputStream outputStream;
        try {
            long start = System.currentTimeMillis();
            socket = getSocket(ptzAddress);

            String socketReadTimeOut = settingManager.getByCode("socketReadTimeOut").getParmValue();
            if (ObjectUtils.isEmpty(socketReadTimeOut)) {
                socketReadTimeOut = "30000";
            }

            socket.setSoTimeout(Integer.parseInt(socketReadTimeOut));
            outputStream = socket.getOutputStream();
            logger.info("sendMcu ----->" + paramsStr);

            outputStream.write(paramsStr.getBytes());
            inputStream = socket.getInputStream();
            ByteArrayOutputStream outBytes = new ByteArrayOutputStream();
            byte[] buff = new byte[1024];
            String result="";
            int len;
            try {
                while(((len = inputStream.read(buff)) != -1)&&(System.currentTimeMillis() - start <= (Integer.parseInt(socketReadTimeOut)))) {
                    outBytes.write(buff, 0, len);
                    String ss = new String(buff, 0, len);
                    if(ss.contains("</body>")) {
                        break;
                    }
                }
            } catch (SocketTimeoutException e) {
                logger.error(e.getMessage(), e);
            }
            result=new String(outBytes.toByteArray());
            logger.info("acceptMcu----->" + (System.currentTimeMillis() - start) + "-->数据长度：" + result.length());
            return result;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw e;
        } finally {
            if (socket != null) {
                socket.close();
            }
        }
    }

    @Override
    public List<Map<String, String>> getCameraList() throws Exception {
        return getAllCameras(new GetCameraVO());
    }

    @Override
    public List<Map<String, String>> getAllCameras(GetCameraVO param) throws Exception {
        int start = param.getStart() == null ? 0 : param.getStart();
        int offset = param.getOffset() == null ? 0 : param.getOffset();
        Map<String, Object> dataMap = this.getCameraList(start, offset);

        int total = Integer.parseInt(dataMap.get("total").toString());
        List<Map<String, String>> dataList = (List<Map<String, String>>) dataMap.get("dataList");
        List<Map<String, String>> resultList = new ArrayList<>(dataList);

        int totalPage = (total + offset - 1) / offset;
        for(int i = 1; i < totalPage; i++) {
            dataMap = this.getCameraList(i * offset, offset);
            resultList.addAll((List<Map<String, String>>) dataMap.get("dataList"));
        }

        return resultList;
    }

    public List<Map<String, String>> getAllCameras(GetCameraVO param,String mcuServer) throws Exception {
        int start = param.getStart() == null ? 0 : param.getStart();
        int offset = param.getOffset() == null ? 0 : param.getOffset();
        Map<String, Object> dataMap = this.getCameraList(start, offset,mcuServer);

        int total = Integer.parseInt(dataMap.get("total").toString());
        List<Map<String, String>> dataList = (List<Map<String, String>>) dataMap.get("dataList");
        List<Map<String, String>> resultList = new ArrayList<>(dataList);

        int totalPage = (total + offset - 1) / offset;
        for(int i = 1; i < totalPage; i++) {
            dataMap = this.getCameraList(i * offset, offset,mcuServer);
            resultList.addAll((List<Map<String, String>>) dataMap.get("dataList"));
        }

        return resultList;
    }

    /**
     * 获取监控摄像头信息
     *
     * @throws Exception
     */
    private Map<String, Object> getCameraList(int start, int offset) throws Exception {
        try {
            Map<String, Object> resultMap = new HashMap<>();
            String content = getXmlPage(start, offset);
            String paramsStr = addHeader("McsChannel", "GET", "modify-" + System.currentTimeMillis(), content);

            //logger.info("获取摄像头全量接口 start ----->" + paramsStr + " (time):" + (System.currentTimeMillis() - start1));
            String result = sendMcuMessage2(paramsStr, null);
            //logger.info("获取摄像头全量接口 end ----->" + result);
            if (result.toLowerCase().contains("failed")) {
                throw new RuntimeException("获取摄像头接口:" + result.substring(result.indexOf("Content") + 8));
            }

            if(!ObjectUtils.isEmpty(result)) {
                List<Map<String, String>> resultList = new ArrayList<>();

                String xml = result.substring(result.indexOf("body") - 1);
                Document document = DocumentHelper.parseText(xml);
                Element root = document.getRootElement();
                Iterator iterator = root.elementIterator("Channels");
                while(iterator.hasNext()){
                    Element element = (Element)iterator.next();
                    List<Element> usersSubElementList = element.elements();
                    for (Element el : usersSubElementList) {
                        Map<String, String> dataMap = new HashMap<>();
                        dataMap.put("name", el.element("name") == null ? "" : el.element("name").getText());
                        dataMap.put("code", el.element("code") == null ? "" : el.element("code").getText());
                        dataMap.put("status", el.element("status") == null ? "" : el.element("status").getText());
                        dataMap.put("platcode", el.element("platcode") == null ? "" : el.element("platcode").getText());
                        dataMap.put("Longitude", el.element("Longitude") == null ? "" : el.element("Longitude").getText());
                        dataMap.put("Latitude", el.element("Latitude") == null ? "" : el.element("Latitude").getText());
                        dataMap.put("CivilCode", el.element("CivilCode") == null ? "" : el.element("CivilCode").getText());
                        dataMap.put("ParentId", el.element("ParentId") == null ? "" : el.element("ParentId").getText());
                        dataMap.put("Address", el.element("Address") == null ? "" : el.element("Address").getText());
                        resultList.add(dataMap);
                    }
                }
                resultMap.put("total", Integer.parseInt(root.element("total").getText()));
                resultMap.put("dataList", resultList);}
            return resultMap;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 获取监控摄像头信息
     *
     * @throws Exception
     */
    private Map<String, Object> getCameraList(int start, int offset,String mcuServer) throws Exception {
        try {
            Map<String, Object> resultMap = new HashMap<>();
            String content = getXmlPage(start, offset);
            String paramsStr = addHeader("sxtmCameraCtrl", "GET", "mc-" + System.currentTimeMillis(), content);

            //logger.info("获取摄像头全量接口 start ----->" + paramsStr + " (time):" + (System.currentTimeMillis() - start1));
            String result = sendMcuMessage2(paramsStr, mcuServer);
            //logger.info("获取摄像头全量接口 end ----->" + result);
            if (result.toLowerCase().contains("failed")) {
                throw new RuntimeException("获取摄像头接口:" + result.substring(result.indexOf("Content") + 8));
            }

            if(!ObjectUtils.isEmpty(result)) {
                List<Map<String, String>> resultList = new ArrayList<>();
                String xml = result.substring(result.indexOf("body") - 1);
                Document document = DocumentHelper.parseText(xml);
                Element root = document.getRootElement();
                Iterator iterator = root.elementIterator("devices");
                while(iterator.hasNext()){
                    Element element = (Element)iterator.next();
                    List<Element> usersSubElementList = element.elements();
                    for (Element el : usersSubElementList) {
                        Map<String, String> dataMap = new HashMap<>();
                        dataMap.put("CameraID", el.element("CameraID") == null ? "" : el.element("CameraID").getText());
                        dataMap.put("CameraName", el.element("CameraName") == null ? "" : el.element("CameraName").getText());
                        dataMap.put("Status", el.element("Status") == null ? "" : el.element("Status").getText());
                        dataMap.put("Alias", el.element("Alias") == null ? "" : el.element("Alias").getText());
                        resultList.add(dataMap);
                    }
                }
                resultMap.put("total", Integer.parseInt(root.element("total").getText()));
                resultMap.put("dataList", resultList);}
            return resultMap;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Page<Map<String, String>> getVirtualGroups(int start, int offset) throws Exception {
        if (offset == 0) {
            throw new RuntimeException("offset can not be 0");
        }
        // Prepare for requesting
        String content = getXmlPage(start, offset);
        String params = addHeader("McsVirtualGroups", "GET", "modify-" + System.currentTimeMillis(), content);

        // Sending request
        String response = sendMcuMessage2(params, null);
        logger.info("getVirtualGroups ----->{}", response);

        // Checking result
        if (response.isEmpty()) {
            throw new RuntimeException("响应为空");
        }
        if (response.toLowerCase().contains("failed")) {
            throw new RuntimeException("获取虚拟分组失败:" + response.substring(response.indexOf("Content") + 8));
        }

        // Start Parsing
        String xml = response.substring(response.indexOf("<body>"));
        Document document = DocumentHelper.parseText(xml);
        Element root = document.getRootElement();

        // Total
        int total = Integer.parseInt(root.element("total").getText());

        // Records
        Element groupContainer = root.element("VirtualGroups");
        if (groupContainer == null) {
            return null;
        }
        List<Element> groupElements = groupContainer.elements("VirtualGroup");

        // Traversal
        List<Map<String, String>> records = new LinkedList<>();
        for (Element element : groupElements) {
            Map<String, String> dataMap = new HashMap<>();

            dataMap.put("name", element.element("VirtualGroupName") == null ? "" : element.element("VirtualGroupName").getText());
            dataMap.put("id", element.element("VirtualGroupID") == null ? "" : element.element("VirtualGroupID").getText());
            dataMap.put("parentId", element.element("VirtualParentId") == null ? "" : element.element("VirtualParentId").getText());

            records.add(dataMap);
        }

        return getPageResult(records, start, offset, total);
    }

    private <T> Page<T> getPageResult(List<T> records, int start, int offset, int total) {
        Page<T> result = new Page<>();
        result.setRecords(records);

        result.setSize(offset);
        result.setTotal(total);
        result.setCurrent(start / offset);
        return result;
    }

    @Override
    public List<Map<String, String>> getAllVirtualGroup() throws Exception {
        return getAllWithPage(this::getVirtualGroups);
    }

    @Override
    public Page<Map<String, String>> getBusinessGroups(int start, int offset) throws Exception {
        String content = getXmlPage(start, offset);
        String params = addHeader("McsBusinessGroups", "GET", "modify-" + System.currentTimeMillis(), content);

        String response = sendMcuMessage2(params, null);
        logger.info("McsBusinessGroups ----->{}", response);

        if (response.isEmpty()) {
            throw new RuntimeException("响应为空");
        }
        if (response.toLowerCase().contains("failed")) {
            throw new RuntimeException("获取虚拟分组失败:" + response.substring(response.indexOf("Content") + 8));
        }

        // Parsing
        String xml = response.substring(response.indexOf("<body>"));
        Document document = DocumentHelper.parseText(xml);
        Element root = document.getRootElement();

        // Total
        int total = Integer.parseInt(root.element("total").getText());

        // Items
        Element groupContainer = root.element("BusinessGroups");
        if (groupContainer == null) {
            return null;
        }
        List<Element> groupElements = groupContainer.elements("BusinessGroup");

        // Traversal
        List<Map<String, String>> result = new ArrayList<>();
        for (Element element : groupElements) {
            Map<String, String> dataMap = new HashMap<>();

            dataMap.put("name", element.element("BusinessGroupName") == null ? "" : element.element("BusinessGroupName").getText());
            dataMap.put("id", element.element("BusinessGroupID") == null ? "" : element.element("BusinessGroupID").getText());
            dataMap.put("parentId", element.element("BusinessParentId") == null ? "" : element.element("BusinessParentId").getText());

            result.add(dataMap);
        }

        return getPageResult(result, start, offset, total);
    }

    @Override
    public List<Map<String, String>> getAllBusinessGroups() throws Exception {
        return this.getAllWithPage(this::getBusinessGroups);
    }

    private <T> List<T> getAllWithPage(PageQuery<T> pageQuery) throws Exception {
        // Fetch the first page
        Page<T> page1 = pageQuery.query(0, FETCH_ALL_PAGE_SIZE);
        if (page1 == null) {
            return new LinkedList<>();
        }
        long total = page1.getTotal();
        if (total < FETCH_ALL_PAGE_SIZE) {
            return page1.getRecords();
        }

        // Query latest pages
        List<T> result = new LinkedList<>(page1.getRecords());
        long pages = (long) Math.ceil(1D * total / FETCH_ALL_PAGE_SIZE);
        for (int curr = 1; curr < pages; curr++) {
            Page<T> currPage = pageQuery.query(curr * FETCH_ALL_PAGE_SIZE, FETCH_ALL_PAGE_SIZE);
            result.addAll(currPage.getRecords());
        }

        return result;
    }

    private static String getXmlPage(int start, int offset) {
        return String.format("<start>%d</start><offset>%d</offset>", start, offset);
    }

    @Override
    public List<Map<String, String>> getCameraListByCodes(Map<String, Object> mapData) throws Exception {
        try {
            String content = "<channelid>" + mapData.get("channelId")  + "</channelid>";
            String paramsStr = addHeader("McsChannel", "SEARCH", "modify-" + System.currentTimeMillis(), content);
            //logger.info("获取摄像头批量接口 start ----->" + paramsStr + " (time):" + (System.currentTimeMillis() - start));
            String result = sendMcuMessage2(paramsStr, null);
            logger.info("获取摄像头批量接口 end ----->{}", result);
            if (result.isEmpty()) {
                throw new RuntimeException("响应为空");
            }
            if (result.toLowerCase().contains("failed")) {
                throw new RuntimeException("获取摄像头接口:" + result.substring(result.indexOf("Content") + 8));
            }

            List<Map<String, String>> resultList = new ArrayList<>();
            if(!ObjectUtils.isEmpty(result)) {
                String xml = result.substring(result.indexOf("body") - 1);
                Document document = DocumentHelper.parseText(xml);
                Element root = document.getRootElement();
                Iterator iterator = root.elementIterator("Channels");
                if(!iterator.hasNext()) {
                    Element el=root.element("Channel");
                    Map<String, String> resultMap = new HashMap<>();
                    resultMap.put("name", el.element("name") == null ? "" : el.element("name").getText());
                    resultMap.put("code", el.element("code") == null ? "" : el.element("code").getText());
                    resultMap.put("status", el.element("status") == null ? "" : el.element("status").getText());
                    resultMap.put("platcode", el.element("platcode") == null ? "" : el.element("platcode").getText());
                    resultMap.put("Longitude", el.element("Longitude") == null ? "" : el.element("Longitude").getText());
                    resultMap.put("Latitude", el.element("Latitude") == null ? "" : el.element("Latitude").getText());
                    resultMap.put("CivilCode", el.element("CivilCode") == null ? "" : el.element("CivilCode").getText());
                    resultMap.put("ParentId", el.element("ParentId") == null ? "" : el.element("ParentId").getText());
                    resultList.add(resultMap);
                }
                while(iterator.hasNext()){
                    Element element = (Element)iterator.next();
                    List<Element> usersSubElementList = element.elements();
                    for (Element el : usersSubElementList) {
                        Map<String, String> resultMap = new HashMap<>();
                        resultMap.put("name", el.element("name") == null ? "" : el.element("name").getText());
                        resultMap.put("code", el.element("code") == null ? "" : el.element("code").getText());
                        resultMap.put("status", el.element("status") == null ? "" : el.element("status").getText());
                        resultMap.put("platcode", el.element("platcode") == null ? "" : el.element("platcode").getText());
                        resultMap.put("Longitude", el.element("Longitude") == null ? "" : el.element("Longitude").getText());
                        resultMap.put("Latitude", el.element("Latitude") == null ? "" : el.element("Latitude").getText());
                        resultMap.put("CivilCode", el.element("CivilCode") == null ? "" : el.element("CivilCode").getText());
                        resultMap.put("ParentId", el.element("ParentId") == null ? "" : el.element("ParentId").getText());
                        resultList.add(resultMap);
                    }
                }
            }
            return resultList;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<Map<String, String>> getCivilGroups(Map<String, Object> mapData) throws Exception {
        int start = mapData.get("start") == null ? 0 : Integer.parseInt(mapData.get("start").toString());
        int offset = mapData.get("offset") == null ? 100 : Integer.parseInt(mapData.get("offset").toString());

        Map<String, Object> dataMap = this.getCivilGroups(start, offset);
        int total = Integer.parseInt(dataMap.get("total").toString());
        List<Map<String, String>> dataList = (List<Map<String, String>>) dataMap.get("dataList");
        List<Map<String, String>> resultList = new ArrayList<>(dataList);

        int totalPage = (total + offset - 1) / offset;
        for(int i = 1; i < totalPage; i++) {
            dataMap = this.getCivilGroups(i * offset, offset);
            resultList.addAll((List<Map<String, String>>) dataMap.get("dataList"));
        }

        return resultList;
    }

    /**
     * 获取mcu机构分组
     *
     * @return
     * @throws Exception
     */
    private Map<String, Object> getCivilGroups(int start, int offset) throws Exception {
        try {
            Map<String, Object> resultMap = new HashMap<>();
            String content = getXmlPage(start, offset);

            long currentTimeMillis = System.currentTimeMillis();
            String paramsStr = addHeader("McsCivilCodeGroups", "GET",
                    "modify-" + currentTimeMillis, content);

            String result = sendMcuMessage2(paramsStr, null);
            logger.info("获取mcu机构分组 end ----->{}", result);

            if (result.isEmpty() || result.toLowerCase().contains("failed")) {
                throw new RuntimeException("获取mcu机构分组:" + result.substring(result.indexOf("Content") + 8));
            }

            if(!ObjectUtils.isEmpty(result)) {
                List<Map<String, String>> resultList = new ArrayList<>();

                String xml = result.substring(result.indexOf("body") - 1);
                Document document = DocumentHelper.parseText(xml);
                Element root = document.getRootElement();

                Iterator<Element> iterator = root.elementIterator("CivilCodeGroups");
                while(iterator.hasNext()){
                    Element element = iterator.next();

                    List<Element> usersSubElementList = element.elements();
                    for (Element el : usersSubElementList) {
                        Map<String, String> dataMap = new HashMap<>();
                        dataMap.put("CivilCodeParentId", el.element("CivilCodeParentId") == null ? "" : el.element("CivilCodeParentId").getText());
                        dataMap.put("CivilCodeGroupID", el.element("CivilCodeGroupID") == null ? "" : el.element("CivilCodeGroupID").getText());
                        dataMap.put("CivilCodeGroupName", el.element("CivilCodeGroupName") == null ? "" : el.element("CivilCodeGroupName").getText());
                        resultList.add(dataMap);
                    }
                }

                resultMap.put("total", Integer.parseInt(root.element("total").getText()));
                resultMap.put("dataList", resultList);
            }

            return resultMap;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw e;
        }
    }

}


@FunctionalInterface
interface PageQuery<T> {
    Page<T> query(int start, int offset) throws Exception;
}