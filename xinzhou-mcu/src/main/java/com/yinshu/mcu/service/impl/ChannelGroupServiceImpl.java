package com.yinshu.mcu.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.mcu.dao.ChannelGroupMapper;
import com.yinshu.mcu.entity.ChannelGroup;
import com.yinshu.mcu.hikvision.utils.VideoZJGCClient;
import com.yinshu.mcu.query.ChannelGroupQuery;
import com.yinshu.mcu.service.ChannelGroupService;
import com.yinshu.mcu.service.McuMeetingService;
import com.yinshu.mcu.vo.ChannelGroupVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 通道分组服务实现
 *
 * <AUTHOR> Bear
 * @date 2024-11-26 15:17:01
 */
@Service
public class ChannelGroupServiceImpl extends ServiceImpl<ChannelGroupMapper, ChannelGroup>
        implements ChannelGroupService {

    /**
     * 在建工程名称
     **/
    public static final String CONSTRUCTION_IN_PROCESS = "zjgc";

    private static final Logger logger = LoggerFactory.getLogger(ChannelGroupServiceImpl.class);
    private final McuMeetingService meetingService;


    public ChannelGroupServiceImpl(McuMeetingService meetingService) {
        this.meetingService = meetingService;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncGroups() throws Exception {
        // 清空表
        this.baseMapper.truncate();

        String mcid = null;

        // 业务组数据
        List<Map<String, String>> businessMaps = meetingService.getAllBusinessGroups();
        List<ChannelGroup> businessGroups = businessMaps.stream().map(group ->
                new ChannelGroup(group.get("id"), null, null, group.get("name"),
                        group.get("parentId"), group.get("id"), LocalDateTime.now())).collect(Collectors.toList());

        // 业务组的上级是平台,他本身相当于一级
        for (ChannelGroup businessGroup : businessGroups) {
            if (businessGroup.getParentId() != null && ObjectUtils.isEmpty(mcid)) {
                mcid = businessGroup.getParentId();
            }
            if (businessGroup.getParentId() != null && businessGroup.getParentId().equals(businessGroup.getId())) {
                businessGroup.setParentId(null);
            }
            // 写死，强制挂在一级
            if (businessGroup.getParentId() != null && "12700000012001111210".equals(businessGroup.getParentId())) {
                businessGroup.setParentId("14000000982000000001");
            }
            businessGroup.setMcId(businessGroup.getParentId());
        }


        // 虚拟组数据 虚拟组的上级可以是虚拟组。也可以是业务组(至少是二级)
        List<Map<String, String>> virtualMaps = meetingService.getAllVirtualGroup();
        String finalMcid = mcid;
        List<ChannelGroup> virtualGroup = virtualMaps.stream().map(group ->
                new ChannelGroup(group.get("id"),
                        group.get("id"),
                        finalMcid,
                        group.get("name"),
                        group.get("parentId"),
                        null,
                        LocalDateTime.now())
        ).collect(Collectors.toList());

        for (ChannelGroup group : virtualGroup) {
            if (group.getParentId() != null && group.getParentId().equals(group.getId())) {
                group.setParentId(null);
            }
            // 写死，强制挂在一级
            if (group.getParentId() != null && "12700000012001111210".equals(group.getParentId())) {
                group.setParentId("14000000982000000001");
            }
        }


        // 合并业务组和虚拟组数据
        List<ChannelGroup> allGroups = new ArrayList<>();
        allGroups.addAll(businessGroups);
        allGroups.addAll(virtualGroup);
        // 强制将交控集团挂在管理平台下
        Map<String, ChannelGroup> nameMap = allGroups.stream().collect(Collectors.toMap(ChannelGroup::getName, Function.identity(), (a, b) -> a));
        // 交控集团改为高速公路
        if (nameMap.containsKey("交控集团")) {
            ChannelGroup devcon = nameMap.get("交控集团");
            devcon.setName("高速公路");
            if (nameMap.containsKey("交通运输视频图像管理平台")) {
                ChannelGroup parent = nameMap.get("交通运输视频图像管理平台");
                devcon.setParentId(String.valueOf(parent.getId()));
            }
        }

        if (nameMap.containsKey("交通运输视频图像管理平台")) {
            ChannelGroup parent = nameMap.get("交通运输视频图像管理平台");
            // 新增在建工程分组
            ChannelGroup construction = new ChannelGroup(CONSTRUCTION_IN_PROCESS, CONSTRUCTION_IN_PROCESS, "14000000982000000001", "在建工程", parent.getId(), null, LocalDateTime.now());
            businessGroups.add(construction);
            // 获取子集
            JSONArray areas = VideoZJGCClient.getAreasByResourceType("camera");
            for (int i = 0; i < areas.size(); i++) {
                JSONObject area = areas.getJSONObject(i);
                ChannelGroup group = new ChannelGroup(area.getString("indexCode"), area.getString("indexCode"), construction.getId(), area.getString("name"), area.getString("parentIndexCode"), null, LocalDateTime.now());
                if ("-1".equals(group.getParentId())) {
                    group.setParentId(construction.getId());
                }
                businessGroups.add(group);
            }
            // 新增轨道交通分组
            ChannelGroup train = new ChannelGroup("train", "train", "14000000982000000001", "轨道交通", parent.getId(), null, LocalDateTime.now());
            businessGroups.add(train);
        }
        // Insert items to database
        this.saveOrUpdateBatch(virtualGroup);
        this.saveOrUpdateBatch(businessGroups);
        logger.info("组织机构同步成功！");
    }

    @Override
    public String getRootId() {
        List<ChannelGroup> groups = this.list(new LambdaQueryWrapper<ChannelGroup>()
                .like(ChannelGroup::getParentId, "__________200_______"));
        if (ObjectUtils.isEmpty(groups)) {
            throw new RuntimeException("无法推导出平台分组");
        }

        return groups.get(0).getParentId();
    }

    @Override
    public IPage<ChannelGroup> page(ChannelGroupQuery query) {
        IPage<ChannelGroup> pageInfo = new Page<>(query.getPage(), query.getPageSize());
        return page(pageInfo, new LambdaQueryWrapper<ChannelGroup>()
                .and(StringUtils.isNotEmpty(query.getKeyword()), wrapper -> {
                    wrapper.like(ChannelGroup::getName, query.getKeyword())
                            .or()
                            .like(ChannelGroup::getChannelCode, query.getKeyword())
                            .or()
                            .like(ChannelGroup::getBusinessGroupId, query.getKeyword())
                    ;
                })
                .eq(!ObjectUtils.isEmpty(query.getId()), ChannelGroup::getId, query.getId())
                .like(!ObjectUtils.isEmpty(query.getChannelCode()), ChannelGroup::getChannelCode, query.getChannelCode())
                .eq(!ObjectUtils.isEmpty(query.getMcId()), ChannelGroup::getMcId, query.getMcId())
                .like(!ObjectUtils.isEmpty(query.getName()), ChannelGroup::getName, query.getName())
                .eq(!ObjectUtils.isEmpty(query.getParentId()), ChannelGroup::getParentId, query.getParentId())
                .eq(StringUtils.isNotEmpty(query.getBusinessGroupId()), ChannelGroup::getBusinessGroupId, query.getBusinessGroupId())
        );
    }

    @Override
    public List<ChannelGroupVO> tree() {
        List<ChannelGroupVO> vos = list().stream().map(val -> {
            ChannelGroupVO vo = new ChannelGroupVO();
            BeanUtils.copyProperties(val, vo);
            return vo;
        }).collect(Collectors.toList());
        Map<String, ChannelGroupVO> map = vos.stream().collect(Collectors.toMap(ChannelGroup::getId, Function.identity()));
        for (ChannelGroupVO vo : vos) {
            if (Objects.isNull(vo.getParentId())) {
                continue;
            }
            ChannelGroupVO parent = map.get(vo.getParentId());
            if (Objects.isNull(parent)) {
                continue;
            }
            if (parent.getChildren() == null) {
                parent.setChildren(new ArrayList<>());
            }
            parent.getChildren().add(vo);
        }
        return vos.stream().filter(val -> !com.alibaba.druid.util.StringUtils.isEmpty(val.getBusinessGroupId()) && val.getParentId() == null).collect(Collectors.toList());
    }

    @Override
    public List<ChannelGroupVO> nameTree(String name) {
        List<ChannelGroupVO> tree = tree();
        return findTreeByName(tree, name);
    }

    private List<ChannelGroupVO> findTreeByName(List<ChannelGroupVO> tree, String name) {
        for (ChannelGroupVO vo : tree) {
            if (vo.getName().equals(name)) {
                return tree;
            }
            if (vo.getChildren() != null) {
                List<ChannelGroupVO> treeByName = findTreeByName(vo.getChildren(), name);
                if (!CollectionUtils.isEmpty(treeByName)) {
                    return treeByName.stream().filter(val -> val.getName().equals(name)).collect(Collectors.toList());
                }
            }
        }
        return new ArrayList<>();
    }
}




