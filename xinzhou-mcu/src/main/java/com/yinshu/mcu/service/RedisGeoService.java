package com.yinshu.mcu.service;

import com.yinshu.mcu.entity.Devcon;
import org.springframework.data.geo.Point;

import java.util.Collection;
import java.util.List;

/**
 * Redis 地理信息服务
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
public interface RedisGeoService {

    /**
     * 保存位置信息
     *
     * @param devcon 开发
     */
    void save(Devcon devcon);

    /**
     * 保存位置信息列表
     *
     * @param devcons 实体列表
     * @return 保存的数据项数量
     */
    Long saveBatch(Collection<Devcon> devcons);

    /**
     * 获取位置
     *
     * @param codes 代码
     * @return {@link List }<{@link Point }>
     */
    List<Point> getPositions(List<String> codes);

    /**
     * 根据中心位置获取范围内设备 ID
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @param radius    半径
     * @param unit      单位
     *                  km: Kilometers
     *                  m: Meters
     *                  mi: Miles
     *                  ft: Feet
     * @return {@link List }<{@link Point }>
     */
    List<String> getByCentral(double longitude, double latitude, double radius, String unit, long limit);

    /**
     * 删除位置
     *
     * @param code 法典
     */
    void remove(String code);

    /**
     * 批量删除
     *
     * @param codes 代码
     * @return {@link Long }
     */
    Long removeBatch(List<String> codes);
}

