package com.yinshu.mcu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinshu.mcu.entity.ChannelGroup;
import com.yinshu.mcu.entity.QueryOrganizesParam;
import com.yinshu.mcu.service.ChannelGroupService;
import com.yinshu.mcu.service.MonitorService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

@Service
public class MonitorServiceImpl implements MonitorService {

    private final ChannelGroupService channelGroupService;

    public MonitorServiceImpl(ChannelGroupService channelGroupService) {
        this.channelGroupService = channelGroupService;
    }

    @Override
    public IPage<ChannelGroup> queryOrganizes(QueryOrganizesParam param) {
        String parentId = param.getId();
        return channelGroupService.page(new Page<>(param.getCurPage(), param.getPageSize()),
                new LambdaQueryWrapper<ChannelGroup>()
                        .notIn(!CollectionUtils.isEmpty(param.getExcludeChannelId()),ChannelGroup::getChannelCode, param.getExcludeChannelId())
                        .isNull(!ObjectUtils.isEmpty(parentId) && "200".equals(parentId), ChannelGroup::getParentId)
                        .eq(!"200".equals(parentId) && !ObjectUtils.isEmpty(parentId), ChannelGroup::getParentId, parentId)
        );
    }

}
