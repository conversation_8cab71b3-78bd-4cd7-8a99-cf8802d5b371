package com.yinshu.mcu.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinshu.mcu.entity.GetCameraVO;

import java.util.List;
import java.util.Map;

/**
 * 创建会议号
 *
 * <AUTHOR>
 *
 */
public interface McuMeetingService {

    /**
     * 获取监控摄像头列表
     * 将添加分页参数: 0-25
     *
     * @return {@link List }<{@link Map }<{@link String }, {@link String }>>
     * @throws Exception 例外
     */
    List<Map<String, String>> getCameraList() throws Exception;

    /**
     * 获取监控摄像头列表
     * 按照支持分页循环查询
     */
    List<Map<String, String>> getAllCameras(GetCameraVO param) throws Exception;

    List<Map<String, String>> getAllCameras(GetCameraVO param,String mcuServer) throws Exception;
    /**
     * 获取虚拟分组
     *
     * @param start  开始
     * @param offset 抵消
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * @throws Exception 例外
     */
    Page<Map<String, String>> getVirtualGroups(int start, int offset) throws Exception;

    /**
     * 获取所有虚拟分组
     *
     * @return {@link List }<{@link Map }<{@link String }, {@link String }>>
     * @throws Exception 例外
     */
    List<Map<String, String>> getAllVirtualGroup() throws Exception;

    /**
     * 获取业务分组
     *
     * @param start  开始
     * @param offset 抵消
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * @throws Exception 例外
     */
    Page<Map<String, String>> getBusinessGroups(int start, int offset) throws Exception;

    /**
     * 获取所有业务分组
     *
     * @return {@link List }<{@link Map }<{@link String }, {@link String }>>
     * @throws Exception 例外
     */
    List<Map<String, String>> getAllBusinessGroups() throws Exception;

    /**
     * 获取监控摄像头信息
     *
     * @param mapData {
     *  conferenceNumber  会议号码
     *  participantNumber 参与人员
     * }
     * @return
     * @throws Exception
     */
    List<Map<String, String>> getCameraListByCodes(Map<String, Object> mapData) throws Exception;

    /**
     * 获取mcu机构分组
     * 按照支持分页循环查询
     */
    List<Map<String, String>> getCivilGroups(Map<String, Object> mapData) throws Exception;

}
