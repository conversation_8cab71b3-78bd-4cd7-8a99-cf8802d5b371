package com.yinshu.mcu.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.exception.RestfulAPIException;
import com.yinshu.mcu.dao.ChannelGroupMapper;
import com.yinshu.mcu.dao.DevconMapper;
import com.yinshu.mcu.entity.ChannelGroup;
import com.yinshu.mcu.entity.Devcon;
import com.yinshu.mcu.entity.GetCameraVO;
import com.yinshu.mcu.entity.QueryDevicesParam;
import com.yinshu.mcu.hikvision.utils.VideoZJGCClient;
import com.yinshu.mcu.ipc.model.Ipc;
import com.yinshu.mcu.ipc.model.State;
import com.yinshu.mcu.ipc.response.IpcResponse;
import com.yinshu.mcu.ipc.utiils.TrackRequestUtils;
import com.yinshu.mcu.service.ChannelGroupService;
import com.yinshu.mcu.service.DevconService;
import com.yinshu.mcu.service.McuMeetingService;
import com.yinshu.mcu.service.RedisGeoService;
import com.yinshu.mcu.utils.GPSUtil;
import com.yinshu.mcu.vo.CameraTreeVO;
import com.yinshu.mcu.vo.ChannelGroupVO;
import com.yinshu.sys.entity.Setting;
import com.yinshu.sys.manager.SettingManager;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 通道服务实现
 *
 * <AUTHOR> Bear
 * @date 2024-11-27 15:05:09
 */
@Service
@Slf4j
public class DevconServiceImpl extends ServiceImpl<DevconMapper, Devcon>
        implements DevconService {

    private final McuMeetingService mcuMeetingService;
    private final SettingManager settingManager;
    private final RedisGeoService redisGeoService;

    @Resource
    ChannelGroupMapper channelGroupMapper;

    @Resource
    ChannelGroupService channelGroupService;

    public DevconServiceImpl(McuMeetingService mcuMeetingService, SettingManager settingManager, RedisGeoService redisGeoService) {
        this.mcuMeetingService = mcuMeetingService;
        this.settingManager = settingManager;
        this.redisGeoService = redisGeoService;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncCameraList(GetCameraVO params) throws Exception {
        // 拉取数据
        List<Map<String, String>> cameraList = mcuMeetingService.getAllCameras(params);

        // 更新数据库
        batchUpdate(cameraList);
    }

    @Override
    public void syncJKCamera(GetCameraVO params) throws Exception {
        // 拉取数据

        Setting setting = settingManager.getByCode("mcuServer_JK");
        if (setting != null && StringUtils.isNotEmpty(setting.getParmValue())) {
            Map<String, Devcon> devconMap = this.list().stream()
                    .filter(entity -> entity.getChannelCode() != null)
                    .collect(Collectors.toMap(Devcon::getChannelCode, Function.identity()));

            if (devconMap.size() > 0) {
                List<Map<String, String>> cameraList = mcuMeetingService.getAllCameras(new GetCameraVO(), setting.getParmValue());
                if (!ObjectUtils.isEmpty(cameraList)) {

                    for (Map<String, String> cameraMap : cameraList) {
                        // Parsing Item
                        String code = cameraMap.get("Alias");
                        if (devconMap.get(code) != null) {
                            devconMap.get(code).setStatus(Integer.valueOf(cameraMap.get("Status")));
                            devconMap.get(code).setBusinessGroupId(cameraMap.get("CameraID"));

                        }
                    }
                    saveOrUpdateBatch(devconMap.values());
                    redisGeoService.saveBatch(devconMap.values());

                }
            }
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void batchUpdate(List<Map<String, String>> resultList) throws Exception {
        // 只删除mcu数据
        this.baseMapper.delete(new LambdaQueryWrapper<Devcon>()
                .eq(Devcon::getDataFrom, 1));
//        this.baseMapper.truncate();
        log.info("list: {}", JSON.toJSONString(resultList));
        // Parsing
        Map<String, Devcon> insertMap = new HashMap<>();
        if (!ObjectUtils.isEmpty(resultList)) {
            List<Devcon> inserts = new LinkedList<>();

            for (Map<String, String> cameraMap : resultList) {
                // Parsing Item
                Devcon devcon = new Devcon();
                String gcs = settingManager.getByCode("GCS").getParmValue();
                double[] gcj02 = GPSUtil.transformGCS(Double.parseDouble(cameraMap.get("Latitude")),
                        Double.parseDouble(cameraMap.get("Longitude")), gcs
                );
                // 设置同步经纬度坐标系 02-高德 09-百度 84-原始GPS
                try {
                    String gcsPos = settingManager.getByCode("GCS-POS").getParmValue();
                    devcon.setOwner(gcsPos);
                } catch (Exception ignore) {
                }
                // TODO Exceptions might be threw when the GCS setting is empty
                devcon.setLongitude(gcj02[1]);
                devcon.setLatitude(gcj02[0]);
                String name = cameraMap.get("name");
                devcon.setName(name);
                // Copy name as area
                devcon.setArea(name);
                devcon.setStatus(Integer.valueOf(cameraMap.get("status")));
                devcon.setMcId(cameraMap.get("platcode"));
                devcon.setParentId(cameraMap.get("ParentId"));
                devcon.setCivilCode(cameraMap.get("CivilCode"));
                devcon.setChannelCode(cameraMap.get("code"));
                devcon.setSyncTime(LocalDateTime.now());
                devcon.setIp(cameraMap.get("Address"));
                insertMap.put(devcon.getChannelCode(), devcon);
                // Add to insert list
                inserts.add(devcon);
            }

            // 3. Save and update data
            this.saveBatch(inserts);

        }
    }

    @Override
    public boolean updateBatchById(Collection<Devcon> devcons) {
        if (ObjectUtils.isEmpty(devcons)) return true;
        // Update database
        boolean result = super.updateBatchById(devcons);

        // Save positions to Redis
        redisGeoService.saveBatch(devcons);

        return result;
    }

    private boolean synRedis() {
        List<Devcon> list = this.list();
        long size = redisGeoService.saveBatch(list);
        return size > 0;
    }

    @Override
    public boolean saveBatch(Collection<Devcon> devcons) {
        if (ObjectUtils.isEmpty(devcons)) return true;

        // Save to database
        boolean result = super.saveBatch(devcons);

        // Save to Redis
        redisGeoService.saveBatch(devcons);

        return result;
    }

    @Override
    public IPage<Devcon> page(QueryDevicesParam param) {
        //用于同步测试数据存到redis里
        // Query in-range devices
        List<String> parentIds = new ArrayList<>();
        if (!ObjectUtils.isEmpty(param.getType())) {
            parentIds = buildCameraParentIdsByType(param.getType());
            if (parentIds.isEmpty()) {
                return new Page<>(param.getCurPage(), param.getPageSize());
            }
        }
        boolean needLocationCheck = !ObjectUtils.isEmpty(param.getRadius()) &&
                !ObjectUtils.isEmpty(param.getLatitude()) &&
                !ObjectUtils.isEmpty(param.getLongitude());
        List<String> inRangeCodes;
        if (needLocationCheck) {
            inRangeCodes = redisGeoService.getByCentral(param.getLongitude(), param.getLatitude(), param.getRadius(), param.getUnit(), param.getRadiusLimit());
            if (inRangeCodes.isEmpty()) {
                return new Page<>(param.getCurPage(), param.getPageSize());
            }
        } else {
            inRangeCodes = new LinkedList<>();
        }
        // 分批查询数据
        if (!CollectionUtils.isEmpty(inRangeCodes)) {
            ExecutorService service = Executors.newFixedThreadPool(10);
            List<Devcon> lists = new ArrayList<>();
            List<Future<List<Devcon>>> futures = new ArrayList<>();
            try {
                for (int i = 0; i < inRangeCodes.size(); i += 500) {
                    int finalI = i;
                    List<String> finalParentIds = parentIds;
                    Future<List<Devcon>> future = service.submit(() -> {
                        List<String> codes = inRangeCodes.subList(finalI, Math.min(finalI + 500, inRangeCodes.size()));
                        val querie = new LambdaQueryWrapper<Devcon>()
                                .in(needLocationCheck, Devcon::getChannelCode, codes)
                                .notIn(!CollectionUtils.isEmpty(param.getExcludeDataFrom()), Devcon::getDataFrom, param.getExcludeDataFrom())
                                .in(!CollectionUtils.isEmpty(finalParentIds), Devcon::getParentId, finalParentIds)
                                .and(StringUtils.isNotBlank(param.getKeyword()), qw -> {
                                    if (StringUtils.isNotBlank(param.getKeyword())) {
                                        qw.like(Devcon::getName, param.getKeyword())
                                                .or()
                                                .like(Devcon::getChannelCode, param.getKeyword());
                                    }
                                });
                        if (param.getStatus() != null && !param.getStatus().equals("")) {
                            querie.eq(Devcon::getStatus, param.getStatus());
                        }
                        return list(querie);
                    });
                    futures.add(future);
                }
                for (Future<List<Devcon>> future : futures) {
                    try {
                        lists.addAll(future.get());
                    } catch (Exception e) {
                        log.error("Error when getting future result", e);
                    }
                }
            } finally {
                // 确保线程池一定关闭
                service.shutdown();
            }
            List<Devcon> result = lists.stream()
                    .sorted(Comparator.comparing(Devcon::getChannelCode))
                    .skip((long) (param.getCurPage() - 1) * param.getPageSize())
                    .limit(param.getPageSize())
                    .collect(Collectors.toList());
            Page<Devcon> page = new Page<>(param.getCurPage(), param.getPageSize(), lists.size());
            page.setRecords(result);
            return page;
        } else {
            LambdaQueryWrapper<Devcon> queries = new LambdaQueryWrapper<Devcon>()
                    .notIn(!CollectionUtils.isEmpty(param.getExcludeDataFrom()), Devcon::getDataFrom, param.getExcludeDataFrom())
                    .in(needLocationCheck, Devcon::getChannelCode, inRangeCodes)
                    .in(!CollectionUtils.isEmpty(parentIds), Devcon::getParentId, parentIds)
                    .like(StringUtils.isNotBlank(param.getKeyword()), Devcon::getName, param.getKeyword())
                    .or()
                    .like(StringUtils.isNotBlank(param.getKeyword()), Devcon::getChannelCode, param.getKeyword());
            if (param.getStatus() != null && !param.getStatus().equals("")) {
                queries.eq(Devcon::getStatus, param.getStatus());
            }


            if (!ObjectUtils.isEmpty(param.getId())) {
                if ("200".equals(param.getId())) {
                    queries.isNull(Devcon::getParentId);
                } else {
                    queries.likeLeft(Devcon::getParentId, param.getId());
                }
            }

            return this.page(new Page<>(param.getCurPage(), param.getPageSize()), queries);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncCameraStatus() throws Exception {
        // 1. 拉取远程摄像头数据
        List<Map<String, String>> cameraList = mcuMeetingService.getAllCameras(new GetCameraVO());
        if (CollectionUtils.isEmpty(cameraList)) {
            return;
        }

        // 2. 封装为 Devcon 对象，并构建名称映射
        Map<String, Devcon> devconMap = cameraList.stream()
                .map(camera -> {
                    Devcon devcon = new Devcon();
                    devcon.setChannelCode(camera.get("code"));
                    devcon.setStatus(Integer.parseInt(camera.get("status")));
                    return devcon;
                })
                .collect(Collectors.toMap(Devcon::getChannelCode, Function.identity(), (v1, v2) -> v1));

        List<String> allNames = new ArrayList<>(devconMap.keySet());

        // 3. 分批处理（每500个）
        int batchSize = 500;
        for (int i = 0; i < allNames.size(); i += batchSize) {
            List<String> subNames = allNames.subList(i, Math.min(i + batchSize, allNames.size()));

            // 4. 查询数据库中已存在的记录
            List<Devcon> dbList = this.list(new LambdaQueryWrapper<Devcon>().in(Devcon::getChannelCode, subNames));
            for (Devcon dbDevcon : dbList) {
                Devcon remoteDevcon = devconMap.get(dbDevcon.getChannelCode());
                if (remoteDevcon != null) {
                    dbDevcon.setStatus(remoteDevcon.getStatus());
                }
            }

            // 5. 批量更新（最多500条/次）
            if (!dbList.isEmpty()) {
                updateBatchById(dbList);
            }
        }
    }

    @Override
    public List<Devcon> getCameraListByType(String types, String keyword) {
        List<CompletableFuture<List<Devcon>>> futures = new ArrayList<>();
        ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
        for (String type : types.split(",")) {
            String finalType = "交控集团".equals(type) ? "高速公路" : type;
            // 并发查询任务封装为 CompletableFuture
            CompletableFuture<List<Devcon>> future = CompletableFuture.supplyAsync(() -> {
                List<String> ids = buildCameraParentIdsByType(finalType);
                if (ids.isEmpty()) {
                    return Collections.emptyList();
                }
                return this.list(new LambdaQueryWrapper<Devcon>()
                        .in(Devcon::getParentId, ids)
                        .like(StringUtils.isNotBlank(keyword), Devcon::getName, keyword));
            }, executor);
            futures.add(future);
        }
        // 等待所有任务执行完成
        List<Devcon> all = futures.stream()
                // 阻塞等待每个结果
                .map(CompletableFuture::join)
                // 合并所有结果
                .flatMap(List::stream)
                .collect(Collectors.toList());
        // 关闭线程池
        executor.shutdown();
        return all;
    }

    /**
     * <AUTHOR>
     * @description //TODO 同步ipc摄像头列表
     * @date 2025/6/13 10:01
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncIpcCameras() {
        IpcResponse ipcs = TrackRequestUtils.getIpcs();
        baseMapper.delete(new LambdaQueryWrapper<Devcon>()
                .eq(Devcon::getDataFrom, 3));
        Devcon max = baseMapper.selectOne(new LambdaQueryWrapper<Devcon>()
                .orderByDesc(Devcon::getId)
                .last("limit 1")
        );
        int maxId = max == null ? 0 : max.getId() + 1;
        String gcsPos = settingManager.getByCode("GCS-POS").getParmValue();
        List<Devcon> list = new ArrayList<>();
        for (Ipc ipc : ipcs.getIpcList()) {
            Devcon devcon = new Devcon();
            devcon.setId(maxId++);
            devcon.setChannelCode(ipc.getUuid());
            devcon.setMcId("14000000982000000001");
            devcon.setName(ipc.getName());
            devcon.setOwner(gcsPos);
            devcon.setCivilCode(ipc.getNodeIp());
            // mcId + channelCode
            devcon.setParentId("14000000982000000001/train");
            // ，init（初始化）、working（正常）、unauth（未认证）、disconnect（未连接）、undispatch（未分配）
            State state = ipc.getState();
            if (ipc.getState() != null) {
                String deviceState = state.getDeviceState();
                switch (deviceState) {
                    case "disconnect":
                        devcon.setStatus(0);
                        break;
                    case "init":
                    case "unauth":
                    case "undispatch":
                    case "working":
                        devcon.setStatus(1);
                        break;
                }
            } else {
                devcon.setStatus(0);
            }
//            devcon.setLongitude(null);
//            devcon.setLatitude(null);
            devcon.setBusinessGroupId("train");
            devcon.setSyncTime(LocalDateTime.now());
            devcon.setIp(ipc.getIp());
//            devcon.setArea();
            devcon.setDataFrom(3);
            list.add(devcon);
        }
        super.saveBatch(list);
    }

    /**
     * <AUTHOR>
     * @description //TODO 同步ipc摄像头状态
     * @date 2025/6/13 10:04
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncIpcCameraStatus() {
        IpcResponse ipcs = TrackRequestUtils.getIpcs();
        Map<String, Ipc> map = ipcs.getIpcList().stream().collect(Collectors.toMap(Ipc::getName, Function.identity(), (a, b) -> a));
        if (!map.isEmpty()) {
            List<Devcon> list = this.list(new LambdaQueryWrapper<Devcon>()
                    .eq(Devcon::getDataFrom, 3)
                    .in(Devcon::getName, map.keySet()));
            Map<String, Devcon> map1 = list.stream().collect(Collectors.toMap(Devcon::getName, Function.identity()));
            map1.forEach((name, devcon) -> {
                Ipc ipc = map.get(name);
                devcon.setStatus("working".equals(ipc.getState().getDeviceState()) ? 1 : 0);
            });
            this.updateBatchById(list);
        }
    }

    @Override
    public List<CameraTreeVO> tree(JSONObject params) {
        List<CameraTreeVO> data = new ArrayList<>();
        // 分组数据
        List<ChannelGroupVO> tree = channelGroupService.tree();
        // 摄像头数据
        Map<String, List<Devcon>> cameraGroup = this.list(new LambdaQueryWrapper<Devcon>()).stream().collect(Collectors.groupingBy(Devcon::getParentId));
        // 构建分组数据的同时。构建摄像头数据
        buildTree(tree, cameraGroup, data);
        return data;
    }

    /**
     * @param tree        频道分组树
     * @param cameraGroup 摄像头分组
     * @param data        最终的树结构数据
     * <AUTHOR>
     * @description //TODO 构建树结构
     * @date 2025/6/24 16:35
     **/
    private void buildTree(List<ChannelGroupVO> tree, Map<String, List<Devcon>> cameraGroup, List<CameraTreeVO> data) {
        for (ChannelGroupVO channelGroup : tree) {
            CameraTreeVO cameraTreeVO = new CameraTreeVO();
            cameraTreeVO.setLabel(channelGroup.getName());
            cameraTreeVO.setId(channelGroup.getId());
//            cameraTreeVO.setChannelCode(channelGroup.getChannelCode());
//            cameraTreeVO.setMcId(channelGroup.getMcId());
//            cameraTreeVO.setName(channelGroup.getName());
//            cameraTreeVO.setParentId(channelGroup.getParentId());
//            cameraTreeVO.setBusinessGroupId(channelGroup.getBusinessGroupId());
//            cameraTreeVO.setSyncTime(channelGroup.getSyncTime());
            cameraTreeVO.setTreeType("group");
            if (CollectionUtils.isEmpty(channelGroup.getChildren())) {
                // 放置摄像头
                if (channelGroup.getMcId() != null && channelGroup.getChannelCode() != null) {
                    if (cameraGroup.containsKey(channelGroup.getMcId() + "/" + channelGroup.getChannelCode())) {
                        List<Devcon> devcons = cameraGroup.get(channelGroup.getMcId() + "/" + channelGroup.getChannelCode());
                        cameraTreeVO.setChildren(devcons.stream().map(devcon -> {
                            CameraTreeVO cameraVo = new CameraTreeVO();
                            cameraVo.setId(String.valueOf(devcon.getId()));
                            cameraVo.setLabel(devcon.getName());
                            cameraVo.setChildren(new ArrayList<>());
                            if (StringUtils.isNotBlank(devcon.getName())) {
                                if (devcon.getName().contains("公司")) {
                                    cameraVo.setTurnName(devcon.getName().substring(devcon.getName().indexOf("公司") + 2));
                                } else if (devcon.getName().contains("K")) {
                                    cameraVo.setTurnName(devcon.getName().substring(devcon.getName().lastIndexOf("K")));
                                } else {
                                    cameraVo.setTurnName(devcon.getName());
                                }
                            }
//                            cameraVo.setChannelCode(devcon.getChannelCode());
//                            cameraVo.setMcId(devcon.getMcId());
//                            cameraVo.setName(devcon.getName());
//                            cameraVo.setOwner(devcon.getOwner());
//                            cameraVo.setCivilCode(devcon.getCivilCode());
//                            cameraVo.setParentId(devcon.getParentId());
                            cameraVo.setStatus(devcon.getStatus());
                            cameraVo.setLongitude(devcon.getLongitude());
                            cameraVo.setLatitude(devcon.getLatitude());
//                            cameraVo.setBusinessGroupId(devcon.getBusinessGroupId());
//                            cameraVo.setSyncTime(devcon.getSyncTime());
//                            cameraVo.setIp(devcon.getIp());
//                            cameraVo.setArea(devcon.getArea());
//                            cameraVo.setDistance(devcon.getDistance());
//                            cameraVo.setType(devcon.getType());
                            cameraVo.setTreeType("camera");
                            cameraVo.setDataFrom(devcon.getDataFrom());
                            return cameraVo;
                        }).collect(Collectors.toList()));
                    }
                }
            } else {

                // 再次构建摄像头分组
                if (cameraTreeVO.getChildren() == null) {
                    cameraTreeVO.setChildren(new ArrayList<>());
                }
                buildTree(channelGroup.getChildren(), cameraGroup, cameraTreeVO.getChildren());
            }
            data.add(cameraTreeVO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncHikiCameraList() {
        this.baseMapper.delete(new LambdaQueryWrapper<Devcon>()
                .eq(Devcon::getDataFrom, 2)
        );
        JSONArray objects = VideoZJGCClient.callAllPostApiGetCamera();
        if (CollectionUtils.isEmpty(objects)) {
            return;
        }
        Devcon max = this.baseMapper.selectOne(new LambdaQueryWrapper<Devcon>()
                .orderByDesc(Devcon::getId)
                .last("limit 1")
        );
        int maxId = max == null ? 0 : max.getId() + 1;
        List<Devcon> devcons = new ArrayList<>();
        for (int i = 0; i < objects.size(); i++) {
            JSONObject object = objects.getJSONObject(i);
            Devcon devcon = new Devcon();
            devcon.setId(maxId++);
            // mcId + channelCode
            devcon.setParentId("zjgc" + "/" + object.getString("regionIndexCode"));
            devcon.setDataFrom(2);
            devcon.setMcId("14000000982000000001");
            devcon.setSyncTime(LocalDateTime.now());

            // 业务动态数据
            devcon.setChannelCode(object.getString("cameraIndexCode"));
            devcon.setName(object.getString("cameraName"));
            String gcs = settingManager.getByCode("GCS").getParmValue();
            if (object.getString("latitude") != null && object.getString("longitude") != null && !"".equals(object.getString("latitude")) && !"".equals(object.getString("longitude"))) {
                double[] gcj02 = GPSUtil.transformGCS(Double.parseDouble(object.getString("latitude")),
                        Double.parseDouble(object.getString("longitude")), gcs
                );
                devcon.setLongitude(gcj02[1]);
                devcon.setLatitude(gcj02[0]);
            }
            // 设置同步经纬度坐标系 02-高德 09-百度 84-原始GPS
            try {
                String gcsPos = settingManager.getByCode("GCS-POS").getParmValue();
                devcon.setOwner(gcsPos);
            } catch (Exception ignore) {
            }
            devcon.setCivilCode(object.getString("cameraType"));
            devcon.setArea(devcon.getName());
            devcon.setStatus(object.getInteger("status"));
            // 默认在线
            if (devcon.getStatus() == null) {
                devcon.setStatus(1);
            }
            devcons.add(devcon);
        }
        // 批量入库
        this.saveOrUpdateBatch(devcons);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncHikiCamerasStatus() {
        JSONArray objects = VideoZJGCClient.callAllPostApiGetCamera();
        if (CollectionUtils.isEmpty(objects)) {
            return;
        }
        Map<String, Integer> codeStatusMap = new HashMap<>();
        List<Devcon> devcons = new ArrayList<>();
        for (int i = 0; i < objects.size(); i++) {
            JSONObject object = objects.getJSONObject(i);
            codeStatusMap.put(object.getString("cameraIndexCode"), object.getInteger("status"));
        }
        for (Devcon devcon : this.list(new LambdaQueryWrapper<Devcon>()
                .eq(Devcon::getDataFrom, 2)
                .in(Devcon::getChannelCode, codeStatusMap.keySet())
        )) {
            Integer status = codeStatusMap.get(devcon.getChannelCode());
            if (status != null) {
                devcon.setStatus(status);
                devcons.add(devcon);
            }
        }
        this.updateBatchById(devcons);
    }


    /*
     * <AUTHOR>
     * @description //TODO 构造cameraParentIds
     * @date 2025/5/20 10:32
     * @param type 类型名称
     * @return java.util.List<java.lang.String>
     **/
    public List<String> buildCameraParentIdsByType(String type) {
        if (StringUtils.isBlank(type)) {
            throw new RestfulAPIException("类型不能为空");
        }
        ChannelGroup one = channelGroupMapper.selectOne(new LambdaQueryWrapper<ChannelGroup>()
                .eq(ChannelGroup::getName, type)
                .orderByDesc(ChannelGroup::getSyncTime)
                .last("limit 1")
        );
        if (ObjectUtils.isEmpty(one)) {
            return new ArrayList<>();
        }

        List<ChannelGroupVO> tree = channelGroupService.tree();
        List<ChannelGroupVO> children = new ArrayList<>();
        findChildrenByParent(children, tree, one.getId());

        // 合并ids
        // mcId + channelCode
        List<String> ids = children.stream().map(item -> item.getMcId() + "/" + item.getChannelCode()).collect(Collectors.toList());
        ids.add(one.getMcId() + "/" + one.getChannelCode());
        return ids;
    }

    /*
     * <AUTHOR>
     * @description //TODO 获取所有子节点的id
     * @date 2025/5/20 14:31
     * @param children 节点
     * @param id 节点id
     **/
    private void findChildrenByParent(List<ChannelGroupVO> allMatch, List<ChannelGroupVO> children, String id) {
        if (children != null) {
            for (ChannelGroupVO child : children) {
                if (child.getParentId() != null && child.getParentId().equals(id)) {
                    allMatch.add(child);
                    findChildrenByParent(allMatch, child.getChildren(), child.getId());
                } else {
                    findChildrenByParent(allMatch, child.getChildren(), id);
                }
            }
        }
    }


    @Override
    public Devcon getById(Serializable id) {
        Devcon po = super.getById(id);
        String parentId = po.getParentId();
        if (StringUtils.isNotEmpty(parentId)) {
            String[] split = parentId.split("/");
            if (split.length > 0) {
                ChannelGroup group = channelGroupMapper.selectById(split[split.length - 1]);
                if (group != null) {
                    po.setType(group.getName());
                }
            }
        }
        return po;
    }
}
