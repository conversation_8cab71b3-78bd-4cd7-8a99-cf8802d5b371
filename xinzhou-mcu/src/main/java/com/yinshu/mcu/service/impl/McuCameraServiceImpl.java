package com.yinshu.mcu.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.yinshu.mcu.service.McuCameraService;
import com.yinshu.mcu.utils.McuUtils;
import com.yinshu.sys.manager.SettingManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetAddress;
import java.net.Socket;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Service
public class McuCameraServiceImpl implements McuCameraService {

    private static final Logger logger = LoggerFactory.getLogger(McuCameraServiceImpl.class);

    private final SettingManager settingManager;

    public McuCameraServiceImpl(SettingManager settingManager) {
        this.settingManager = settingManager;
    }

    private Socket getSocket(String ptzAddress) {
        try {
            if(StringUtils.isEmpty(ptzAddress)) {
                ptzAddress = settingManager.getByCode("mcuServer").getParmValue()
                    .replace("_", ":");
                if(StringUtils.isEmpty(ptzAddress)) {
                    throw new RuntimeException("请配置mcu服务器地址和端口！");
                }
            }
            String[] strs = ptzAddress.split(":");
            String ip = strs[0];
            int port = Integer.parseInt(strs[1]);
            logger.info("McuSocket=========================>>>>IP:" + ip + "&Port:" + port);

            Socket socket = null;
            if(McuUtils.isIpAddress(ip)) {
                socket = new Socket(ip, port);
            } else {
                InetAddress netAddress = InetAddress.getByName(ip);
                socket = new Socket(netAddress, port);
            }
            //logger.info("McuSocket=========================>>>>连接成功");
            return socket;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public Map<String, String> ptzAction(String action, String status, String partyNumber, String step, String ptzAddress) throws Exception {
        InputStream inputStream = null;
        OutputStream outputStream = null;
        Map<String, String> resultMap = new HashMap<>();

        try (Socket socket = getSocket(ptzAddress)) {
            outputStream = socket.getOutputStream();
            String content = String.format("<action>%s</action><status>%s</status><partyNumber>%s</partyNumber><step>%s</step>",
                action, status, partyNumber, Integer.parseInt(step));
            String paramsStr = McuUtils.addHeader("ptzAction", "POST", "mc-40005", content);
            logger.info("ptzAction-start ----->" + paramsStr);

            outputStream.write(paramsStr.getBytes());
            inputStream = socket.getInputStream();
            byte[] buff = new byte[1024];
            int len = inputStream.read(buff);
            String result = new String(buff, 0, len, StandardCharsets.UTF_8);
            logger.info("ptzAction-end ----->" + result);

            resultMap.put("code", result.contains("OK") ? "OK" : result);
            return resultMap;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw e;
        }
    }
}
