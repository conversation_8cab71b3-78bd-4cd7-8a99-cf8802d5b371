package com.yinshu.mcu.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yinshu.mcu.entity.ChannelGroup;
import com.yinshu.mcu.query.ChannelGroupQuery;
import com.yinshu.mcu.vo.ChannelGroupVO;

import java.util.List;

/**
 * 通道分组服务
 *
 * <AUTHOR> <PERSON>
 * @date  2024-11-26 15:17:01
 */
public interface ChannelGroupService extends IService<ChannelGroup> {

    /**
     * 同步机构数据
     *
     * @throws Exception 例外
     */
    void syncGroups() throws Exception;

    /**
     * 获取根机构 ID
     *
     * @return {@link String }
     * @throws RuntimeException 数据库中没有根节点下的机构，会抛出异常
     */
    String getRootId() throws RuntimeException;


    /*
     * <AUTHOR>
     * @description //TODO 分页查询数据
     * @date 2025/5/20 10:38
     * @param query 查询类
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.yinshu.mcu.entity.ChannelGroup>
     **/
    IPage<ChannelGroup> page(ChannelGroupQuery query);

    /*
     * <AUTHOR>
     * @description //TODO 获取组织树
     * @date 2025/5/20 14:29
     * @return java.util.List<com.yinshu.mcu.vo.ChannelGroupVO>
     **/
    List<ChannelGroupVO> tree();


    /*
     * <AUTHOR>
     * @description //TODO 获取指定名称的组织树
     * @date 2025/6/5 14:54
     * @param name
     * @return java.util.List<com.yinshu.mcu.vo.ChannelGroupVO>
     **/
    List<ChannelGroupVO> nameTree(String name);
}
