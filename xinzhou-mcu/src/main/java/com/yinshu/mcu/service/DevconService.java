package com.yinshu.mcu.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yinshu.mcu.entity.Devcon;
import com.yinshu.mcu.entity.GetCameraVO;
import com.yinshu.mcu.entity.QueryDevicesParam;
import com.yinshu.mcu.vo.CameraTreeVO;

import java.util.List;

/**
 * 通道服务
 *
 * <AUTHOR> Bear
 * @description 针对表【DEVCON】的数据库操作Service
 * @createDate 2024-11-27 15:05:09
 * <p>
 * WARNING: 如果有数据进行插入、修改、删除等操作，请将影响的数据项坐标同步到 Redis 中。
 * Page 方法中需要根据其坐标进行查找。
 */
public interface DevconService extends IService<Devcon> {

    /**
     * 同步摄像机列表
     *
     * @param params 参数
     * @throws Exception 例外
     */
    void syncCameraList(GetCameraVO params) throws Exception;

    void syncJKCamera(GetCameraVO params) throws Exception;

    /**
     * 查询设备
     *
     * @return {@link IPage }<{@link Devcon }>
     */
    IPage<Devcon> page(QueryDevicesParam param);

    /*
     * <AUTHOR>
     * @description //TODO 同步摄像机列表状态
     * @date 2025/5/12 17:27
     **/
    void syncCameraStatus() throws Exception;


    /*
     * <AUTHOR>
     * @description //TODO 同步海康摄像头
     * @date 2025/5/27 14:36
     **/
    void syncHikiCameraList();

    /*
     * <AUTHOR>
     * @description //TODO 同步海康摄像头状态
     * @date 2025/5/27 15:03
     **/
    void syncHikiCamerasStatus();

    List<Devcon> getCameraListByType(String type, String keyword);

    /**
     * <AUTHOR>
     * @description //TODO 同步ipc摄像头列表
     * @date 2025/6/13 10:01
     **/
    void syncIpcCameras();

    /**
     * <AUTHOR>
     * @description //TODO 同步ipc摄像头状态
     * @date 2025/6/13 10:04
     **/
    void syncIpcCameraStatus();

    /*
     * <AUTHOR>
     * @description //TODO 获取摄像机树
     * @date 2025/6/24 16:24
     * @param params 请求参数
     * @return java.util.List<com.yinshu.mcu.vo.CameraTreeVO>
     **/
    List<CameraTreeVO> tree(JSONObject params);
}
