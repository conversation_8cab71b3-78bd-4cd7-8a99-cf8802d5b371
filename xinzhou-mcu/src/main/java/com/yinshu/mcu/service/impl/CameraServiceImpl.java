package com.yinshu.mcu.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.mcu.dao.JKCameraMapper;
import com.yinshu.mcu.entity.Camera;
import com.yinshu.mcu.entity.Devcon;
import com.yinshu.mcu.hikvision.utils.VideoZJGCClient;
import com.yinshu.mcu.ipc.query.RecordQueryRequest;
import com.yinshu.mcu.ipc.response.RtspRecordResponse;
import com.yinshu.mcu.ipc.utiils.TrackRequestUtils;
import com.yinshu.mcu.service.CameraService;
import com.yinshu.mcu.service.DevconService;
import com.yinshu.mcu.utils.VideoPlatformClient;
import com.yinshu.sys.manager.SettingManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
public class CameraServiceImpl extends ServiceImpl<JKCameraMapper, Camera>
        implements CameraService {

    @Autowired
    private DevconService devconService;

    @Autowired
    private VideoPlatformClient videoPlatformClient;

    @Autowired
    private SettingManager settingManager;

    //获取链接
    @Override
    public String getVideoUrl(String id, int videoType) {
        if (id.length() < 36) {
            Devcon devcon = devconService.getById(id);
            // 海康威视视频预览
            if (devcon.getDataFrom() == 2) {
                JSONObject object = VideoZJGCClient.callPostApiGetVideoUrlByCameraIndexCode(devcon.getChannelCode());
                if (object.getInteger("code") == 0) {
                    JSONObject obj1 = object.getJSONObject("data");
                    return obj1.getString("url");
                }
                throw new RuntimeException(object.getString("msg"));
            }
            if (StringUtils.isEmpty(devcon.getBusinessGroupId())) {
                String[] names = devcon.getName().split("公司");
                if (names.length > 1 && StringUtils.isNotEmpty(names[1])) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("cameraName", names[1]);
                    QueryWrapper<Camera> wrapper = new QueryWrapper();
                    wrapper.like("camera_name", names[1]);
                    List<Camera> cameraList = this.baseMapper.selectList(wrapper);
                    if (cameraList != null && cameraList.size() > 0) {
                        id = cameraList.get(0).getId();
                    }
                }
            } else {
                id = devcon.getBusinessGroupId();
            }
        }
        if (videoType == -1) {
            videoType = Integer.parseInt(settingManager.getByCode("videoType").getParmValue());
        }
        String result = videoPlatformClient.getVideoUrl(id, videoType);
        JSONObject obj = JSON.parseObject(result);
        if (obj.getInteger("code") == 200) {
            JSONObject obj1 = obj.getJSONObject("data");
            return obj1.getString("VideoUrl");
        }

        return "";

    }

    public void syncCameraList() {
        try {
            //获取设备信息
            String result = videoPlatformClient.getCameraList(null);
            JSONObject obj = JSON.parseObject(result);
            if (obj.getInteger("code") == 200) {
                JSONArray arr = obj.getJSONArray("data");
                List<Camera> cameraList = new ArrayList<>();
                for (int i = 0; i < arr.size(); i++) {
                    JSONObject cameraJson = arr.getJSONObject(i);
                    Camera camera = new Camera();
                    camera.setId(cameraJson.getString("CameraID"));
                    camera.setCameraName(cameraJson.getString("CameraName"));
                    cameraList.add(camera);

                }
                saveOrUpdateBatch(cameraList);
            }
            //获取状态
            String statusResult = videoPlatformClient.getCameraStatus();
            JSONObject statusObj = JSON.parseObject(statusResult);
            if (statusObj.getInteger("code") == 200) {
                List<Camera> cameraList = new ArrayList<>();
                JSONArray statusArr = statusObj.getJSONArray("data");
                for (int i = 0; i < statusArr.size(); i++) {
                    JSONObject obj1 = statusArr.getJSONObject(i);
                    Camera camera = new Camera();
                    camera.setId(obj1.getString("CameraID"));
                    camera.setStatus(obj1.getInteger("Status"));
                    cameraList.add(camera);


                }
                saveOrUpdateBatch(cameraList);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * @param request 请求信息
     * @return com.yinshu.mcu.ipc.response.RtspRecordResponse
     * <AUTHOR>
     * @description //TODO 获取IPC视频流地址
     * @date 2025/6/13 10:56
     **/
    @Override
    public RtspRecordResponse getIpcVideoUrl(RecordQueryRequest request) {
        return TrackRequestUtils.getRtspRecord(request);
    }

}
