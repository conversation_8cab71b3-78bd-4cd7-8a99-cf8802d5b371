package com.yinshu.mcu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yinshu.mcu.entity.Camera;
import com.yinshu.mcu.ipc.query.RecordQueryRequest;
import com.yinshu.mcu.ipc.response.RtspRecordResponse;

/**
 * 通道服务
 */
public interface CameraService extends IService<Camera> {

    String getVideoUrl(String id, int type);

    void syncCameraList();

    /**
     * @param request 请求信息
     * @return com.yinshu.mcu.ipc.response.RtspRecordResponse
     * <AUTHOR>
     * @description //TODO 获取IPC视频流地址
     * @date 2025/6/13 10:56
     **/
    RtspRecordResponse getIpcVideoUrl(RecordQueryRequest request);
}
