package com.yinshu.mcu.service.impl;

import com.yinshu.mcu.entity.Devcon;
import com.yinshu.mcu.service.RedisGeoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.geo.*;
import org.springframework.data.redis.connection.RedisGeoCommands;
import org.springframework.data.redis.core.GeoOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RedisGeoImplService implements RedisGeoService {


    static final String DEVICE_POSITIONS_KEY = "device_positions";
    private final StringRedisTemplate redisTemplate;

    public RedisGeoImplService(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public void save(Devcon devcon) {
        GeoOperations<String, String> geoOperations = redisTemplate.opsForGeo();
        geoOperations.add(DEVICE_POSITIONS_KEY,
                new Point(devcon.getLongitude(), devcon.getLatitude()),
                devcon.getChannelCode());
    }

    @Override
    public Long saveBatch(Collection<Devcon> devcons) {
        GeoOperations<String, String> geoOperations = redisTemplate.opsForGeo();

        List<RedisGeoCommands.GeoLocation<String>> positions = devcons.stream().map(devcon ->
                new RedisGeoCommands.GeoLocation<>(devcon.getChannelCode(),
                        new Point(devcon.getLongitude(), devcon.getLatitude())
                )).collect(Collectors.toList());

        return geoOperations.add(DEVICE_POSITIONS_KEY, positions);
    }

    @Override
    public List<Point> getPositions(List<String> codes) {
        String[] codeArr = codes.toArray(new String[0]);

        GeoOperations<String, String> ops = redisTemplate.opsForGeo();

        return ops.position(DEVICE_POSITIONS_KEY, codeArr);
    }

    @Override
    public List<String> getByCentral(double longitude, double latitude, double radius, String unit, long limit) {
        GeoOperations<String, String> ops = redisTemplate.opsForGeo();


        // Choose metric
        Metric metric = Metrics.NEUTRAL;

        switch (unit) {
            case "m":
                metric = RedisGeoCommands.DistanceUnit.METERS;
                break;
            case "km":
                metric = RedisGeoCommands.DistanceUnit.KILOMETERS;
                break;
            case "mi":
                metric = RedisGeoCommands.DistanceUnit.MILES;
                break;
            case "ft":
                metric = RedisGeoCommands.DistanceUnit.FEET;
                break;
        }

        // 查询总数
        RedisGeoCommands.GeoRadiusCommandArgs countArgs = RedisGeoCommands.GeoRadiusCommandArgs.newGeoRadiusArgs();
        GeoResults<RedisGeoCommands.GeoLocation<String>> countResults = ops.radius(
                DEVICE_POSITIONS_KEY,
                new Circle(new Point(longitude, latitude), new Distance(radius, metric)),
                countArgs
        );
        int total = countResults != null ? countResults.getContent().size() : 0;
        log.info("数据总数为: {}", total);

        RedisGeoCommands.GeoRadiusCommandArgs args = RedisGeoCommands.GeoRadiusCommandArgs.newGeoRadiusArgs()
                .sortAscending()      // 按距离升序
                .limit(limit);        // 最多返回多少条
        GeoResults<RedisGeoCommands.GeoLocation<String>> geoResults = ops.radius(DEVICE_POSITIONS_KEY,
                new Circle(new Point(longitude, latitude),
                        new Distance(radius, metric)),
                args);

        if (geoResults == null) {
            return null;
        }

        List<GeoResult<RedisGeoCommands.GeoLocation<String>>> content = geoResults.getContent();
        return content.stream().map(c -> c.getContent().getName()).collect(Collectors.toList());
    }

    @Override
    public void remove(String code) {
        GeoOperations<String, String> ops = redisTemplate.opsForGeo();
        ops.remove(DEVICE_POSITIONS_KEY, code);
    }

    @Override
    public Long removeBatch(List<String> codes) {
        GeoOperations<String, String> ops = redisTemplate.opsForGeo();
        return ops.remove(DEVICE_POSITIONS_KEY, codes.toArray(new String[0]));
    }
}
