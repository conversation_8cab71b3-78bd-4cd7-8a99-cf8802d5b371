package com.yinshu.mcu.dao;

import com.yinshu.mcu.entity.ChannelGroup;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.springframework.stereotype.Repository;

/**
 * 通道分组 Mapper
 *
 * <AUTHOR>
 * @date  2024-11-26 15:17:01
 */
@Repository
public interface ChannelGroupMapper extends BaseMapper<ChannelGroup> {

    @Delete("TRUNCATE TABLE CHANNEL_GROUP")
    void truncate();
}




