package com.yinshu.mcu.dao;

import java.util.List;
import java.util.Map;

import com.yinshu.mcu.entity.Camera;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 描述:
 * author: lushihu
 * date: 2024-12-12 11:41:20
 */
public interface JKCameraMapper extends BaseMapper<Camera> {

	/**
	 * 按条件查询
	 */
	List<String> queryList(@Param("param")Map<String, Object> params);
}


