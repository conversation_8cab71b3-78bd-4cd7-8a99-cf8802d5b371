package com.yinshu.mcu.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.mcu.entity.Devcon;
import com.yinshu.sys.entity.User;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 通道 Mapper
 *
 * <AUTHOR>
 * @date  2024-11-26 15:04:32
 */
@Repository
public interface DevconMapper extends BaseMapper<Devcon> {

	/**
	 * 条件分页查询
	 * @param page
	 * @param params
	 * @return
	 */
	IPage<Devcon> queryPageList(IPage<User> page, @Param("param") Devcon entity);

	/**
	 * 重制表
	 */
	@Delete("truncate table DEVCON")
	void truncate();
}




