<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yinshu.tocc</groupId>
        <artifactId>project</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <groupId>mcu</groupId>
    <artifactId>xinzhou-mcu</artifactId>
    <version>0.1.0</version>
    <packaging>jar</packaging>

    <name>yinshu-aps</name>
    <url>http://maven.apache.org</url>
    <description>该模块引入了 aps(指挥调度) 中的部分功能。</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>3.8.1</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>

        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>1.6.1</version>
        </dependency>

        <dependency>
            <groupId>com.yinshu</groupId>
            <artifactId>yinshu-common</artifactId>
            <version>1.0</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.yinshu</groupId>
            <artifactId>yinshu-sys</artifactId>
            <version>1.0</version>
            <scope>compile</scope>
        </dependency>

        <!--海康威视sdk依赖-->
        <!-- todo 如果遇到无法引入的情况，需要执行本地安装命令，并将Dfile地址换成你的本地地址,如果没有mvn命令，则需要先添加maven环境变量到系统中

        mac版-maven环境变量添加（如果mvn命令没有找到）:
        1、编辑
        vim ~/.bash_profile
        2、配置
        export MAVEN_HOME=/Users/<USER>/.m2/wrapper/dists/apache-maven-3.9.9-bin/4nf9hui3q3djbarqar9g711ggc/apache-maven-3.9.9
        export PATH=$MAVEN_HOME/bin:$PATH
        3、生效
        source ~/.bash_profile

        安装jar到本地仓库命令:
        mvn install:install-file -Dfile=/Users/<USER>/IdeaProjects/company/yingshu/zhjt/tocc-admin-boot/yinshu-mcu/libs/artemis-http-client-1.1.13.RELEASE.jar -DgroupId=com.hikvision.ga -DartifactId=artemis-http-client -Dversion=1.1.13.RELEASE -Dpackaging=jar
        -->
        <dependency>
            <groupId>com.hikvision.ga</groupId>
            <artifactId>artemis-http-client</artifactId>
            <version>1.1.13.RELEASE</version>
        </dependency>
    </dependencies>
</project>
