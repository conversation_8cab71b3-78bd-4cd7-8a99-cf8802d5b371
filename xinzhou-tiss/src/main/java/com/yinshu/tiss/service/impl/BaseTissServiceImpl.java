package com.yinshu.tiss.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.tiss.common.RemoteUrlConstants;
import com.yinshu.tiss.common.RequestFilterBuilder;
import com.yinshu.tiss.service.BaseTissService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class BaseTissServiceImpl implements BaseTissService {

    @Resource
    DvisualHttpTemplate template;

    @Override
    public JSONObject getCountyData(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.dim_xinzhou_county, query);
        return post;
    }
}
