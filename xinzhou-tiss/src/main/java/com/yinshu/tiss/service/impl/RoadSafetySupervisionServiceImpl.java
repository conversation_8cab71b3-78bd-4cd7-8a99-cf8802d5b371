package com.yinshu.tiss.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.tiss.common.RemoteUrlConstants;
import com.yinshu.tiss.service.RoadSafetySupervisionService;
import io.swagger.v3.core.util.Json;
import lombok.extern.log4j.Log4j;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.yinshu.tiss.common.RequestFilterBuilder;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.yinshu.common.ApiConstant;

@Service
@Slf4j
public class RoadSafetySupervisionServiceImpl implements RoadSafetySupervisionService {
    @Resource
    DvisualHttpTemplate template;

    @Value("${server.port}")
    private int serverPort;

    @Value("${server.servlet.context-path}")
    private String contextPath;


    private final static RestTemplate restTemplate = new RestTemplate();


    /**
     * 获取动态查岗数据
     */
    @Override
    public JSONObject getDynamicJobCheckData(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_road_safe_dynamic_inspection, query);
        JSONArray list = post.getJSONArray("list");

        List<String> companyList = new ArrayList<>();

        for (int i = 0; i < list.size(); i++) {
            JSONObject jsonObject = list.getJSONObject(i);
            companyList.add(jsonObject.getString("company"));
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(companyList), headers);

        String url = "http://localhost:" + serverPort + contextPath + ApiConstant.API_TACP_PREFIX + "/checkDutyRecord/getCountByEnterpriseNames";
        log.info("请求url: " + url);

        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        log.info("返回结果: " + response.getBody());
        if (response.getStatusCode().is2xxSuccessful()) {
            JSONObject jsonObject = JSONObject.parseObject(Objects.requireNonNull(response.getBody()));
            JSONObject jsonObject1 = JSON.parseObject(jsonObject.get("data").toString());

            // 开始替换 inspection 字段
            for (int i = 0; i < list.size(); i++) {
                JSONObject item = list.getJSONObject(i);
                String company = item.getString("company");
                String inspectionValue = jsonObject1.getString(company);
                if (inspectionValue != null) {
                    item.put("inspection", inspectionValue);
                } else {
                    item.put("inspection", "0");
                }
            }

        }

        return post;
    }

    /**
     * 获取报警督办数据
     */
    @Override
    public JSONObject getAlarmSupervisionData(JSONObject query) {
        boolean isMonth = query.containsKey("month");
        String month = "";
        if (isMonth) {
            month = query.get("month").toString();
            query.remove("month");
        }
        RequestFilterBuilder.buildParam(query);
        if (query.containsKey("filter") && isMonth) {
            String filter = query.get("filter").toString();
            YearMonth yearMonth = YearMonth.parse(month);
            LocalDate firstDay = yearMonth.atDay(1);
            LocalDate lastDay = yearMonth.atEndOfMonth();
            query.put("filter", filter + " and alarmtime>= '" + firstDay + "' and  alarmtime<='" + lastDay + "'");
        }
        JSONObject post = template.post(RemoteUrlConstants.ads_road_safe_alarm_supervision, query);
        return post;
    }

    /**
     * 获取安全隐患整改数据
     */
    @Override
    public JSONObject getSafeHazardRectificationData(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_road_safe_hidden_danger_rectification, query);
        return post;
    }

    /**
     * 获取隐患详情数据
     */
    @Override
    public JSONObject getHiddenDangerDetailsData(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_road_safe_hidden_danger_details, query);
        return post;
    }

    /**
     * 获取各区域隐患数量分析数据
     */
    @Override
    public JSONObject getCountyHiddenDangerSumData(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_road_safe_county_hidden_danger_sum, query);
        return post;
    }

    /**
     * 获取各类型平均治理时间数据
     */
    @Override
    public JSONObject getHiddenDangerTypeAvgGovernanceTimeData(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_road_safe_hidden_danger_type_avg_governance_time, query);
        return post;
    }

    /**
     * 获取各等级平均治理时间数据
     */
    @Override
    public JSONObject getHiddenDangerLevelAvgGovernanceTimeData(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_road_safe_hidden_danger_level_avg_governance_time, query);
        return post;
    }

    /**
     * 获取当前隐患治理进度数据
     */
    @Override
    public JSONObject getHazardGovernanceProgressData(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_road_safe_hazard_governance, query);
        return post;
    }

    /**
     * 获取企业安全评估数据
     */
    @Override
    public JSONObject getCompanySecurityAssessmentData(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        String filter = query.getString("filter");
        if (filter == null) {
            filter = "";
        }
        query.put("filter", filter + " order by rank asc");
        JSONObject post = template.post(RemoteUrlConstants.ads_road_safe_company_security_assessment, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject item = list.getJSONObject(i);
            String totalkm = item.getString("total_mileage");
            if (StringUtils.isNotEmpty(totalkm)) {
                item.put("total_mileage", totalkm + "km");
            }
        }
        return post;
    }

    /**
     * 获取企业综合评分走势日数据
     */
    @Override
    public JSONObject getCompanySafetyScoresDayData(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        if (query.containsKey("filter")) {
            String filter = query.get("filter").toString();
            filter = filter + " order by dt";
            query.put("filter", filter);
        }
        JSONObject post = template.post(RemoteUrlConstants.ads_road_safe_company_safety_scores_day, query);
        return post;
    }

    /**
     * 获取企业报警构成情况月数据
     */
    @Override
    public JSONObject getCompanyAlarmCompositionMonthData(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_road_safety_company_alarm_composition_month, query);
        return post;
    }

    /**
     * 获取企业车辆风险排行月数据
     */
    @Override
    public JSONObject getCarRiskRankMonthData(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_road_safety_car_risk_rank_month, query);
        return post;
    }

    /**
     * 获取车辆安全评估数据
     */
    @Override
    public JSONObject getCarSafetyEvaluationData(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_road_safe_car_safety_evaluation, query);
        return post;
    }

    /**
     * 获取车辆出车日历数据
     */
    @Override
    public JSONObject getCarDepartureCalendarData(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_road_safe_car_departure_calendar, query);
        return post;
    }

    /**
     * 获取车辆运营信息数据
     */
    @Override
    public JSONObject getCarOperationInfoData(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_road_safe_car_operation_info, query);
        return post;
    }

    /**
     * 获取百公里报警趋势数据
     */
    @Override
    public JSONObject getCarPer100kmAlarmSumData(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        if (query.containsKey("filter")) {
            String filter = query.get("filter").toString();
            filter = filter + " order by dt";
            query.put("filter", filter);
        }
        JSONObject post = template.post(RemoteUrlConstants.ads_road_safe_car_per_100km_alarm_sum, query);
        return post;
    }

    /**
     * 获取车辆报警构成情况数据
     */
    @Override
    public JSONObject getCarAlarmCompositionData(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_road_safe_car_alarm_composition, query);
        return post;
    }

    @Override
    public JSONObject getAlarmSupervisionTableData(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        if (query.containsKey("filter")) {
            String filter = query.get("filter").toString();
            filter = filter + " order by process_status ASC";
            query.put("filter", filter);
        }
        JSONObject post = template.post(RemoteUrlConstants.ads_road_safe_alarm_supervision_table, query);
        JSONArray list = post.getJSONArray("list");
        // 创建适合解析 "yyyy-MM-dd HH:mm:ss" 格式的 DateTimeFormatter
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        for (int i = 0; i < list.size(); i++) {
            JSONObject jsonObject = list.getJSONObject(i);
            String alarmtime = jsonObject.getString("alarmtime");
            // 计算等待时间，格式为 xx分钟
            if (StringUtils.isNotEmpty(alarmtime)) {
                // 使用自定义的 formatter 来解析日期时间字符串
                LocalDateTime localDateTime = LocalDateTime.parse(alarmtime, formatter);
                long between = ChronoUnit.MINUTES.between(localDateTime, LocalDateTime.now());
                jsonObject.put("wait_time", between + "分钟");
            }
        }
        return post;
    }

}
