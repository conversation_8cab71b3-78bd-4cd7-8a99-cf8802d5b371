package com.yinshu.tiss.service;

import com.alibaba.fastjson.JSONObject;

/**
 * 道路安全监管监督
 */
public interface RoadSafetySupervisionService {
    /**
     * 获取动态查岗数据
     */
    JSONObject getDynamicJobCheckData(JSONObject query);

    /**
     * 获取报警督办数据
     */
    JSONObject getAlarmSupervisionData(JSONObject query);

    /**
     * 获取安全隐患整改数据
     */
    JSONObject getSafeHazardRectificationData(JSONObject query);

    /**
     * 获取隐患详情数据
     */
    JSONObject getHiddenDangerDetailsData(JSONObject query);

    /**
     * 获取各区域隐患数量分析数据
     */
    JSONObject getCountyHiddenDangerSumData(JSONObject query);

    /**
     * 获取各类型平均治理时间数据
     */
    JSONObject getHiddenDangerTypeAvgGovernanceTimeData(JSONObject query);

    /**
     * 获取各等级平均治理时间数据
     */
    JSONObject getHiddenDangerLevelAvgGovernanceTimeData(JSONObject query);

    /**
     * 获取当前隐患治理进度数据
     */
    JSONObject getHazardGovernanceProgressData(JSONObject query);

    /**
     * 获取企业安全评估数据
     */
    JSONObject getCompanySecurityAssessmentData(JSONObject query);

    /**
     * 获取企业综合评分走势日数据
     */
    JSONObject getCompanySafetyScoresDayData(JSONObject query);

    /**
     * 获取企业报警构成情况月数据
     */
    JSONObject getCompanyAlarmCompositionMonthData(JSONObject query);

    /**
     * 获取企业车辆风险排行月数据
     */
    JSONObject getCarRiskRankMonthData(JSONObject query);

    /**
     * 获取车辆安全评估数据
     */
    JSONObject getCarSafetyEvaluationData(JSONObject query);

    /**
     * 获取车辆出车日历数据
     */
    JSONObject getCarDepartureCalendarData(JSONObject query);

    /**
     * 获取车辆运营信息数据
     */
    JSONObject getCarOperationInfoData(JSONObject query);

    /**
     * 获取百公里报警趋势数据
     */
    JSONObject getCarPer100kmAlarmSumData(JSONObject query);

    /**
     * 获取车辆报警构成情况数据
     */
    JSONObject getCarAlarmCompositionData(JSONObject query);

    JSONObject getAlarmSupervisionTableData(JSONObject query);
}