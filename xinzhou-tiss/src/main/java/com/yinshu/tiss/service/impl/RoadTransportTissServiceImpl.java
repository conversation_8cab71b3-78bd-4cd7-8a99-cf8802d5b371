package com.yinshu.tiss.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.tiss.common.RemoteUrlConstants;
import com.yinshu.tiss.common.RequestFilterBuilder;
import com.yinshu.tiss.service.RoadTransportTissService;
import com.yinshu.tiss.vo.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 道路运输监管
 */
@Service
public class RoadTransportTissServiceImpl implements RoadTransportTissService {
    @Resource
    DvisualHttpTemplate template;

    /**
     * 道路运输监管行车监控车辆基础信息及最新定位
     * @param query
     * @return
     */
    @Override
    public JSONObject carInfoAndNewLocation(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_road_control_car_info_and_newlocation, query);
        return post;
    }
    /**
     * 道路运输视频巡查相关视频地址
     * @param query
     * @return
     */
    @Override
    public JSONObject vedioInfo(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_road_control_veido_info, query);
        return post;
    }

    /**
     * 道路运输监管报警运行台账按企业
     * @param query
     * @return
     */
    @Override
    public JSONObject warnInfoCompany(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_road_control_warn_info_company, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject item = list.getJSONObject(i);
            String totalkm = item.getString("totalkm");
            if (StringUtils.isNotEmpty(totalkm)) {
                item.put("totalkm", totalkm + "km");
            }
            String kmavgday = item.getString("kmavgday");
            if (StringUtils.isNotEmpty(kmavgday)) {
                item.put("kmavgday", kmavgday + "km");
            }
        }
        return post;
    }

    /**
     * 道路运输监管运行台账按机构
     * @param query
     * @return
     */
    @Override
    public JSONObject runingInfoCon(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_road_control_runing_info_con, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject item = list.getJSONObject(i);
            String totalkm = item.getString("totalkm");
            if (StringUtils.isNotEmpty(totalkm)) {
                item.put("totalkm", totalkm + "km");
            }
            String kmavgday = item.getString("kmavgday");
            if (StringUtils.isNotEmpty(kmavgday)) {
                item.put("kmavgday", kmavgday + "km");
            }
        }

        return post;
    }

    /**
     * 道路运输监管报警运行台账按车辆
     * @param query
     * @return
     */
    @Override
    public JSONObject warnInfoCar(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        String filter = query.getString("filter");
        if (StringUtils.isNotEmpty(filter) && filter.contains("carid=")) {
            filter = filter.replace("carid=", "carid like ");
        }
        query.put("filter", filter);
        JSONObject post = template.post(RemoteUrlConstants.ads_road_control_warn_info_car, query);
        JSONArray list = post.getJSONArray("list");
        // 开始替换 inspection 字段
        for (int i = 0; i < list.size(); i++) {
            JSONObject item = list.getJSONObject(i);
            String totalkm = item.get("totalkm").toString();
            if (StringUtils.isNotEmpty(totalkm)){
                item.put("totalkm", totalkm + "km");
            }
            String kmavgday = item.get("kmavgday").toString();
            if (StringUtils.isNotEmpty(kmavgday)){
                item.put("kmavgday", kmavgday + "km");
            }
        }
        return post;
    }

    /**
     * 报警台账车辆明细表
     * @param query
     * @return
     */
    @Override
    public JSONObject infoCarControl(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        String filter = query.getString("filter");
        if (StringUtils.isNotEmpty(filter) && filter.contains("carid=")) {
            filter = filter.replace("carid=", "carid like ");
        }
        query.put("filter", filter);
        JSONObject post = template.post(RemoteUrlConstants.ads_warn_info_car_control, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject item = list.getJSONObject(i);

            String warnvStr = item.getString("warnv");
            if (StringUtils.isNotEmpty(warnvStr)) {
                if (warnvStr.endsWith(".0")) {
                    warnvStr = warnvStr.substring(0, warnvStr.length() - 2);
                }
                item.put("warnv", warnvStr + "km/h");
            }

            String continuetimeStr = item.getString("continuetime");
            if (StringUtils.isNotEmpty(continuetimeStr)) {
                if (continuetimeStr.endsWith(".0")) {
                    continuetimeStr = continuetimeStr.substring(0, continuetimeStr.length() - 2);
                }
                item.put("continuetime", continuetimeStr + "s");
            }
        }

        return post;
    }
}
