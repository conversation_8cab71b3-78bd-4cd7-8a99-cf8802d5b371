package com.yinshu.tiss.service;


import com.alibaba.fastjson.JSONObject;

/**
 * 综合统计分析
 */
public interface StatisticAnalysisTissService {

    /**
     * 综合统计分析报警事件详情
     * @param query
     * @return
     */
    JSONObject alarmAnalysis(JSONObject query);
    /**
     *  综合统计分析机构统计-安全数据按季度
     * @param query
     * @return
     */
    JSONObject quarterSafetyStats(JSONObject query);
    /**
     * 综合统计分析机构统计-安全数据按年
     * @param query
     * @return
     */
    JSONObject yearSafetyStats(JSONObject query);
    /**
     * 综合统计分析机构统计-安全数据按日
     * @param query
     * @return
     */
    JSONObject dailySafetyStats(JSONObject query);
    /**
     * 综合统计分析机构统计-安全数据按月
     * @param query
     * @return
     */
    JSONObject monthSafetyStats(JSONObject query);
    /**
     * 综合统计分析机构统计-营运数据按季
     * @param query
     * @return
     */
    JSONObject quarterOperationStats(JSONObject query);
    /**
     * 综合统计分析机构统计-营运数据按年
     * @param query
     * @return
     */
    JSONObject yearOperationStats(JSONObject query);
    /**
     * 综合统计分析机构统计-营运数据按日
     * @param query
     * @return
     */
    JSONObject dailyOperationStats(JSONObject query);
    /**
     *综合统计分析机构统计-营运数据按月
     * @param query
     * @return
     */
    JSONObject monthOperationStats(JSONObject query);

    JSONObject safetyStatics(JSONObject query);

    JSONObject operateStatics(JSONObject query);
}
