package com.yinshu.tiss.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.tact.entity.ScoreConfig;
import com.yinshu.tact.entity.StrategyConfig;
import com.yinshu.tact.service.ScoreConfigService;
import com.yinshu.tact.service.StrategyConfigService;
import com.yinshu.tiss.common.RemoteUrlConstants;
import com.yinshu.tiss.common.RequestFilterBuilder;
import com.yinshu.tact.enums.IndicatorEnum;
import com.yinshu.tiss.service.SupervisionAssessmentService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 监督考核管理
 */
@Service
public class SupervisionAssessmentServiceImpl implements SupervisionAssessmentService {
    @Resource
    DvisualHttpTemplate template;
    @Resource
    ScoreConfigService scoreConfigService;
    @Resource
    StrategyConfigService strategyConfigService;

    /**
     * 报警处理率统计-按月
     * @param query
     * @return
     */
    @Override
    public JSONObject rateStatisticsMth(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        String filter = query.getString("filter");
        if (StringUtils.isNotEmpty(filter)){
            String replace = filter.replace("dt", "mth");
            query.put("filter", replace);
        }
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_warn_handle_rate_statistics_mth, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject json = list.getJSONObject(i);
            json.put("warn_handle_rate", json.getString("warn_handle_rate")+ "%");
        }
        return post;
    }
    /**
     * 报警处理率统计-按年
     * @param query
     * @return
     */
    @Override
    public JSONObject rateStatisticsYear(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_warn_handle_rate_statistics_year, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject json = list.getJSONObject(i);
            json.put("warn_handle_rate", json.getString("warn_handle_rate")+ "%");
        }
        return post;
    }
    /**
     * 报警处理率统计-按季
     * @param query
     * @return
     */
    @Override
    public JSONObject rateStatisticsQuarter(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_warn_handle_rate_statistics_quarter, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject json = list.getJSONObject(i);
            json.put("warn_handle_rate", json.getString("warn_handle_rate")+ "%");
        }
        return post;
    }
    /**
     * 报警督办率统计-按月
     * @param query
     * @return
     */
    @Override
    public JSONObject warnSuperviseRateStatisticsMth(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        String filter = query.getString("filter");
        if (StringUtils.isNotEmpty(filter)){
            String replace = filter.replace("dt", "mth");
            query.put("filter", replace);
        }
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_warn_supervise_rate_statistics_mth, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject json = list.getJSONObject(i);
            json.put("warn_supervise_rate", json.getString("warn_supervise_rate")+ "%");
            json.put("supervise_over_rate", json.getString("supervise_over_rate")+ "%");
        }
        return post;
    }
    /**
     * 报警督办率统计-按年
     * @param query
     * @return
     */
    @Override
    public JSONObject warnSuperviseRateStatisticsYear(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_warn_supervise_rate_statistics_year, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject json = list.getJSONObject(i);
            json.put("warn_supervise_rate", json.getString("warn_supervise_rate")+ "%");
            json.put("supervise_over_rate", json.getString("supervise_over_rate")+ "%");
        }
        return post;
    }
    /**
     * 报警督办率统计-按季
     * @param query
     * @return
     */
    @Override
    public JSONObject warnSuperviseRateStatisticsQuarter(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_warn_supervise_rate_statistics_quarter, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject json = list.getJSONObject(i);
            json.put("warn_supervise_rate", json.getString("warn_supervise_rate")+ "%");
            json.put("supervise_over_rate", json.getString("supervise_over_rate")+ "%");
        }
        return post;
    }
    /**
     * 超速驾驶统计-按月
     * @param query
     * @return
     */
    @Override
    public JSONObject speedstatisticsmth(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        String filter = query.getString("filter");
        if (StringUtils.isNotBlank(filter)) {
            filter = filter.replace("operator = null and","");
            query.put("filter", filter.trim());
        }
        if (StringUtils.isNotEmpty(filter)){
            String replace = filter.replace("dt", "mth");
            query.put("filter", replace);
        }

        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_over_speed_statistics_mth, query);
        return post;
    }
    /**
     * 超速驾驶统计-按年
     * @param query
     * @return
     */
    @Override
    public JSONObject speedstatisticsyear(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        String filter = query.getString("filter");
        filter = filter.replace("operator = null and","");

        query.put("filter", filter.trim());
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_over_speed_statistics_year, query);
        return post;
    }
    /**
     * 超速驾驶统计-按季
     * @param query
     * @return
     */
    @Override
    public JSONObject speedStatisticsQuarter(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        String filter = query.getString("filter");
        filter = filter.replace("operator = null and","");

        query.put("filter", filter.trim());
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_over_speed_statistics_quarter, query);
        return post;
    }
    /**
     * 疲劳驾驶统计-按月
     * @param query
     * @return
     */
    @Override
    public JSONObject drivingStatisticsMth(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        String filter = query.getString("filter");
        if (StringUtils.isNotEmpty(filter)){
            String replace = filter.replace("dt", "mth");
            query.put("filter", replace);
        }
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_fatigue_driving_statistics_mth, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject json = list.getJSONObject(i);
            json.put("fatigue_driving_avg", json.getString("fatigue_driving_avg")+ "小时");
            json.put("fatigue_driving_time",json.getString("fatigue_driving_time") + "小时");
        }
        return post;
    }
    /**
     * 疲劳驾驶统计-按年
     * @param query
     * @return
     */
    @Override
    public JSONObject drivingStatisticsYear(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_fatigue_driving_statistics_year, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject json = list.getJSONObject(i);
            json.put("fatigue_driving_avg", json.getString("fatigue_driving_avg")+ "小时");
            json.put("fatigue_driving_time",json.getString("fatigue_driving_time") + "小时");
        }
        return post;
    }
    /**
     * 疲劳驾驶统计-按季
     * @param query
     * @return
     */
    @Override
    public JSONObject drivingStatisticsQuarter(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_fatigue_driving_statistics_quarter, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject json = list.getJSONObject(i);
            json.put("fatigue_driving_avg", json.getString("fatigue_driving_avg")+ "小时");
            json.put("fatigue_driving_time",json.getString("fatigue_driving_time") + "小时");
        }
        return post;
    }
    /**
     * 平台联通率统计-按月
     * @param query
     * @return
     */
    @Override
    public JSONObject platformUnicomRateStatisticsMth(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        String filter = query.getString("filter");
        if (StringUtils.isNotEmpty(filter)){
            String replace = filter.replace("dt", "mth");
            query.put("filter", replace);
        }
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_platform_unicom_rate_statistics_mth, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject json = list.getJSONObject(i);
            json.put("unicom_rate", json.getString("unicom_rate")+ "%");
            json.put("real_unicom_time", json.getString("real_unicom_time")+ "小时");
            json.put("should_unicom_time", json.getString("should_unicom_time")+ "小时");
        }
        return post;
    }
    /**
     * 平台联通率统计-按年
     * @param query
     * @return
     */
    @Override
    public JSONObject platformUnicomRateStatisticsYear(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_platform_unicom_rate_statistics_year, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject json = list.getJSONObject(i);
            json.put("unicom_rate", json.getString("unicom_rate")+ "%");
            json.put("real_unicom_time", json.getString("real_unicom_time")+ "小时");
            json.put("should_unicom_time", json.getString("should_unicom_time")+ "小时");
        }
        return post;
    }
    /**
     * 平台联通率统计-按季
     * @param query
     * @return
     */
    @Override
    public JSONObject platformUnicomRateStatisticsQuarter(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_platform_unicom_rate_statistics_quarter, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject json = list.getJSONObject(i);
            json.put("unicom_rate", json.getString("unicom_rate")+ "%");
            json.put("real_unicom_time", json.getString("real_unicom_time")+ "小时");
            json.put("should_unicom_time", json.getString("should_unicom_time")+ "小时");
        }
        return post;
    }
    /**
     * 卫星定位漂移率统计-按月
     * @param query
     * @return
     */
    @Override
    public JSONObject gpsDriftRateStatisticsMth(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        String filter = query.getString("filter");
        if (StringUtils.isNotEmpty(filter)){
            String replace = filter.replace("dt", "mth");
            query.put("filter", replace);
        }
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_gps_drift_rate_statistics_mth, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject json = list.getJSONObject(i);
            json.put("gps_drift_rate", json.getString("gps_drift_rate")+ "%");
        }
        return post;
    }
    /**
     * 卫星定位漂移率统计-按年
     * @param query
     * @return
     */
    @Override
    public JSONObject gpsDriftRateStatisticsYear(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_gps_drift_rate_statistics_year, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject json = list.getJSONObject(i);
            json.put("gps_drift_rate", json.getString("gps_drift_rate")+ "%");
        }
        return post;
    }
    /**
     * 卫星定位漂移率统计-按季
     * @param query
     * @return
     */
    @Override
    public JSONObject gpsDriftRateStatisticsQuarter(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_gps_drift_rate_statistics_quarter, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject json = list.getJSONObject(i);
            json.put("gps_drift_rate", json.getString("gps_drift_rate")+ "%");
        }
        return post;
    }
    /**
     * 数据合格率统计-按月
     * @param query
     * @return
     */
    @Override
    public JSONObject qualifiedRateStatisticsMth(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        String filter = query.getString("filter");
        if (StringUtils.isNotEmpty(filter)){
            String replace = filter.replace("dt", "mth");
            query.put("filter", replace);
        }
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_data_qualified_rate_statistics_mth, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject json = list.getJSONObject(i);
            json.put("qualified_rate", json.getString("qualified_rate")+ "%");
        }
        return post;
    }
    /**
     * 数据合格率统计-按年
     * @param query
     * @return
     */
    @Override
    public JSONObject qualifiedRateStatisticsYear(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_data_qualified_rate_statistics_year, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject json = list.getJSONObject(i);
            json.put("qualified_rate", json.getString("qualified_rate")+ "%");
        }
        return post;
    }
    /**
     * 数据合格率统计-按季
     * @param query
     * @return
     */
    @Override
    public JSONObject qualifiedRateStatisticsQuarter(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_data_qualified_rate_statistics_quarter, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject json = list.getJSONObject(i);
            json.put("qualified_rate", json.getString("qualified_rate")+ "%");
        }
        return post;
    }
    /**
     * 轨迹完整率统计-按月
     * @param query
     * @return
     */
    @Override
    public JSONObject completenessRateStatisticsMth(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        String filter = query.getString("filter");
        if (StringUtils.isNotEmpty(filter)){
            String replace = filter.replace("dt", "mth");
            query.put("filter", replace);
        }
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_trajectory_completeness_rate_statistics_mth, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject json = list.getJSONObject(i);
            json.put("operations_mileage", json.getString("operations_mileage")+"km");
            json.put("trajectory_rate", json.getString("trajectory_rate")+ "%");
        }
        return post;
    }
    /**
     * 轨迹完整率统计-按年
     * @param query
     * @return
     */
    @Override
    public JSONObject completenessRateStatisticsYear(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);

        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_trajectory_completeness_rate_statistics_year, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject json = list.getJSONObject(i);
            json.put("operations_mileage", json.getString("operations_mileage")+"km");
            json.put("trajectory_rate", json.getString("trajectory_rate")+ "%");
        }
        return post;
    }
    /**
     * 轨迹完整率统计-按季
     * @param query
     * @return
     */
    @Override
    public JSONObject completenessRateStatisticsQuarter(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_trajectory_completeness_rate_statistics_quarter, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject json = list.getJSONObject(i);
            json.put("operations_mileage", json.getString("operations_mileage")+"km");
            json.put("trajectory_rate", json.getString("trajectory_rate")+ "%");
        }
        return post;
    }
    /**
     * 监控考核-按月
     *
     * 车辆离线时长 car_offline_time
     * 平台连通率 unicom_rate
     * 车辆入网率 car_network_access_rate
     * 车辆上线率 car_online_rate
     * 轨迹完整率  trajectory_rate
     * 数据合格率  qualified_rate
     * 卫星定位漂移率 gps_drift_rate
     * 平台查岗响应率  platform_check_responsivity
     * 平均疲劳驾驶时长  fatigue_driving_avg
     *  禁行时段行驶次数 prohibition_time_num
     *  平均超速次数 over_speed_avg
     * @param query
     * @return
     */
    @Override
    public JSONObject monitorExamineMth(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        String filter = query.getString("filter");
        if (StringUtils.isNotEmpty(filter)) {
            String replace = filter.replace("dt", "mth");
            query.put("filter", replace);
        }
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_monitor_examine_mth , query);
        if (StringUtils.isNotEmpty(query.getString("navType"))){
            post = filterPost(query, post);
        }


        return post;
    }
    /**
     * 监控考核-按年
     * @param query
     * @return
     */
    @Override
    public JSONObject monitorExamineYear(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        //query = filterByParam(query);
        String filter = query.getString("filter");
        if (StringUtils.isNotEmpty(filter) &&filter.contains("navType='org'")){
            filter = filter.replace("navType='org'"," operator = null and  enterprise = null");
        }
        // 运营商
        if (StringUtils.isNotEmpty(filter) &&filter.contains("navType='operator'")){
            filter = filter.replace("navType='operator'"," operator != null");
        }
        // 企业
        if (StringUtils.isNotEmpty(filter) &&filter.contains("navType='enterprise'")){
            filter = filter.replace("navType='enterprise'"," enterprise != null");
        }

        query.put("filter",filter);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_monitor_examine_year, query);
        if (StringUtils.isNotEmpty(query.getString("navType"))){
            post = filterPost(query, post);
        }
        return post;
    }
    /**
     * 监控考核-按季
     * @param query
     * @return
     */
    @Override
    public JSONObject monitorExamineQuarter(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_monitor_examine_quarter, query);
        if (StringUtils.isNotEmpty(query.getString("navType"))){
            post = filterPost(query, post);
        }
        return post;
    }
    /**
     * 监控统计详情-按年
     * @param query
     * @return
     */
    @Override
    public JSONObject monitorStatisticsDetailsYear(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        String filter = query.getString("filter");
        if (filter.contains("driver=")){
            filter = filter.replace("driver=", "driver like ");
        }

        query.put("filter", filter);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_monitor_statistics_details_year, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject json = list.getJSONObject(i);
            json.put("trajectory_rate", json.getString("trajectory_rate")+"%");
            json.put("qualified_rate", json.getString("qualified_rate")+ "%");
            json.put("gps_drift_rate", json.getString("gps_drift_rate")+ "%");
            json.put("fatigue_driving_avg", json.getString("fatigue_driving_avg")+ "小时");
            json.put("warn_supervise_rate", json.getString("warn_supervise_rate")+ "%");
            json.put("supervise_over_rate", json.getString("supervise_over_rate")+ "%");
            json.put("warn_handle_rate", json.getString("warn_handle_rate")+ "%");
        }
        return post;
    }
    /**
     * 监控统计详情-按季
     * @param query
     * @return
     */
    @Override
    public JSONObject monitorStatisticsDetailsQuarter(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        String filter = query.getString("filter");
        if (filter.contains("driver=")){
            filter = filter.replace("driver=", "driver like ");
        }
        if (filter.contains("tabName=")){
            filter = filter.replaceAll("tabName='[^']*'\\s*(and\\s+)?", "").trim();
        }
        query.put("filter", filter);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_monitor_statistics_details_quarter, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject json = list.getJSONObject(i);
            json.put("trajectory_rate", json.getString("trajectory_rate")+"%");
            json.put("qualified_rate", json.getString("qualified_rate")+ "%");
            json.put("gps_drift_rate", json.getString("gps_drift_rate")+ "%");
            json.put("fatigue_driving_avg", json.getString("fatigue_driving_avg")+ "小时");
            json.put("warn_supervise_rate", json.getString("warn_supervise_rate")+ "%");
            json.put("supervise_over_rate", json.getString("supervise_over_rate")+ "%");
            json.put("warn_handle_rate", json.getString("warn_handle_rate")+ "%");
        }
        return post;
    }
    /**
     * 监控统计详情-按月
     * @param query
     * @return
     */
    @Override
    public JSONObject monitorStatisticsDetailsMth(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        String filter = query.getString("filter");
        if (filter.contains("driver=")){
            filter = filter.replace("driver=", "driver like ");
        }
        if (filter.contains("vehicle_num=")) {
            filter = filter.replace("vehicle_num=", "vehicle_num like ");
        }
        if (filter.contains("tabName=")){
            filter = filter.replaceAll("tabName='[^']*'\\s*(and\\s+)?", "").trim();
        }
        if (StringUtils.isNotEmpty(filter)){
            filter = filter.replace("dt", "mth");
        }
        query.put("filter", filter);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_monitor_statistics_details_mth, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject json = list.getJSONObject(i);
            json.put("trajectory_rate", json.getString("trajectory_rate")+"%");
            json.put("qualified_rate", json.getString("qualified_rate")+ "%");
            json.put("gps_drift_rate", json.getString("gps_drift_rate")+ "%");
            json.put("fatigue_driving_avg", json.getString("fatigue_driving_avg")+ "小时");
            json.put("warn_supervise_rate", json.getString("warn_supervise_rate")+ "%");
            json.put("supervise_over_rate", json.getString("supervise_over_rate")+ "%");
            json.put("warn_handle_rate", json.getString("warn_handle_rate")+ "%");
        }
        return post;
    }
    /**
     * 离线车辆统计
     * @param query
     * @return
     */
    @Override
    public JSONObject offlineStatistics(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        String filter = query.getString("filter");
        if (StringUtils.isNotEmpty(filter)){
            String replace = filter.replace("=", " like ");
            query.put("filter", replace);
        }
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_offline_statistics, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject json = list.getJSONObject(i);
            json.put("offline_time", json.getString("offline_time")+"小时");
        }
        return post;
    }

    private static JSONObject filterByParam(JSONObject query) {
        String filter = query.getString("filter");
        if (StringUtils.isEmpty(filter)){
            return query;
        }
        filter = removeAnd(filter);
        // 按机构
        if (filter.contains("navType='org'")){
            filter = filter.replace("navType='org'"," operator = null and  enterprise = null");
        }
        // 运营商
        if (filter.contains("navType='operator'")){
            filter = filter.replace("navType='operator'"," operator != null");
        }
        // 企业
        if (filter.contains("navType='enterprise'")){
            filter = filter.replace("navType='enterprise'"," enterprise != null");
        }

        query.put("filter",filter);
        return query;
    }

    private static @NotNull String removeAnd(String filter) {
        // 再处理"and"
        if (filter.endsWith(" and")) {
            filter =  filter.substring(0, filter.length() - 4);
        } else if (filter.endsWith("and")) {
            filter =  filter.substring(0, filter.length() - 3);
        }else if (filter.endsWith("and ")) {
            filter =  filter.substring(0, filter.length() - 4);
        }else if (filter.contains("and  and")) {
            filter = filter.replace("and  and"," and ");
        }
        return filter;
    }


    /**
     * 车辆离线时长 car_offline_time
     * 平台连通率 unicom_rate
     * 车辆入网率 car_network_access_rate
     * 车辆上线率 car_online_rate
     * 轨迹完整率  trajectory_rate
     * 数据合格率  qualified_rate
     * 卫星定位漂移率 gps_drift_rate
     * 平台查岗响应率  platform_check_responsivity
     * 平均疲劳驾驶时长  fatigue_driving_avg
     *  禁行时段行驶次数 prohibition_time_num
     *  平均超速次数 over_speed_avg
     * @param query
     * @param post
     * @return
     */
    private  JSONObject filterPost(JSONObject query,JSONObject post){
        DecimalFormat df = new DecimalFormat("0.0"); // 或 "0.0"  , #.0
        String navType = query.getString("navType");
        Integer type = null;
        String navTypeName = "";
        if (navType.equals("org")){
            type = 1;
            navTypeName = "按企业";
        }else if (navType.equals("operator")){
            type = 2;
            navTypeName = "按运营商";
        }else{
            type = 3;
            navTypeName = "按企业";
        }
        // 选项配置
        List<StrategyConfig> strategyConfigs = strategyConfigService.list(new LambdaQueryWrapper<StrategyConfig>().eq(StrategyConfig::getType, type));

        // 分数配置
        List<Integer> idList = strategyConfigs.stream().map(StrategyConfig::getId).collect(Collectors.toList());
        if (idList.size()<=0){
            post.put("list", new JSONArray());
            post.put("total",0);
            return post;
        }
        List<ScoreConfig> scoreConfigs = scoreConfigService.list(new LambdaQueryWrapper<ScoreConfig>().in(ScoreConfig::getIndicatorId, idList));

        // 大数据返回数据
        JSONArray postList = post.getJSONArray("list");

        JSONArray returnList = new JSONArray();

        /**
         * 1.遍历大数据返回数据
         */
        for (int i = 0; i < postList.size(); i++) {
            JSONObject json = postList.getJSONObject(i);
            JSONObject jsonObject = new JSONObject();
            Double totalScore = 0.0;
            /**
             * 2.遍历选项配置
             */
            for (StrategyConfig strategyConfig : strategyConfigs) {
                String name = IndicatorEnum.getDataNameByName(strategyConfig.getIndicator());
                System.out.println("--------------------");
                System.out.println("name = " + name);
                String value = json.getString(name);
                System.out.println("value = " + value);
                /**
                 * 3. 遍历分数配置
                 */
                List<ScoreConfig> scoreConfigList = scoreConfigs.stream().filter(item -> item.getIndicatorId().equals(strategyConfig.getId()) ).collect(Collectors.toList());

                //TissScoreConfig scoreConfig = null;
                for (ScoreConfig tissScoreConfig : scoreConfigList) {
                    System.out.println(isScoreMeetCondition(Double.valueOf(value),tissScoreConfig));
                    boolean scoreMeetCondition = isScoreMeetCondition(Double.valueOf(value), tissScoreConfig);
                    if (scoreMeetCondition) {
                        if (ObjectUtils.isEmpty(strategyConfig.getWeight())){
                            throw new RuntimeException("策略配置" +navTypeName + "，权重信息为空，去前往完善");
                        }
                        // 计算吧保留一位小数
                        Double score = (Double.valueOf(tissScoreConfig.getScore())) *  ((double)strategyConfig.getWeight() / 100);
                        // 四舍五入保留一位小数
                        score = Math.round(score * 10) / 10.0;
                        String scoreString = df.format(score);
                        jsonObject.put(name,scoreString + "（"+value+"）");
                        totalScore += score;
                        break;
                    }else {
                        jsonObject.put(name, "0.0");
                    }

                }


            }


            jsonObject.put("mtn",json.getString("mth"));
            jsonObject.put("id",json.getString("id"));
            jsonObject.put("district",json.getString("district"));
            // 总分保留一位小数
            String totalScoreString = df.format(totalScore);
            jsonObject.put("totalScore",totalScoreString);
            if (navType.equals("operator")){
                jsonObject.put("operator",json.getString("operator"));
            }else if (navType.equals("enterprise")){
                jsonObject.put("enterprise",json.getString("enterprise"));

            }

            returnList.add(jsonObject);


        }
        post.put("list", returnList);
        return post;
    }


    /**
     * 判断分数是否满足规则条件
     * @param score 待判断的分数
     * @param config 规则配置对象
     * @return true表示满足条件，false表示不满足
     */
    public static boolean isScoreMeetCondition(double score, ScoreConfig config) {
        // 提取规则参数
        String leftRule = config.getLeftRule();   // 左规则（如 "lt"）
        double leftValue = config.getLeftRuleValue(); // 左规则值（如 10）
        String rightRule = config.getRightRule(); // 右规则（如 "gt"）
        double rightValue = config.getRightRuleValue(); // 右规则值（如 0）

        // 判断左规则
        boolean leftCondition = false;
        switch (leftRule.toLowerCase()) {
            case "lt": leftCondition = score < leftValue; break;   // 小于
            case "lte": leftCondition = score <= leftValue; break;  // 小于等于
            case "gt": leftCondition = score > leftValue; break;   // 大于
            case "gte": leftCondition = score >= leftValue; break; // 大于等于
            case "eq": leftCondition = score == leftValue; break; // 等于
            default: throw new IllegalArgumentException("不支持的左规则: " + leftRule);
        }

        // 判断右规则
        boolean rightCondition = false;
        switch (rightRule.toLowerCase()) {
            case "lt": rightCondition = score < rightValue; break;
            case "lte": rightCondition = score <= rightValue; break;
            case "gt": rightCondition = score > rightValue; break;
            case "gte": rightCondition = score >= rightValue; break;
            case "eq": rightCondition = score == rightValue; break;
            default: throw new IllegalArgumentException("不支持的右规则: " + rightRule);
        }

        // 综合判断左右规则是否同时满足
        return leftCondition && rightCondition;
    }

}
