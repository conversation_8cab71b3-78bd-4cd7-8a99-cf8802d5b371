package com.yinshu.tiss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yinshu.tiss.entity.RegularCheckRuleDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 定期自动查岗规则表;(regular_check_rule)表服务接口
 * <AUTHOR> http://www.chiner.pro
 * @date : 2025-7-15
 */
public interface RegularCheckRuleService extends IService<RegularCheckRuleDTO>{

    /**
     * 查询列表
     *
     * @param regularCheckRuleDto 筛选条件
     * @return
     */
    Page<RegularCheckRuleDTO> pageQuery(RegularCheckRuleDTO regularCheckRuleDto);
    /**
     * 新增数据
     *
     * @param regularCheckRuleDto 实例对象
     * @return 实例对象
     */
    boolean insert(RegularCheckRuleDTO regularCheckRuleDto);
    /**
     * 更新数据
     *
     * @param regularCheckRuleDto 实例对象
     * @return 实例对象
     */
    boolean update(RegularCheckRuleDTO regularCheckRuleDto);
    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(String id);
    /**
     * 通过主键集合删除数据
     *
     * @param ids 主键
     * @return 是否成功
     */
    boolean deleteByIds(String[] ids);
}
