package com.yinshu.tiss.service;

import com.alibaba.fastjson.JSONObject;

/**
 * 道路运输监管
 */
public interface RoadTransportTissService {
    /**
     * 道路运输监管行车监控车辆基础信息及最新定位
     * @param query
     * @return
     */
    JSONObject carInfoAndNewLocation(JSONObject query);

    /**
     * 道路运输视频巡查相关视频地址
     * @param query
     * @return
     */
    JSONObject vedioInfo(JSONObject query);

    /**
     * 道路运输监管报警运行台账按企业
     * @param query
     * @return
     */
    JSONObject warnInfoCompany(JSONObject query);

    /**
     * 道路运输监管运行台账按机构
     * @param query
     * @return
     */
    JSONObject runingInfoCon(JSONObject query);

    /**
     * 道路运输监管报警运行台账按车辆
     * @param query
     * @return
     */
    JSONObject warnInfoCar(JSONObject query);

    /**
     * 报警台账车辆明细表
     * @param query
     * @return
     */
    JSONObject infoCarControl(JSONObject query);
}
