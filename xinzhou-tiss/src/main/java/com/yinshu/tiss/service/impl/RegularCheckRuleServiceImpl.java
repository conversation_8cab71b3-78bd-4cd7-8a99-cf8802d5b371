package com.yinshu.tiss.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.tiss.dao.RegularCheckRuleMapper;
import com.yinshu.tiss.entity.RegularCheckRuleDTO;
import com.yinshu.tiss.service.RegularCheckRuleService;
import org.springframework.stereotype.Service;

import java.util.Arrays;

/**
 * 定期自动查岗规则表;(regular_check_rule)表服务实现类
 *
 * <AUTHOR> http://www.chiner.pro
 * @date : 2025-7-15
 */
@Service
public class RegularCheckRuleServiceImpl extends ServiceImpl<RegularCheckRuleMapper, RegularCheckRuleDTO> implements RegularCheckRuleService {

    /**
     * 分页查询
     *
     * @param regularCheckRuleDto 筛选条件
     * @return 分页结果
     */
    public Page<RegularCheckRuleDTO> pageQuery(RegularCheckRuleDTO regularCheckRuleDto) {
        // 构建查询条件
        LambdaQueryWrapper<RegularCheckRuleDTO> queryWrapper = new LambdaQueryWrapper<>();

        // 如果有具体的查询条件，可以在这里添加
        if (StrUtil.isNotBlank(regularCheckRuleDto.getEnterpriseName())) {
            queryWrapper.like(RegularCheckRuleDTO::getEnterpriseName, regularCheckRuleDto.getEnterpriseName());
        }

        // 创建分页对象
        Page<RegularCheckRuleDTO> page = new Page<>(regularCheckRuleDto.getPageNum(), regularCheckRuleDto.getPageSize());

        // 执行分页查询
        return this.page(page, queryWrapper);
    }

    /**
     * 新增数据
     *
     * @param regularCheckRuleDto 实例对象
     * @return 是否成功
     */
    @Override
    public boolean insert(RegularCheckRuleDTO regularCheckRuleDto) {
        return this.save(regularCheckRuleDto);
    }

    /**
     * 更新数据
     *
     * @param regularCheckRuleDto 实例对象
     * @return 是否成功
     */
    public boolean update(RegularCheckRuleDTO regularCheckRuleDto) {
        return this.updateById(regularCheckRuleDto);
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(String id) {
        return this.removeById(id);
    }

    /**
     * 批量删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteByIds(String[] id) {
        return this.removeBatchByIds(Arrays.asList(id));
    }
}
