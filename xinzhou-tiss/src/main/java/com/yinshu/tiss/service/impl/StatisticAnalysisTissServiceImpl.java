package com.yinshu.tiss.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.tiss.common.RemoteUrlConstants;
import com.yinshu.tiss.common.RequestFilterBuilder;
import com.yinshu.tiss.service.StatisticAnalysisTissService;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * 综合统计分析
 */
@Service
public class StatisticAnalysisTissServiceImpl implements StatisticAnalysisTissService {
    @Resource
    DvisualHttpTemplate template;

    /**
     *综合统计分析报警事件详情
     * @param query
     * @return
     */
    @Override
    public JSONObject alarmAnalysis(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        String filter = query.getString("filter");
        if (StringUtils.isNotEmpty(filter) && filter.contains("dt")) {
            filter = filter.replace("dt", "mth");
        }
        if (StringUtils.isNotEmpty(filter) && filter.contains("driver_name=")) {
            filter = filter.replace("driver_name=", "f_driver_name like ");
        }
        if (StringUtils.isNotEmpty(filter) && filter.contains("alarm_type=")) {
            filter = filter.replace("alarm_type=", "alarm_type like ");
        }
        filter = removeAnd(filter);
        if (filter.contains("_")){
            filter = filter.replaceAll("\\b(\\d{4})_(\\d{2})\\b", "$1-$2");
        }
        query.put("filter", filter);
        JSONObject post = template.post(RemoteUrlConstants.ads_comprehensive_alarm_analysis, query);
        return post;
    }
    /**
     * 综合统计分析机构统计-安全数据按季度
     * @param query
     * @return
     */
    @Override
    public JSONObject quarterSafetyStats(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        String filter = query.getString("filter");
        //if (filter.contains("_01")){
        //    filter = filter.replaceAll("(quarter\\s*=\\s*'\\d{4}_)(\\d{2})(')", "quarter = '第一季度' and year = '"+ query.getString("year") + "'");
        //}else if (filter.contains("_02")){
        //    filter = filter.replaceAll("(quarter\\s*=\\s*'\\d{4}_)(\\d{2})(')", "quarter = '第二季度' and year = '"+ query.getString("year") + "'");
        //}else if (filter.contains("_03")){
        //    filter = filter.replaceAll("(quarter\\s*=\\s*'\\d{4}_)(\\d{2})(')", "quarter = '第三季度' and year = '"+ query.getString("year") + "'");
        //}else {
        //    filter = filter.replaceAll("(quarter\\s*=\\s*'\\d{4}_)(\\d{2})(')", "quarter = '第四季度' and year = '"+ query.getString("year") + "'");
        //}
        if (filter.contains("_")){
            filter = filter.replaceAll("\\b(\\d{4})_(\\d{2})\\b", "$1-$2");
        }
        query.put("filter", filter);
        JSONObject post = template.post(RemoteUrlConstants.ads_quarter_safety_stats, query);

        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject jsonObject = list.getJSONObject(i);
            jsonObject.put("alarm_rate",calculateData2(jsonObject.getString("alarm_total_num"),jsonObject.getString("alarm_handled_num")));
            jsonObject.put("supervise_rate",calculateData2(jsonObject.getString("alarm_total_num"),jsonObject.getString("supervision")));
            jsonObject.put("supervise_finish_rate",calculateData2(jsonObject.getString("alarm_total_num"),jsonObject.getString("supervision_done")));
        }

        return post;
    }
    /**
     * 综合统计分析机构统计-安全数据按年
     * @param query
     * @return
     */
    @Override
    public JSONObject yearSafetyStats(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_year_safety_stats, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject jsonObject = list.getJSONObject(i);
            jsonObject.put("alarm_rate",calculateData2(jsonObject.getString("alarm_total_num"),jsonObject.getString("alarm_handled_num")));
            jsonObject.put("supervise_rate",calculateData2(jsonObject.getString("alarm_total_num"),jsonObject.getString("supervision")));
            jsonObject.put("supervise_finish_rate",calculateData2(jsonObject.getString("alarm_total_num"),jsonObject.getString("supervision_done")));
        }

        return post;
    }
    /**
     * 综合统计分析机构统计-安全数据按日
     * @param query
     * @return
     */
    @Override
    public JSONObject dailySafetyStats(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        String filter = query.getString("filter");
        if (StringUtils.isEmpty(query.getString("county"))){
            if (StringUtils.isEmpty(filter)){
                filter = "county = null";
            }else {
                filter += "and county = null";
            }

        }
        if (StringUtils.isEmpty(query.getString("company"))){
            if (StringUtils.isEmpty(filter)){
                filter = "company = null";
            }else {
                filter += " and company = null";
            }
        }
        if (StringUtils.isNotEmpty(filter) && filter.contains("dt")) {
            filter = filter.replace("dt", "mth");
        }
        filter += " order by dt asc";
        query.put("filter",filter);
        JSONObject post = template.post(RemoteUrlConstants.ads_daily_safety_stats, query);
        return post;
    }
    /**
     * 综合统计分析机构统计-安全数据按月
     * @param query
     * @return
     */
    @Override
    public JSONObject monthSafetyStats(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        String filter = query.getString("filter");
        // 季度
        //if (filter.contains("_01")){
        //    filter = filter.replaceAll("(quarter\\s*=\\s*'\\d{4}_)(\\d{2})(')", "quarter = '第一季度' and year = '"+ query.getString("year") + "'");
        //}else if (filter.contains("_02")){
        //    filter = filter.replaceAll("(quarter\\s*=\\s*'\\d{4}_)(\\d{2})(')", "quarter = '第二季度' and year = '"+ query.getString("year") + "'");
        //}else if (filter.contains("_03")){
        //    filter = filter.replaceAll("(quarter\\s*=\\s*'\\d{4}_)(\\d{2})(')", "quarter = '第三季度' and year = '"+ query.getString("year") + "'");
        //}else if (filter.contains("_04")){
        //    filter = filter.replaceAll("(quarter\\s*=\\s*'\\d{4}_)(\\d{2})(')", "quarter = '第四季度' and year = '"+ query.getString("year") + "'");
        //}
        if (filter.contains("_")){
            filter = filter.replaceAll("\\b(\\d{4})_(\\d{2})\\b", "$1-$2");
        }
        if ((filter.contains("quarter") && !filter.contains("county"))||
                (filter.contains("year") && !filter.contains("county")) ||
                (StringUtils.isNotEmpty("startTime") && StringUtils.isNotEmpty("endTime") && !filter.contains("county"))){
            filter += " and county = null and company = null";
        } else if ((filter.contains("quarter") && filter.contains("county")) ||
                (filter.contains("year") && filter.contains("county")) ) {
            filter += " and company = null";
        }
        filter += " order by mth asc";
        if (StringUtils.isNotEmpty(filter) && filter.contains("dt")) {
            filter = filter.replace("dt", "mth");
        }
        // 自定义日期处理
        if (StringUtils.isNotEmpty(query.getString("startTime")) && StringUtils.isNotEmpty(query.getString("endTime"))){
            String monthBet = "";
            try {
                monthBet = getMonthsBetween(query.getString("startTime"),query.getString("endTime"));
                System.out.println("monthBet = " + monthBet);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
            filter = filter.replaceAll("mth>='\\d{4}-\\d{2}'\\s+and\\s+mth<='\\d{4}-\\d{2}'", monthBet);
        }
        if (filter.startsWith(" and")){
            filter = filter.replaceFirst(" and", "");
        }
        query.put("filter",filter);
        JSONObject post = template.post(RemoteUrlConstants.ads_month_safety_stats, query);

        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject jsonObject = list.getJSONObject(i);
            jsonObject.put("alarm_rate",calculateData2(jsonObject.getString("alarm_total_num"),jsonObject.getString("alarm_handled_num")));
            jsonObject.put("supervise_rate",calculateData2(jsonObject.getString("alarm_total_num"),jsonObject.getString("supervision")));
            jsonObject.put("supervise_finish_rate",calculateData2(jsonObject.getString("alarm_total_num"),jsonObject.getString("supervision_done")));
        }
        return post;
    }



    /**
     * 综合统计分析机构统计-营运数据按季
     * @param query
     * @return
     */
    @Override
    public JSONObject quarterOperationStats(JSONObject query) {
        DecimalFormat df = new DecimalFormat("0"); // 或 "0.0"  , #.0
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        String filter = query.getString("filter");
        //if (filter.contains("_01")){
        //    filter = filter.replaceAll("(quarter\\s*=\\s*'\\d{4}_)(\\d{2})(')", "quarter = '第一季度' and year = '"+ query.getString("year") + "'");
        //}else if (filter.contains("_02")){
        //    filter = filter.replaceAll("(quarter\\s*=\\s*'\\d{4}_)(\\d{2})(')", "quarter = '第二季度' and year = '"+ query.getString("year") + "'");
        //}else if (filter.contains("_03")){
        //    filter = filter.replaceAll("(quarter\\s*=\\s*'\\d{4}_)(\\d{2})(')", "quarter = '第三季度' and year = '"+ query.getString("year") + "'");
        //}else {
        //    filter = filter.replaceAll("(quarter\\s*=\\s*'\\d{4}_)(\\d{2})(')", "quarter = '第四季度' and year = '"+ query.getString("year") + "'");
        //}
        if (filter.contains("_")){
            filter = filter.replaceAll("\\b(\\d{4})_(\\d{2})\\b", "$1-$2");
        }
        query.put("filter", filter);
        JSONObject post = template.post(RemoteUrlConstants.ads_quarter_operation_stats, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject jsonObject = list.getJSONObject(i);
            jsonObject.put("driving_distance_km",df.format(jsonObject.getDouble("driving_distance"))+"km");
            jsonObject.put("operating_distance_km",df.format(jsonObject.getDouble("operating_distance"))+"km");
        }
        return post;
    }
    /**
     * 综合统计分析机构统计-营运数据按年
     * @param query
     * @return
     */
    @Override
    public JSONObject yearOperationStats(JSONObject query) {
        DecimalFormat df = new DecimalFormat("0"); // 或 "0.0"  , #.0
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_year_operation_stats, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject jsonObject = list.getJSONObject(i);
            jsonObject.put("driving_distance_km",df.format(jsonObject.getDouble("driving_distance"))+"km");
            jsonObject.put("operating_distance_km",df.format(jsonObject.getDouble("operating_distance"))+"km");
        }
        return post;
    }
    /**
     * 综合统计分析机构统计-营运数据按日
     * @param query
     * @return
     */
    @Override
    public JSONObject dailyOperationStats(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        String filter = query.getString("filter");
        if (StringUtils.isEmpty(query.getString("county"))){
            if (StringUtils.isEmpty(filter)){
                filter = "county = null";
            }else {
                filter += "and county = null";
            }
        }
        if (StringUtils.isEmpty(query.getString("company"))){
            if (StringUtils.isEmpty(filter)){
                filter = "company = null";
            }else {
                filter += " and company = null";
            }
        }
        if (StringUtils.isNotEmpty(filter) && filter.contains("dt")) {
            filter = filter.replace("dt", "mth");
        }
        filter += " order by dt asc";
        query.put("filter",filter);
        JSONObject post = template.post(RemoteUrlConstants.ads_daily_operation_stats, query);
        return post;
    }
    /**
     * 综合统计分析机构统计-营运数据按月
     * @param query
     * @return
     */
    @Override
    public JSONObject monthOperationStats(JSONObject query) {
        DecimalFormat df = new DecimalFormat("0"); // 或 "0.0"  , #.0
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        String filter = query.getString("filter");
        // 季度
        //if (filter.contains("_01")){
        //    filter = filter.replaceAll("(quarter\\s*=\\s*'\\d{4}_)(\\d{2})(')", "quarter = '第一季度' and year = '"+ query.getString("year") + "'");
        //}else if (filter.contains("_02")){
        //    filter = filter.replaceAll("(quarter\\s*=\\s*'\\d{4}_)(\\d{2})(')", "quarter = '第二季度' and year = '"+ query.getString("year") + "'");
        //}else if (filter.contains("_03")){
        //    filter = filter.replaceAll("(quarter\\s*=\\s*'\\d{4}_)(\\d{2})(')", "quarter = '第三季度' and year = '"+ query.getString("year") + "'");
        //}else if (filter.contains("_04")){
        //    filter = filter.replaceAll("(quarter\\s*=\\s*'\\d{4}_)(\\d{2})(')", "quarter = '第四季度' and year = '"+ query.getString("year") + "'");
        //}
        if (filter.contains("_")){
            filter = filter.replaceAll("\\b(\\d{4})_(\\d{2})\\b", "$1-$2");
        }
        if (filter.contains("dt")) {
            filter = filter.replace("dt", "mth");
        }
        if ((filter.contains("quarter") && !filter.contains("county"))||
                (filter.contains("year") && !filter.contains("county")) ||
                (StringUtils.isNotEmpty("startTime") && StringUtils.isNotEmpty("endTime") && !filter.contains("county"))  ){
            filter += " and county = null and company = null";
        } else if ((filter.contains("quarter") && filter.contains("county")) ||
                (filter.contains("year") && filter.contains("county")) ) {
            filter += " and company = null";
        }
        if (filter.startsWith(" and")){
            filter = filter.replaceFirst(" and","");
        }
        filter += " order by mth asc";
        System.out.println("filter = " + filter);
        // 自定义日期处理
        if (StringUtils.isNotEmpty(query.getString("startTime")) && StringUtils.isNotEmpty(query.getString("endTime"))){
            String monthBet = "";
            try {
                monthBet = getMonthsBetween(query.getString("startTime"),query.getString("endTime"));
                System.out.println("monthBet = " + monthBet);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
            filter = filter.replaceAll("mth>='\\d{4}-\\d{2}'\\s+and\\s+mth<='\\d{4}-\\d{2}'", monthBet);
        }
        query.put("filter",filter);
        JSONObject post = template.post(RemoteUrlConstants.ads_month_operation_stats, query);
        JSONArray list = post.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject jsonObject = list.getJSONObject(i);
            jsonObject.put("driving_distance_km",df.format(jsonObject.getDouble("driving_distance"))+"km");
            jsonObject.put("operating_distance_km",df.format(jsonObject.getDouble("operating_distance"))+"km");
        }
        return post;
    }

    @Override
    public JSONObject safetyStatics(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        JSONObject post = null;
        String filter = query.getString("filter");
        if (query.getString("tabName").equals("M")||query.getString("tabName").equals("Z")){
            // 区县
            if (StringUtils.isNotEmpty(query.getString("county")) && StringUtils.isEmpty(query.getString("company"))){
                filter += "and county != null and company = null";
            }
            if (StringUtils.isEmpty(filter)){
                filter = "county = null and company = null";
            }else {
                if (StringUtils.isEmpty(query.getString("county")) && StringUtils.isEmpty(query.getString("company"))){
                    filter += "and county = null and company = null";
                }
            }
            if (StringUtils.isNotEmpty(filter) && filter.contains("dt")) {
                filter = filter.replace("dt", "mth");
            }
            query.put("filter",filter);
            // 按月
            post = template.post(RemoteUrlConstants.ads_month_safety_stats, query);
        } else if (query.getString("tabName").equals("Q")) {
            // 区县
            if (StringUtils.isNotEmpty(query.getString("county")) && StringUtils.isEmpty(query.getString("company"))){
                filter += "and county != null and company = null";
            }
            if (StringUtils.isEmpty(filter)){
                filter = "county = null and company = null";
            }else {
                if (StringUtils.isEmpty(query.getString("county")) && StringUtils.isEmpty(query.getString("company"))){
                    filter += "and county = null and company = null";
                }
            }

            // 按季
            //if (filter.contains("_01")){
            //    filter = filter.replaceAll("(quarter\\s*=\\s*'\\d{4}_)(\\d{2})(')", "quarter = '第一季度' and year = '"+ query.getString("year") + "'");
            //}else if (filter.contains("_02")){
            //    filter = filter.replaceAll("(quarter\\s*=\\s*'\\d{4}_)(\\d{2})(')", "quarter = '第二季度' and year = '"+ query.getString("year") + "'");
            //}else if (filter.contains("_03")){
            //    filter = filter.replaceAll("(quarter\\s*=\\s*'\\d{4}_)(\\d{2})(')", "quarter = '第三季度' and year = '"+ query.getString("year") + "'");
            //}else {
            //    filter = filter.replaceAll("(quarter\\s*=\\s*'\\d{4}_)(\\d{2})(')", "quarter = '第四季度' and year = '"+ query.getString("year") + "'");
            //}
            if (filter.contains("_")){
                filter = filter.replaceAll("\\b(\\d{4})_(\\d{2})\\b", "$1-$2");
            }
            query.put("filter", filter);
            post = template.post(RemoteUrlConstants.ads_quarter_safety_stats, query);
        }else {
            // 区县
            if (StringUtils.isNotEmpty(query.getString("county")) && StringUtils.isEmpty(query.getString("company"))){
                filter += "and county != null and company = null";
            }
            if (StringUtils.isEmpty(filter)){
                filter = "county = null and company = null";
            }else {
                if (StringUtils.isEmpty(query.getString("county")) && StringUtils.isEmpty(query.getString("company"))){
                    filter += "and county = null and company = null";
                }
            }
            query.put("filter",filter);
            // 按年
            post = template.post(RemoteUrlConstants.ads_year_safety_stats, query);
        }
        return post;
    }

    @Override
    public JSONObject operateStatics(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        query = filterByParam(query);
        JSONObject post = null;
        String filter = query.getString("filter");
        if (query.getString("tabName").equals("M")||query.getString("tabName").equals("Z")){
            // 区县
            if (StringUtils.isNotEmpty(query.getString("county")) && StringUtils.isEmpty(query.getString("company"))){
                filter += "and county != null and company = null";
            }
            if (StringUtils.isEmpty(filter)){
                filter = "county = null and company = null";
            }else {
                if (StringUtils.isEmpty(query.getString("county")) && StringUtils.isEmpty(query.getString("company"))){
                    filter += "and county = null and company = null";
                }
            }
            if (StringUtils.isNotEmpty(filter)) {
                filter = filter.replace("dt", "mth");
            }
            query.put("filter",filter);
            // 按月
            post = template.post(RemoteUrlConstants.ads_month_operation_stats, query);
        } else if (query.getString("tabName").equals("Q")) {
            // 区县
            if (StringUtils.isNotEmpty(query.getString("county")) && StringUtils.isEmpty(query.getString("company"))){
                filter += "and county != null and company = null";
            }
            if (StringUtils.isEmpty(filter)){
                filter = "county = null and company = null";
            }else {
                if (StringUtils.isEmpty(query.getString("county")) && StringUtils.isEmpty(query.getString("company"))){
                    filter += "and county = null and company = null";
                }
            }
            // 按季
            //if (filter.contains("_01")){
            //    filter = filter.replaceAll("(quarter\\s*=\\s*'\\d{4}_)(\\d{2})(')", "quarter = '第一季度' and year = '"+ query.getString("year") + "'");
            //}else if (filter.contains("_02")){
            //    filter = filter.replaceAll("(quarter\\s*=\\s*'\\d{4}_)(\\d{2})(')", "quarter = '第二季度' and year = '"+ query.getString("year") + "'");
            //}else if (filter.contains("_03")){
            //    filter = filter.replaceAll("(quarter\\s*=\\s*'\\d{4}_)(\\d{2})(')", "quarter = '第三季度' and year = '"+ query.getString("year") + "'");
            //}else {
            //    filter = filter.replaceAll("(quarter\\s*=\\s*'\\d{4}_)(\\d{2})(')", "quarter = '第四季度' and year = '"+ query.getString("year") + "'");
            //}
            if (filter.contains("_")){
                filter = filter.replaceAll("\\b(\\d{4})_(\\d{2})\\b", "$1-$2");
            }
            query.put("filter", filter);
            // 按季
            post = template.post(RemoteUrlConstants.ads_quarter_operation_stats, query);
        }else {
            // 区县
            if (StringUtils.isNotEmpty(query.getString("county")) && StringUtils.isEmpty(query.getString("company"))){
                filter += "and county != null and company = null";
            }
            if (StringUtils.isEmpty(filter)){
                filter = "county = null and company = null";
            }else {
                if (StringUtils.isEmpty(query.getString("county")) && StringUtils.isEmpty(query.getString("company"))){
                    filter += "and county = null and company = null";
                }
            }

            query.put("filter",filter);
            // 按年
            post = template.post(RemoteUrlConstants.ads_year_operation_stats, query);
        }
        return post;
    }

    private static JSONObject filterByParam(JSONObject query) {
        String filter = query.getString("filter");
        if (StringUtils.isEmpty(filter)){
            filter = "";
        }
        // 再处理"and"
        filter = removeAnd(filter);
        // 机构统计
        if (filter.contains("type='jgtj'")){
            filter = filter.replace("type='jgtj'"," and county != null and  car_number = null and company = null");
        }
        // 企业统计
        if (filter.contains("type='qitj'")){
            filter = filter.replace("type='qitj'"," and county != null and company != null and car_number = null");
        }
        // 车辆
        if (filter.contains("type='cltj'")){
            filter = filter.replace("type='cltj'"," and county != null and company != null and car_number != null");
        }
        if (filter.startsWith(" and")){
            filter = filter.replaceFirst(" and","");
        }
        //if (!ObjectUtil.isEmpty(filter)) {
        //    filter = filter.replaceFirst("and", "").trim();
        //    query.put("filter", filter);
        //}
        // 再处理"and"
        filter = removeAnd(filter);
        query.put("filter",filter);
        return query;
    }

    private static @NotNull String removeAnd(String filter) {
        if (filter.endsWith(" and")) {
            filter =  filter.substring(0, filter.length() - 4);
        } else if (filter.endsWith("and")) {
            filter =  filter.substring(0, filter.length() - 3);
        }else if (filter.endsWith("and ")) {
            filter =  filter.substring(0, filter.length() - 4);
        }else if (filter.contains("and  and  and")){
            filter = filter.replace("and  and  and"," and ");
        }else if (filter.contains("and  and")) {
            filter = filter.replace("and  and"," and ");
        }else if (filter.contains("and   and")){
            filter = filter.replace("and   and"," and ");
        }
        return filter;
    }


    public String calculateData2(String num1, String num2) {
        if (StringUtils.isNotEmpty(num1) && StringUtils.isNotEmpty(num2)) {
            double handledVal = (Double.valueOf(num2) / Double.valueOf(num1)) * 100;
            // 四舍五入保留一位小数
            handledVal = Math.round(handledVal * 10) / 10.0;
            return handledVal + "%";
        } else {
            return "0.0%";
        }
    }


    public JSONObject calculateData(JSONObject post) {
        JSONArray postList = post.getJSONArray("list");

        for (int i = 0; i < postList.size(); i++) {
            JSONObject json = postList.getJSONObject(i);
            String alarm_total_num = json.getString("alarm_total_num"); // 报警总数
            String alarm_handled_num = json.getString("alarm_handled_num");// 报警处理数
            if (StringUtils.isNotEmpty(alarm_total_num) && StringUtils.isNotEmpty(alarm_handled_num)) {
                double handledVal = (Double.valueOf(alarm_handled_num) / Double.valueOf(alarm_total_num)) * 100;
                // 四舍五入保留一位小数
                handledVal = Math.round(handledVal * 10) / 10.0;
                json.put("alarm_rate", handledVal + "%");
            } else {
                json.put("alarm_rate", "0.0%");
            }
            String supervision = json.getString("supervision"); // 督办数
            if (StringUtils.isNotEmpty(alarm_total_num) && StringUtils.isNotEmpty(supervision)) {
                double handledVal = (Double.valueOf(supervision) / Double.valueOf(alarm_total_num)) * 100;
                // 四舍五入保留一位小数
                handledVal = Math.round(handledVal * 10) / 10.0;
                json.put("supervise_rate", handledVal + "%");
            } else {
                json.put("supervise_rate", "0.0%");
            }
            String supervision_done = json.getString("supervision_done"); // 督办完成数
            if (StringUtils.isNotEmpty(alarm_total_num) && StringUtils.isNotEmpty(supervision_done)) {
                double handledVal = (Double.valueOf(supervision_done) / Double.valueOf(alarm_total_num)) * 100;
                // 四舍五入保留一位小数
                handledVal = Math.round(handledVal * 10) / 10.0;
                json.put("supervise_finish_rate", handledVal + "%");
            } else {
                json.put("supervise_finish_rate", "0.0%");
            }
        }
        return post;
    }

    public static String getMonthsBetween(String start, String end) throws ParseException {
        StringBuilder  str = new StringBuilder();
        List<String> months = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Calendar startCal = Calendar.getInstance();
        Calendar endCal = Calendar.getInstance();

        startCal.setTime(sdf.parse(start));
        endCal.setTime(sdf.parse(end));
        endCal.add(Calendar.MONTH, 1); // 包含结束月份[2,4](@ref)

        while (startCal.before(endCal)) {
            months.add(sdf.format(startCal.getTime()));
            startCal.add(Calendar.MONTH, 1); // 按月递增
        }
        str.append("( ");
        for (String month : months) {
            str.append(" or mth = '" +month + "'");
        }
        str.append(" ) ");
        String filter = str.toString();
        return filter.replaceFirst("or","");
    }

}
