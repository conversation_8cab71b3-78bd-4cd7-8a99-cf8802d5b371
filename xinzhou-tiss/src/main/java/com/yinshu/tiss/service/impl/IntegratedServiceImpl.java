package com.yinshu.tiss.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.tiss.common.RemoteUrlConstants;
import com.yinshu.tiss.common.RequestFilterBuilder;
import com.yinshu.tiss.service.IntegratedService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 道路运输监管
 */
@Service
public class IntegratedServiceImpl implements IntegratedService {
    @Resource
    DvisualHttpTemplate template;

    /**
     * 运输行业统计汇总表
     * @param query
     * @return
     */
    @Override
    public JSONObject statisticsSummary(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_transport_statistics_summary, query);
        return post;
    }
    /**
     * 图层管理
     * @param query
     * @return
     */
    @Override
    public JSONObject networkMapHighway(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_highway_network_map_highway, query);
        return post;
    }
    /**
     * 公路事件分类占比
     * @param query
     * @return
     */
    @Override
    public JSONObject roadEventClassificationRatio(JSONObject query) {
        RequestFilterBuilder.buildParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_road_event_classification_ratio, query);
        return post;
    }
    /**
     * 公路事件详情按月
     * @param query
     * @return
     */
    @Override
    public JSONObject classificationRatioMonth(JSONObject query) {
        query = getFilterByParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_road_event_classification_ratio_month, query);
        return post;
    }
    /**
     * 公路事件详情按年季度
     * @param query
     * @return
     */
    @Override
    public JSONObject classificationRatioYearQuarter(JSONObject query) {
        query = getFilterByParam(query);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_road_event_classification_ratio_year_quarter, query);
        return post;
    }


    private static  JSONObject getFilterByParam(JSONObject query) {
        String filter = "";
        //月份
        if (StringUtils.isNotEmpty(query.getString("mth")))  {
            filter += " and mth = '"+ query.getString("mth")+"'";
        }
        // 季
        if (StringUtils.isNotEmpty(query.getString("quarter"))){
            filter += " and quarter = '"+ query.getString("quarter")+"'";
        }
        // 年
        if (StringUtils.isNotEmpty(query.getString("year"))){
            filter += " and year = '"+ query.getString("year")+"'";
        }
        if (!ObjectUtil.isEmpty(filter)) {
            filter = filter.replaceFirst("and", "").trim();
            query.put("filter", filter);
        }
        return query;
    }
}
