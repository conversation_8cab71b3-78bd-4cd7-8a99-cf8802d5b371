package com.yinshu.tiss.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yinshu.tiss.entity.IntelligentCheckRuleDTO;

/**
 * 智能查岗规则表;(intelligent_check_rule)表服务接口
 * <AUTHOR> http://www.chiner.pro
 * @date : 2025-7-15
 */
public interface IntelligentCheckRuleService extends IService<IntelligentCheckRuleDTO>{

    /**
     * 查询列表
     *
     * @param intelligentCheckRuleDto 筛选条件
     * @return
     */
    Page<IntelligentCheckRuleDTO> pageQuery(IntelligentCheckRuleDTO intelligentCheckRuleDto);
    /**
     * 新增数据
     *
     * @param intelligentCheckRuleDto 实例对象
     * @return 实例对象
     */
    boolean insert(IntelligentCheckRuleDTO intelligentCheckRuleDto);
    /**
     * 更新数据
     *
     * @param intelligentCheckRuleDto 实例对象
     * @return 实例对象
     */
    boolean update(IntelligentCheckRuleDTO intelligentCheckRuleDto);
    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(String id);
    /**
     * 通过主键集合删除数据
     *
     * @param ids 主键
     * @return 是否成功
     */
    boolean deleteByIds(String[] ids);
}
