package com.yinshu.tiss.service.impl;

import cn.hutool.core.util.StrUtil;
import com.yinshu.tiss.dao.IntelligentCheckRuleMapper;
import com.yinshu.tiss.entity.IntelligentCheckRuleDTO;
import com.yinshu.tiss.service.IntelligentCheckRuleService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Arrays;

/**
 * 智能查岗规则表;(intelligent_check_rule)表服务实现类
 *
 * <AUTHOR> http://www.chiner.pro
 * @date : 2025-7-15
 */
@Service
public class IntelligentCheckRuleServiceImpl extends ServiceImpl<IntelligentCheckRuleMapper, IntelligentCheckRuleDTO> implements IntelligentCheckRuleService {

    /**
     * 分页查询
     *
     * @param intelligentCheckRuleDto 筛选条件
     * @return
     */
    public Page<IntelligentCheckRuleDTO> pageQuery(IntelligentCheckRuleDTO intelligentCheckRuleDto) {
        // 构建查询条件
        LambdaQueryWrapper<IntelligentCheckRuleDTO> queryWrapper = new LambdaQueryWrapper<>();

        // 如果有具体的查询条件，可以在这里添加
        if (StrUtil.isNotBlank(intelligentCheckRuleDto.getEnterpriseName())) {
            queryWrapper.like(IntelligentCheckRuleDTO::getEnterpriseName, intelligentCheckRuleDto.getEnterpriseName());
        }

        // 创建分页对象
        Page<IntelligentCheckRuleDTO> page = new Page<>(intelligentCheckRuleDto.getPageNum(), intelligentCheckRuleDto.getPageSize());
        Page<IntelligentCheckRuleDTO> result = this.page(page, queryWrapper);
        result.getRecords().forEach(item -> {
            item.setCheckCondition("1、近1小时报警处理率低于" + item.getLessThan() + "%;\n2、报警督办率大于" + item.getGreaterThan() + "%");
        });
        // 执行分页查询
        return result;
    }

    /**
     * 新增数据
     *
     * @param intelligentCheckRuleDto 实例对象
     * @return 是否成功
     */
    @Override
    public boolean insert(IntelligentCheckRuleDTO intelligentCheckRuleDto) {
        return this.save(intelligentCheckRuleDto);
    }

    /**
     * 更新数据
     *
     * @param intelligentCheckRuleDto 实例对象
     * @return 是否成功
     */
    public boolean update(IntelligentCheckRuleDTO intelligentCheckRuleDto) {
        return this.updateById(intelligentCheckRuleDto);
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(String id) {
        return this.removeById(id);
    }

    /**
     * 批量删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteByIds(String[] id) {
        return this.removeBatchByIds(Arrays.asList(id));
    }
}
