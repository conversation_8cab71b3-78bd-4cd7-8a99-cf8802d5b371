package com.yinshu.tiss.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.fast.excel.util.ExcelUtils;
import com.yinshu.tiss.service.RoadTransportTissService;
import com.yinshu.tiss.vo.*;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * 道路运输监管
 */
@RestController
@RequestMapping(ApiConstant.API_TISS_PREFIX + "/road-transport/dangerous")
public class RoadTransportTissController {
    @Autowired
    private RoadTransportTissService roadTransportService;

    /**
     * 道路运输监管行车监控车辆基础信息及最新定位
     */
    @PostMapping("/carInfoAndNewLocation")
    public ResultVO<?> carInfoAndNewLocation(@RequestBody JSONObject query){
        JSONObject object = roadTransportService.carInfoAndNewLocation(query);
        return ResultVO.suc(object);
    }
    /**
     * 道路运输视频巡查相关视频地址
     */
    @PostMapping("/vedioInfo")
    public ResultVO<?> vedioInfo(@RequestBody JSONObject query){
        JSONObject object = roadTransportService.vedioInfo(query);
        return ResultVO.suc(object);
    }
    /**
     * 道路运输监管报警运行台账按企业
     */
    @PostMapping("/warnInfoCompany")
    public ResultVO<?> warnInfoCompany(@RequestBody JSONObject query){
        JSONObject object = roadTransportService.warnInfoCompany(query);
        return ResultVO.suc(object);
    }

    /**
     * 道路运输监管报警运行台账按企业导出
     */
    @PostMapping("/exportWarnInfoCompany")
    public void exportWarnInfoCompany(@RequestBody JSONObject query){
        JSONObject object = roadTransportService.warnInfoCompany(query);
        ExcelUtils.exportExcelSheet(WarnInfoCompanyVO.class, object);
    }

    /**
     * 道路运输监管报数据统计按企业导出
     */
    @PostMapping("/exportWarnInfoCompanyTj")
    public void exportWarnInfoCompanyTj(@RequestBody JSONObject query){
        JSONObject object = roadTransportService.warnInfoCompany(query);
        ExcelUtils.exportExcelSheet(WarnInfoCompanyTjVO.class, object);
    }

    /**
     * 道路运输监管运行台账按机构
     */
    @PostMapping("/runingInfoCon")
    public ResultVO<?> runingInfoCon(@RequestBody JSONObject query){
        JSONObject object = roadTransportService.runingInfoCon(query);
        return ResultVO.suc(object);
    }

    /**
     * 道路运输监管运行台账按机构
     */
    @PostMapping("/exportRuningInfoCon")
    public void exportRuningInfoCon(@RequestBody JSONObject query){
        JSONObject object = roadTransportService.runingInfoCon(query);
        ExcelUtils.exportExcelSheet(RuningInfoConVO.class, object);
    }

    /**
     * 道路运输监管报警运行台账按车辆
     */
    @PostMapping("/warnInfoCar")
    public ResultVO<?> warnInfoCar(@RequestBody JSONObject query){
        JSONObject object = roadTransportService.warnInfoCar(query);
        return ResultVO.suc(object);
    }

    /**
     * 道路运输监管报警运行台账按车辆导出
     */
    @PostMapping("/exportWarnInfoCar")
    public void exportWarnInfoCar(@RequestBody JSONObject query){
        JSONObject object = roadTransportService.warnInfoCar(query);
        ExcelUtils.exportExcelSheet(WarnInfoCarVO.class, object);
    }

    /**
     * 道路运输监管数据统计按车辆导出
     */
    @PostMapping("/exportWarnInfoCarTj")
    public void exportWarnInfoCarTj(@RequestBody JSONObject query){
        JSONObject object = roadTransportService.warnInfoCar(query);
        ExcelUtils.exportExcelSheet(WarnInfoCarTjVO.class, object);
    }

    /**
     * 报警台账车辆明细表
     */
    @PostMapping("/infoCarControl")
    public ResultVO<?> infoCarControl(@RequestBody JSONObject query){
        JSONObject object = roadTransportService.infoCarControl(query);
        return ResultVO.suc(object);
    }

    /**
     * 报警台账车辆明细导出
     */
    @PostMapping("/exportInfoCarControl")
    public void exportInfoCarControl(@RequestBody JSONObject query){
        JSONObject object = roadTransportService.infoCarControl(query);
        List<InfoCarControlVO> list = JSON.parseArray(JSON.toJSONString(object.get("list")), InfoCarControlVO.class);
        list.forEach(item->{
            if (item.getState().equals("0")){
                item.setState("未处理");
            }else{
                item.setState("已处理");
            }
        });
        // 将修改后的 list 转换为 JSONArray 并重新放入 object
        JSONArray updatedArray = JSON.toJSON(list) instanceof JSONArray ? (JSONArray) JSON.toJSON(list) : new JSONArray(Collections.singletonList(list));
        object.put("list", updatedArray);
        ExcelUtils.exportExcelSheet(InfoCarControlVO.class, object);
    }
}
