package com.yinshu.tiss.controller;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.fast.excel.util.ExcelUtils;
import com.yinshu.tiss.service.StatisticAnalysisTissService;
import com.yinshu.tiss.vo.*;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 综合统计分析
 */
@RestController
@RequestMapping(ApiConstant.API_TISS_PREFIX + "/statisticAnalysis/analysis")
public class StatisticAnalysisTissController {
    @Autowired
    private StatisticAnalysisTissService statisticsService;

    /**
     * 综合统计分析报警事件详情
     */
    @PostMapping("/alarmAnalysis")
    public ResultVO<?> alarmAnalysis(@RequestBody JSONObject query){
        JSONObject object = statisticsService.alarmAnalysis(query);
        return ResultVO.suc(object);
    }

    /**
     * 综合统计分析机构统计-安全数据按季度
     */
    @PostMapping("/quarterSafetyStats")
    public ResultVO<?> quarterSafetyStats(@RequestBody JSONObject query){
        JSONObject object = statisticsService.quarterSafetyStats(query);
        return ResultVO.suc(object);
    }

    /**
     * 综合统计分析机构统计-安全数据按年
     */
    @PostMapping("/yearSafetyStats")
    public ResultVO<?> yearSafetyStats(@RequestBody JSONObject query){
        JSONObject object = statisticsService.yearSafetyStats(query);
        return ResultVO.suc(object);
    }

    /**
     * 综合统计分析机构统计-安全数据按日
     */
    @PostMapping("/dailySafetyStats")
    public ResultVO<?> dailySafetyStats(@RequestBody JSONObject query){
        JSONObject object = statisticsService.dailySafetyStats(query);
        return ResultVO.suc(object);
    }

    /**
     * 综合统计分析机构统计-安全数据按月
     */
    @PostMapping("/monthSafetyStats")
    public ResultVO<?> monthSafetyStats(@RequestBody JSONObject query){
        JSONObject object = statisticsService.monthSafetyStats(query);
        return ResultVO.suc(object);
    }

    /**
     * 综合统计分析机构统计-安全数据导出
     */
    @PostMapping("/exportSafetyStats")
    public void exportSafetyStats(@RequestBody JSONObject query){
        JSONObject object = new JSONObject();
        String type = query.get("type").toString();
        if (query.get("defaultActive").toString().equals("Y")){
            query.remove("defaultActive");
            object = statisticsService.yearSafetyStats(query);
        } else if (query.get("defaultActive").toString().equals("Q")) {
            query.remove("defaultActive");
            object = statisticsService.quarterSafetyStats(query);
        }else{
//            月与自定义
            query.remove("defaultActive");
            object = statisticsService.monthSafetyStats(query);
        }
        if (type.equals("qitj")){
//            企业统计
            ExcelUtils.exportExcelSheet(SafetyStatsQitjVO.class, object);

        }else if (type.equals("cltj")){
//            车辆统计
            ExcelUtils.exportExcelSheet(SafetyStatsCltjVO.class, object);

        }else{
//            机构统计
            ExcelUtils.exportExcelSheet(SafetyStatsJgVO.class, object);

        }
    }


    /**
     * 综合统计分析机构统计-营运数据按季
     */
    @PostMapping("/quarterOperationStats")
    public ResultVO<?> quarterOperationStats(@RequestBody JSONObject query){
        JSONObject object = statisticsService.quarterOperationStats(query);
        return ResultVO.suc(object);
    }

    /**
     * 综合统计分析机构统计-营运数据按年
     */
    @PostMapping("/yearOperationStats")
    public ResultVO<?> yearOperationStats(@RequestBody JSONObject query){
        JSONObject object = statisticsService.yearOperationStats(query);
        return ResultVO.suc(object);
    }

    /**
     * 综合统计分析机构统计-营运数据按日
     */
    @PostMapping("/dailyOperationStats")
    public ResultVO<?> dailyOperationStats(@RequestBody JSONObject query){
        JSONObject object = statisticsService.dailyOperationStats(query);
        return ResultVO.suc(object);
    }

    /**
     * 综合统计分析机构统计-营运数据按月
     */
    @PostMapping("/monthOperationStats")
    public ResultVO<?> monthOperationStats(@RequestBody JSONObject query){
        JSONObject object = statisticsService.monthOperationStats(query);
        return ResultVO.suc(object);
    }

    /**
     * 综合统计分析机构统计-营运数据导出
     */
    @PostMapping("/exportOperationStats")
    public void exportOperationStats(@RequestBody JSONObject query){
        JSONObject object = new JSONObject();
        String type = query.get("type").toString();
        if (query.get("defaultActive").toString().equals("Y")){
            query.remove("defaultActive");
            object = statisticsService.yearOperationStats(query);

        } else if (query.get("defaultActive").toString().equals("Q")) {
            query.remove("defaultActive");
            object = statisticsService.quarterOperationStats(query);
        }else{
//            月与自定义
            query.remove("defaultActive");
            object = statisticsService.monthOperationStats(query);
        }
        if (type.equals("qitj")){
//            企业统计
            ExcelUtils.exportExcelSheet(OperationStatsQitjVO.class, object);

        }else if (type.equals("cltj")){
//            车辆统计
            ExcelUtils.exportExcelSheet(OperationStatsCltjVO.class, object);

        }else{
//            机构统计
            ExcelUtils.exportExcelSheet(OperationStatsJgtjVO.class, object);

        }
    }

    /**
     * 汇总数据 - 安全数据
     */
    @PostMapping("/safetyStatics")
    public ResultVO<?> safetyStatics(@RequestBody JSONObject query){
        JSONObject object = statisticsService.safetyStatics(query);
        return ResultVO.suc(object);
    }

    /**
     * 汇总数据 - 营运
     */
    @PostMapping("/operateStatics")
    public ResultVO<?> operateStatics(@RequestBody JSONObject query){
        JSONObject object = statisticsService.operateStatics(query);
        return ResultVO.suc(object);
    }
}
