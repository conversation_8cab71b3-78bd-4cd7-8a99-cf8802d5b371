package com.yinshu.tiss.controller;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.tiss.service.IntegratedService;
import com.yinshu.tiss.service.RoadTransportTissService;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 行业监督看板
 */
@RestController
@RequestMapping(ApiConstant.API_TISS_PREFIX + "/integrated")
public class IntegratedController {

    @Autowired
    private IntegratedService integratedService;

    /**
     * 运输行业统计汇总表
     */
    @PostMapping("/statisticsSummary")
    public ResultVO<?> statisticsSummary(@RequestBody JSONObject query){
        JSONObject object = integratedService.statisticsSummary(query);
        return ResultVO.suc(object);
    }

    /**
     * 图层管理
     */
    @PostMapping("/networkMapHighway")
    public ResultVO<?> networkMapHighway(@RequestBody JSONObject query){
        JSONObject object = integratedService.networkMapHighway(query);
        return ResultVO.suc(object);
    }

    /**
     * 公路事件分类占比
     */
    @PostMapping("/roadEventClassificationRatio")
    public ResultVO<?> roadEventClassificationRatio(@RequestBody JSONObject query){
        JSONObject object = integratedService.roadEventClassificationRatio(query);
        return ResultVO.suc(object);
    }

    /**
     * 公路事件详情按月
     */
    @PostMapping("/classificationRatioMonth")
    public ResultVO<?> classificationRatioMonth(@RequestBody JSONObject query){
        JSONObject object = integratedService.classificationRatioMonth(query);
        return ResultVO.suc(object);
    }

    /**
     * 公路事件详情按年季度
     */
    @PostMapping("/classificationRatioYearQuarter")
    public ResultVO<?> classificationRatioYearQuarter(@RequestBody JSONObject query){
        JSONObject object = integratedService.classificationRatioYearQuarter(query);
        return ResultVO.suc(object);
    }

}
