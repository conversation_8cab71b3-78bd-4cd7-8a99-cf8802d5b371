package com.yinshu.tiss.controller;

import com.yinshu.common.ApiConstant;
import com.yinshu.tiss.entity.IntelligentCheckRuleDTO;
import com.yinshu.tiss.service.IntelligentCheckRuleService;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 智能查岗规则表;(intelligent_check_rule)表控制层
 * <AUTHOR> http://www.chiner.pro
 * @date : 2025-7-15
 */
@RestController
@RequestMapping(ApiConstant.API_TISS_PREFIX + "/supervision/safety/intelligentCheckRule")
public class IntelligentCheckRuleController{
    @Autowired
    private IntelligentCheckRuleService intelligentCheckRuleService;

    /**
     * 通过ID查询单条数据 
     *
     * @param id 主键
     * @return 实例对象
     */
    @GetMapping("/{id}")
    public ResultVO<IntelligentCheckRuleDTO> queryById(@PathVariable String id){
        return ResultVO.suc(intelligentCheckRuleService.getById(id));
    }

    /**
     * 获取全部的企业名称
     *
     * @return 实例对象
     */
    @GetMapping("/getAllEnterpriseName")
    public ResultVO<List<IntelligentCheckRuleDTO>> getAllEnterpriseName(){
        List<IntelligentCheckRuleDTO> list = intelligentCheckRuleService.lambdaQuery()
                .select(IntelligentCheckRuleDTO::getEnterpriseName)
                .groupBy(IntelligentCheckRuleDTO::getEnterpriseName)
                .list();
        return ResultVO.suc(list);
    }

    /**
     * 分页查询
     *
     * @param intelligentCheckRule 筛选条件
     * @return 查询结果
     */
    @PostMapping("/pageQuery")
    public ResultVO<Page<IntelligentCheckRuleDTO>> pageQuery(@RequestBody IntelligentCheckRuleDTO intelligentCheckRule){
        Page<IntelligentCheckRuleDTO> result = intelligentCheckRuleService.pageQuery(intelligentCheckRule);
        return ResultVO.suc(result);
    }

    /**
     * 新增数据
     *
     * @param intelligentCheckRule 实例对象
     * @return 实例对象
     */
    @PostMapping("/add")
    public ResultVO<IntelligentCheckRuleDTO> add(@RequestBody IntelligentCheckRuleDTO intelligentCheckRule){
        intelligentCheckRuleService.insert(intelligentCheckRule);
        return ResultVO.suc(intelligentCheckRule);
    }

    /**
     * 更新数据
     *
     * @param intelligentCheckRule 实例对象
     * @return 实例对象
     */
    @PutMapping("/update")
    public ResultVO<IntelligentCheckRuleDTO> edit(@RequestBody IntelligentCheckRuleDTO intelligentCheckRule){
        intelligentCheckRuleService.update(intelligentCheckRule);
        return ResultVO.suc(intelligentCheckRule);
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @DeleteMapping("/delete/{id}")
    public ResultVO<Boolean> deleteById(@PathVariable String id){
        return ResultVO.suc(intelligentCheckRuleService.deleteById(id));
    }
}
