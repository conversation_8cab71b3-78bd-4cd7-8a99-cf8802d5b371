package com.yinshu.tiss.controller;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.tiss.service.BaseTissService;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/***
 * 公共接口
 * @auther zhonglz
 */
@RestController
@RequestMapping(ApiConstant.API_TISS_PREFIX + "/base")
public class BaseTissController {

    @Autowired
    private BaseTissService baseTissService;

    /**
     * 获取地市行政区划
     * @param query
     * @return
     */
    @PostMapping("/getCountyData")
    public ResultVO<?> getCountyData(@RequestBody JSONObject query) {
        JSONObject object = baseTissService.getCountyData(query);
        return ResultVO.suc(object);
    }

}
