package com.yinshu.tiss.controller;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.fast.excel.util.ExcelUtils;
import com.yinshu.tiss.service.RoadSafetySupervisionService;
import com.yinshu.tiss.vo.EnterpriseSecurityVO;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.yinshu.tiss.vo.VehicleInfoVO;
@RestController
@RequestMapping(ApiConstant.API_TISS_PREFIX + "/supervision")
public class RoadSafetySupervisionController {
    @Autowired
    private RoadSafetySupervisionService roadSafetySupervisionService;

    /**
     * 获取动态查岗数据
     */
    @PostMapping("/safety/getDynamicJobCheckData")
    public ResultVO<?> getDynamicJobCheckData(@RequestBody JSONObject query) {
        JSONObject object = roadSafetySupervisionService.getDynamicJobCheckData(query);
        return ResultVO.suc(object);
    }

    /**
     * 获取报警督办数据
     */
    @PostMapping("/safety/getAlarmSupervisionData")
    public ResultVO<?> getAlarmSupervisionData(@RequestBody JSONObject query) {
        JSONObject object = roadSafetySupervisionService.getAlarmSupervisionData(query);
        return ResultVO.suc(object);
    }

    /**
     * 获取报警督办表格数据
     */
    @PostMapping("/safety/getAlarmSupervisionTableData")
    public ResultVO<?> getAlarmSupervisionTableData(@RequestBody JSONObject query) {
        JSONObject object = roadSafetySupervisionService.getAlarmSupervisionTableData(query);
        return ResultVO.suc(object);
    }

    /**
     * 获取安全隐患整改数据
     */
    @PostMapping("/rectification/getSafeHazardRectificationData")
    public ResultVO<?> getSafeHazardRectificationData(@RequestBody JSONObject query) {
        JSONObject object = roadSafetySupervisionService.getSafeHazardRectificationData(query);
        return ResultVO.suc(object);
    }

    /**
     * 隐患详情数据接口
     */
    @PostMapping("/rectification/getHiddenDangerDetailsData")
    public ResultVO<?> getHiddenDangerDetailsData(@RequestBody JSONObject query) {
        JSONObject object = roadSafetySupervisionService.getHiddenDangerDetailsData(query);
        return ResultVO.suc(object);
    }

    /**
     * 各区域隐患数量分析数据接口
     */
    @PostMapping("/rectification/getCountyHiddenDangerSumData")
    public ResultVO<?> getCountyHiddenDangerSumData(@RequestBody JSONObject query) {
        JSONObject object = roadSafetySupervisionService.getCountyHiddenDangerSumData(query);
        return ResultVO.suc(object);
    }

    /**
     * 各类型平均治理时间数据接口
     */
    @PostMapping("/rectification/getHiddenDangerTypeAvgGovernanceTimeData")
    public ResultVO<?> getHiddenDangerTypeAvgGovernanceTimeData(@RequestBody JSONObject query) {
        JSONObject object = roadSafetySupervisionService.getHiddenDangerTypeAvgGovernanceTimeData(query);
        return ResultVO.suc(object);
    }

    /**
     * 各等级平均治理时间数据接口
     */
    @PostMapping("/rectification/getHiddenDangerLevelAvgGovernanceTimeData")
    public ResultVO<?> getHiddenDangerLevelAvgGovernanceTimeData(@RequestBody JSONObject query) {
        JSONObject object = roadSafetySupervisionService.getHiddenDangerLevelAvgGovernanceTimeData(query);
        return ResultVO.suc(object);
    }

    /**
     * 当前隐患治理进度数据接口
     */
    @PostMapping("/rectification/getHazardGovernanceProgressData")
    public ResultVO<?> getHazardGovernanceProgressData(@RequestBody JSONObject query) {
        JSONObject object = roadSafetySupervisionService.getHazardGovernanceProgressData(query);
        return ResultVO.suc(object);
    }

    /**
     * 企业安全评估数据接口
     */
    @PostMapping("/rectification/getCompanySecurityAssessmentData")
    public ResultVO<?> getCompanySecurityAssessmentData(@RequestBody JSONObject query) {
        JSONObject object = roadSafetySupervisionService.getCompanySecurityAssessmentData(query);
        return ResultVO.suc(object);
    }

    /**
     * 企业安全评估导出
     * @param query
     * @return
     */
    @PostMapping("/rectification/exportEnterpriseSecurity")
    public void exportEnterpriseSecurity(@RequestBody JSONObject query) {
        JSONObject object = roadSafetySupervisionService.getCompanySecurityAssessmentData(query);
        ExcelUtils.exportExcelSheet(EnterpriseSecurityVO.class, object);
    }

    /**
     * 企业综合评分走势日数据接口
     */
    @PostMapping("/rectification/getCompanySafetyScoresDayData")
    public ResultVO<?> getCompanySafetyScoresDayData(@RequestBody JSONObject query) {
        JSONObject object = roadSafetySupervisionService.getCompanySafetyScoresDayData(query);
        return ResultVO.suc(object);
    }

    /**
     * 企业报警构成情况月数据接口
     */
    @PostMapping("/rectification/getCompanyAlarmCompositionMonthData")
    public ResultVO<?> getCompanyAlarmCompositionMonthData(@RequestBody JSONObject query) {
        JSONObject object = roadSafetySupervisionService.getCompanyAlarmCompositionMonthData(query);
        return ResultVO.suc(object);
    }

    /**
     * 企业车辆风险排行月数据接口
     */
    @PostMapping("/rectification/getCarRiskRankMonthData")
    public ResultVO<?> getCarRiskRankMonthData(@RequestBody JSONObject query) {
        JSONObject object = roadSafetySupervisionService.getCarRiskRankMonthData(query);
        return ResultVO.suc(object);
    }

    /**
     * 车辆安全评估数据接口
     */
    @PostMapping("/rectification/getCarSafetyEvaluationData")
    public ResultVO<?> getCarSafetyEvaluationData(@RequestBody JSONObject query) {
        JSONObject object = roadSafetySupervisionService.getCarSafetyEvaluationData(query);
        return ResultVO.suc(object);
    }

    /**
     * 车辆安全评估导出
     */
    @PostMapping("/rectification/exportVehicleInfoExport")
    public void exportVehicleInfoExport(@RequestBody JSONObject query) {
        JSONObject object = roadSafetySupervisionService.getCarSafetyEvaluationData(query);
        ExcelUtils.exportExcelSheet(VehicleInfoVO.class, object);
    }

    /**
     * 车辆出车日历数据接口
     */
    @PostMapping("/rectification/getCarDepartureCalendarData")
    public ResultVO<?> getCarDepartureCalendarData(@RequestBody JSONObject query) {
        JSONObject object = roadSafetySupervisionService.getCarDepartureCalendarData(query);
        return ResultVO.suc(object);
    }

    /**
     * 车辆运营信息数据接口
     */
    @PostMapping("/rectification/getCarOperationInfoData")
    public ResultVO<?> getCarOperationInfoData(@RequestBody JSONObject query) {
        JSONObject object = roadSafetySupervisionService.getCarOperationInfoData(query);
        return ResultVO.suc(object);
    }

    /**
     * 百公里报警趋势数据接口
     */
    @PostMapping("/rectification/getCarPer100kmAlarmSumData")
    public ResultVO<?> getCarPer100kmAlarmSumData(@RequestBody JSONObject query) {
        JSONObject object = roadSafetySupervisionService.getCarPer100kmAlarmSumData(query);
        return ResultVO.suc(object);
    }

    /**
     * 车辆报警构成情况数据接口
     */
    @PostMapping("/rectification/getCarAlarmCompositionData")
    public ResultVO<?> getCarAlarmCompositionData(@RequestBody JSONObject query) {
        JSONObject object = roadSafetySupervisionService.getCarAlarmCompositionData(query);
        return ResultVO.suc(object);
    }
}