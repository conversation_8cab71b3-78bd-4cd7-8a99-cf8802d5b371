package com.yinshu.tiss.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinshu.common.ApiConstant;
import com.yinshu.tiss.entity.RegularCheckRuleDTO;
import com.yinshu.tiss.service.RegularCheckRuleService;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 定期自动查岗规则表;(regular_check_rule)表控制层
 *
 * <AUTHOR> http://www.chiner.pro
 * @date : 2025-7-15
 */
@RestController
@RequestMapping(ApiConstant.API_TISS_PREFIX + "/supervision/safety/regularCheckRule")
public class RegularCheckRuleController {
    @Autowired
    private RegularCheckRuleService regularCheckRuleService;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @GetMapping("/{id}")
    public ResultVO<RegularCheckRuleDTO> queryById(@PathVariable String id) {
        return ResultVO.suc(regularCheckRuleService.getById(id));
    }

    /**
     * 获取全部的企业名称
     *
     * @return 实例对象
     */
    @GetMapping("/getAllEnterpriseName")
    public ResultVO<List<RegularCheckRuleDTO>> getAllEnterpriseName() {
        List<RegularCheckRuleDTO> list = regularCheckRuleService.lambdaQuery().select(RegularCheckRuleDTO::getEnterpriseName).groupBy(RegularCheckRuleDTO::getEnterpriseName).list();
        return ResultVO.suc(list);
    }

    /**
     * 分页查询
     *
     * @param regularCheckRule 筛选条件
     * @return 查询结果
     */
    @PostMapping("/pageQuery")
    public ResultVO<Page<RegularCheckRuleDTO>> pageQuery(@RequestBody RegularCheckRuleDTO regularCheckRule) {
        Page<RegularCheckRuleDTO> result = regularCheckRuleService.pageQuery(regularCheckRule);
        return ResultVO.suc(result);
    }


    /**
     * 新增数据
     *
     * @param regularCheckRule 实例对象
     * @return 实例对象
     */
    @PostMapping("/add")
    public ResultVO<RegularCheckRuleDTO> add(@RequestBody RegularCheckRuleDTO regularCheckRule) {
        regularCheckRuleService.insert(regularCheckRule);
        return ResultVO.suc(regularCheckRule);
    }

    /**
     * 更新数据
     *
     * @param regularCheckRule 实例对象
     * @return 实例对象
     */
    @PutMapping("/update")
    public ResultVO<RegularCheckRuleDTO> edit(@RequestBody RegularCheckRuleDTO regularCheckRule) {
        regularCheckRuleService.update(regularCheckRule);
        return ResultVO.suc(regularCheckRule);
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @DeleteMapping("/delete/{id}")
    public ResultVO<Boolean> deleteById(@PathVariable String id) {
        return ResultVO.suc(regularCheckRuleService.deleteById(id));
    }
}
