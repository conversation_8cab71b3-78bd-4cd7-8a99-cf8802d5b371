package com.yinshu.tiss.entity;


import cn.idev.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;

import com.yinshu.tiss.common.BaseEntity;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * 定期自动查岗规则表;
 * <AUTHOR> http://www.chiner.pro
 * @date : 2025-7-15
 */
@TableName(value="tocc_check_regular_rule")
@Data
public class RegularCheckRuleDTO extends BaseEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    /** 主键 */
    @TableField(value = "id")
    @ExcelProperty(value = "主键")
    @TableId(type = IdType.ASSIGN_ID)
    private String id ;
    /** 企业名称 */
    @TableField(value = "enterprise_name")
    @ExcelProperty(value = "企业名称")
    private String enterpriseName ;
    /** 查岗执行时间段 */
    @TableField(value = "check_exec_time_str")
    @ExcelProperty(value = "查岗执行时间段")
    private String checkExecTimeStr ;
    /** 查岗应答人 */
    @TableField(value = "check_responder")
    @ExcelProperty(value = "查岗应答人")
    private String checkResponder ;
    /** 查岗接收手机号 */
    @TableField(value = "check_receive_phone")
    @ExcelProperty(value = "查岗接收手机号")
    private String checkReceivePhone ;
    /** 查岗问题 */
    @TableField(value = "check_content")
    @ExcelProperty(value = "查岗问题")
    private String checkContent ;
    /** 状态 */
    @TableField(value = "status")
    @ExcelProperty(value = "状态")
    private String status ;


}
