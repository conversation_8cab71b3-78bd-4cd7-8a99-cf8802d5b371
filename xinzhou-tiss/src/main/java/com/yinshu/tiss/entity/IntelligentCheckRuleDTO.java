package com.yinshu.tiss.entity;

import cn.idev.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.yinshu.tiss.common.BaseEntity;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * 智能查岗规则表;
 * <AUTHOR> http://www.chiner.pro
 * @date : 2025-7-15
 */
@TableName(value="tocc_check_intelligent_rule")
@Data
public class IntelligentCheckRuleDTO extends BaseEntity implements Serializable{
    private static final long serialVersionUID = 1L;
    /** 主键ID */
    @TableField(value = "id")
    @TableId(type = IdType.ASSIGN_ID)
    private String id ;
    /** 查岗企业名称 */
    @TableField(value = "enterprise_name")
    private String enterpriseName ;
    /** 查岗执行条件（如多条规则可存JSON或文本） */
    @TableField(exist = false)
    private String checkCondition ;
    /** 查岗应答人 */
    @TableField(value = "responder")
    private String responder ;
    /** 查岗接收手机号 */
    @TableField(value = "receive_phone")
    private String receivePhone ;
    /** 查岗问题 */
    @TableField(value = "check_content")
    private String checkContent ;
    /** 状态（开启/停用等） */
    @TableField(value = "status")
    private String status ;

    /**
     * 近一个小时报警处理率低于
     */
    @TableField(value = "less_than")
    private Double lessThan;

    /**
     * 报警督办率大于
     */
    @TableField(value = "greater_than")
    private Double greaterThan ;


}
