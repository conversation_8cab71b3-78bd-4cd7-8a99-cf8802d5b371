package com.yinshu.tiss.common;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class RequestFilterBuilder {
    /**
     * 大数据构建查询参数
     *
     * @param jsonObject
     */
    public static void buildParam(JSONObject jsonObject) {
        StringBuilder filterSb = new StringBuilder();
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            // 跳过 s 和 n
            if ("s".equals(key) || "n".equals(key) || "pageSize".equals(key) || "pageNum".equals(key) || "pageNumber".equals(key)) {
                continue;
            }
            // 忽略空值
            if (value == null || "".equals(value)) {
                continue;
            }

            if (value instanceof List) {
                List array = (List) value;
                if (array.isEmpty()) { // 新增判断
                    continue;
                }
            }

            if (filterSb.length() > 0) {
                filterSb.append(" and ");
            }

            if (value instanceof ArrayList) {
                List array = (List) value;
                if (!array.isEmpty()) {
//                    if (filterSb.length() > 0) {
//                        filterSb.append("&");
//                    }

                    filterSb.append("(");
                    for (int i = 0; i < array.size(); i++) {
                        if (i > 0) filterSb.append(" or ");

                        Object item = array.get(i);
                        if (item instanceof String) {
                            filterSb.append(key).append("='").append(item).append("'");
                        } else {
                            filterSb.append(key).append("=").append(item);
                        }
                    }
                    filterSb.append(")");
                }
            } else if (value instanceof String) {
                if (value.toString().trim().isEmpty()) {
                    continue;
                }

                if (key.equals("startDate")) {
                    filterSb.append("dt").append(">='").append(value).append("'");
                } else if (key.equals("endDate")) {
                    filterSb.append("dt").append("<='").append(value).append("'");
                } else if (key.equals("startTime")) {
                    filterSb.append("dt").append(">='").append(value).append("'");
                }else if (key.equals("endTime")) {
                    filterSb.append("dt").append("<='").append(value).append("'");
                } else {
                    if ((key.equals("year") || key.equals("quarter")) && StringUtils.isNotEmpty(jsonObject.getString("year")) && StringUtils.isNotEmpty(jsonObject.getString("quarter")) )  {
                        continue;
                    }
                    if (key.equals("tabName")) {
                        continue;
                    }
                    filterSb.append(key).append("='").append(value).append("'");
                }


            } else {
                // 普通值处理
                if (value.toString().trim().isEmpty()) {
                    continue;
                }
//                if (filterSb.length() > 0) {
//                    filterSb.append("&");
//                }
                if ((key.equals("year") || key.equals("quarter")) && StringUtils.isNotEmpty(jsonObject.getString("year")) && StringUtils.isNotEmpty(jsonObject.getString("quarter")) )  {
                    continue;
                }
                filterSb.append(key).append("=").append(value);
            }

        }

        // 季度查询
        if (StringUtils.isNotEmpty(jsonObject.getString("year")) && StringUtils.isNotEmpty(jsonObject.getString("quarter")) )  {
            String formatted = String.format("_%02d", Integer.valueOf(jsonObject.getString("quarter"))); // 输出: 01
            formatted = jsonObject.getString("year")+formatted;
            filterSb.append("quarter = '"+ formatted+"'");
        }

        if (filterSb.length() > 0) {
            jsonObject.put("filter", filterSb.toString());
        }

    }

}
