package com.yinshu.tiss.vo;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * 车辆安全评估导出
 */
@Data
public class VehicleInfoVO {

    @ExcelProperty(value = "区县")
    @ColumnWidth(15)
    private String county;

    @ExcelProperty(value = "企业名称")
    private String company;

    @ExcelProperty(value = "车牌号")
    @ColumnWidth(30)
    private String carid;

    @ExcelProperty(value = "车牌颜色")
    private String license_plate_color;

    @ExcelProperty(value = "品牌")
    @ColumnWidth(30)
    private String brand;

    @ExcelProperty(value = "车型")
    @ColumnWidth(30)
    private String car_type;

    @ExcelProperty(value = "驾驶员")
    @ColumnWidth(20)
    private String driver;

    @ExcelProperty(value = "企业评分")
    private Double company_score;

    @ExcelProperty(value = "全市排名")
    private String rank_city;


}
