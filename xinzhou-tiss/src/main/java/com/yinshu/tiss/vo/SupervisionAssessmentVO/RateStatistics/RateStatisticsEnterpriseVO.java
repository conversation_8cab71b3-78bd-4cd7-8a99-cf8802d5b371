package com.yinshu.tiss.vo.SupervisionAssessmentVO.RateStatistics;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
public class RateStatisticsEnterpriseVO {
    @ExcelProperty(value = "区县")
    @ColumnWidth(15)
    private String district;

    @ExcelProperty(value = "企业名称")
    @ColumnWidth(15)
    private String enterprise;

    @ExcelProperty(value = "报警总数")
    @ColumnWidth(15)
    private String warn_num;

    @ExcelProperty(value = "处理总数")
    @ColumnWidth(15)
    private String handle_num;

    @ExcelProperty(value = "报警处理率")
    @ColumnWidth(15)
    private String warn_handle_rate;
}
