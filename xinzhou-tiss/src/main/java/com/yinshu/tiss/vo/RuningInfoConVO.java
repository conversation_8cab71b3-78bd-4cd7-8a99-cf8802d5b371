package com.yinshu.tiss.vo;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
public class RuningInfoConVO {
    @ExcelProperty(value = "区县")
    @ColumnWidth(15)
    private String name;

    @ExcelProperty(value = "上级机构")
    private String pname;

    @ExcelProperty(value = "企业数量")
    private String companynum;

    @ExcelProperty(value = "企业车辆数")
    private String carnum;

    @ExcelProperty(value = "营运车辆数")
    private String runingnum;

    @ExcelProperty(value = "总里程数")
    private String totalkm;

    @ExcelProperty(value = "日均行驶里程")
    private String kmavgday;
}
