package com.yinshu.tiss.vo;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
public class WarnInfoCompanyTjVO {
    @ExcelProperty(value = "区县")
    @ColumnWidth(15)
    private String county;

    @ExcelProperty(value = "企业名称")
    private String name;

    @ExcelProperty(value = "企业地址")
    private String address;

    @ExcelProperty(value = "企业类型")
    private String companytype;

    @ExcelProperty(value = "企业车辆数")
    private String carnum;

    @ExcelProperty(value = "营运车辆数")
    private String runcarnum;

    @ExcelProperty(value = "总里程数")
    private String totalkm;

    @ExcelProperty(value = "日均行驶里程")
    private String kmavgday;

}
