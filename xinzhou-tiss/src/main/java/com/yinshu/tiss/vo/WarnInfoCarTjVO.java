package com.yinshu.tiss.vo;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
public class WarnInfoCarTjVO {
    @ExcelProperty(value = "区县")
    @ColumnWidth(15)
    private String county;

    @ExcelProperty(value = "企业名称")
    private String company;

    @ExcelProperty(value = "车辆类型")
    private String cartype;

    @ExcelProperty(value = "车牌号")
    private String carid;

    @ExcelProperty(value = "车牌颜色")
    private String carcolor;

    @ExcelProperty(value = "品牌")
    private String carmodle;

    @ExcelProperty(value = "车型")
    private String carmodletype;

    @ExcelProperty(value = "总里程数")
    private String totalkm;

    @ExcelProperty(value = "日均行驶里程")
    private String kmavgday;
}
