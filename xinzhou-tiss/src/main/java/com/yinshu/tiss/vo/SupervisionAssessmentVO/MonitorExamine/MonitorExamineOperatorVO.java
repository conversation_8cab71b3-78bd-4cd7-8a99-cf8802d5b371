package com.yinshu.tiss.vo.SupervisionAssessmentVO.MonitorExamine;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * 监控考核导出
 */
@Data
public class MonitorExamineOperatorVO {

    //@ExcelProperty(value = "区县")
    //@ColumnWidth(15)
    //private String district;

    @ExcelProperty(value = "运营商")
    @ColumnWidth(15)
    private String operator;

    @ExcelProperty(value = "总分")
    @ColumnWidth(15)
    private String totalScore;

    @ExcelProperty(value = "车辆离线时长")
    @ColumnWidth(15)
    private String car_offline_time;

    @ExcelProperty(value = "平台连通率")
    @ColumnWidth(15)
    private String unicom_rate;

    @ExcelProperty(value = "车辆入网率")
    @ColumnWidth(15)
    private String car_network_access_rate;

    @ExcelProperty(value = "车辆上线率")
    @ColumnWidth(15)
    private String car_online_rate;

    @ExcelProperty(value = "轨迹完整率")
    @ColumnWidth(15)
    private String trajectory_rate;

    @ExcelProperty(value = "数据合格率")
    @ColumnWidth(15)
    private String qualified_rate;

    @ExcelProperty(value = "卫星定位漂移率")
    @ColumnWidth(16)
    private String gps_drift_rate;

    @ExcelProperty(value = "平台查岗响应率")
    @ColumnWidth(16)
    private String platform_check_responsivity;

    @ExcelProperty(value = "平均疲劳驾驶时长")
    @ColumnWidth(18)
    private String fatigue_driving_avg;

    @ExcelProperty(value = "禁行时段行驶次数")
    @ColumnWidth(18)
    private String prohibition_time_num;

    @ExcelProperty(value = "平均超速次数")
    @ColumnWidth(15)
    private String over_speed_avg;




}
