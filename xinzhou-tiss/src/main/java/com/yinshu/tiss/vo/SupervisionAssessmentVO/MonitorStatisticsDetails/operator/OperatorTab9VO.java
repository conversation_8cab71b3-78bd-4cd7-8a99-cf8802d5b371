package com.yinshu.tiss.vo.SupervisionAssessmentVO.MonitorStatisticsDetails.operator;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
public class OperatorTab9VO {
    @ExcelProperty(value = "区县")
    @ColumnWidth(15)
    private String district;

    @ExcelProperty(value = "所属企业")
    @ColumnWidth(15)
    private String operator;

    @ExcelProperty(value = "车牌号码")
    @ColumnWidth(15)
    private String vehicle_num;

    @ExcelProperty(value = "驾驶员")
    @ColumnWidth(15)
    private String driver;

    @ExcelProperty(value = "车牌颜色")
    @ColumnWidth(15)
    private String vehicle_color;

    @ExcelProperty(value = "报警处理率")
    @ColumnWidth(15)
    private String warn_handle_rate;
}
