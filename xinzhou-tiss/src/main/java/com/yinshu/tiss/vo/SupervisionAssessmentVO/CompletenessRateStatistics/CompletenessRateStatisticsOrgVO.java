package com.yinshu.tiss.vo.SupervisionAssessmentVO.CompletenessRateStatistics;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * 轨迹完整率 - 导出
 */
@Data
public class CompletenessRateStatisticsOrgVO {

    @ExcelProperty(value = "区县")
    @ColumnWidth(15)
    private String district;

    @ExcelProperty(value = "营运车辆数")
    @ColumnWidth(15)
    private String operations_num;

    @ExcelProperty(value = "营运总里程")
    @ColumnWidth(15)
    private String operations_mileage;

    @ExcelProperty(value = "轨迹完整率")
    @ColumnWidth(15)
    private String trajectory_rate;
}
