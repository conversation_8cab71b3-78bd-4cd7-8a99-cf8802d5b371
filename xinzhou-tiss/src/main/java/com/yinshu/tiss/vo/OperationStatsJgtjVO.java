package com.yinshu.tiss.vo;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 机构统计-营运数据
 */
@Data
public class OperationStatsJgtjVO {
    @ExcelProperty(value = "区县")
    private String county;

    @ExcelProperty(value = "车辆总数")
    private String total_vehicles;

    @ExcelProperty(value = "上线车辆数")
    private String online_vehicles;

    @ExcelProperty(value = "营运车辆数")
    private String operating_vehicles;

    @ExcelProperty(value = "行驶里程")
    private String driving_distance_km;

    @ExcelProperty(value = "营运里程")
    private String operating_distance_km;
}
