package com.yinshu.tiss.vo.SupervisionAssessmentVO.DrivingStatistics;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * 疲劳驾驶统计
 */
@Data
public class DrivingStatisticsEnterpriseVO {
    @ExcelProperty(value = "区县")
    @ColumnWidth(15)
    private String district;

    @ExcelProperty(value = "企业名称")
    @ColumnWidth(15)
    private String enterprise;

    @ExcelProperty(value = "疲劳驾驶总时长")
    @ColumnWidth(15)
    private String fatigue_driving_time;

    @ExcelProperty(value = "营运车辆数")
    @ColumnWidth(15)
    private String operations_num;

    @ExcelProperty(value = "平均疲劳驾驶时长")
    @ColumnWidth(15)
    private String fatigue_driving_avg;
}
