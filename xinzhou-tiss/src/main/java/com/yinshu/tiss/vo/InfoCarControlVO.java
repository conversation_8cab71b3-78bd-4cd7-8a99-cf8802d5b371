package com.yinshu.tiss.vo;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
public class InfoCarControlVO {
    @ExcelProperty(value = "区县")
    @ColumnWidth(15)
    private String county;

    @ExcelProperty(value = "所属企业")
    private String company;

    @ExcelProperty(value = "车牌号码")
    private String carid;

    @ExcelProperty(value = "车牌颜色")
    private String carcolor;

    @ExcelProperty(value = "报警类型")
    private String warntype;

    @ExcelProperty(value = "报警信息")
    private String warninfo;

    @ExcelProperty(value = "定位速度")
    private String warnv;

    @ExcelProperty(value = "持续时间")
    private String continuetime;

    @ExcelProperty(value = "处理状态")
    private String state;
}
