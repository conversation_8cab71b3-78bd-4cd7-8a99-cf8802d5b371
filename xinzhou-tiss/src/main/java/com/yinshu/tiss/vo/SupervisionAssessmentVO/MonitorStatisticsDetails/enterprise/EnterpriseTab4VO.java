package com.yinshu.tiss.vo.SupervisionAssessmentVO.MonitorStatisticsDetails.enterprise;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
public class EnterpriseTab4VO {
    @ExcelProperty(value = "区县")
    @ColumnWidth(15)
    private String district;

    @ExcelProperty(value = "所属企业")
    @ColumnWidth(15)
    private String enterprise;

    @ExcelProperty(value = "车牌号码")
    @ColumnWidth(15)
    private String vehicle_num;

    @ExcelProperty(value = "驾驶员")
    @ColumnWidth(15)
    private String driver;

    @ExcelProperty(value = "车牌颜色")
    @ColumnWidth(15)
    private String vehicle_color;

    @ExcelProperty(value = "卫星定位漂移率")
    @ColumnWidth(15)
    private String gps_drift_rate;
}
