package com.yinshu.tiss.vo.SupervisionAssessmentVO.OffLineStatistics;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * 离线车辆-导出
 */
@Data
public class OffLineStatisticsVO {
    @ExcelProperty(value = "企业名称")
    @ColumnWidth(15)
    private String enterprise;

    @ExcelProperty(value = "车牌号")
    @ColumnWidth(15)
    private String vehicle_num;

    @ExcelProperty(value = "车牌颜色")
    @ColumnWidth(15)
    private String vehicle_color;

    @ExcelProperty(value = "品牌")
    @ColumnWidth(15)
    private String brand;

    @ExcelProperty(value = "车型")
    @ColumnWidth(15)
    private String vehicle_model;

    @ExcelProperty(value = "驾驶员")
    @ColumnWidth(15)
    private String driver;

    @ExcelProperty(value = "最后在线时间")
    @ColumnWidth(15)
    private String last_online;

    @ExcelProperty(value = "离线时长")
    @ColumnWidth(15)
    private String offline_time;
}
