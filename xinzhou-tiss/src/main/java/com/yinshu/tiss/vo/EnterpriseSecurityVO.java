package com.yinshu.tiss.vo;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
public class EnterpriseSecurityVO {

    @ExcelProperty(value = "区县")
    @ColumnWidth(15)
    private String county;

    @ExcelProperty(value = "企业名称")
    private String company;

    @ExcelProperty(value = "企业地址")
    private String address;

    @ExcelProperty(value = "法人")
    private String legal;

    @ExcelProperty(value = "联系电话")
    private String tel;

    @ExcelProperty(value = "企业评分")
    private Double company_score;

    @ExcelProperty(value = "全市排名")
    private String rank;

    @ExcelProperty(value = "车辆总数")
    private String carsum;

    @ExcelProperty(value = "入网数")
    private String connected_cars;

    @ExcelProperty(value = "总行驶里程")
    private String total_mileage;

}
