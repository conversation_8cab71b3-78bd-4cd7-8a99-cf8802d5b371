package com.yinshu.tiss.vo;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * 车辆统计-安全数据
 */
@Data
public class SafetyStatsCltjVO {
    @ExcelProperty(value = "企业名称")
    private String company;

    @ExcelProperty(value = "车牌号")
    private String car_number;

    @ExcelProperty(value = "报警总数")
    private String alarm_total_num;

    @ExcelProperty(value = "百公里报警数")
    private String alarm_per_100km;

    @ExcelProperty(value = "报警处理率")
    private String alarm_rate;

    @ExcelProperty(value = "督办率")
    private String supervise_rate;

    @ExcelProperty(value = "督办完成率")
    private String supervise_finish_rate;

    @ExcelProperty(value = "超速预警")
    private String over_speed_warn;

    @ExcelProperty(value = "疲劳驾驶预警")
    private String fatigue_drive_warn;


    @ExcelProperty(value = "异常聚集预警")
    private String abnormal_gather_warn;

    @ExcelProperty(value = "在线时长过长预警")
    private String online_overtime_warn;

    @ExcelProperty(value = "长期不在线预警")
    private String offline_long_warn;

    @ExcelProperty(value = "非营运时段行驶预警")
    private String non_op_hours_warn;
}
