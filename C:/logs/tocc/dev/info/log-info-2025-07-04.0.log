2025-07-04 22:56:11.699 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-04 22:56:11.705 [main] INFO  com.yinshu.StartApplication - Starting StartApplication using Java 21.0.6 on xiaoxiongxueJavadeMacBook-Air.local with PID 3677 (/Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot/tocc-admin-all-starter/target/classes started by javaxiaobear in /Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot)
2025-07-04 22:56:11.706 [main] INFO  com.yinshu.StartApplication - The following 1 profile is active: "dev"
2025-07-04 22:56:12.161 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-04 22:56:12.162 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 22:56:12.181 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-07-04 22:56:12.401 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 22:56:12.404 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$9166129b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 22:56:12.416 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 22:56:12.613 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9091 (http)
2025-07-04 22:56:12.617 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9091"]
2025-07-04 22:56:12.617 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-04 22:56:12.617 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-04 22:56:12.669 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring embedded WebApplicationContext
2025-07-04 22:56:12.669 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 944 ms
2025-07-04 22:56:12.882 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [syncDb] success
2025-07-04 22:56:12.882 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [syncDb]
2025-07-04 22:56:15.988 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/**'] with [org.springframework.security.web.session.DisableEncodeUrlFilter@230dd372, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@663cc8c9, org.springframework.security.web.context.SecurityContextPersistenceFilter@34a482d0, org.springframework.security.web.header.HeaderWriterFilter@7925e772, org.springframework.web.filter.CorsFilter@51c15508, org.springframework.security.web.authentication.logout.LogoutFilter@77d42ed7, com.yinshu.sys.security.JwtAuthenticationTokenFilter@7d3815f7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4f9e9c21, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@********, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@164cad52, org.springframework.security.web.session.SessionManagementFilter@648f48d3, org.springframework.security.web.access.ExceptionTranslationFilter@3891db3a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@77a1df4d]
2025-07-04 22:56:16.300 [main] INFO  org.ehcache.core.EhcacheManager - Cache 's_setting' created in EhcacheManager.
2025-07-04 22:56:16.306 [main] INFO  org.ehcache.core.EhcacheManager - Cache 'road_network' created in EhcacheManager.
2025-07-04 22:56:16.413 [main] WARN  o.s.b.a.freemarker.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-04 22:56:16.496 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9091"]
2025-07-04 22:56:16.510 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9091 (http) with context path '/tocc'
2025-07-04 22:56:16.511 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Starting...
2025-07-04 22:56:16.511 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@bda4cbe]]
2025-07-04 22:56:16.511 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Started.
2025-07-04 22:56:16.520 [main] INFO  com.yinshu.StartApplication - Started StartApplication in 5.029 seconds (JVM running for 6.292)
2025-07-04 22:56:31.461 [http-nio-9091-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04 22:56:31.461 [http-nio-9091-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-04 22:56:31.463 [http-nio-9091-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-04 22:56:31.488 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/getKaptchaCode [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM1ODMxNjcsInVzZXJJZCI6IjEifQ.QlSo4safRLpkEswYimxZgmA8XaK6KTHN5Nq1JmV03FA
2025-07-04 22:56:35.742 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/login [jwt.token]null
2025-07-04 22:56:35.762 [http-nio-9091-exec-4] INFO  o.s.s.a.AnnotationAsyncExecutionInterceptor - More than one TaskExecutor bean found within the context, and none is named 'taskExecutor'. Mark one of them as primary or name it 'taskExecutor' (possibly as an alias) in order to use it for async processing: [clientInboundChannelExecutor, clientOutboundChannelExecutor, brokerChannelExecutor]
2025-07-04 22:56:37.482 [SimpleAsyncTaskExecutor-1] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1,syncDb} inited
2025-07-04 22:56:40.134 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/integrated-monitor/getRoadTunnel [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDA5OTgsInVzZXJJZCI6IjEifQ.mb8yu5Q7Zc8CfBjRGTLHppYxc-spddauyrRX9OzVY4I
2025-07-04 22:56:40.135 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/integrated-monitor/getRoadMileage [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDA5OTgsInVzZXJJZCI6IjEifQ.mb8yu5Q7Zc8CfBjRGTLHppYxc-spddauyrRX9OzVY4I
2025-07-04 22:56:40.135 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/integrated-monitor/getBaseInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDA5OTgsInVzZXJJZCI6IjEifQ.mb8yu5Q7Zc8CfBjRGTLHppYxc-spddauyrRX9OzVY4I
2025-07-04 22:56:40.136 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/integrated-monitor/getRoadBridge [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDA5OTgsInVzZXJJZCI6IjEifQ.mb8yu5Q7Zc8CfBjRGTLHppYxc-spddauyrRX9OzVY4I
2025-07-04 22:56:40.141 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/integrated-monitor/getDockingData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDA5OTgsInVzZXJJZCI6IjEifQ.mb8yu5Q7Zc8CfBjRGTLHppYxc-spddauyrRX9OzVY4I
2025-07-04 22:56:41.150 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://10.8.0.212:8081/api/100495/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO | {"filter":"id = '2025-06-12_10'"}
2025-07-04 22:56:41.269 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://10.8.0.212:8081/api/100496/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO | {"filter":"id = '2025-06'"}
2025-07-04 22:56:41.282 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://10.8.0.212:8081/api/100497/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO | {"filter":"id = '2025-06'"}
2025-07-04 22:56:41.316 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://10.8.0.212:8081/api/100494/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO | {"filter":"id = '2025-07'"}
2025-07-04 22:56:41.448 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://10.8.0.212:8081/api/100458/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO | {"filter":""}
2025-07-04 22:56:51.194 [http-nio-9091-exec-4] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:56:51.194 [http-nio-9091-exec-4] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:56:51.275 [http-nio-9091-exec-3] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:56:51.276 [http-nio-9091-exec-3] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:56:51.287 [http-nio-9091-exec-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:56:51.288 [http-nio-9091-exec-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:56:51.321 [http-nio-9091-exec-10] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:56:51.322 [http-nio-9091-exec-10] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:56:51.454 [http-nio-9091-exec-2] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:56:51.454 [http-nio-9091-exec-2] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:56:52.683 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDA5OTgsInVzZXJJZCI6IjEifQ.mb8yu5Q7Zc8CfBjRGTLHppYxc-spddauyrRX9OzVY4I
2025-07-04 22:56:52.698 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://10.8.0.212:8081/api/100454/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO | {"filter":"type = '公交车' ","type":"公交车"}
2025-07-04 22:57:01.199 [http-nio-9091-exec-4] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:57:01.200 [http-nio-9091-exec-4] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:57:01.281 [http-nio-9091-exec-3] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:57:01.281 [http-nio-9091-exec-3] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:57:01.295 [http-nio-9091-exec-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:57:01.295 [http-nio-9091-exec-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:57:01.327 [http-nio-9091-exec-10] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:57:01.327 [http-nio-9091-exec-10] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:57:01.458 [http-nio-9091-exec-2] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:57:01.458 [http-nio-9091-exec-2] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:57:02.702 [http-nio-9091-exec-9] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:57:02.703 [http-nio-9091-exec-9] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:57:11.205 [http-nio-9091-exec-4] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:57:11.206 [http-nio-9091-exec-4] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:57:11.286 [http-nio-9091-exec-3] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:57:11.287 [http-nio-9091-exec-3] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:57:11.300 [http-nio-9091-exec-1] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:57:11.300 [http-nio-9091-exec-1] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:57:11.332 [http-nio-9091-exec-10] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:57:11.332 [http-nio-9091-exec-10] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:57:11.462 [http-nio-9091-exec-2] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:57:11.462 [http-nio-9091-exec-2] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:57:12.708 [http-nio-9091-exec-9] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:57:12.709 [http-nio-9091-exec-9] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:57:15.598 [MessageBroker-1] INFO  o.s.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-04 22:57:21.219 [http-nio-9091-exec-4] ERROR com.yinshu.exception.GlobalExceptionHandler - 全局异常捕获>>>>>I/O error on POST request for "http://10.8.0.212:8081/api/100495/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://10.8.0.212:8081/api/100495/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:791)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:717)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:443)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:70)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:49)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:76)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:41)
	at com.yinshu.toms.service.impl.IntegratedMonitorServiceImpl.getDockingData(IntegratedMonitorServiceImpl.java:75)
	at com.yinshu.toms.controller.IntegratedMonitorController.getDockingData(IntegratedMonitorController.java:42)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.github.xiaoymin.knife4j.extend.filter.basic.ServletSecurityBasicAuthFilter.doFilter(ServletSecurityBasicAuthFilter.java:56)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.yinshu.sys.security.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:64)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.net.SocketException: Malformed reply from SOCKS server
	at java.base/java.net.SocksSocketImpl.readSocksReply(SocksSocketImpl.java:128)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:429)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:782)
	... 106 common frames omitted
2025-07-04 22:57:21.292 [http-nio-9091-exec-3] ERROR com.yinshu.exception.GlobalExceptionHandler - 全局异常捕获>>>>>I/O error on POST request for "http://10.8.0.212:8081/api/100496/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://10.8.0.212:8081/api/100496/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:791)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:717)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:443)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:70)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:49)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:76)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:41)
	at com.yinshu.toms.service.impl.IntegratedMonitorServiceImpl.getRoadBridge(IntegratedMonitorServiceImpl.java:134)
	at com.yinshu.toms.controller.IntegratedMonitorController.getRoadBridge(IntegratedMonitorController.java:64)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.github.xiaoymin.knife4j.extend.filter.basic.ServletSecurityBasicAuthFilter.doFilter(ServletSecurityBasicAuthFilter.java:56)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.yinshu.sys.security.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:64)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.net.SocketException: Malformed reply from SOCKS server
	at java.base/java.net.SocksSocketImpl.readSocksReply(SocksSocketImpl.java:128)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:429)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:782)
	... 106 common frames omitted
2025-07-04 22:57:21.307 [http-nio-9091-exec-1] ERROR com.yinshu.exception.GlobalExceptionHandler - 全局异常捕获>>>>>I/O error on POST request for "http://10.8.0.212:8081/api/100497/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://10.8.0.212:8081/api/100497/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:791)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:717)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:443)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:70)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:49)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:76)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:41)
	at com.yinshu.toms.service.impl.IntegratedMonitorServiceImpl.getRoadTunnel(IntegratedMonitorServiceImpl.java:148)
	at com.yinshu.toms.controller.IntegratedMonitorController.getRoadTunnel(IntegratedMonitorController.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.github.xiaoymin.knife4j.extend.filter.basic.ServletSecurityBasicAuthFilter.doFilter(ServletSecurityBasicAuthFilter.java:56)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.yinshu.sys.security.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:64)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.net.SocketException: Malformed reply from SOCKS server
	at java.base/java.net.SocksSocketImpl.readSocksReply(SocksSocketImpl.java:128)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:429)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:782)
	... 106 common frames omitted
2025-07-04 22:57:21.338 [http-nio-9091-exec-10] ERROR com.yinshu.exception.GlobalExceptionHandler - 全局异常捕获>>>>>I/O error on POST request for "http://10.8.0.212:8081/api/100494/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://10.8.0.212:8081/api/100494/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:791)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:717)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:443)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:70)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:49)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:76)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:41)
	at com.yinshu.toms.service.impl.IntegratedMonitorServiceImpl.getBaseInfo(IntegratedMonitorServiceImpl.java:60)
	at com.yinshu.toms.controller.IntegratedMonitorController.getBaseInfo(IntegratedMonitorController.java:31)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.github.xiaoymin.knife4j.extend.filter.basic.ServletSecurityBasicAuthFilter.doFilter(ServletSecurityBasicAuthFilter.java:56)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.yinshu.sys.security.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:64)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.net.SocketException: Malformed reply from SOCKS server
	at java.base/java.net.SocksSocketImpl.readSocksReply(SocksSocketImpl.java:128)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:429)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:782)
	... 106 common frames omitted
2025-07-04 22:57:21.468 [http-nio-9091-exec-2] ERROR com.yinshu.exception.GlobalExceptionHandler - 全局异常捕获>>>>>I/O error on POST request for "http://10.8.0.212:8081/api/100458/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://10.8.0.212:8081/api/100458/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:791)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:717)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:443)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:70)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:49)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:76)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:41)
	at com.yinshu.toms.service.impl.IntegratedMonitorServiceImpl.getRoadMileage(IntegratedMonitorServiceImpl.java:93)
	at com.yinshu.toms.controller.IntegratedMonitorController.getRoadMileage(IntegratedMonitorController.java:53)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.github.xiaoymin.knife4j.extend.filter.basic.ServletSecurityBasicAuthFilter.doFilter(ServletSecurityBasicAuthFilter.java:56)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.yinshu.sys.security.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:64)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.net.SocketException: Malformed reply from SOCKS server
	at java.base/java.net.SocksSocketImpl.readSocksReply(SocksSocketImpl.java:128)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:429)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:782)
	... 106 common frames omitted
2025-07-04 22:57:22.713 [http-nio-9091-exec-9] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:57:22.714 [http-nio-9091-exec-9] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:57:32.722 [http-nio-9091-exec-9] ERROR com.yinshu.exception.GlobalExceptionHandler - 全局异常捕获>>>>>I/O error on POST request for "http://10.8.0.212:8081/api/100454/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://10.8.0.212:8081/api/100454/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:791)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:717)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:443)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:70)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:49)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:76)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:41)
	at com.yinshu.toms.service.impl.CityTrafficServiceImpl.getBasicData(CityTrafficServiceImpl.java:124)
	at com.yinshu.toms.controller.CityTrafficController.getBasicData(CityTrafficController.java:58)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.github.xiaoymin.knife4j.extend.filter.basic.ServletSecurityBasicAuthFilter.doFilter(ServletSecurityBasicAuthFilter.java:56)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.yinshu.sys.security.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:64)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.net.SocketException: Malformed reply from SOCKS server
	at java.base/java.net.SocksSocketImpl.readSocksReply(SocksSocketImpl.java:128)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:429)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:782)
	... 106 common frames omitted
2025-07-04 22:58:07.238 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getSuspectedBlackCarData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDA5OTgsInVzZXJJZCI6IjEifQ.mb8yu5Q7Zc8CfBjRGTLHppYxc-spddauyrRX9OzVY4I
2025-07-04 22:58:07.260 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://10.8.0.212:8081/api/100476/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO | {"filter":"hour != null and type = '疑似黑车' ","s":0,"pageSize":10,"pageNum":1,"n":10}
2025-07-04 22:58:08.867 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getSuspectedBlackCarData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDA5OTgsInVzZXJJZCI6IjEifQ.mb8yu5Q7Zc8CfBjRGTLHppYxc-spddauyrRX9OzVY4I
2025-07-04 22:58:08.871 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://10.8.0.212:8081/api/100476/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO | {"filter":"hour != null and type = '疑似黑车' ","s":0,"pageSize":10,"pageNum":1,"n":10}
2025-07-04 22:58:17.270 [http-nio-9091-exec-6] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:58:17.272 [http-nio-9091-exec-6] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:58:18.876 [http-nio-9091-exec-8] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:58:18.876 [http-nio-9091-exec-8] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:58:27.278 [http-nio-9091-exec-6] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:58:27.279 [http-nio-9091-exec-6] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:58:28.882 [http-nio-9091-exec-8] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:58:28.883 [http-nio-9091-exec-8] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:58:37.285 [http-nio-9091-exec-6] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:58:37.286 [http-nio-9091-exec-6] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:58:38.888 [http-nio-9091-exec-8] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:58:38.889 [http-nio-9091-exec-8] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:58:47.294 [http-nio-9091-exec-6] ERROR com.yinshu.exception.GlobalExceptionHandler - 全局异常捕获>>>>>I/O error on POST request for "http://10.8.0.212:8081/api/100476/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://10.8.0.212:8081/api/100476/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:791)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:717)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:443)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:70)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:49)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:76)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:41)
	at com.yinshu.toms.service.impl.CityTrafficServiceImpl.getSuspectedBlackCarData(CityTrafficServiceImpl.java:382)
	at com.yinshu.toms.controller.CityTrafficController.getSuspectedBlackCarData(CityTrafficController.java:148)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.github.xiaoymin.knife4j.extend.filter.basic.ServletSecurityBasicAuthFilter.doFilter(ServletSecurityBasicAuthFilter.java:56)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.yinshu.sys.security.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:64)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.net.SocketException: Malformed reply from SOCKS server
	at java.base/java.net.SocksSocketImpl.readSocksReply(SocksSocketImpl.java:128)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:429)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:782)
	... 106 common frames omitted
2025-07-04 22:58:48.896 [http-nio-9091-exec-8] ERROR com.yinshu.exception.GlobalExceptionHandler - 全局异常捕获>>>>>I/O error on POST request for "http://10.8.0.212:8081/api/100476/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://10.8.0.212:8081/api/100476/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:791)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:717)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:443)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:70)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:49)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:76)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:41)
	at com.yinshu.toms.service.impl.CityTrafficServiceImpl.getSuspectedBlackCarData(CityTrafficServiceImpl.java:382)
	at com.yinshu.toms.controller.CityTrafficController.getSuspectedBlackCarData(CityTrafficController.java:148)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.github.xiaoymin.knife4j.extend.filter.basic.ServletSecurityBasicAuthFilter.doFilter(ServletSecurityBasicAuthFilter.java:56)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.yinshu.sys.security.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:64)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.net.SocketException: Malformed reply from SOCKS server
	at java.base/java.net.SocksSocketImpl.readSocksReply(SocksSocketImpl.java:128)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:429)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:782)
	... 106 common frames omitted
2025-07-04 22:59:45.456 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDA5OTgsInVzZXJJZCI6IjEifQ.mb8yu5Q7Zc8CfBjRGTLHppYxc-spddauyrRX9OzVY4I
2025-07-04 22:59:45.468 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://10.8.0.212:8081/api/100454/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO | {"filter":"type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-04 22:59:46.716 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDA5OTgsInVzZXJJZCI6IjEifQ.mb8yu5Q7Zc8CfBjRGTLHppYxc-spddauyrRX9OzVY4I
2025-07-04 22:59:46.722 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://10.8.0.212:8081/api/100476/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO | {"filter":"hour != null and type = '预警车辆' and vehicle_type = '网约车'  and dt >= '2025-07-04' and dt <= '2025-07-04' ","dt":["2025-07-04","2025-07-04"],"s":0,"pageSize":10,"vehicle_type":"网约车","pageNum":1,"n":10}
2025-07-04 22:59:48.181 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDA5OTgsInVzZXJJZCI6IjEifQ.mb8yu5Q7Zc8CfBjRGTLHppYxc-spddauyrRX9OzVY4I
2025-07-04 22:59:48.187 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://10.8.0.212:8081/api/100476/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO | {"filter":"hour != null and type = '预警车辆' and vehicle_type = '网约车'  and dt >= '2025-07-04' and dt <= '2025-07-04' ","dt":["2025-07-04","2025-07-04"],"s":0,"pageSize":10,"vehicle_type":"网约车","pageNum":1,"n":10}
2025-07-04 22:59:51.873 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDA5OTgsInVzZXJJZCI6IjEifQ.mb8yu5Q7Zc8CfBjRGTLHppYxc-spddauyrRX9OzVY4I
2025-07-04 22:59:51.880 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://10.8.0.212:8081/api/100476/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO | {"filter":"hour != null and type = '预警车辆' and vehicle_type = '网约车'  and dt >= '2025-07-01' and dt <= '2025-08-12' ","dt":["2025-07-01","2025-08-12"],"s":0,"dtExtraYear":"2025","pageSize":10,"vehicle_type":"网约车","pageNum":1,"n":10}
2025-07-04 22:59:55.475 [http-nio-9091-exec-10] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:59:55.475 [http-nio-9091-exec-10] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:59:55.982 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDA5OTgsInVzZXJJZCI6IjEifQ.mb8yu5Q7Zc8CfBjRGTLHppYxc-spddauyrRX9OzVY4I
2025-07-04 22:59:55.988 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://10.8.0.212:8081/api/100476/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO | {"filter":"hour != null and type = '预警车辆' and vehicle_type = '网约车'  and dt >= '2025-07-01' and dt <= '2025-08-12' ","dt":["2025-07-01","2025-08-12"],"s":0,"dtExtraYear":"2025","pageSize":10,"vehicle_type":"网约车","pageNum":1,"n":10}
2025-07-04 22:59:56.742 [http-nio-9091-exec-9] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:59:56.742 [http-nio-9091-exec-9] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 22:59:58.194 [http-nio-9091-exec-7] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 22:59:58.194 [http-nio-9091-exec-7] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 23:00:01.886 [http-nio-9091-exec-5] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 23:00:01.887 [http-nio-9091-exec-5] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 23:00:05.480 [http-nio-9091-exec-10] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 23:00:05.481 [http-nio-9091-exec-10] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 23:00:05.995 [http-nio-9091-exec-3] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 23:00:05.995 [http-nio-9091-exec-3] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 23:00:06.746 [http-nio-9091-exec-9] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 23:00:06.746 [http-nio-9091-exec-9] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 23:00:08.199 [http-nio-9091-exec-7] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 23:00:08.200 [http-nio-9091-exec-7] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 23:00:11.894 [http-nio-9091-exec-5] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 23:00:11.895 [http-nio-9091-exec-5] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 23:00:15.486 [http-nio-9091-exec-10] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 23:00:15.486 [http-nio-9091-exec-10] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 23:00:15.999 [http-nio-9091-exec-3] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 23:00:15.999 [http-nio-9091-exec-3] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 23:00:16.750 [http-nio-9091-exec-9] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 23:00:16.750 [http-nio-9091-exec-9] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 23:00:18.205 [http-nio-9091-exec-7] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 23:00:18.206 [http-nio-9091-exec-7] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 23:00:21.901 [http-nio-9091-exec-5] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 23:00:21.901 [http-nio-9091-exec-5] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 23:00:25.494 [http-nio-9091-exec-10] ERROR com.yinshu.exception.GlobalExceptionHandler - 全局异常捕获>>>>>I/O error on POST request for "http://10.8.0.212:8081/api/100454/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://10.8.0.212:8081/api/100454/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:791)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:717)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:443)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:70)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:49)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:76)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:41)
	at com.yinshu.toms.service.impl.CityTrafficServiceImpl.getBasicData(CityTrafficServiceImpl.java:124)
	at com.yinshu.toms.controller.CityTrafficController.getBasicData(CityTrafficController.java:58)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.github.xiaoymin.knife4j.extend.filter.basic.ServletSecurityBasicAuthFilter.doFilter(ServletSecurityBasicAuthFilter.java:56)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.yinshu.sys.security.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:64)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.net.SocketException: Malformed reply from SOCKS server
	at java.base/java.net.SocksSocketImpl.readSocksReply(SocksSocketImpl.java:128)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:429)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:782)
	... 106 common frames omitted
2025-07-04 23:00:26.003 [http-nio-9091-exec-3] INFO  org.apache.http.impl.execchain.RetryExec - I/O exception (java.net.SocketException) caught when processing request to {}->http://10.8.0.212:8081: Malformed reply from SOCKS server
2025-07-04 23:00:26.004 [http-nio-9091-exec-3] INFO  org.apache.http.impl.execchain.RetryExec - Retrying request to {}->http://10.8.0.212:8081
2025-07-04 23:00:26.755 [http-nio-9091-exec-9] ERROR com.yinshu.exception.GlobalExceptionHandler - 全局异常捕获>>>>>I/O error on POST request for "http://10.8.0.212:8081/api/100476/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://10.8.0.212:8081/api/100476/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:791)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:717)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:443)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:70)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:49)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:76)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:41)
	at com.yinshu.toms.service.impl.CityTrafficServiceImpl.getVehicleMonitorWarning(CityTrafficServiceImpl.java:362)
	at com.yinshu.toms.controller.CityTrafficController.getVehicleMonitorWarning(CityTrafficController.java:138)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.github.xiaoymin.knife4j.extend.filter.basic.ServletSecurityBasicAuthFilter.doFilter(ServletSecurityBasicAuthFilter.java:56)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.yinshu.sys.security.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:64)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.net.SocketException: Malformed reply from SOCKS server
	at java.base/java.net.SocksSocketImpl.readSocksReply(SocksSocketImpl.java:128)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:429)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:782)
	... 106 common frames omitted
2025-07-04 23:00:28.211 [http-nio-9091-exec-7] ERROR com.yinshu.exception.GlobalExceptionHandler - 全局异常捕获>>>>>I/O error on POST request for "http://10.8.0.212:8081/api/100476/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://10.8.0.212:8081/api/100476/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:791)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:717)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:443)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:70)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:49)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:76)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:41)
	at com.yinshu.toms.service.impl.CityTrafficServiceImpl.getVehicleMonitorWarning(CityTrafficServiceImpl.java:362)
	at com.yinshu.toms.controller.CityTrafficController.getVehicleMonitorWarning(CityTrafficController.java:138)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.github.xiaoymin.knife4j.extend.filter.basic.ServletSecurityBasicAuthFilter.doFilter(ServletSecurityBasicAuthFilter.java:56)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.yinshu.sys.security.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:64)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.net.SocketException: Malformed reply from SOCKS server
	at java.base/java.net.SocksSocketImpl.readSocksReply(SocksSocketImpl.java:128)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:429)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:782)
	... 106 common frames omitted
2025-07-04 23:00:31.908 [http-nio-9091-exec-5] ERROR com.yinshu.exception.GlobalExceptionHandler - 全局异常捕获>>>>>I/O error on POST request for "http://10.8.0.212:8081/api/100476/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://10.8.0.212:8081/api/100476/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:791)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:717)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:443)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:70)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:49)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:76)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:41)
	at com.yinshu.toms.service.impl.CityTrafficServiceImpl.getVehicleMonitorWarning(CityTrafficServiceImpl.java:362)
	at com.yinshu.toms.controller.CityTrafficController.getVehicleMonitorWarning(CityTrafficController.java:138)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.github.xiaoymin.knife4j.extend.filter.basic.ServletSecurityBasicAuthFilter.doFilter(ServletSecurityBasicAuthFilter.java:56)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.yinshu.sys.security.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:64)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.net.SocketException: Malformed reply from SOCKS server
	at java.base/java.net.SocksSocketImpl.readSocksReply(SocksSocketImpl.java:128)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:429)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:782)
	... 106 common frames omitted
2025-07-04 23:00:36.009 [http-nio-9091-exec-3] ERROR com.yinshu.exception.GlobalExceptionHandler - 全局异常捕获>>>>>I/O error on POST request for "http://10.8.0.212:8081/api/100476/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://10.8.0.212:8081/api/100476/data.json": Malformed reply from SOCKS server; nested exception is java.net.SocketException: Malformed reply from SOCKS server
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:791)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:717)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:443)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:70)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:49)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:76)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:41)
	at com.yinshu.toms.service.impl.CityTrafficServiceImpl.getVehicleMonitorWarning(CityTrafficServiceImpl.java:362)
	at com.yinshu.toms.controller.CityTrafficController.getVehicleMonitorWarning(CityTrafficController.java:138)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.github.xiaoymin.knife4j.extend.filter.basic.ServletSecurityBasicAuthFilter.doFilter(ServletSecurityBasicAuthFilter.java:56)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.yinshu.sys.security.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:64)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.net.SocketException: Malformed reply from SOCKS server
	at java.base/java.net.SocksSocketImpl.readSocksReply(SocksSocketImpl.java:128)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:429)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:782)
	... 106 common frames omitted
2025-07-04 23:00:42.322 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/sys/user/pageList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDA5OTgsInVzZXJJZCI6IjEifQ.mb8yu5Q7Zc8CfBjRGTLHppYxc-spddauyrRX9OzVY4I
2025-07-04 23:00:42.322 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/sys/unit/getUnitTree [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDA5OTgsInVzZXJJZCI6IjEifQ.mb8yu5Q7Zc8CfBjRGTLHppYxc-spddauyrRX9OzVY4I
2025-07-04 23:00:42.372 [http-nio-9091-exec-6] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/sys/user/pageList?keyword=&unitId=&userName=&pageNumber=1&pageSize=10&total=0
2025-07-04 23:00:42.372 [http-nio-9091-exec-8] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/sys/unit/getUnitTree
2025-07-04 23:00:42.372 [http-nio-9091-exec-6] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@356b718, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-04 23:00:42.372 [http-nio-9091-exec-8] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@1d9507b6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-04 23:00:44.405 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/sys/unit/getUnitTree [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDA5OTgsInVzZXJJZCI6IjEifQ.mb8yu5Q7Zc8CfBjRGTLHppYxc-spddauyrRX9OzVY4I
2025-07-04 23:00:44.406 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/sys/unit/pageList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDA5OTgsInVzZXJJZCI6IjEifQ.mb8yu5Q7Zc8CfBjRGTLHppYxc-spddauyrRX9OzVY4I
2025-07-04 23:00:44.411 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/sys/unit/pageList?parentId=&uname=&status=&pageNumber=1&pageSize=7&total=0
2025-07-04 23:00:44.411 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@5dc55fac, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-04 23:00:44.411 [http-nio-9091-exec-10] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/sys/unit/getUnitTree
2025-07-04 23:00:44.412 [http-nio-9091-exec-10] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@499b287a, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-04 23:00:47.635 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/sys/setting/pageList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDA5OTgsInVzZXJJZCI6IjEifQ.mb8yu5Q7Zc8CfBjRGTLHppYxc-spddauyrRX9OzVY4I
2025-07-04 23:00:47.642 [http-nio-9091-exec-5] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/sys/setting/pageList?queryKeyword=&pageNumber=1&pageSize=10&total=0
2025-07-04 23:00:47.642 [http-nio-9091-exec-5] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@2183bdee, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-04 23:00:48.302 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/sys/monitor/getServerInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDA5OTgsInVzZXJJZCI6IjEifQ.mb8yu5Q7Zc8CfBjRGTLHppYxc-spddauyrRX9OzVY4I
2025-07-04 23:00:48.307 [http-nio-9091-exec-1] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/sys/monitor/getServerInfo
2025-07-04 23:00:48.307 [http-nio-9091-exec-1] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3ac31a91, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-04 23:00:49.105 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/sys/setting/pageList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDA5OTgsInVzZXJJZCI6IjEifQ.mb8yu5Q7Zc8CfBjRGTLHppYxc-spddauyrRX9OzVY4I
2025-07-04 23:00:49.110 [http-nio-9091-exec-4] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/sys/setting/pageList?queryKeyword=&pageNumber=1&pageSize=10&total=0
2025-07-04 23:00:49.110 [http-nio-9091-exec-4] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@66062938, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-04 23:00:51.318 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/sys/setting/getById/7265188714321948672 [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDA5OTgsInVzZXJJZCI6IjEifQ.mb8yu5Q7Zc8CfBjRGTLHppYxc-spddauyrRX9OzVY4I
2025-07-04 23:00:51.322 [http-nio-9091-exec-6] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/sys/setting/getById/7265188714321948672?loading=false
2025-07-04 23:00:51.322 [http-nio-9091-exec-6] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@197bab8e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-04 23:00:54.216 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/sys/setting/update [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDA5OTgsInVzZXJJZCI6IjEifQ.mb8yu5Q7Zc8CfBjRGTLHppYxc-spddauyrRX9OzVY4I
2025-07-04 23:00:54.220 [http-nio-9091-exec-10] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/sys/setting/update
2025-07-04 23:00:54.220 [http-nio-9091-exec-10] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@151943c3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-04 23:00:55.098 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/sys/setting/pageList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDA5OTgsInVzZXJJZCI6IjEifQ.mb8yu5Q7Zc8CfBjRGTLHppYxc-spddauyrRX9OzVY4I
2025-07-04 23:00:55.104 [http-nio-9091-exec-7] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/sys/setting/pageList?queryKeyword=&pageNumber=1&pageSize=10&total=19
2025-07-04 23:00:55.104 [http-nio-9091-exec-7] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@1293e837, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-04 23:00:59.535 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/logout [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDA5OTgsInVzZXJJZCI6IjEifQ.mb8yu5Q7Zc8CfBjRGTLHppYxc-spddauyrRX9OzVY4I
2025-07-04 23:00:59.582 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/getKaptchaCode [jwt.token]null
2025-07-04 23:01:03.639 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/login [jwt.token]null
2025-07-04 23:01:05.058 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/integrated-monitor/getRoadMileage [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:01:05.058 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/integrated-monitor/getDockingData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:01:05.060 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/integrated-monitor/getBaseInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:01:05.060 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/integrated-monitor/getRoadTunnel [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:01:05.058 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/integrated-monitor/getRoadBridge [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:01:05.064 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100497/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO | {"filter":"id = '2025-06'"}
2025-07-04 23:01:05.064 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100495/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO | {"filter":"id = '2025-06-12_10'"}
2025-07-04 23:01:05.064 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100458/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO | {"filter":""}
2025-07-04 23:01:05.066 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100494/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO | {"filter":"id = '2025-07'"}
2025-07-04 23:01:05.066 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100496/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO | {"filter":"id = '2025-06'"}
2025-07-04 23:01:05.410 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:345 | http://yinshu.iok.la:8081/api/100494/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO
2025-07-04 23:01:05.416 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/auth | {"appid":"tocc_api","time":"TFa1sXAouXOK7UurtXFwvQ=="}
2025-07-04 23:01:05.443 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:379 | http://yinshu.iok.la:8081/api/100495/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO
2025-07-04 23:01:05.447 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/auth | {"appid":"tocc_api","time":"tNLRHX8sIMMSvfZXx9pl3Q=="}
2025-07-04 23:01:05.449 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:383 | http://yinshu.iok.la:8081/api/100496/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO
2025-07-04 23:01:05.451 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/auth | {"appid":"tocc_api","time":"TKD73m1MXhDiZq6V9Mv4Yw=="}
2025-07-04 23:01:05.467 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:403 | http://yinshu.iok.la:8081/api/100458/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO
2025-07-04 23:01:05.469 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/auth | {"appid":"tocc_api","time":"vDowIXrJ1cfLWOVadH6i1Q=="}
2025-07-04 23:01:05.474 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:410 | http://yinshu.iok.la:8081/api/100497/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO
2025-07-04 23:01:05.477 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/auth | {"appid":"tocc_api","time":"oSuLY2gF4d+VU2UO7VM59g=="}
2025-07-04 23:01:05.596 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:149 | http://yinshu.iok.la:8081/api/auth
2025-07-04 23:01:05.600 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:149 | http://yinshu.iok.la:8081/api/auth
2025-07-04 23:01:05.604 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100496/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO&token=e5fdube5tb0aoeYUN3cPyCNeMYBjD0bNm | {"filter":"id = '2025-06'"}
2025-07-04 23:01:05.604 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100495/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO&token=e5fdube5tb0aocOzn6BHINnqNHkEtXFlE | {"filter":"id = '2025-06-12_10'"}
2025-07-04 23:01:05.624 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:155 | http://yinshu.iok.la:8081/api/auth
2025-07-04 23:01:05.624 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:208 | http://yinshu.iok.la:8081/api/auth
2025-07-04 23:01:05.626 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100494/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO&token=e5fdube5tb0ao6ptqjkAk3VvPui1HUUvO | {"filter":"id = '2025-07'"}
2025-07-04 23:01:05.626 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100458/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO&token=e5fdube5tb0aocEoTVP3YgKmhSGb7hcvX | {"filter":""}
2025-07-04 23:01:05.631 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:154 | http://yinshu.iok.la:8081/api/auth
2025-07-04 23:01:05.632 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100497/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"id = '2025-06'"}
2025-07-04 23:01:05.788 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:184 | http://yinshu.iok.la:8081/api/100495/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO&token=e5fdube5tb0aocOzn6BHINnqNHkEtXFlE
2025-07-04 23:01:05.795 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:191 | http://yinshu.iok.la:8081/api/100496/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO&token=e5fdube5tb0aoeYUN3cPyCNeMYBjD0bNm
2025-07-04 23:01:05.810 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:184 | http://yinshu.iok.la:8081/api/100494/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO&token=e5fdube5tb0ao6ptqjkAk3VvPui1HUUvO
2025-07-04 23:01:05.841 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:215 | http://yinshu.iok.la:8081/api/100458/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO&token=e5fdube5tb0aocEoTVP3YgKmhSGb7hcvX
2025-07-04 23:01:05.902 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:270 | http://yinshu.iok.la:8081/api/100497/data.json?&token=e5fdube5tb0aoxpzq07NTqgmwl5TOelcO&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:01:07.038 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopping...
2025-07-04 23:01:07.038 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@bda4cbe]]
2025-07-04 23:01:07.038 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopped.
2025-07-04 23:01:07.053 [SpringApplicationShutdownHook] INFO  org.ehcache.core.EhcacheManager - Cache 's_setting' removed from EhcacheManager.
2025-07-04 23:01:07.053 [SpringApplicationShutdownHook] INFO  org.ehcache.core.EhcacheManager - Cache 'road_network' removed from EhcacheManager.
2025-07-04 23:01:07.060 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-04 23:01:07.061 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-04 23:01:07.062 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-04 23:01:07.062 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-04 23:01:09.670 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-04 23:01:09.678 [main] INFO  com.yinshu.StartApplication - Starting StartApplication using Java 21.0.6 on xiaoxiongxueJavadeMacBook-Air.local with PID 6066 (/Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot/tocc-admin-all-starter/target/classes started by javaxiaobear in /Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot)
2025-07-04 23:01:09.678 [main] INFO  com.yinshu.StartApplication - The following 1 profile is active: "dev"
2025-07-04 23:01:10.113 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-04 23:01:10.114 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 23:01:10.132 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-07-04 23:01:10.333 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:01:10.337 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$c859217b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:01:10.347 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:01:10.535 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9091 (http)
2025-07-04 23:01:10.538 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9091"]
2025-07-04 23:01:10.539 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-04 23:01:10.539 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-04 23:01:10.592 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring embedded WebApplicationContext
2025-07-04 23:01:10.592 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 894 ms
2025-07-04 23:01:10.793 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [syncDb] success
2025-07-04 23:01:10.793 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [syncDb]
2025-07-04 23:01:12.208 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/**'] with [org.springframework.security.web.session.DisableEncodeUrlFilter@23c61c1b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4a6d6308, org.springframework.security.web.context.SecurityContextPersistenceFilter@********, org.springframework.security.web.header.HeaderWriterFilter@2a738d47, org.springframework.web.filter.CorsFilter@773cc551, org.springframework.security.web.authentication.logout.LogoutFilter@1ece052b, com.yinshu.sys.security.JwtAuthenticationTokenFilter@75adb1c0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@46baac0d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@93c66ef, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5cbaafbd, org.springframework.security.web.session.SessionManagementFilter@7bf2e475, org.springframework.security.web.access.ExceptionTranslationFilter@41041c31, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5cf39df6]
2025-07-04 23:01:12.400 [main] INFO  org.ehcache.core.EhcacheManager - Cache 's_setting' created in EhcacheManager.
2025-07-04 23:01:12.404 [main] INFO  org.ehcache.core.EhcacheManager - Cache 'road_network' created in EhcacheManager.
2025-07-04 23:01:12.480 [main] WARN  o.s.b.a.freemarker.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-04 23:01:12.530 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9091"]
2025-07-04 23:01:12.557 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9091 (http) with context path '/tocc'
2025-07-04 23:01:12.557 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Starting...
2025-07-04 23:01:12.557 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@21d30ba5]]
2025-07-04 23:01:12.558 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Started.
2025-07-04 23:01:12.564 [main] INFO  com.yinshu.StartApplication - Started StartApplication in 3.088 seconds (JVM running for 3.684)
2025-07-04 23:01:20.905 [http-nio-9091-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04 23:01:20.905 [http-nio-9091-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-04 23:01:20.906 [http-nio-9091-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-04 23:01:20.923 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:01:21.786 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/road-network/overview/getRoadBaseInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:01:21.789 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/road-network/overview/getTrafficInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:01:21.793 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/basic-resource/getCountyList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:01:22.231 [http-nio-9091-exec-1] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1,syncDb} inited
2025-07-04 23:01:23.563 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100447/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"id = '2025'"}
2025-07-04 23:01:23.566 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100451/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"id like '2025-07' order by id"}
2025-07-04 23:01:23.723 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '公交车' ","type":"公交车"}
2025-07-04 23:01:23.734 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100430/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"order by id"}
2025-07-04 23:01:23.972 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:414 | http://yinshu.iok.la:8081/api/100447/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:01:23.973 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:407 | http://yinshu.iok.la:8081/api/100451/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:01:24.123 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:400 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:01:24.129 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:01:24.132 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null and company = null and line_name = null and type = '公交车' and dt = '2025-07-04' ","selectType":"1"}
2025-07-04 23:01:24.154 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:420 | http://yinshu.iok.la:8081/api/100430/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:01:24.342 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:210 | http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:01:27.311 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleClassification [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:01:27.311 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getCapacityClassification [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:01:27.311 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOverallSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:01:27.314 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null order by dt desc"}
2025-07-04 23:01:27.314 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null order by dt desc"}
2025-07-04 23:01:27.314 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null order by dt desc"}
2025-07-04 23:01:27.456 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:142 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:01:27.487 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:173 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:01:27.499 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:185 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:01:28.391 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getSuspectedBlackCarData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:01:28.393 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"hour != null and type = '疑似黑车' ","s":0,"pageSize":10,"pageNum":1,"n":10}
2025-07-04 23:01:28.569 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:176 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:01:37.006 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getSuspectedBlackCarData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:01:37.012 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"hour != null and type = '疑似黑车' ","s":0,"pageSize":10,"pageNum":1,"n":10}
2025-07-04 23:01:37.185 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:173 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:02:12.048 [MessageBroker-1] INFO  o.s.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-04 23:02:16.201 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:02:16.209 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-04 23:02:16.646 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:438 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:02:16.657 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:02:16.663 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour = null and vehicle_type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-04 23:02:16.816 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:153 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:02:17.661 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:02:17.665 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"hour != null and type = '预警车辆' and vehicle_type = '网约车'  and dt >= '2025-07-04' and dt <= '2025-07-04' ","dt":["2025-07-04","2025-07-04"],"s":0,"pageSize":10,"vehicle_type":"网约车","pageNum":1,"n":10}
2025-07-04 23:02:17.836 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:172 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:02:26.734 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:02:26.742 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"hour != null and type = '预警车辆' and vehicle_type = '网约车'  and dt >= '2025-07-04' and dt <= '2025-07-04' ","dt":["2025-07-04","2025-07-04"],"s":0,"pageSize":10,"vehicle_type":"网约车","pageNum":1,"n":10}
2025-07-04 23:02:26.957 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:215 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:03:19.083 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:03:19.099 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-04 23:03:19.459 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:360 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:03:19.470 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:03:19.477 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour = null and vehicle_type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-04 23:03:19.655 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:178 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:03:21.662 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:03:21.665 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"hour != null and type = '预警车辆' and vehicle_type = '网约车'  and dt >= '2025-07-04' and dt <= '2025-07-04' ","dt":["2025-07-04","2025-07-04"],"s":0,"pageSize":10,"vehicle_type":"网约车","pageNum":1,"n":10}
2025-07-04 23:03:21.839 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:174 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:04:37.764 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getPassengerMapData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:04:37.770 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county != null and company != null and vehicle_type = '网约车'","selectType":"3"}
2025-07-04 23:04:38.194 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:424 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:04:47.313 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:04:47.316 [http-nio-9091-exec-4] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getRealLocation-----------------
2025-07-04 23:04:47.321 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"plate":"晋A3344"}
2025-07-04 23:04:47.471 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:150 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:04:52.313 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:04:52.315 [http-nio-9091-exec-1] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getRealLocation-----------------
2025-07-04 23:04:52.317 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"plate":"晋A3344"}
2025-07-04 23:04:52.469 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:152 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:04:57.311 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:04:57.313 [http-nio-9091-exec-5] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getRealLocation-----------------
2025-07-04 23:04:57.316 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"plate":"晋A3344"}
2025-07-04 23:04:57.464 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:148 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:05:02.312 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:05:02.314 [http-nio-9091-exec-3] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getRealLocation-----------------
2025-07-04 23:05:02.318 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"plate":"晋A3344"}
2025-07-04 23:05:02.483 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:166 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:05:07.308 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:05:07.309 [http-nio-9091-exec-10] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getRealLocation-----------------
2025-07-04 23:05:07.312 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"plate":"晋A3344"}
2025-07-04 23:05:07.538 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:227 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:05:12.311 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:05:12.312 [http-nio-9091-exec-4] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getRealLocation-----------------
2025-07-04 23:05:12.315 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"plate":"晋A3344"}
2025-07-04 23:05:12.524 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:209 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:05:17.309 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:05:17.312 [http-nio-9091-exec-1] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getRealLocation-----------------
2025-07-04 23:05:17.315 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"plate":"晋A3344"}
2025-07-04 23:05:17.531 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:216 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:05:22.309 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:05:22.313 [http-nio-9091-exec-5] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getRealLocation-----------------
2025-07-04 23:05:22.318 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"plate":"晋A3344"}
2025-07-04 23:05:22.523 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:206 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:05:27.706 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:05:27.708 [http-nio-9091-exec-3] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getRealLocation-----------------
2025-07-04 23:05:27.710 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"plate":"晋A3344"}
2025-07-04 23:05:27.911 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:201 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:05:32.310 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:05:32.312 [http-nio-9091-exec-10] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getRealLocation-----------------
2025-07-04 23:05:32.315 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"plate":"晋A3344"}
2025-07-04 23:05:32.515 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:200 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:05:37.707 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:05:37.709 [http-nio-9091-exec-4] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getRealLocation-----------------
2025-07-04 23:05:37.738 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"plate":"晋A3344"}
2025-07-04 23:05:37.948 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:210 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:05:42.706 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:05:42.707 [http-nio-9091-exec-1] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getRealLocation-----------------
2025-07-04 23:05:42.709 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"plate":"晋A3344"}
2025-07-04 23:05:42.916 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:207 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:05:47.703 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:05:47.704 [http-nio-9091-exec-5] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getRealLocation-----------------
2025-07-04 23:05:47.706 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"plate":"晋A3344"}
2025-07-04 23:05:47.919 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:213 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:05:52.707 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:05:52.709 [http-nio-9091-exec-3] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getRealLocation-----------------
2025-07-04 23:05:52.713 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"plate":"晋A3344"}
2025-07-04 23:05:52.920 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:206 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:05:57.707 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:05:57.709 [http-nio-9091-exec-10] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getRealLocation-----------------
2025-07-04 23:05:57.712 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"plate":"晋A3344"}
2025-07-04 23:05:57.924 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:212 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:06:02.705 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:06:02.706 [http-nio-9091-exec-4] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getRealLocation-----------------
2025-07-04 23:06:02.707 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"plate":"晋A3344"}
2025-07-04 23:06:02.908 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:201 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:06:07.708 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:06:07.710 [http-nio-9091-exec-1] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getRealLocation-----------------
2025-07-04 23:06:07.712 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"plate":"晋A3344"}
2025-07-04 23:06:07.920 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:208 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:06:12.310 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:06:12.312 [http-nio-9091-exec-5] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getRealLocation-----------------
2025-07-04 23:06:12.315 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"plate":"晋A3344"}
2025-07-04 23:06:12.521 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:206 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋A3344' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:06:13.208 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:06:13.210 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:06:13.417 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:207 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:06:13.422 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:06:13.425 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour = null and vehicle_type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:06:13.661 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:236 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:06:14.152 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getPassengerMapData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:06:14.156 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county != null and company != null and vehicle_type = '巡游车'","selectType":"2"}
2025-07-04 23:06:14.369 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:213 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:06:22.197 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/road-transport/ads_basic_indicator_data [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:06:22.197 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/road-transport/ads_passenger_transport_station [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:06:22.197 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/road-transport/ads_passenger_transport_station [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:06:22.197 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/road-transport/ads_daily_overview_trend_hourly [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:06:22.200 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100459/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"dt = '2025-07-04'"}
2025-07-04 23:06:22.200 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100461/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '客运' and dt =  '2025-07-04'"}
2025-07-04 23:06:22.201 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100449/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '客运'"}
2025-07-04 23:06:22.202 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100459/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"dt = '2025-07-04'"}
2025-07-04 23:06:22.422 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:222 | http://yinshu.iok.la:8081/api/100461/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:06:22.504 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:303 | http://yinshu.iok.la:8081/api/100449/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:06:22.566 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:364 | http://yinshu.iok.la:8081/api/100459/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:06:22.666 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:466 | http://yinshu.iok.la:8081/api/100459/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:06:23.317 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getAnalysisList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:06:23.317 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getTotalInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:06:23.317 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getArriverOutList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:06:23.318 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getPassengerList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:06:23.317 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getBusList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:06:23.320 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100448/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" dt='2025-07-04' and hour = null","dataType":"1"}
2025-07-04 23:06:23.320 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100452/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"dt='2025-06-17' and hour = null and type = '机场'","dataType":"1","transportType":"1"}
2025-07-04 23:06:23.320 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100446/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"id='2025'","s":0,"n":100}
2025-07-04 23:06:23.320 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100450/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"dt='2025-06-17' and city = null and type = '机场'","dataType":"1","transportType":"1"}
2025-07-04 23:06:23.320 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100453/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"dt='2025-06-16'  and hour = null","dataType":"1"}
2025-07-04 23:06:23.467 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:147 | http://yinshu.iok.la:8081/api/100452/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:06:23.467 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:147 | http://yinshu.iok.la:8081/api/100450/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:06:23.494 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:174 | http://yinshu.iok.la:8081/api/100453/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:06:23.494 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:174 | http://yinshu.iok.la:8081/api/100448/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:06:23.731 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:411 | http://yinshu.iok.la:8081/api/100446/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:06:24.239 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getVehicleList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:06:24.243 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100479/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"order by station, flag desc","s":0,"stationType":"机场","n":100000}
2025-07-04 23:06:24.663 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:420 | http://yinshu.iok.la:8081/api/100479/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:07:13.345 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getPassengerList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:07:13.344 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getTotalInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:07:13.344 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getBusList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:07:13.344 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getArriverOutList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:07:13.345 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getAnalysisList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:07:13.355 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100446/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"id='2025'","s":0,"n":100}
2025-07-04 23:07:13.355 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100450/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"dt='2025-06-17' and city = null and type = '机场'","dataType":"1","transportType":"1"}
2025-07-04 23:07:13.355 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100453/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"dt='2025-06-16'  and hour = null","dataType":"1"}
2025-07-04 23:07:13.355 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100452/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"dt='2025-06-17' and hour = null and type = '机场'","dataType":"1","transportType":"1"}
2025-07-04 23:07:13.355 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100448/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" dt='2025-07-04' and hour = null","dataType":"1"}
2025-07-04 23:07:13.693 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:338 | http://yinshu.iok.la:8081/api/100448/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:07:13.701 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:346 | http://yinshu.iok.la:8081/api/100453/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:07:13.765 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:410 | http://yinshu.iok.la:8081/api/100452/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:07:13.772 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:417 | http://yinshu.iok.la:8081/api/100446/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:07:13.778 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:423 | http://yinshu.iok.la:8081/api/100450/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:07:14.849 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getCapacityClassification [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:07:14.849 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOverallSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:07:14.849 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleClassification [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:07:14.851 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null order by dt desc"}
2025-07-04 23:07:14.851 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null order by dt desc"}
2025-07-04 23:07:14.851 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null order by dt desc"}
2025-07-04 23:07:15.016 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:165 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:07:15.112 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:261 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:07:15.112 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:261 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:07:16.490 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:07:16.493 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-04 23:07:16.635 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:142 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:07:16.644 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:07:16.649 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour = null and vehicle_type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-04 23:07:16.869 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:220 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:07:17.211 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getPassengerMapData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:07:17.213 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county != null and company != null and vehicle_type = '网约车'","selectType":"3"}
2025-07-04 23:07:17.416 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:203 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:07:23.750 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:07:23.753 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:07:23.964 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:211 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:07:23.971 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:07:23.974 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour = null and vehicle_type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:07:24.201 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:227 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:07:24.574 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getPassengerMapData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:07:24.577 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county != null and company != null and vehicle_type = '巡游车'","selectType":"2"}
2025-07-04 23:07:24.772 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:196 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:08:37.462 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:08:37.466 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:08:37.822 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:356 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:08:37.832 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:08:37.836 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour = null and vehicle_type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:08:38.065 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:229 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:08:38.467 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:08:38.469 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_mileage != null order by hour","field":"operation_mileage","selectType":"2","type":"巡游车"}
2025-07-04 23:08:38.651 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:182 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:08:38.692 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:08:38.695 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_duration != null order by hour","field":"operation_duration","selectType":"2","type":"巡游车"}
2025-07-04 23:08:38.919 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:224 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:08:38.938 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusinessHoursRanking [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:08:38.943 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"vehicle_type = '巡游车' ","vehicle_type":"巡游车"}
2025-07-04 23:08:39.166 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:223 | http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:09:37.117 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:09:37.129 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_mileage != null order by hour","field":"operation_mileage","selectType":"2","type":"巡游车"}
2025-07-04 23:09:37.542 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:413 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:09:37.561 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:09:37.571 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_duration != null order by hour","field":"operation_duration","selectType":"2","type":"巡游车"}
2025-07-04 23:09:37.752 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:181 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:09:37.763 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusinessHoursRanking [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:09:37.765 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"vehicle_type = '巡游车' ","vehicle_type":"巡游车"}
2025-07-04 23:09:37.968 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:203 | http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:10:17.098 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:10:17.102 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_mileage != null order by hour","field":"operation_mileage","selectType":"2","type":"巡游车"}
2025-07-04 23:10:17.434 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:332 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:10:17.452 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:10:17.478 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_duration != null order by hour","field":"operation_duration","selectType":"2","type":"巡游车"}
2025-07-04 23:10:17.679 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:201 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:10:17.692 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusinessHoursRanking [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:10:17.695 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"vehicle_type = '巡游车' ","vehicle_type":"巡游车"}
2025-07-04 23:10:17.892 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:197 | http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:10:20.258 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:10:20.260 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:10:20.426 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:166 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:10:20.442 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:10:20.450 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour = null and vehicle_type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:10:20.700 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:250 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:10:21.414 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:10:21.419 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_mileage != null order by hour","field":"operation_mileage","selectType":"2","type":"巡游车"}
2025-07-04 23:10:21.628 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:209 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:10:21.658 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:10:21.663 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_duration != null order by hour","field":"operation_duration","selectType":"2","type":"巡游车"}
2025-07-04 23:10:21.888 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:225 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:10:21.907 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusinessHoursRanking [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:10:21.914 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"vehicle_type = '巡游车' ","vehicle_type":"巡游车"}
2025-07-04 23:10:22.127 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:213 | http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:10:33.193 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:10:33.197 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:10:33.364 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:167 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:10:33.371 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:10:33.374 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour = null and vehicle_type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:10:33.565 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:191 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:11:03.915 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:11:03.921 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_mileage != null order by hour","field":"operation_mileage","selectType":"2","type":"巡游车"}
2025-07-04 23:11:04.336 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:416 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:11:04.357 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:11:04.360 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_duration != null order by hour","field":"operation_duration","selectType":"2","type":"巡游车"}
2025-07-04 23:11:04.547 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:187 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:11:04.558 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusinessHoursRanking [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:11:04.562 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"vehicle_type = '巡游车' ","vehicle_type":"巡游车"}
2025-07-04 23:11:04.752 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:190 | http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:11:19.727 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:11:19.743 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:11:19.926 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:183 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:11:19.931 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:11:19.934 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour = null and vehicle_type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:11:20.131 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:197 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:11:20.540 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:11:20.544 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_mileage != null order by hour","field":"operation_mileage","selectType":"2","type":"巡游车"}
2025-07-04 23:11:20.730 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:186 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:11:20.746 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:11:20.750 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_duration != null order by hour","field":"operation_duration","selectType":"2","type":"巡游车"}
2025-07-04 23:11:20.960 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:210 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:11:20.976 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusinessHoursRanking [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:11:20.980 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"vehicle_type = '巡游车' ","vehicle_type":"巡游车"}
2025-07-04 23:11:21.167 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:187 | http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:14:00.245 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:14:00.259 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:14:00.595 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:336 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:14:00.607 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:14:00.611 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour = null and vehicle_type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:14:00.799 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:188 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:14:12.357 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:14:12.360 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_mileage != null order by hour","field":"operation_mileage","selectType":"2","type":"巡游车"}
2025-07-04 23:14:12.538 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:178 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:14:12.558 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:14:12.563 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_duration != null order by hour","field":"operation_duration","selectType":"2","type":"巡游车"}
2025-07-04 23:14:12.740 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:177 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:14:12.756 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusinessHoursRanking [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:14:12.759 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"vehicle_type = '巡游车' ","vehicle_type":"巡游车"}
2025-07-04 23:14:12.939 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:180 | http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:14:14.726 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:14:14.728 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:14:14.892 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:164 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:14:14.912 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:14:14.914 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour = null and vehicle_type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:14:15.085 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:171 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:14:15.433 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:14:15.436 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_mileage != null order by hour","field":"operation_mileage","selectType":"2","type":"巡游车"}
2025-07-04 23:14:15.707 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:271 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:14:15.715 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:14:15.717 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_duration != null order by hour","field":"operation_duration","selectType":"2","type":"巡游车"}
2025-07-04 23:14:15.952 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:235 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:14:15.968 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusinessHoursRanking [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:14:15.972 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"vehicle_type = '巡游车' ","vehicle_type":"巡游车"}
2025-07-04 23:14:16.150 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:178 | http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:14:27.069 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:14:27.074 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:14:27.286 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:212 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:14:27.290 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:14:27.291 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour = null and vehicle_type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:14:27.527 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:236 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:14:28.095 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:14:28.098 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_mileage != null order by hour","field":"operation_mileage","selectType":"2","type":"巡游车"}
2025-07-04 23:14:28.319 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:221 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:14:28.331 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:14:28.335 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_duration != null order by hour","field":"operation_duration","selectType":"2","type":"巡游车"}
2025-07-04 23:14:28.573 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:238 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:14:28.591 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusinessHoursRanking [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:14:28.594 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"vehicle_type = '巡游车' ","vehicle_type":"巡游车"}
2025-07-04 23:14:28.815 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:221 | http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:15:15.464 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:15:15.472 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:15:15.825 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:353 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:15:15.830 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:15:15.832 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour = null and vehicle_type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:15:15.999 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:167 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:15:16.632 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:15:16.636 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_mileage != null order by hour","field":"operation_mileage","selectType":"2","type":"巡游车"}
2025-07-04 23:15:16.809 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:174 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:15:16.826 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:15:16.828 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_duration != null order by hour","field":"operation_duration","selectType":"2","type":"巡游车"}
2025-07-04 23:15:17.010 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:182 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:15:17.027 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusinessHoursRanking [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:15:17.030 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"vehicle_type = '巡游车' ","vehicle_type":"巡游车"}
2025-07-04 23:15:17.208 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:178 | http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:15:27.962 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:15:27.965 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:15:28.181 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:216 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:15:28.186 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:15:28.190 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour = null and vehicle_type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:15:28.420 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:230 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:15:28.580 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:15:28.586 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_mileage != null order by hour","field":"operation_mileage","selectType":"2","type":"巡游车"}
2025-07-04 23:15:28.801 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:215 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:15:28.815 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:15:28.819 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_duration != null order by hour","field":"operation_duration","selectType":"2","type":"巡游车"}
2025-07-04 23:15:29.041 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:222 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:15:29.056 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusinessHoursRanking [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:15:29.059 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"vehicle_type = '巡游车' ","vehicle_type":"巡游车"}
2025-07-04 23:15:29.285 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:226 | http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:15:36.506 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:15:36.514 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:15:36.745 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:231 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:15:36.750 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:15:36.754 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour = null and vehicle_type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:15:36.965 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:211 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:15:37.449 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:15:37.452 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_mileage != null order by hour","field":"operation_mileage","selectType":"2","type":"巡游车"}
2025-07-04 23:15:37.639 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:187 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:15:37.653 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:15:37.656 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_duration != null order by hour","field":"operation_duration","selectType":"2","type":"巡游车"}
2025-07-04 23:15:37.818 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:162 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:15:37.835 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusinessHoursRanking [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:15:37.838 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"vehicle_type = '巡游车' ","vehicle_type":"巡游车"}
2025-07-04 23:15:38.049 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:211 | http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:15:53.788 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:15:53.791 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:15:53.939 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:148 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:15:53.948 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:15:53.952 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour = null and vehicle_type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:15:54.144 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:192 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:15:54.723 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:15:54.727 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_mileage != null order by hour","field":"operation_mileage","selectType":"2","type":"巡游车"}
2025-07-04 23:15:54.901 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:175 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:15:54.913 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:15:54.915 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour != null and vehicle_type = '巡游车' and operation_duration != null order by hour","field":"operation_duration","selectType":"2","type":"巡游车"}
2025-07-04 23:15:55.102 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:187 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:15:55.118 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusinessHoursRanking [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:15:55.122 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"vehicle_type = '巡游车' ","vehicle_type":"巡游车"}
2025-07-04 23:15:55.301 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:179 | http://yinshu.iok.la:8081/api/100486/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:17:41.713 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopping...
2025-07-04 23:17:41.714 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@21d30ba5]]
2025-07-04 23:17:41.714 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopped.
2025-07-04 23:17:41.742 [SpringApplicationShutdownHook] INFO  org.ehcache.core.EhcacheManager - Cache 's_setting' removed from EhcacheManager.
2025-07-04 23:17:41.742 [SpringApplicationShutdownHook] INFO  org.ehcache.core.EhcacheManager - Cache 'road_network' removed from EhcacheManager.
2025-07-04 23:17:41.755 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-04 23:17:41.756 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-04 23:17:41.757 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-04 23:17:41.757 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-04 23:17:44.811 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-04 23:17:44.818 [main] INFO  com.yinshu.StartApplication - Starting StartApplication using Java 21.0.6 on xiaoxiongxueJavadeMacBook-Air.local with PID 12870 (/Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot/tocc-admin-all-starter/target/classes started by javaxiaobear in /Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot)
2025-07-04 23:17:44.818 [main] INFO  com.yinshu.StartApplication - The following 1 profile is active: "dev"
2025-07-04 23:17:45.227 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-04 23:17:45.228 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 23:17:45.246 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-07-04 23:17:45.429 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:17:45.432 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$c859217b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:17:45.441 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:17:45.633 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9091 (http)
2025-07-04 23:17:45.636 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9091"]
2025-07-04 23:17:45.636 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-04 23:17:45.636 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-04 23:17:45.677 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring embedded WebApplicationContext
2025-07-04 23:17:45.677 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 836 ms
2025-07-04 23:17:45.871 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [syncDb] success
2025-07-04 23:17:45.872 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [syncDb]
2025-07-04 23:17:47.281 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/**'] with [org.springframework.security.web.session.DisableEncodeUrlFilter@1dba7721, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@24381e4a, org.springframework.security.web.context.SecurityContextPersistenceFilter@1e1d813a, org.springframework.security.web.header.HeaderWriterFilter@6fb51e17, org.springframework.web.filter.CorsFilter@6c3627c, org.springframework.security.web.authentication.logout.LogoutFilter@53570fff, com.yinshu.sys.security.JwtAuthenticationTokenFilter@1ce57bbb, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@64b20d9c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1bcdd302, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@e5d9fa7, org.springframework.security.web.session.SessionManagementFilter@2b569858, org.springframework.security.web.access.ExceptionTranslationFilter@4fe9fb65, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5de6c7d7]
2025-07-04 23:17:47.479 [main] INFO  org.ehcache.core.EhcacheManager - Cache 's_setting' created in EhcacheManager.
2025-07-04 23:17:47.484 [main] INFO  org.ehcache.core.EhcacheManager - Cache 'road_network' created in EhcacheManager.
2025-07-04 23:17:47.550 [main] WARN  o.s.b.a.freemarker.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-04 23:17:47.603 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9091"]
2025-07-04 23:17:47.612 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9091 (http) with context path '/tocc'
2025-07-04 23:17:47.613 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Starting...
2025-07-04 23:17:47.613 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@1676e501]]
2025-07-04 23:17:47.613 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Started.
2025-07-04 23:17:47.619 [main] INFO  com.yinshu.StartApplication - Started StartApplication in 3.006 seconds (JVM running for 3.521)
2025-07-04 23:17:56.549 [http-nio-9091-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04 23:17:56.550 [http-nio-9091-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-04 23:17:56.552 [http-nio-9091-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-04 23:17:56.581 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:17:57.954 [http-nio-9091-exec-1] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1,syncDb} inited
2025-07-04 23:17:58.738 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:17:59.022 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:17:59.026 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"hour != null and type = '预警车辆' and vehicle_type = '巡游车'  and dt >= '2025-07-04' and dt <= '2025-07-04' ","dt":["2025-07-04","2025-07-04"],"s":0,"pageSize":10,"vehicle_type":"巡游车","pageNum":1,"n":10}
2025-07-04 23:17:59.120 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:386 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:17:59.138 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:17:59.141 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour = null and vehicle_type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:17:59.325 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:299 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:17:59.342 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:201 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:18:06.011 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:18:06.018 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"hour != null and type = '预警车辆' and vehicle_type = '巡游车'  and dt >= '2025-07-04' and dt <= '2025-07-04'  and carid like '%晋H' ","dt":["2025-07-04","2025-07-04"],"s":0,"pageSize":10,"vehicle_type":"巡游车","pageNum":1,"n":10,"carid":"晋H"}
2025-07-04 23:18:06.168 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:150 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:18:25.311 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:18:25.315 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"hour != null and type = '预警车辆' and vehicle_type = '巡游车'  and dt >= '2025-07-04' and dt <= '2025-07-04' ","dt":["2025-07-04","2025-07-04"],"s":0,"pageSize":10,"vehicle_type":"巡游车","pageNum":1,"n":10,"carid":""}
2025-07-04 23:18:25.537 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:222 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:18:29.675 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:18:29.683 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"hour != null and type = '预警车辆' and vehicle_type = '巡游车'  and dt >= '2025-07-04' and dt <= '2025-07-04' ","dt":["2025-07-04","2025-07-04"],"s":0,"pageSize":10,"vehicle_type":"巡游车","pageNum":1,"n":10,"carid":""}
2025-07-04 23:18:29.858 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:175 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:18:47.103 [MessageBroker-1] INFO  o.s.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-04 23:19:21.546 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopping...
2025-07-04 23:19:21.547 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@1676e501]]
2025-07-04 23:19:21.547 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopped.
2025-07-04 23:19:21.567 [SpringApplicationShutdownHook] INFO  org.ehcache.core.EhcacheManager - Cache 's_setting' removed from EhcacheManager.
2025-07-04 23:19:21.567 [SpringApplicationShutdownHook] INFO  org.ehcache.core.EhcacheManager - Cache 'road_network' removed from EhcacheManager.
2025-07-04 23:19:21.575 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-04 23:19:21.577 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-04 23:19:21.580 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-04 23:19:21.580 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-04 23:19:24.581 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-04 23:19:24.589 [main] INFO  com.yinshu.StartApplication - Starting StartApplication using Java 21.0.6 on xiaoxiongxueJavadeMacBook-Air.local with PID 13537 (/Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot/tocc-admin-all-starter/target/classes started by javaxiaobear in /Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot)
2025-07-04 23:19:24.590 [main] INFO  com.yinshu.StartApplication - The following 1 profile is active: "dev"
2025-07-04 23:19:25.027 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-04 23:19:25.028 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 23:19:25.050 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-07-04 23:19:25.245 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:19:25.249 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$93f0b0b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:19:25.260 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:19:25.455 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9091 (http)
2025-07-04 23:19:25.459 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9091"]
2025-07-04 23:19:25.459 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-04 23:19:25.459 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-04 23:19:25.504 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring embedded WebApplicationContext
2025-07-04 23:19:25.504 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 893 ms
2025-07-04 23:19:25.710 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [syncDb] success
2025-07-04 23:19:25.710 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [syncDb]
2025-07-04 23:19:27.139 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/**'] with [org.springframework.security.web.session.DisableEncodeUrlFilter@6dcdc378, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4accc3de, org.springframework.security.web.context.SecurityContextPersistenceFilter@7bf2e475, org.springframework.security.web.header.HeaderWriterFilter@30ce78e3, org.springframework.web.filter.CorsFilter@1a23136f, org.springframework.security.web.authentication.logout.LogoutFilter@7d086485, com.yinshu.sys.security.JwtAuthenticationTokenFilter@5535e9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2ca464bb, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3358805a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@23a78d2e, org.springframework.security.web.session.SessionManagementFilter@3328db4f, org.springframework.security.web.access.ExceptionTranslationFilter@1bcdd302, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3705a85c]
2025-07-04 23:19:27.350 [main] INFO  org.ehcache.core.EhcacheManager - Cache 's_setting' created in EhcacheManager.
2025-07-04 23:19:27.356 [main] INFO  org.ehcache.core.EhcacheManager - Cache 'road_network' created in EhcacheManager.
2025-07-04 23:19:27.427 [main] WARN  o.s.b.a.freemarker.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-04 23:19:27.480 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9091"]
2025-07-04 23:19:27.489 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9091 (http) with context path '/tocc'
2025-07-04 23:19:27.490 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Starting...
2025-07-04 23:19:27.491 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@24df4805]]
2025-07-04 23:19:27.491 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Started.
2025-07-04 23:19:27.497 [main] INFO  com.yinshu.StartApplication - Started StartApplication in 3.11 seconds (JVM running for 3.625)
2025-07-04 23:19:32.785 [http-nio-9091-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04 23:19:32.786 [http-nio-9091-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-04 23:19:32.787 [http-nio-9091-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-04 23:19:32.817 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:19:34.096 [http-nio-9091-exec-1] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1,syncDb} inited
2025-07-04 23:19:34.742 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:19:35.115 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:377 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:19:35.143 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:19:35.147 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour = null and vehicle_type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:19:35.351 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:204 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:19:35.507 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:19:35.513 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"hour != null and type = '预警车辆' and vehicle_type = '巡游车'  and dt >= '2025-07-04' and dt <= '2025-07-04' ","dt":["2025-07-04","2025-07-04"],"s":0,"pageSize":10,"vehicle_type":"巡游车","pageNum":1,"n":10}
2025-07-04 23:19:35.662 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:150 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:19:43.219 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:19:43.224 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"hour != null and type = '预警车辆' and vehicle_type = '巡游车'  and dt >= '2025-07-04' and dt <= '2025-07-04'  and f_carid like '%890' ","dt":["2025-07-04","2025-07-04"],"s":0,"pageSize":10,"vehicle_type":"巡游车","pageNum":1,"n":10,"carid":"890"}
2025-07-04 23:19:43.406 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:182 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:19:52.044 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:19:52.050 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"hour != null and type = '预警车辆' and vehicle_type = '巡游车'  and dt >= '2025-07-04' and dt <= '2025-07-04'  and f_driver like '%六' ","dt":["2025-07-04","2025-07-04"],"s":0,"name":"六","pageSize":10,"vehicle_type":"巡游车","pageNum":1,"n":10,"carid":""}
2025-07-04 23:19:52.227 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:177 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:20:02.508 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:20:02.512 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-04 23:20:02.683 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:171 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:20:02.688 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:20:02.693 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour = null and vehicle_type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-04 23:20:02.912 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:219 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:20:03.666 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:20:03.669 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"hour != null and type = '预警车辆' and vehicle_type = '网约车'  and dt >= '2025-07-04' and dt <= '2025-07-04' ","dt":["2025-07-04","2025-07-04"],"s":0,"pageSize":10,"vehicle_type":"网约车","pageNum":1,"n":10}
2025-07-04 23:20:03.860 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:191 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:20:09.544 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:20:09.549 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"hour != null and type = '预警车辆' and vehicle_type = '网约车'  and dt >= '2025-07-04' and dt <= '2025-07-04'  and f_carid like '%89A' ","dt":["2025-07-04","2025-07-04"],"s":0,"pageSize":10,"vehicle_type":"网约车","pageNum":1,"n":10,"carid":"89A"}
2025-07-04 23:20:09.727 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:178 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:20:14.405 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:20:14.408 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:20:14.584 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:176 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:20:14.595 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:20:14.600 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":" hour = null and vehicle_type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-04 23:20:14.805 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:205 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:20:16.004 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:20:16.007 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"hour != null and type = '预警车辆' and vehicle_type = '巡游车'  and dt >= '2025-07-04' and dt <= '2025-07-04' ","dt":["2025-07-04","2025-07-04"],"s":0,"pageSize":10,"vehicle_type":"巡游车","pageNum":1,"n":10}
2025-07-04 23:20:16.186 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:179 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:20:26.977 [MessageBroker-1] INFO  o.s.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-04 23:21:56.984 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:21:56.997 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '公交车' ","type":"公交车"}
2025-07-04 23:21:57.376 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:380 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:21:57.383 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:21:57.389 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null and company = null and line_name = null and type = '公交车' and dt = '2025-07-04' ","selectType":"1"}
2025-07-04 23:21:57.554 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:165 | http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:21:58.774 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusRouteDetail [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:21:58.780 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county != null and company != null and line_name != null","selectType":"2"}
2025-07-04 23:21:58.943 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:163 | http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:21:59.615 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusStationDetail [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:21:59.619 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100474/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county != null and station_name != null","selectType":"2"}
2025-07-04 23:21:59.801 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:182 | http://yinshu.iok.la:8081/api/100474/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:22:18.092 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusRouteDetail [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:22:18.098 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county != null and company != null and line_name != null","selectType":"2"}
2025-07-04 23:22:18.273 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:175 | http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:22:19.000 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusStationDetail [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:22:19.007 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100474/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county != null and station_name != null","selectType":"2"}
2025-07-04 23:22:19.187 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:180 | http://yinshu.iok.la:8081/api/100474/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:23:53.076 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusRouteDetail [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:23:53.088 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county != null and company != null and line_name != null","selectType":"2"}
2025-07-04 23:23:53.463 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:375 | http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:23:58.245 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusStationDetail [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:23:58.249 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100474/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county != null and station_name != null","selectType":"2"}
2025-07-04 23:23:58.399 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:150 | http://yinshu.iok.la:8081/api/100474/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:25:24.036 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopping...
2025-07-04 23:25:24.037 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@24df4805]]
2025-07-04 23:25:24.037 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopped.
2025-07-04 23:25:24.058 [SpringApplicationShutdownHook] INFO  org.ehcache.core.EhcacheManager - Cache 's_setting' removed from EhcacheManager.
2025-07-04 23:25:24.059 [SpringApplicationShutdownHook] INFO  org.ehcache.core.EhcacheManager - Cache 'road_network' removed from EhcacheManager.
2025-07-04 23:25:24.066 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-04 23:25:24.068 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-04 23:25:24.069 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-04 23:25:24.069 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-04 23:25:27.243 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-04 23:25:27.251 [main] INFO  com.yinshu.StartApplication - Starting StartApplication using Java 21.0.6 on xiaoxiongxueJavadeMacBook-Air.local with PID 15949 (/Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot/tocc-admin-all-starter/target/classes started by javaxiaobear in /Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot)
2025-07-04 23:25:27.251 [main] INFO  com.yinshu.StartApplication - The following 1 profile is active: "dev"
2025-07-04 23:25:27.682 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-04 23:25:27.683 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 23:25:27.702 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-07-04 23:25:27.903 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:25:27.906 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$5a953e3c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:25:27.914 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:25:28.106 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9091 (http)
2025-07-04 23:25:28.109 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9091"]
2025-07-04 23:25:28.110 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-04 23:25:28.110 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-04 23:25:28.153 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring embedded WebApplicationContext
2025-07-04 23:25:28.153 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 879 ms
2025-07-04 23:25:28.349 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [syncDb] success
2025-07-04 23:25:28.349 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [syncDb]
2025-07-04 23:25:29.747 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/**'] with [org.springframework.security.web.session.DisableEncodeUrlFilter@2472ba04, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1a0e5c0c, org.springframework.security.web.context.SecurityContextPersistenceFilter@4f63909f, org.springframework.security.web.header.HeaderWriterFilter@53570fff, org.springframework.web.filter.CorsFilter@5bb45afc, org.springframework.security.web.authentication.logout.LogoutFilter@1f2d0ca2, com.yinshu.sys.security.JwtAuthenticationTokenFilter@757c685d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@133d0471, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@387ef6a7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2f0b5d79, org.springframework.security.web.session.SessionManagementFilter@7e916dc2, org.springframework.security.web.access.ExceptionTranslationFilter@53202b06, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@34893693]
2025-07-04 23:25:29.942 [main] INFO  org.ehcache.core.EhcacheManager - Cache 's_setting' created in EhcacheManager.
2025-07-04 23:25:29.947 [main] INFO  org.ehcache.core.EhcacheManager - Cache 'road_network' created in EhcacheManager.
2025-07-04 23:25:30.011 [main] WARN  o.s.b.a.freemarker.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-04 23:25:30.060 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9091"]
2025-07-04 23:25:30.069 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9091 (http) with context path '/tocc'
2025-07-04 23:25:30.070 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Starting...
2025-07-04 23:25:30.070 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@7671922b]]
2025-07-04 23:25:30.070 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Started.
2025-07-04 23:25:30.076 [main] INFO  com.yinshu.StartApplication - Started StartApplication in 3.027 seconds (JVM running for 3.511)
2025-07-04 23:25:54.102 [http-nio-9091-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04 23:25:54.102 [http-nio-9091-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-04 23:25:54.103 [http-nio-9091-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-04 23:25:54.116 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:25:55.303 [http-nio-9091-exec-1] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1,syncDb} inited
2025-07-04 23:25:55.768 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusStationDetail [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:25:56.159 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '公交车' ","type":"公交车"}
2025-07-04 23:25:56.327 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100474/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county != null and station_name != null","s":0,"pageSize":10,"selectType":"1","pageNum":1,"n":10}
2025-07-04 23:25:56.561 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:405 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:25:56.588 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:25:56.592 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null and company = null and line_name = null and type = '公交车' and dt = '2025-07-04' ","selectType":"1"}
2025-07-04 23:25:56.715 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:388 | http://yinshu.iok.la:8081/api/100474/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:25:56.779 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:187 | http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:25:57.349 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getExport [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:25:57.353 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100474/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county != null and station_name != null","s":0,"exportType":"2","selectType":"1","n":1000000}
2025-07-04 23:25:57.530 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:177 | http://yinshu.iok.la:8081/api/100474/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:26:29.589 [MessageBroker-1] INFO  o.s.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-04 23:26:54.799 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopping...
2025-07-04 23:26:54.799 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@7671922b]]
2025-07-04 23:26:54.800 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopped.
2025-07-04 23:26:54.824 [SpringApplicationShutdownHook] INFO  org.ehcache.core.EhcacheManager - Cache 's_setting' removed from EhcacheManager.
2025-07-04 23:26:54.824 [SpringApplicationShutdownHook] INFO  org.ehcache.core.EhcacheManager - Cache 'road_network' removed from EhcacheManager.
2025-07-04 23:26:54.832 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-04 23:26:54.833 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-04 23:26:54.837 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-04 23:26:54.837 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-04 23:26:57.635 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-04 23:26:57.643 [main] INFO  com.yinshu.StartApplication - Starting StartApplication using Java 21.0.6 on xiaoxiongxueJavadeMacBook-Air.local with PID 16557 (/Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot/tocc-admin-all-starter/target/classes started by javaxiaobear in /Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot)
2025-07-04 23:26:57.643 [main] INFO  com.yinshu.StartApplication - The following 1 profile is active: "dev"
2025-07-04 23:26:58.063 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-04 23:26:58.064 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 23:26:58.082 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-07-04 23:26:58.265 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:26:58.268 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$c9aee3d0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:26:58.276 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:26:58.469 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9091 (http)
2025-07-04 23:26:58.472 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9091"]
2025-07-04 23:26:58.472 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-04 23:26:58.472 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-04 23:26:58.514 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring embedded WebApplicationContext
2025-07-04 23:26:58.514 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 847 ms
2025-07-04 23:26:58.707 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [syncDb] success
2025-07-04 23:26:58.708 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [syncDb]
2025-07-04 23:27:00.124 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/**'] with [org.springframework.security.web.session.DisableEncodeUrlFilter@71f86562, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5c141c7b, org.springframework.security.web.context.SecurityContextPersistenceFilter@5ee76cf2, org.springframework.security.web.header.HeaderWriterFilter@410e043b, org.springframework.web.filter.CorsFilter@14f254f, org.springframework.security.web.authentication.logout.LogoutFilter@7962a364, com.yinshu.sys.security.JwtAuthenticationTokenFilter@71eaef6a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2c1a0f82, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@83bbab, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@363ee411, org.springframework.security.web.session.SessionManagementFilter@1c149539, org.springframework.security.web.access.ExceptionTranslationFilter@790ea58f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@65b07b9]
2025-07-04 23:27:00.334 [main] INFO  org.ehcache.core.EhcacheManager - Cache 's_setting' created in EhcacheManager.
2025-07-04 23:27:00.338 [main] INFO  org.ehcache.core.EhcacheManager - Cache 'road_network' created in EhcacheManager.
2025-07-04 23:27:00.409 [main] WARN  o.s.b.a.freemarker.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-04 23:27:00.464 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9091"]
2025-07-04 23:27:00.472 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9091 (http) with context path '/tocc'
2025-07-04 23:27:00.473 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Starting...
2025-07-04 23:27:00.473 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2d036335]]
2025-07-04 23:27:00.474 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Started.
2025-07-04 23:27:00.481 [main] INFO  com.yinshu.StartApplication - Started StartApplication in 3.035 seconds (JVM running for 3.53)
2025-07-04 23:27:09.025 [http-nio-9091-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04 23:27:09.026 [http-nio-9091-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-04 23:27:09.027 [http-nio-9091-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-04 23:27:09.045 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusRouteDetail [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:27:10.268 [http-nio-9091-exec-1] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1,syncDb} inited
2025-07-04 23:27:10.951 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county!= null and line_name != null","s":0,"pageSize":10,"selectType":"1","pageNum":1,"n":10}
2025-07-04 23:27:11.368 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:423 | http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:27:11.425 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusRouteDetail [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:27:11.429 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county!= null and line_name != null","s":0,"pageSize":10,"selectType":"1","pageNum":1,"n":10}
2025-07-04 23:27:11.584 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:155 | http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:27:12.982 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getExport [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:27:12.987 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county!= null and line_name != null","s":0,"exportType":"1","selectType":"1","n":1000000}
2025-07-04 23:27:13.143 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:156 | http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:27:40.310 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopping...
2025-07-04 23:27:40.311 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2d036335]]
2025-07-04 23:27:40.311 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopped.
2025-07-04 23:27:40.330 [SpringApplicationShutdownHook] INFO  org.ehcache.core.EhcacheManager - Cache 's_setting' removed from EhcacheManager.
2025-07-04 23:27:40.330 [SpringApplicationShutdownHook] INFO  org.ehcache.core.EhcacheManager - Cache 'road_network' removed from EhcacheManager.
2025-07-04 23:27:40.338 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-04 23:27:40.339 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-04 23:27:40.343 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-04 23:27:40.343 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-04 23:27:42.865 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-04 23:27:42.872 [main] INFO  com.yinshu.StartApplication - Starting StartApplication using Java 21.0.6 on xiaoxiongxueJavadeMacBook-Air.local with PID 16873 (/Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot/tocc-admin-all-starter/target/classes started by javaxiaobear in /Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot)
2025-07-04 23:27:42.872 [main] INFO  com.yinshu.StartApplication - The following 1 profile is active: "dev"
2025-07-04 23:27:43.307 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-04 23:27:43.308 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 23:27:43.326 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-07-04 23:27:43.519 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:27:43.523 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$a39698d2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:27:43.533 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:27:43.732 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9091 (http)
2025-07-04 23:27:43.736 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9091"]
2025-07-04 23:27:43.736 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-04 23:27:43.736 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-04 23:27:43.783 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring embedded WebApplicationContext
2025-07-04 23:27:43.783 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 892 ms
2025-07-04 23:27:43.985 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [syncDb] success
2025-07-04 23:27:43.986 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [syncDb]
2025-07-04 23:27:45.405 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/**'] with [org.springframework.security.web.session.DisableEncodeUrlFilter@16160a37, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@380e470a, org.springframework.security.web.context.SecurityContextPersistenceFilter@********, org.springframework.security.web.header.HeaderWriterFilter@796a7c9, org.springframework.web.filter.CorsFilter@17b9c9ff, org.springframework.security.web.authentication.logout.LogoutFilter@3857c5d5, com.yinshu.sys.security.JwtAuthenticationTokenFilter@1983a4e4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1067bc4c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3203a4ae, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7b45f7ef, org.springframework.security.web.session.SessionManagementFilter@2b69ff13, org.springframework.security.web.access.ExceptionTranslationFilter@70fe33fa, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1e284d90]
2025-07-04 23:27:45.603 [main] INFO  org.ehcache.core.EhcacheManager - Cache 's_setting' created in EhcacheManager.
2025-07-04 23:27:45.607 [main] INFO  org.ehcache.core.EhcacheManager - Cache 'road_network' created in EhcacheManager.
2025-07-04 23:27:45.698 [main] WARN  o.s.b.a.freemarker.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-04 23:27:45.751 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9091"]
2025-07-04 23:27:45.760 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9091 (http) with context path '/tocc'
2025-07-04 23:27:45.761 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Starting...
2025-07-04 23:27:45.761 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@7719b257]]
2025-07-04 23:27:45.761 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Started.
2025-07-04 23:27:45.767 [main] INFO  com.yinshu.StartApplication - Started StartApplication in 3.09 seconds (JVM running for 3.613)
2025-07-04 23:27:50.360 [http-nio-9091-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04 23:27:50.360 [http-nio-9091-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-04 23:27:50.361 [http-nio-9091-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-04 23:27:50.391 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:27:51.605 [http-nio-9091-exec-1] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1,syncDb} inited
2025-07-04 23:27:52.290 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '公交车' ","type":"公交车"}
2025-07-04 23:27:52.664 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:380 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:27:52.700 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:27:52.704 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null and company = null and line_name = null and type = '公交车' and dt = '2025-07-04' ","selectType":"1"}
2025-07-04 23:27:52.864 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:160 | http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:27:53.419 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusRouteDetail [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:27:53.423 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county!= null and line_name != null","s":0,"pageSize":10,"selectType":"1","pageNum":1,"n":10}
2025-07-04 23:27:53.576 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:153 | http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:27:56.770 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getExport [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:27:56.776 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county!= null and line_name != null","s":0,"exportType":"1","selectType":"1","n":1000000}
2025-07-04 23:27:56.956 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:180 | http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:28:45.227 [MessageBroker-1] INFO  o.s.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-04 23:32:54.714 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopping...
2025-07-04 23:32:54.715 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@7719b257]]
2025-07-04 23:32:54.715 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopped.
2025-07-04 23:32:54.734 [SpringApplicationShutdownHook] INFO  org.ehcache.core.EhcacheManager - Cache 's_setting' removed from EhcacheManager.
2025-07-04 23:32:54.734 [SpringApplicationShutdownHook] INFO  org.ehcache.core.EhcacheManager - Cache 'road_network' removed from EhcacheManager.
2025-07-04 23:32:54.742 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-04 23:32:54.743 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-04 23:32:54.744 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-04 23:32:54.744 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-04 23:32:57.962 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-04 23:32:57.969 [main] INFO  com.yinshu.StartApplication - Starting StartApplication using Java 21.0.6 on xiaoxiongxueJavadeMacBook-Air.local with PID 18986 (/Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot/tocc-admin-all-starter/target/classes started by javaxiaobear in /Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot)
2025-07-04 23:32:57.969 [main] INFO  com.yinshu.StartApplication - The following 1 profile is active: "dev"
2025-07-04 23:32:58.410 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-04 23:32:58.411 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 23:32:58.429 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-07-04 23:32:58.614 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:32:58.617 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$bd03ae55] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:32:58.626 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:32:58.818 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9091 (http)
2025-07-04 23:32:58.822 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9091"]
2025-07-04 23:32:58.822 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-04 23:32:58.822 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-04 23:32:58.865 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring embedded WebApplicationContext
2025-07-04 23:32:58.865 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 875 ms
2025-07-04 23:32:59.076 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [syncDb] success
2025-07-04 23:32:59.076 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [syncDb]
2025-07-04 23:33:00.508 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/**'] with [org.springframework.security.web.session.DisableEncodeUrlFilter@1d9cac6e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5f510929, org.springframework.security.web.context.SecurityContextPersistenceFilter@679886ad, org.springframework.security.web.header.HeaderWriterFilter@3c7787a7, org.springframework.web.filter.CorsFilter@55a2ca5e, org.springframework.security.web.authentication.logout.LogoutFilter@1a1c308b, com.yinshu.sys.security.JwtAuthenticationTokenFilter@757c685d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@643fed50, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7d086485, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6abca7a6, org.springframework.security.web.session.SessionManagementFilter@258291de, org.springframework.security.web.access.ExceptionTranslationFilter@53570fff, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2f70d78c]
2025-07-04 23:33:00.714 [main] INFO  org.ehcache.core.EhcacheManager - Cache 's_setting' created in EhcacheManager.
2025-07-04 23:33:00.719 [main] INFO  org.ehcache.core.EhcacheManager - Cache 'road_network' created in EhcacheManager.
2025-07-04 23:33:00.788 [main] WARN  o.s.b.a.freemarker.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-04 23:33:00.850 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9091 (http) with context path '/tocc'
2025-07-04 23:33:00.851 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Starting...
2025-07-04 23:33:00.851 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35ab83a7]]
2025-07-04 23:33:00.852 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Started.
2025-07-04 23:33:00.858 [main] INFO  com.yinshu.StartApplication - Started StartApplication in 3.091 seconds (JVM running for 3.673)
2025-07-04 23:33:00.859 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopping...
2025-07-04 23:33:00.859 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35ab83a7]]
2025-07-04 23:33:00.859 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopped.
2025-07-04 23:34:37.083 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-04 23:34:37.092 [main] INFO  com.yinshu.StartApplication - Starting StartApplication using Java 21.0.6 on xiaoxiongxueJavadeMacBook-Air.local with PID 19557 (/Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot/tocc-admin-all-starter/target/classes started by javaxiaobear in /Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot)
2025-07-04 23:34:37.093 [main] INFO  com.yinshu.StartApplication - The following 1 profile is active: "dev"
2025-07-04 23:34:37.549 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-04 23:34:37.550 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 23:34:37.568 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-07-04 23:34:37.815 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:34:37.818 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$6e3fad56] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:34:37.828 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:34:38.038 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9091 (http)
2025-07-04 23:34:38.042 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9091"]
2025-07-04 23:34:38.042 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-04 23:34:38.042 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-04 23:34:38.092 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring embedded WebApplicationContext
2025-07-04 23:34:38.092 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 973 ms
2025-07-04 23:34:38.294 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [syncDb] success
2025-07-04 23:34:38.294 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [syncDb]
2025-07-04 23:34:39.719 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/**'] with [org.springframework.security.web.session.DisableEncodeUrlFilter@69cc3370, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@9120cb5, org.springframework.security.web.context.SecurityContextPersistenceFilter@4c81e7c2, org.springframework.security.web.header.HeaderWriterFilter@23307c6c, org.springframework.web.filter.CorsFilter@18419ab7, org.springframework.security.web.authentication.logout.LogoutFilter@69ee0861, com.yinshu.sys.security.JwtAuthenticationTokenFilter@5868ea91, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@95958d9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@452a32f2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5f93ec02, org.springframework.security.web.session.SessionManagementFilter@3b171fbd, org.springframework.security.web.access.ExceptionTranslationFilter@54da736e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1d77d9c6]
2025-07-04 23:34:39.915 [main] INFO  org.ehcache.core.EhcacheManager - Cache 's_setting' created in EhcacheManager.
2025-07-04 23:34:39.920 [main] INFO  org.ehcache.core.EhcacheManager - Cache 'road_network' created in EhcacheManager.
2025-07-04 23:34:39.995 [main] WARN  o.s.b.a.freemarker.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-04 23:34:40.047 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9091"]
2025-07-04 23:34:40.056 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9091 (http) with context path '/tocc'
2025-07-04 23:34:40.057 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Starting...
2025-07-04 23:34:40.057 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6d13cd6d]]
2025-07-04 23:34:40.057 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Started.
2025-07-04 23:34:40.063 [main] INFO  com.yinshu.StartApplication - Started StartApplication in 3.199 seconds (JVM running for 3.809)
2025-07-04 23:34:47.968 [http-nio-9091-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04 23:34:47.969 [http-nio-9091-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-04 23:34:47.970 [http-nio-9091-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-04 23:34:47.993 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:34:49.381 [http-nio-9091-exec-1] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1,syncDb} inited
2025-07-04 23:34:49.431 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusRouteDetail [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:34:50.398 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '公交车' ","type":"公交车"}
2025-07-04 23:34:50.500 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county!= null and line_name != null","s":0,"pageSize":10,"selectType":"1","pageNum":1,"n":10}
2025-07-04 23:34:50.680 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusRouteDetail [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:34:50.686 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county!= null and line_name != null","s":0,"pageSize":10,"selectType":"1","pageNum":1,"n":10}
2025-07-04 23:34:50.785 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:394 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:34:50.801 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:301 | http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:34:50.807 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:34:50.811 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null and company = null and line_name = null and type = '公交车' and dt = '2025-07-04' ","selectType":"1"}
2025-07-04 23:34:51.005 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:194 | http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:34:51.058 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:372 | http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:34:51.884 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getExport [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:34:51.890 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county!= null and line_name != null","s":0,"exportType":"1","selectType":"1","n":1000000}
2025-07-04 23:34:52.063 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:173 | http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:35:39.550 [MessageBroker-1] INFO  o.s.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-04 23:35:53.208 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusRouteDetail [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:35:53.225 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county!= null and line_name != null","s":0,"pageSize":10,"selectType":"1","pageNum":1,"n":10}
2025-07-04 23:35:53.568 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:344 | http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:35:59.513 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusStationDetail [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:35:59.516 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100474/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county != null and station_name != null","s":0,"pageSize":10,"selectType":"1","pageNum":1,"n":10}
2025-07-04 23:35:59.698 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:182 | http://yinshu.iok.la:8081/api/100474/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:37:34.309 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:37:34.314 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county != null and company = null and line_name != null and type = '公交车'","s":0,"pageSize":10,"selectType":"2","pageNum":1,"n":10}
2025-07-04 23:37:34.719 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:405 | http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:38:17.066 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:38:17.080 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county != null and company = null and line_name != null and type = '公交车' and line_name like '1' ","f_line_name":"1","s":0,"pageSize":10,"selectType":"2","pageNum":1,"n":10}
2025-07-04 23:38:17.428 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:348 | http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:38:19.682 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:38:19.688 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county != null and company = null and line_name != null and type = '公交车' and line_name like '1' ","f_line_name":"1","s":0,"pageSize":10,"selectType":"2","pageNum":1,"n":10}
2025-07-04 23:38:19.866 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:178 | http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:38:20.851 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopping...
2025-07-04 23:38:20.851 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6d13cd6d]]
2025-07-04 23:38:20.851 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopped.
2025-07-04 23:38:20.897 [SpringApplicationShutdownHook] INFO  org.ehcache.core.EhcacheManager - Cache 's_setting' removed from EhcacheManager.
2025-07-04 23:38:20.898 [SpringApplicationShutdownHook] INFO  org.ehcache.core.EhcacheManager - Cache 'road_network' removed from EhcacheManager.
2025-07-04 23:38:20.906 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-04 23:38:20.907 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-04 23:38:20.908 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-04 23:38:20.908 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-04 23:38:23.907 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-04 23:38:23.914 [main] INFO  com.yinshu.StartApplication - Starting StartApplication using Java 21.0.6 on xiaoxiongxueJavadeMacBook-Air.local with PID 20766 (/Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot/tocc-admin-all-starter/target/classes started by javaxiaobear in /Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot)
2025-07-04 23:38:23.914 [main] INFO  com.yinshu.StartApplication - The following 1 profile is active: "dev"
2025-07-04 23:38:24.320 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-04 23:38:24.321 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 23:38:24.338 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-07-04 23:38:24.517 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:38:24.520 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$c0994457] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:38:24.528 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:38:24.718 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9091 (http)
2025-07-04 23:38:24.721 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9091"]
2025-07-04 23:38:24.721 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-04 23:38:24.721 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-04 23:38:24.762 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring embedded WebApplicationContext
2025-07-04 23:38:24.762 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 826 ms
2025-07-04 23:38:24.960 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [syncDb] success
2025-07-04 23:38:24.960 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [syncDb]
2025-07-04 23:38:26.336 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/**'] with [org.springframework.security.web.session.DisableEncodeUrlFilter@761ea1f5, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@f047670, org.springframework.security.web.context.SecurityContextPersistenceFilter@387ef6a7, org.springframework.security.web.header.HeaderWriterFilter@3ae91bcc, org.springframework.web.filter.CorsFilter@7a91027c, org.springframework.security.web.authentication.logout.LogoutFilter@30a79476, com.yinshu.sys.security.JwtAuthenticationTokenFilter@2ae7c1d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@21b3d356, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5263f554, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@27d44578, org.springframework.security.web.session.SessionManagementFilter@643fed50, org.springframework.security.web.access.ExceptionTranslationFilter@258291de, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@30dd23e2]
2025-07-04 23:38:26.526 [main] INFO  org.ehcache.core.EhcacheManager - Cache 's_setting' created in EhcacheManager.
2025-07-04 23:38:26.531 [main] INFO  org.ehcache.core.EhcacheManager - Cache 'road_network' created in EhcacheManager.
2025-07-04 23:38:26.594 [main] WARN  o.s.b.a.freemarker.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-04 23:38:26.643 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9091"]
2025-07-04 23:38:26.656 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9091 (http) with context path '/tocc'
2025-07-04 23:38:26.657 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Starting...
2025-07-04 23:38:26.657 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2afe263]]
2025-07-04 23:38:26.658 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Started.
2025-07-04 23:38:26.664 [main] INFO  com.yinshu.StartApplication - Started StartApplication in 2.943 seconds (JVM running for 3.421)
2025-07-04 23:39:26.179 [MessageBroker-1] INFO  o.s.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-04 23:40:18.924 [http-nio-9091-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04 23:40:18.924 [http-nio-9091-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-04 23:40:18.926 [http-nio-9091-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-04 23:40:18.938 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:40:20.017 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:40:20.195 [http-nio-9091-exec-1] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1,syncDb} inited
2025-07-04 23:40:20.205 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:40:20.386 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:40:20.561 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:40:21.680 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county != null and company = null and line_name != null and type = '公交车' and f_line_name like '%1' ","f_line_name":"1","s":0,"pageSize":10,"selectType":"2","pageNum":1,"n":10}
2025-07-04 23:40:21.721 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county != null and company = null and line_name != null and type = '公交车' and f_line_name like '%1' ","f_line_name":"1","s":0,"pageSize":10,"selectType":"2","pageNum":1,"n":10}
2025-07-04 23:40:21.861 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county != null and company = null and line_name != null and type = '公交车' and f_line_name like '%1' ","f_line_name":"1","s":0,"pageSize":10,"selectType":"2","pageNum":1,"n":10}
2025-07-04 23:40:21.891 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county != null and company = null and line_name != null and type = '公交车' and f_line_name like '%1' ","f_line_name":"1","s":0,"pageSize":10,"selectType":"2","pageNum":1,"n":10}
2025-07-04 23:40:22.055 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county != null and company = null and line_name != null and type = '公交车' and f_line_name like '%1' ","f_line_name":"1","s":0,"pageSize":10,"selectType":"2","pageNum":1,"n":10}
2025-07-04 23:40:22.061 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:340 | http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:40:22.061 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:385 | http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:40:22.241 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:380 | http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:40:22.315 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:424 | http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:40:22.434 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:380 | http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:40:24.053 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:40:24.064 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"type = '公交车' ","type":"公交车"}
2025-07-04 23:40:24.218 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:154 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:40:24.229 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:40:24.236 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null and company = null and line_name = null and type = '公交车' and dt = '2025-07-04' ","selectType":"1"}
2025-07-04 23:40:24.424 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:188 | http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:40:26.286 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:40:26.289 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county != null and company = null and line_name != null and type = '公交车'","s":0,"pageSize":10,"selectType":"2","pageNum":1,"n":10}
2025-07-04 23:40:26.483 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:194 | http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:40:29.425 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:40:29.431 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county != null and company = null and line_name != null and type = '公交车' and f_line_name like '%2' ","f_line_name":"2","s":0,"pageSize":10,"selectType":"2","pageNum":1,"n":10}
2025-07-04 23:40:29.618 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:187 | http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:41:11.298 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getCapacityClassification [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:41:11.298 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleClassification [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:41:11.298 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOverallSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:41:11.301 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null order by dt desc"}
2025-07-04 23:41:11.301 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null order by dt desc"}
2025-07-04 23:41:11.301 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null order by dt desc"}
2025-07-04 23:41:11.622 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:321 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:41:11.643 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:342 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:41:11.742 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:441 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:41:12.351 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOverallSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:41:12.354 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null order by dt desc","s":0,"pageSize":10,"pageNum":1,"n":10}
2025-07-04 23:41:12.548 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:194 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:41:59.039 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOverallSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:41:59.048 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null order by dt desc","s":0,"pageSize":10,"pageNum":1,"n":10}
2025-07-04 23:41:59.433 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:385 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:45:26.367 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopping...
2025-07-04 23:45:26.368 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2afe263]]
2025-07-04 23:45:26.368 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopped.
2025-07-04 23:45:26.391 [SpringApplicationShutdownHook] INFO  org.ehcache.core.EhcacheManager - Cache 's_setting' removed from EhcacheManager.
2025-07-04 23:45:26.391 [SpringApplicationShutdownHook] INFO  org.ehcache.core.EhcacheManager - Cache 'road_network' removed from EhcacheManager.
2025-07-04 23:45:26.400 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-04 23:45:26.401 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-04 23:45:26.402 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-04 23:45:26.402 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-04 23:45:29.743 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-04 23:45:29.750 [main] INFO  com.yinshu.StartApplication - Starting StartApplication using Java 21.0.6 on xiaoxiongxueJavadeMacBook-Air.local with PID 23681 (/Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot/tocc-admin-all-starter/target/classes started by javaxiaobear in /Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot)
2025-07-04 23:45:29.750 [main] INFO  com.yinshu.StartApplication - The following 1 profile is active: "dev"
2025-07-04 23:45:30.191 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-04 23:45:30.192 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 23:45:30.211 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-07-04 23:45:30.404 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:45:30.407 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$c9aee3d0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:45:30.415 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-04 23:45:30.611 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9091 (http)
2025-07-04 23:45:30.614 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9091"]
2025-07-04 23:45:30.615 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-04 23:45:30.615 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-04 23:45:30.656 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring embedded WebApplicationContext
2025-07-04 23:45:30.656 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 882 ms
2025-07-04 23:45:30.857 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [syncDb] success
2025-07-04 23:45:30.858 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [syncDb]
2025-07-04 23:45:32.809 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/**'] with [org.springframework.security.web.session.DisableEncodeUrlFilter@5f5076f9, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@19439ec4, org.springframework.security.web.context.SecurityContextPersistenceFilter@12e13abd, org.springframework.security.web.header.HeaderWriterFilter@5bbf3869, org.springframework.web.filter.CorsFilter@6f53f5a4, org.springframework.security.web.authentication.logout.LogoutFilter@a5df98c, com.yinshu.sys.security.JwtAuthenticationTokenFilter@76c587ce, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5cf39df6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@634d56eb, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@21ebf9be, org.springframework.security.web.session.SessionManagementFilter@244c0fbe, org.springframework.security.web.access.ExceptionTranslationFilter@40f77135, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3af6d7a7]
2025-07-04 23:45:33.024 [main] INFO  org.ehcache.core.EhcacheManager - Cache 's_setting' created in EhcacheManager.
2025-07-04 23:45:33.028 [main] INFO  org.ehcache.core.EhcacheManager - Cache 'road_network' created in EhcacheManager.
2025-07-04 23:45:33.093 [main] WARN  o.s.b.a.freemarker.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-04 23:45:33.152 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9091"]
2025-07-04 23:45:33.161 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9091 (http) with context path '/tocc'
2025-07-04 23:45:33.162 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Starting...
2025-07-04 23:45:33.162 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@27ca8a5a]]
2025-07-04 23:45:33.162 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Started.
2025-07-04 23:45:33.169 [main] INFO  com.yinshu.StartApplication - Started StartApplication in 3.631 seconds (JVM running for 4.175)
2025-07-04 23:46:10.006 [http-nio-9091-exec-2] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04 23:46:10.007 [http-nio-9091-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-04 23:46:10.014 [http-nio-9091-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 7 ms
2025-07-04 23:46:10.037 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleClassification [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:46:10.037 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getCapacityClassification [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:46:10.037 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOverallSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:46:11.316 [http-nio-9091-exec-2] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1,syncDb} inited
2025-07-04 23:46:12.453 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null order by dt desc"}
2025-07-04 23:46:12.569 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null order by dt desc"}
2025-07-04 23:46:12.607 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null order by dt desc"}
2025-07-04 23:46:12.873 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:426 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:46:12.884 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:315 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:46:12.950 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:343 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:46:14.331 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOverallSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:46:14.335 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null order by dt desc","s":0,"pageSize":10,"pageNum":1,"n":10}
2025-07-04 23:46:14.519 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:184 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:46:32.091 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOverallSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:46:32.091 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleClassification [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:46:32.091 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getCapacityClassification [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:46:32.113 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null order by dt desc"}
2025-07-04 23:46:32.113 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null order by dt desc"}
2025-07-04 23:46:32.113 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null order by dt desc"}
2025-07-04 23:46:32.289 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:176 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:46:32.319 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:206 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:46:32.319 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:206 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:46:32.630 [MessageBroker-1] INFO  o.s.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-04 23:46:33.377 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOverallSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:46:33.382 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null order by dt desc","s":0,"pageSize":10,"pageNum":1,"n":10}
2025-07-04 23:46:33.560 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:178 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:47:20.762 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getCapacityClassification [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:47:20.764 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleClassification [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:47:20.764 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOverallSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:47:20.772 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null order by dt desc"}
2025-07-04 23:47:20.772 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null order by dt desc"}
2025-07-04 23:47:20.772 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null order by dt desc"}
2025-07-04 23:47:21.091 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:319 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:47:21.108 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:336 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:47:21.201 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:429 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:47:21.724 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOverallSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:47:21.729 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null order by dt desc","s":0,"pageSize":10,"pageNum":1,"n":10}
2025-07-04 23:47:21.913 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:184 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:47:22.735 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getExport [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:47:22.744 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null order by dt desc","s":0,"exportType":"5","n":1000000}
2025-07-04 23:47:22.886 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:142 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
2025-07-04 23:47:55.531 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOverallSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTM4MDEyNjQsInVzZXJJZCI6IjEifQ.dnZ5Ntb0d8zmUnR7HflqB_ciX6zQkhJasMmOaQrzUJk
2025-07-04 23:47:55.535 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD | {"filter":"county = null order by dt desc","s":0,"pageSize":10,"pageNum":1,"n":10}
2025-07-04 23:47:55.928 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:393 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoY2yeWQpPPWxODMeSfRmD
