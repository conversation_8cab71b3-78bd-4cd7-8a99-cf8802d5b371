2025-07-18 21:03:56.128 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:04:01.645 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:04:07.163 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:04:12.673 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:04:18.186 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:04:23.696 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:04:29.203 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:04:34.714 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:04:40.220 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:22:31.846 [scheduled-task-2] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 minute(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:277)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1085)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:938)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:665)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker.just(LettuceInvoker.java:94)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:55)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:279)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:58)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:61)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:224)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:191)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:97)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:54)
	at com.yinshu.utils.RedisCache.getCacheObject(RedisCache.java:82)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:54)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:41)
	at com.yinshu.toms.service.impl.WarnMsgServiceImpl.getWarnInfoUnreadCount(WarnMsgServiceImpl.java:101)
	at com.yinshu.toms.task.WebSocketWarnTask.cron(WebSocketWarnTask.java:36)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 minute(s)
	at io.lettuce.core.internal.ExceptionFactory.createTimeoutException(ExceptionFactory.java:59)
	at io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:246)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1083)
	... 27 common frames omitted
2025-07-18 21:22:32.426 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:22:37.918 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:22:43.419 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:22:48.918 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:22:54.426 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:22:59.928 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:23:05.434 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:23:10.943 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:34:55.881 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:34:56.488 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:607 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:34:56.491 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:34:56.673 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:182 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:34:56.677 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:34:56.891 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:214 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:34:56.896 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:34:57.081 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:185 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:34:57.086 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:34:57.603 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:517 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:34:57.607 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:34:57.855 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:248 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:34:57.860 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:34:58.051 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:191 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:34:58.056 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:34:58.221 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:34:58.262 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:206 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:34:58.264 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:34:58.493 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:229 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:34:58.496 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:34:59.083 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:587 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:34:59.087 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:34:59.337 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:250 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:34:59.340 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:34:59.530 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:190 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:34:59.534 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:34:59.770 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:236 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:34:59.773 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:34:59.965 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:192 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:34:59.968 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:00.214 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:246 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:00.219 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:00.407 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:188 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:00.411 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:00.653 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:242 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:00.657 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:00.846 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:189 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:00.851 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:01.084 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:233 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:01.088 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:01.282 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:194 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:01.286 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:01.482 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:196 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:01.486 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:01.726 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:240 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:01.730 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:02.322 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:592 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:02.326 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:02.573 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:247 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:02.574 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:02.769 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:195 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:02.773 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:03.016 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:243 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:03.019 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:03.212 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:193 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:03.216 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:03.454 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:238 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:03.458 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:03.617 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:159 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:03.620 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:03.732 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:35:04.240 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:620 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:04.244 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:04.805 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:561 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:04.807 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:05.364 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:557 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:05.367 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:05.600 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:233 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:05.603 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:05.761 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:158 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:05.764 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:06.360 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:596 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:06.364 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:06.553 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:189 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:06.555 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:06.804 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:249 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:06.806 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:07.036 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:230 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:07.040 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:07.231 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:190 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:07.234 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:07.464 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:230 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:07.469 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:07.659 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:190 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:07.663 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:07.913 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:250 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:07.917 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:08.105 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:188 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:08.109 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:08.340 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:231 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:08.342 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:08.892 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:549 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:08.897 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:09.088 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:191 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:09.093 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:09.243 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:35:09.329 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:236 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:09.332 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:09.525 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:193 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:09.527 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:09.761 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:234 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:09.765 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:09.951 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:186 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:09.954 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:10.199 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:245 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:10.203 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:10.396 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:193 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:10.400 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:10.604 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:204 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:10.609 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:10.800 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:191 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:10.804 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:11.016 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:212 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:11.020 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:11.255 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:235 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:11.259 [scheduled-task-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:11.422 [scheduled-task-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:163 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:11.425 [scheduled-task-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:11.632 [scheduled-task-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:207 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:11.636 [scheduled-task-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:11.827 [scheduled-task-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:191 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:11.832 [scheduled-task-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:12.076 [scheduled-task-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:244 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:12.080 [scheduled-task-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:12.275 [scheduled-task-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:196 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:12.279 [scheduled-task-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:12.518 [scheduled-task-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:239 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:12.523 [scheduled-task-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:12.743 [scheduled-task-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:220 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:12.746 [scheduled-task-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:12.907 [scheduled-task-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:161 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:12.912 [scheduled-task-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:13.164 [scheduled-task-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:252 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:13.168 [scheduled-task-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:13.355 [scheduled-task-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:187 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:13.360 [scheduled-task-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:13.591 [scheduled-task-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:231 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:13.597 [scheduled-task-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:13.790 [scheduled-task-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:193 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:13.795 [scheduled-task-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:14.027 [scheduled-task-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:232 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:14.030 [scheduled-task-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:14.220 [scheduled-task-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:190 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:14.223 [scheduled-task-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:14.471 [scheduled-task-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:248 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:14.475 [scheduled-task-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:14.663 [scheduled-task-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:188 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:14.667 [scheduled-task-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:14.767 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:35:14.904 [scheduled-task-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:237 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:14.907 [scheduled-task-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:15.071 [scheduled-task-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:164 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:15.075 [scheduled-task-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:15.311 [scheduled-task-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:236 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:15.314 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:15.503 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:189 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:15.507 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:15.756 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:249 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:15.760 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:15.962 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:202 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:15.966 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:16.608 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:642 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:16.610 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:16.844 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:234 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:16.847 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:17.057 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:210 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:17.061 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:17.288 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:227 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:17.291 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:17.644 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:353 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:17.650 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:17.895 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:245 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:17.899 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:18.629 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:730 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:18.632 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:18.984 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:352 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:18.987 [scheduled-task-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:19.288 [scheduled-task-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:301 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:19.292 [scheduled-task-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:19.519 [scheduled-task-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:227 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:19.525 [scheduled-task-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:19.718 [scheduled-task-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:193 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:19.722 [scheduled-task-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:19.957 [scheduled-task-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:235 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:19.961 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:20.147 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:186 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:20.151 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:20.277 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:35:20.340 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:189 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:20.346 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:20.572 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:226 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:20.575 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:20.760 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:185 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:20.765 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:21.008 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:244 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:21.012 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:21.198 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:186 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:21.203 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:21.439 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:236 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:21.444 [scheduled-task-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:21.635 [scheduled-task-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:191 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:21.639 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:21.838 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:199 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:21.842 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:22.124 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:282 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:22.129 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:22.309 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:180 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:22.312 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:22.548 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:236 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:22.552 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:22.743 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:191 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:22.745 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:22.990 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:245 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:22.994 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:23.170 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:176 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:23.173 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:23.372 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:199 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:23.376 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:23.588 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:212 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:23.590 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:23.781 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:191 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:23.784 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:23.991 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:207 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:23.994 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:24.188 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:194 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:24.190 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:24.435 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:245 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:24.438 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:24.597 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:159 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:24.600 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:24.832 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:232 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:24.835 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:24.998 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:163 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:25.000 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:25.240 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:240 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:25.243 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:25.434 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:191 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:25.436 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:25.684 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:248 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:25.686 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:25.785 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:35:25.873 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:187 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:25.876 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:26.111 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:235 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:26.114 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:26.277 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:163 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:26.280 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:26.516 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:236 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:26.517 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:26.709 [scheduled-task-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:192 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:26.711 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:26.935 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:224 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:26.941 [scheduled-task-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:27.173 [scheduled-task-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:232 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:27.176 [scheduled-task-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:27.364 [scheduled-task-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:188 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:27.367 [scheduled-task-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:27.619 [scheduled-task-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:252 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:27.623 [scheduled-task-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:27.817 [scheduled-task-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:194 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:27.821 [scheduled-task-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:28.435 [scheduled-task-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:614 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:28.439 [scheduled-task-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:28.626 [scheduled-task-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:187 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:28.630 [scheduled-task-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:28.857 [scheduled-task-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:227 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:28.861 [scheduled-task-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:29.138 [scheduled-task-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:277 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:29.142 [scheduled-task-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:29.377 [scheduled-task-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:235 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:29.381 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:29.616 [scheduled-task-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:235 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:29.619 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:29.789 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:170 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:29.792 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:30.028 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:236 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:30.031 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:30.230 [scheduled-task-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:199 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:30.234 [scheduled-task-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:30.479 [scheduled-task-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:245 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:30.483 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:30.672 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:189 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:31.293 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:35:32.958 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:33.125 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:167 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:36.802 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:35:42.310 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:35:42.951 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:43.531 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:580 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:47.819 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:35:52.953 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:35:53.132 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:179 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:35:53.324 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:35:58.829 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:36:02.953 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:36:03.118 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:166 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:36:04.337 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:36:09.846 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:36:12.949 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:36:13.111 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:162 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:36:15.353 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:36:20.860 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:36:22.949 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:36:23.124 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:175 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:36:26.367 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:36:31.875 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:36:32.955 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:36:33.120 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:165 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:36:37.382 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:36:42.891 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:36:42.955 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:36:43.126 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:171 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:36:48.399 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:36:52.951 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:36:53.129 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:178 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:36:53.904 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:36:59.409 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:37:02.952 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:37:03.122 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:170 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:37:04.917 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:37:10.429 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:37:12.951 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:37:13.114 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:164 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:37:15.938 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:37:21.446 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:37:22.955 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:37:23.122 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:167 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:37:26.957 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:37:32.469 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:37:32.959 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:37:33.131 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:172 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:37:37.974 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:37:42.951 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:37:43.120 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:169 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:37:43.482 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:37:48.989 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:37:52.950 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:37:53.118 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:168 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:37:54.493 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:37:59.998 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:38:02.950 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:38:03.166 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:216 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:38:05.508 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:38:11.014 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:38:12.952 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:38:13.128 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:176 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:38:16.523 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:38:22.028 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:38:22.948 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:38:23.107 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:159 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:38:27.537 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:38:32.955 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:38:33.043 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:38:33.119 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:164 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:38:38.549 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:38:42.950 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:38:43.113 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:163 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:38:44.057 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:38:49.568 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:38:52.950 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:38:53.486 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:536 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:38:55.076 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:39:00.583 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:39:02.950 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:39:03.116 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:166 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:39:06.092 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:39:11.606 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:39:12.951 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:39:13.117 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:166 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:39:17.114 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:39:22.623 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:39:22.951 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:39:23.145 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:194 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:39:28.127 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:39:32.959 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:39:33.132 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:173 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:39:33.636 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:39:39.146 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:39:42.950 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:39:43.118 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:168 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:39:44.655 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:39:50.166 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:39:52.949 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:39:53.121 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:172 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:39:55.672 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:40:01.179 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:40:02.948 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:40:03.114 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:166 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:40:06.683 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:40:12.186 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:40:12.961 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:40:13.139 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:178 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:40:17.698 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:40:22.948 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:40:23.109 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:161 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:40:23.208 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:40:28.715 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:40:32.949 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:40:33.123 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:174 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:40:34.219 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:40:39.730 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:40:42.510 [MessageBroker-7] INFO  o.s.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 1 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(1)-CONNECTED(1)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 12], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 2], sockJsScheduler[pool size = 10, active threads = 1, queued tasks = 1, completed tasks = 706]
2025-07-18 21:40:42.949 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:40:43.111 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:162 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:40:45.240 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:40:50.747 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:40:52.948 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:40:53.101 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:153 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:40:56.255 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:41:01.763 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:41:02.948 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:41:03.113 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:165 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:41:07.269 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:41:12.776 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:41:12.950 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:41:13.864 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:914 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:41:18.286 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:41:22.948 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:41:23.114 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:167 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:41:23.790 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:41:29.297 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:41:32.951 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:41:33.136 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:185 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:41:34.803 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:41:40.313 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:41:42.949 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:41:43.112 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:163 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:41:45.823 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:41:51.331 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:41:52.947 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:41:53.113 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:166 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:41:56.838 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:42:02.345 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:42:02.950 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:42:03.158 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:208 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:42:07.859 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:42:12.963 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:42:13.132 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:169 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:42:13.378 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:42:18.893 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:42:22.968 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:42:23.135 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:167 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:42:24.401 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:42:29.910 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:42:32.976 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:42:33.216 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:240 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:42:35.422 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:42:40.929 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:42:42.972 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:42:43.138 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:166 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:42:46.440 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:42:51.947 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:42:52.975 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:42:53.144 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:169 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:42:57.457 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:43:02.967 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:43:02.979 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:43:03.146 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:167 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:43:08.477 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:43:12.974 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:43:13.523 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:549 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:43:13.997 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:43:19.502 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
2025-07-18 21:43:22.977 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL | {"flag":0}
2025-07-18 21:43:23.134 [scheduled-task-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:157 | http://yinshu.iok.la:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aolvBYLxGfTrhaa3QBDltL
2025-07-18 21:43:25.010 [Druid-ConnectionPool-Create-1631969118] ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: **************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:132)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:244)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:126)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1687)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2914)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 11 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 17 common frames omitted
