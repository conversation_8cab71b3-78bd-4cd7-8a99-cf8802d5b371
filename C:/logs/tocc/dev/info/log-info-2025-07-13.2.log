2025-07-13 13:35:43.707 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/road-transport/ads_warning_vehicle_detail [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:35:43.713 [http-nio-9091-exec-5] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/road-transport/ads_warning_vehicle_detail
2025-07-13 13:35:43.714 [http-nio-9091-exec-5] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@57eab4d7, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:35:43.716 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '客运' AND (dt >= '2025-07-02' AND dt <= '2025-08-13')","dt":["2025-07-02","2025-08-13"],"s":0,"dtExtraYear":"2025","pageSize":10,"type":"客运","pageNum":1,"n":10}
2025-07-13 13:35:43.924 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:208 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:35:44.857 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/road-transport/ads_warning_vehicle_detail [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:35:44.862 [http-nio-9091-exec-10] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/road-transport/ads_warning_vehicle_detail
2025-07-13 13:35:44.863 [http-nio-9091-exec-10] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@60c883c9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:35:44.867 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '客运' AND (dt >= '2025-07-02' AND dt <= '2025-08-13')","dt":["2025-07-02","2025-08-13"],"s":0,"dtExtraYear":"2025","pageSize":10,"type":"客运","pageNum":1,"n":10}
2025-07-13 13:35:45.079 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:213 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:35:45.705 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/road-transport/ads_warning_vehicle_detail [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:35:45.709 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/road-transport/ads_warning_vehicle_detail
2025-07-13 13:35:45.710 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@2539612c, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:35:45.712 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '客运' AND (dt >= '2025-07-02' AND dt <= '2025-08-13')","dt":["2025-07-02","2025-08-13"],"s":0,"dtExtraYear":"2025","pageSize":10,"type":"客运","pageNum":1,"n":10}
2025-07-13 13:35:45.978 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:266 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:35:48.231 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getCapacityClassification [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:35:48.231 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOverallSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:35:48.231 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleClassification [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:35:48.234 [http-nio-9091-exec-7] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOverallSummary
2025-07-13 13:35:48.234 [http-nio-9091-exec-8] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getCapacityClassification
2025-07-13 13:35:48.234 [http-nio-9091-exec-3] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleClassification
2025-07-13 13:35:48.235 [http-nio-9091-exec-7] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@44772c6e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:35:48.235 [http-nio-9091-exec-3] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@7acdd14b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:35:48.235 [http-nio-9091-exec-8] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@2833c17c, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:35:48.237 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null order by dt desc"}
2025-07-13 13:35:48.237 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null order by dt desc"}
2025-07-13 13:35:48.237 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null order by dt desc"}
2025-07-13 13:35:48.474 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:237 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:35:48.476 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:239 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:35:48.477 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:240 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:35:49.743 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:35:49.747 [http-nio-9091-exec-6] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBasicData
2025-07-13 13:35:49.747 [http-nio-9091-exec-6] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@1bb794cf, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:35:49.750 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-13 13:35:49.947 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:197 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:35:49.954 [http-nio-9091-exec-16] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:35:49.956 [http-nio-9091-exec-16] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOperationOverview
2025-07-13 13:35:49.956 [http-nio-9091-exec-16] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@568ce293, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:35:49.958 [http-nio-9091-exec-16] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" hour = null and vehicle_type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-13 13:35:50.200 [http-nio-9091-exec-16] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:242 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:35:53.170 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:35:53.174 [http-nio-9091-exec-2] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBasicData
2025-07-13 13:35:53.174 [http-nio-9091-exec-2] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3b2610a1, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:35:53.175 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-13 13:35:53.389 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:214 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:35:53.393 [http-nio-9091-exec-15] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:35:53.395 [http-nio-9091-exec-15] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOperationOverview
2025-07-13 13:35:53.395 [http-nio-9091-exec-15] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@2a59f78, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:35:53.397 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" hour = null and vehicle_type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-13 13:35:53.670 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:273 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:35:55.524 [http-nio-9091-exec-13] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:35:55.528 [http-nio-9091-exec-13] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:35:55.528 [http-nio-9091-exec-13] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@4fe14660, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:35:55.531 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '网约车'  and warndt >= '2025-07-13' and warndt <= '2025-07-13' ","dt":["2025-07-13","2025-07-13"],"s":0,"pageSize":10,"vehicle_type":"网约车","pageNum":1,"n":10}
2025-07-13 13:35:55.783 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:252 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:05.935 [http-nio-9091-exec-11] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:05.937 [http-nio-9091-exec-11] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:36:05.937 [http-nio-9091-exec-11] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@1ad8792, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:05.938 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:36:05.993 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:05.993 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:05.993 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:05.995 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnInfoUnreadCount [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:05.996 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBasicData
2025-07-13 13:36:05.996 [http-nio-9091-exec-10] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:36:05.996 [http-nio-9091-exec-10] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@7a45b5ad, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:05.996 [http-nio-9091-exec-5] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:36:05.996 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@238c5964, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:05.996 [http-nio-9091-exec-5] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3b0061ce, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:05.997 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:36:05.997 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:36:05.997 [http-nio-9091-exec-8] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnInfoUnreadCount
2025-07-13 13:36:05.997 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-13 13:36:05.997 [http-nio-9091-exec-8] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@79badc80, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:06.000 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"warndt = '2025-07-13' and flag = 0","flag":0,"s":0,"n":1}
2025-07-13 13:36:06.047 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:06.049 [http-nio-9091-exec-3] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:36:06.050 [http-nio-9091-exec-3] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@15dcad01, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:06.052 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:36:06.119 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:181 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:06.120 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:06.123 [http-nio-9091-exec-1] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:36:06.123 [http-nio-9091-exec-1] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@1059093d, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:06.126 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:36:06.165 [http-nio-9091-exec-6] INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_cd8218cd51f800ed2b73e5751cb3f4f9=1750299100,1751184993;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-07-13 13:36:06.165 [http-nio-9091-exec-4] INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_cd8218cd51f800ed2b73e5751cb3f4f9=1750299100,1751184993;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-07-13 13:36:06.173 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/ws/info [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:06.173 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/ws/info [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:06.173 [http-nio-9091-exec-16] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/ws/info [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:06.192 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:195 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:06.193 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:196 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:06.198 [http-nio-9091-exec-15] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:06.199 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnInfoUnreadCount [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:06.202 [http-nio-9091-exec-12] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/ws/info [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:06.202 [http-nio-9091-exec-15] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBasicData
2025-07-13 13:36:06.203 [http-nio-9091-exec-15] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@197571d7, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:06.203 [http-nio-9091-exec-13] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/ws/info [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:06.204 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnInfoUnreadCount
2025-07-13 13:36:06.204 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@38cc81d9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:06.208 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-13 13:36:06.208 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"warndt = '2025-07-13' and flag = 0","flag":0,"s":0,"n":1}
2025-07-13 13:36:06.212 [http-nio-9091-exec-14] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/ws/555/n3okzj1v/websocket [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:06.310 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:184 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:06.312 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:06.313 [http-nio-9091-exec-5] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:36:06.313 [http-nio-9091-exec-5] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3e901a04, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:06.314 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:36:06.355 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:358 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:06.357 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:06.358 [http-nio-9091-exec-2] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:36:06.359 [http-nio-9091-exec-2] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@44cd23fc, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:06.359 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:36:06.375 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:167 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:06.376 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnInfoUnreadCount [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:06.378 [http-nio-9091-exec-6] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnInfoUnreadCount
2025-07-13 13:36:06.378 [http-nio-9091-exec-6] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3898efbb, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:06.380 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"warndt = '2025-07-13' and flag = 0","flag":0,"s":0,"n":1}
2025-07-13 13:36:06.381 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:173 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:06.382 [http-nio-9091-exec-16] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:06.385 [http-nio-9091-exec-16] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:36:06.385 [http-nio-9091-exec-16] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@5fee18ad, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:06.386 [http-nio-9091-exec-16] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:36:06.398 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:398 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:06.400 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:06.403 [http-nio-9091-exec-4] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:36:06.403 [http-nio-9091-exec-4] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@405b6d41, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:06.405 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:36:06.502 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:450 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:06.503 [http-nio-9091-exec-13] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnInfoUnreadCount [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:06.524 [http-nio-9091-exec-13] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnInfoUnreadCount
2025-07-13 13:36:06.524 [http-nio-9091-exec-13] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@52d2e026, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:06.528 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"warndt = '2025-07-13' and flag = 0","flag":0,"s":0,"n":1}
2025-07-13 13:36:06.540 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:226 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:06.543 [http-nio-9091-exec-12] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:06.546 [http-nio-9091-exec-12] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:36:06.546 [http-nio-9091-exec-12] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@37d5a79d, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:06.548 [http-nio-9091-exec-12] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:36:06.560 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:201 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:06.565 [http-nio-9091-exec-14] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:06.570 [http-nio-9091-exec-14] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:36:06.570 [http-nio-9091-exec-14] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@15bddd5f, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:06.573 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:36:06.580 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:175 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:06.582 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnInfoUnreadCount [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:06.587 [http-nio-9091-exec-7] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnInfoUnreadCount
2025-07-13 13:36:06.587 [http-nio-9091-exec-16] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:201 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:06.587 [http-nio-9091-exec-7] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@39a7b743, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:06.589 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"warndt = '2025-07-13' and flag = 0","flag":0,"s":0,"n":1}
2025-07-13 13:36:06.590 [http-nio-9091-exec-11] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:06.592 [http-nio-9091-exec-11] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBasicData
2025-07-13 13:36:06.593 [http-nio-9091-exec-11] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@4cd2c791, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:06.594 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-13 13:36:06.609 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:229 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:06.611 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:06.614 [http-nio-9091-exec-1] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:36:06.615 [http-nio-9091-exec-1] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3b87ba5, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:06.618 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:36:06.718 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:190 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:06.720 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:06.722 [http-nio-9091-exec-10] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:36:06.722 [http-nio-9091-exec-10] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@28029b27, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:06.723 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:36:06.746 [http-nio-9091-exec-12] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:198 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:06.747 [http-nio-9091-exec-15] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:06.749 [http-nio-9091-exec-15] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOperationOverview
2025-07-13 13:36:06.749 [http-nio-9091-exec-15] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@10603401, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:06.750 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" hour = null and vehicle_type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-13 13:36:06.771 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:198 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:06.773 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:06.774 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOperationOverview
2025-07-13 13:36:06.774 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@5934b401, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:06.775 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" hour = null and vehicle_type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-13 13:36:06.775 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:186 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:06.806 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:188 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:06.840 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:246 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:06.846 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:06.850 [http-nio-9091-exec-8] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOperationOverview
2025-07-13 13:36:06.850 [http-nio-9091-exec-8] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@42dc4a9b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:06.855 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" hour = null and vehicle_type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-13 13:36:06.971 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:221 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:06.992 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:269 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:07.033 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:258 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:07.112 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:257 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:08.837 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:08.837 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnInfoUnreadCount [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:08.837 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:08.837 [http-nio-9091-exec-16] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:08.839 [http-nio-9091-exec-2] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:36:08.840 [http-nio-9091-exec-4] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnInfoUnreadCount
2025-07-13 13:36:08.840 [http-nio-9091-exec-5] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:36:08.840 [http-nio-9091-exec-4] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@5858aae3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:08.840 [http-nio-9091-exec-5] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3ad96a82, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:08.840 [http-nio-9091-exec-16] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBasicData
2025-07-13 13:36:08.841 [http-nio-9091-exec-16] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3355e209, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:08.840 [http-nio-9091-exec-2] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3782a7d6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:08.843 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:36:08.843 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"warndt = '2025-07-13' and flag = 0","flag":0,"s":0,"n":1}
2025-07-13 13:36:08.843 [http-nio-9091-exec-16] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-13 13:36:08.844 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:36:08.863 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/ws/info [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:08.906 [http-nio-9091-exec-13] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/ws/209/jizhns1h/websocket [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:09.039 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:196 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:09.040 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:197 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:09.044 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:200 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:09.079 [http-nio-9091-exec-16] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:236 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:09.082 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:09.085 [http-nio-9091-exec-7] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOperationOverview
2025-07-13 13:36:09.085 [http-nio-9091-exec-7] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@77a38bf9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:09.088 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" hour = null and vehicle_type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-13 13:36:09.310 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:222 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:11.952 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnInfoUnreadCount [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:11.952 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:11.952 [http-nio-9091-exec-15] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:11.952 [http-nio-9091-exec-11] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:11.957 [http-nio-9091-exec-15] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:36:11.957 [http-nio-9091-exec-10] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnInfoUnreadCount
2025-07-13 13:36:11.957 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBasicData
2025-07-13 13:36:11.957 [http-nio-9091-exec-11] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:36:11.957 [http-nio-9091-exec-15] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@1d706192, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:11.957 [http-nio-9091-exec-10] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@15be48f9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:11.957 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3e77ce5d, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:11.957 [http-nio-9091-exec-11] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@b9b272b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:11.959 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:36:11.959 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-13 13:36:11.959 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"warndt = '2025-07-13' and flag = 0","flag":0,"s":0,"n":1}
2025-07-13 13:36:11.959 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:36:11.973 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/ws/info [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:12.009 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/ws/590/2liqwpaw/websocket [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:12.148 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:189 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:12.149 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:190 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:12.152 [http-nio-9091-exec-12] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:12.154 [http-nio-9091-exec-12] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOperationOverview
2025-07-13 13:36:12.154 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:195 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:12.154 [http-nio-9091-exec-12] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@56683ab7, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:12.155 [http-nio-9091-exec-12] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" hour = null and vehicle_type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-13 13:36:12.192 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:233 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:12.360 [http-nio-9091-exec-12] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:205 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:14.248 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/road-transport/ads_passenger_transport_station [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:14.248 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/road-transport/ads_daily_overview_trend_hourly [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:14.248 [http-nio-9091-exec-14] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/road-transport/ads_basic_indicator_data [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:14.248 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:14.251 [http-nio-9091-exec-5] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:36:14.251 [http-nio-9091-exec-4] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/road-transport/ads_passenger_transport_station
2025-07-13 13:36:14.251 [http-nio-9091-exec-2] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/road-transport/ads_daily_overview_trend_hourly
2025-07-13 13:36:14.251 [http-nio-9091-exec-14] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/road-transport/ads_basic_indicator_data
2025-07-13 13:36:14.251 [http-nio-9091-exec-5] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@4b93612c, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:14.251 [http-nio-9091-exec-4] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@5c02621c, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:14.251 [http-nio-9091-exec-14] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@1e356d84, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:14.251 [http-nio-9091-exec-2] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@13b3b6d0, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:14.253 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100461/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '客运' and dt =  '2025-07-13'"}
2025-07-13 13:36:14.253 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100449/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '客运'"}
2025-07-13 13:36:14.253 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100459/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"dt = '2025-07-13'"}
2025-07-13 13:36:14.253 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:36:14.443 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:190 | http://yinshu.iok.la:8081/api/100459/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:14.449 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:196 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:14.484 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:231 | http://yinshu.iok.la:8081/api/100449/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:14.517 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:264 | http://yinshu.iok.la:8081/api/100461/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:14.971 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:14.971 [http-nio-9091-exec-16] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/road-transport/ads_warning_vehicle_detail [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:14.971 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/road-transport/ads_warning_vehicle_detail [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:14.975 [http-nio-9091-exec-7] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:36:14.975 [http-nio-9091-exec-1] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/road-transport/ads_warning_vehicle_detail
2025-07-13 13:36:14.975 [http-nio-9091-exec-16] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/road-transport/ads_warning_vehicle_detail
2025-07-13 13:36:14.975 [http-nio-9091-exec-1] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@67aa07a9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:14.975 [http-nio-9091-exec-7] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@510e1446, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:14.975 [http-nio-9091-exec-16] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@1dedc272, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:14.978 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:36:14.979 [http-nio-9091-exec-16] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"cartype = '客运' and warndt = '2025-07-13'"}
2025-07-13 13:36:14.978 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"cartype = '客运' and warndt = '2025-07-13'"}
2025-07-13 13:36:15.182 [http-nio-9091-exec-16] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:203 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:15.223 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:245 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:15.236 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:258 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:17.003 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/road-transport/ads_warning_vehicle_detail [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:17.007 [http-nio-9091-exec-8] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/road-transport/ads_warning_vehicle_detail
2025-07-13 13:36:17.008 [http-nio-9091-exec-8] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3a237e47, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:17.012 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"cartype = '客运' AND (warndt >= '2025-07-13' AND warndt <= '2025-07-13')","cartype":"客运","s":0,"pageSize":10,"warndt":["2025-07-13","2025-07-13"],"pageNum":1,"n":10}
2025-07-13 13:36:17.241 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:229 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:23.205 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOverallSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:23.205 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleClassification [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:23.205 [http-nio-9091-exec-13] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getCapacityClassification [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:23.208 [http-nio-9091-exec-3] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOverallSummary
2025-07-13 13:36:23.208 [http-nio-9091-exec-6] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleClassification
2025-07-13 13:36:23.208 [http-nio-9091-exec-13] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getCapacityClassification
2025-07-13 13:36:23.208 [http-nio-9091-exec-3] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@6cb8214a, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:23.208 [http-nio-9091-exec-6] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@1394d0a4, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:23.208 [http-nio-9091-exec-13] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3b6326a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:23.210 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null order by dt desc"}
2025-07-13 13:36:23.210 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null order by dt desc"}
2025-07-13 13:36:23.210 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null order by dt desc"}
2025-07-13 13:36:23.408 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:198 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:23.408 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:198 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:23.448 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:238 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:24.731 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:24.735 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBasicData
2025-07-13 13:36:24.735 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@2c072cc7, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:24.737 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '公交车' ","type":"公交车"}
2025-07-13 13:36:25.139 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:402 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:25.149 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:25.154 [http-nio-9091-exec-10] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOperationSummary
2025-07-13 13:36:25.154 [http-nio-9091-exec-10] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@18158e51, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:25.157 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null and company = null and line_name = null and type = '公交车' and dt = '2025-07-13' ","selectType":"1"}
2025-07-13 13:36:25.369 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:212 | http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:25.863 [http-nio-9091-exec-12] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:25.863 [http-nio-9091-exec-15] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:25.867 [http-nio-9091-exec-15] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:36:25.867 [http-nio-9091-exec-12] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:36:25.868 [http-nio-9091-exec-15] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@426253e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:25.868 [http-nio-9091-exec-12] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@4b1f6af2, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:25.870 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:36:25.870 [http-nio-9091-exec-12] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:36:26.081 [http-nio-9091-exec-12] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:211 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:26.096 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:226 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:27.333 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:27.337 [http-nio-9091-exec-4] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:36:27.337 [http-nio-9091-exec-4] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@4e26b836, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:27.340 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:36:27.590 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:250 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:28.005 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:28.009 [http-nio-9091-exec-5] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:36:28.009 [http-nio-9091-exec-5] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@6ef43162, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:28.013 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:36:28.414 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:402 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:28.793 [http-nio-9091-exec-14] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:28.796 [http-nio-9091-exec-14] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:36:28.796 [http-nio-9091-exec-14] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@244f1b44, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:28.798 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt >= '2025-07-13' and warndt <= '2025-07-13' ","dt":["2025-07-13","2025-07-13"],"s":0,"pageSize":10,"vehicle_type":"公交车","pageNum":1,"n":10}
2025-07-13 13:36:29.053 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:255 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:36:58.068 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:36:58.070 [http-nio-9091-exec-2] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:36:58.070 [http-nio-9091-exec-2] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@65afe875, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:36:58.071 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:36:58.291 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:220 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:37:01.535 [http-nio-9091-exec-16] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:37:01.537 [http-nio-9091-exec-16] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:37:01.537 [http-nio-9091-exec-16] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@48d63130, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:37:01.538 [http-nio-9091-exec-16] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:37:01.741 [http-nio-9091-exec-16] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:203 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:37:10.268 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:37:10.270 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:37:10.270 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3a73f6c4, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:37:10.271 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:37:10.709 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:438 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:37:10.866 [http-nio-9091-exec-11] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:37:10.868 [http-nio-9091-exec-11] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:37:10.868 [http-nio-9091-exec-11] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@355f2e1a, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:37:10.869 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:37:11.080 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:211 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:37:11.416 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:37:11.430 [http-nio-9091-exec-10] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:37:11.430 [http-nio-9091-exec-10] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@39dd61f2, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:37:11.432 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:37:11.630 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:198 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:37:12.070 [http-nio-9091-exec-12] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:37:12.072 [http-nio-9091-exec-12] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:37:12.072 [http-nio-9091-exec-12] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@4473d70d, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:37:12.073 [http-nio-9091-exec-12] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:37:12.264 [http-nio-9091-exec-12] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:191 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:37:12.718 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:37:12.720 [http-nio-9091-exec-4] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:37:12.720 [http-nio-9091-exec-4] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@7dd8b549, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:37:12.721 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:37:12.917 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:196 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:37:13.384 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:37:13.385 [http-nio-9091-exec-5] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:37:13.385 [http-nio-9091-exec-5] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@10bd4069, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:37:13.386 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:37:13.631 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:245 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:37:15.303 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:37:15.305 [http-nio-9091-exec-2] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:37:15.305 [http-nio-9091-exec-2] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@114ec710, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:37:15.306 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:37:15.516 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:210 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:37:16.160 [http-nio-9091-exec-16] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/road-transport/ads_basic_indicator_data [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:37:16.160 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/road-transport/ads_passenger_transport_station [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:37:16.160 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/road-transport/ads_daily_overview_trend_hourly [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:37:16.164 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:37:16.169 [http-nio-9091-exec-16] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/road-transport/ads_basic_indicator_data
2025-07-13 13:37:16.169 [http-nio-9091-exec-7] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:37:16.169 [http-nio-9091-exec-3] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/road-transport/ads_daily_overview_trend_hourly
2025-07-13 13:37:16.169 [http-nio-9091-exec-1] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/road-transport/ads_passenger_transport_station
2025-07-13 13:37:16.169 [http-nio-9091-exec-7] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@386f8a4d, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:37:16.169 [http-nio-9091-exec-16] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@6a5fb0cb, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:37:16.169 [http-nio-9091-exec-3] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@7bbb79cb, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:37:16.169 [http-nio-9091-exec-1] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@2ba391f1, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:37:16.171 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:37:16.171 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100459/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"dt = '2025-07-13'"}
2025-07-13 13:37:16.172 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100461/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '客运' and dt =  '2025-07-13'"}
2025-07-13 13:37:16.172 [http-nio-9091-exec-16] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100449/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '客运'"}
2025-07-13 13:37:16.357 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:186 | http://yinshu.iok.la:8081/api/100459/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:37:16.543 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:372 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:37:16.544 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:372 | http://yinshu.iok.la:8081/api/100461/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:37:16.567 [http-nio-9091-exec-16] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:395 | http://yinshu.iok.la:8081/api/100449/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:37:22.794 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getCapacityClassification [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:37:22.794 [http-nio-9091-exec-13] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleClassification [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:37:22.794 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOverallSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:37:22.797 [http-nio-9091-exec-6] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getCapacityClassification
2025-07-13 13:37:22.797 [http-nio-9091-exec-8] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOverallSummary
2025-07-13 13:37:22.797 [http-nio-9091-exec-13] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleClassification
2025-07-13 13:37:22.797 [http-nio-9091-exec-13] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@715bc539, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:37:22.797 [http-nio-9091-exec-8] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3685992f, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:37:22.797 [http-nio-9091-exec-6] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@16264aed, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:37:22.799 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null order by dt desc"}
2025-07-13 13:37:22.799 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null order by dt desc"}
2025-07-13 13:37:22.799 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null order by dt desc"}
2025-07-13 13:37:22.992 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:193 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:37:23.006 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:207 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:37:23.006 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:207 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:37:26.985 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:37:26.989 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBasicData
2025-07-13 13:37:26.989 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@6a570d4b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:37:26.991 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '公交车' ","type":"公交车"}
2025-07-13 13:37:27.149 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:158 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:37:27.162 [http-nio-9091-exec-11] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:37:27.170 [http-nio-9091-exec-11] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOperationSummary
2025-07-13 13:37:27.170 [http-nio-9091-exec-11] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@5b3c484b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:37:27.172 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null and company = null and line_name = null and type = '公交车' and dt = '2025-07-13' ","selectType":"1"}
2025-07-13 13:37:27.378 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:206 | http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:37:28.577 [http-nio-9091-exec-15] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusRouteDetail [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:37:28.581 [http-nio-9091-exec-15] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBusRouteDetail
2025-07-13 13:37:28.581 [http-nio-9091-exec-15] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@1e4f4c1a, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:37:28.583 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county != null and company != null and line_name != null","selectType":"2"}
2025-07-13 13:37:28.784 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:201 | http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:37:29.286 [http-nio-9091-exec-12] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:37:29.286 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:37:29.293 [http-nio-9091-exec-4] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:37:29.294 [http-nio-9091-exec-12] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:37:29.294 [http-nio-9091-exec-12] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@4fe67eeb, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:37:29.294 [http-nio-9091-exec-4] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@38e9c647, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:37:29.298 [http-nio-9091-exec-12] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:37:29.298 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:37:29.529 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:231 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:37:29.562 [http-nio-9091-exec-12] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:264 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:37:31.005 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:37:31.011 [http-nio-9091-exec-5] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:37:31.011 [http-nio-9091-exec-5] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@641781e3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:37:31.015 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:37:31.266 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:251 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:37:31.639 [http-nio-9091-exec-14] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:37:31.644 [http-nio-9091-exec-14] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:37:31.644 [http-nio-9091-exec-14] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@68f3c723, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:37:31.646 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:37:31.864 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:218 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:38:04.881 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/build-transport/invest/getInvestmentProgress [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:38:04.930 [http-nio-9091-exec-2] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/build-transport/invest/getInvestmentProgress
2025-07-13 13:38:04.930 [http-nio-9091-exec-2] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3956d3bb, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:38:04.945 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100492/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"(year = '2025' or year = '2024' or year = '2023' or year = '2022' or year = '2021') order by year asc","s":0,"n":100}
2025-07-13 13:38:05.349 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:404 | http://yinshu.iok.la:8081/api/100492/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:38:05.591 [http-nio-9091-exec-16] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getAnalysisList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:38:05.591 [http-nio-9091-exec-13] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getBusList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:38:05.591 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getPassengerList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:38:05.591 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getTotalInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:38:05.591 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getArriverOutList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:38:05.598 [http-nio-9091-exec-13] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getBusList
2025-07-13 13:38:05.598 [http-nio-9091-exec-16] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getAnalysisList
2025-07-13 13:38:05.598 [http-nio-9091-exec-13] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@1380a1bd, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:38:05.598 [http-nio-9091-exec-16] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3f74fca8, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:38:05.598 [http-nio-9091-exec-7] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getArriverOutList
2025-07-13 13:38:05.599 [http-nio-9091-exec-1] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getTotalInfo
2025-07-13 13:38:05.599 [http-nio-9091-exec-7] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@4e86e703, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:38:05.599 [http-nio-9091-exec-1] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@68b2a24b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:38:05.599 [http-nio-9091-exec-3] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getPassengerList
2025-07-13 13:38:05.599 [http-nio-9091-exec-3] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@7751f670, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:38:05.601 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100453/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"dt='2025-07-13' and hour = null","dataType":"1"}
2025-07-13 13:38:05.602 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100452/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"dt='2025-07-13' and hour = null and type = '机场'","dataType":"1","transportType":"1"}
2025-07-13 13:38:05.601 [http-nio-9091-exec-16] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100450/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"dt='2025-07-13' and city = null and type = '机场'","dataType":"1","transportType":"1"}
2025-07-13 13:38:05.602 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100446/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"id='2025'","s":0,"n":100}
2025-07-13 13:38:05.602 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100448/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" dt='2025-07-13' and hour = null","dataType":"1"}
2025-07-13 13:38:05.858 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:256 | http://yinshu.iok.la:8081/api/100452/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:38:06.001 [http-nio-9091-exec-16] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:400 | http://yinshu.iok.la:8081/api/100450/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:38:06.008 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:406 | http://yinshu.iok.la:8081/api/100446/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:38:06.010 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:409 | http://yinshu.iok.la:8081/api/100453/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:38:06.044 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:441 | http://yinshu.iok.la:8081/api/100448/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:38:06.529 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getVehicleList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:38:06.532 [http-nio-9091-exec-8] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getVehicleList
2025-07-13 13:38:06.532 [http-nio-9091-exec-8] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@7f5b4b3f, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:38:06.534 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100479/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"order by station, flag desc","s":0,"stationType":"机场","n":100000}
2025-07-13 13:38:07.107 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:573 | http://yinshu.iok.la:8081/api/100479/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:38:07.210 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:38:07.212 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getWarningTotal [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:38:07.213 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:38:07.214 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@41cf04d6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:38:07.214 [http-nio-9091-exec-10] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getWarningTotal
2025-07-13 13:38:07.215 [http-nio-9091-exec-10] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@36fb04b3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:38:07.216 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:38:07.217 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"locationtype='机场'","s":0,"locationtype":"机场","n":10000}
2025-07-13 13:38:07.469 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:253 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:38:10.364 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:3148 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:38:12.140 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnInfoUnreadCount [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:38:12.140 [http-nio-9091-exec-14] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getTotalInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:38:12.140 [http-nio-9091-exec-12] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:38:12.140 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getAnalysisList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:38:12.140 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:38:12.140 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getArriverOutList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:38:12.141 [http-nio-9091-exec-12] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:38:12.141 [http-nio-9091-exec-4] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:38:12.141 [http-nio-9091-exec-12] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@6acf1c68, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:38:12.142 [http-nio-9091-exec-4] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@7c786446, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:38:12.142 [http-nio-9091-exec-3] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getAnalysisList
2025-07-13 13:38:12.142 [http-nio-9091-exec-3] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@6003136b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:38:12.147 [http-nio-9091-exec-2] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getArriverOutList
2025-07-13 13:38:12.147 [http-nio-9091-exec-14] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getTotalInfo
2025-07-13 13:38:12.147 [http-nio-9091-exec-14] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@6290417d, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:38:12.148 [http-nio-9091-exec-5] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnInfoUnreadCount
2025-07-13 13:38:12.148 [http-nio-9091-exec-5] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@615b6f5b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:38:12.148 [http-nio-9091-exec-2] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@1b4da294, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:38:12.148 [http-nio-9091-exec-12] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:38:12.148 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100450/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"dt='2025-07-13' and city = null and type = '机场'","dataType":"1","transportType":"1"}
2025-07-13 13:38:12.148 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:38:12.151 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"warndt = '2025-07-13' and flag = 0","flag":0,"s":0,"n":1}
2025-07-13 13:38:12.151 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100446/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"id='2025'","s":0,"n":100}
2025-07-13 13:38:12.151 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100448/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" dt='2025-07-13' and hour = null","dataType":"1"}
2025-07-13 13:38:12.165 [http-nio-9091-exec-16] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/ws/info [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:38:12.204 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/ws/727/hroytlvj/websocket [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:38:12.312 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:161 | http://yinshu.iok.la:8081/api/100446/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:38:12.319 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getPassengerList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:38:12.326 [http-nio-9091-exec-6] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getPassengerList
2025-07-13 13:38:12.326 [http-nio-9091-exec-6] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@49b71f3b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:38:12.328 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:177 | http://yinshu.iok.la:8081/api/100448/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:38:12.329 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100452/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"dt='2025-07-13' and hour = null and type = '机场'","dataType":"1","transportType":"1"}
2025-07-13 13:38:12.330 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getBusList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:38:12.331 [http-nio-9091-exec-8] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getBusList
2025-07-13 13:38:12.331 [http-nio-9091-exec-8] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@6abb6e75, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:38:12.332 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100453/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"dt='2025-07-13' and hour = null","dataType":"1"}
2025-07-13 13:38:12.333 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:185 | http://yinshu.iok.la:8081/api/100450/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:38:12.350 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:202 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:38:12.350 [http-nio-9091-exec-12] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:202 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:38:12.495 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:344 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:38:12.518 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:189 | http://yinshu.iok.la:8081/api/100452/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:38:12.523 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:191 | http://yinshu.iok.la:8081/api/100453/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:38:13.651 [http-nio-9091-exec-11] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:38:13.651 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getWarningTotal [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:38:13.653 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getWarningTotal
2025-07-13 13:38:13.653 [http-nio-9091-exec-11] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:38:13.653 [http-nio-9091-exec-11] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3045b1c4, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:38:13.653 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@c1d10d4, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:38:13.653 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"locationtype='机场'","s":0,"locationtype":"机场","n":10000}
2025-07-13 13:38:13.653 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:38:13.859 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:206 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:38:16.655 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:3002 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:38:25.249 [http-nio-9091-exec-15] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getWarningList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:38:25.252 [http-nio-9091-exec-15] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getWarningList
2025-07-13 13:38:25.252 [http-nio-9091-exec-15] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@7fa97c8f, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:38:25.255 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"locationtype='机场'  order by warntime desc","s":0,"pageSize":10,"locationtype":"机场","pageNum":1,"n":10}
2025-07-13 13:38:25.456 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:201 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:38:59.692 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getCapacityClassification [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:38:59.692 [http-nio-9091-exec-13] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleClassification [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:38:59.692 [http-nio-9091-exec-16] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOverallSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:38:59.695 [http-nio-9091-exec-1] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getCapacityClassification
2025-07-13 13:38:59.695 [http-nio-9091-exec-16] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOverallSummary
2025-07-13 13:38:59.695 [http-nio-9091-exec-13] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleClassification
2025-07-13 13:38:59.695 [http-nio-9091-exec-13] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@14529331, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:38:59.695 [http-nio-9091-exec-16] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@6b0d5a1e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:38:59.695 [http-nio-9091-exec-1] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@5b435fe5, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:38:59.697 [http-nio-9091-exec-16] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null order by dt desc"}
2025-07-13 13:38:59.697 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null order by dt desc"}
2025-07-13 13:38:59.697 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null order by dt desc"}
2025-07-13 13:39:00.085 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:388 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:39:00.089 [http-nio-9091-exec-16] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:392 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:39:00.100 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:403 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:39:01.522 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:39:01.526 [http-nio-9091-exec-7] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBasicData
2025-07-13 13:39:01.527 [http-nio-9091-exec-7] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@2b6412c9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:39:01.529 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '公交车' ","type":"公交车"}
2025-07-13 13:39:01.693 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:164 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:39:01.702 [http-nio-9091-exec-14] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:39:01.706 [http-nio-9091-exec-14] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOperationSummary
2025-07-13 13:39:01.707 [http-nio-9091-exec-14] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@751a7709, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:39:01.710 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null and company = null and line_name = null and type = '公交车' and dt = '2025-07-13' ","selectType":"1"}
2025-07-13 13:39:02.018 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:308 | http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:39:02.467 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:39:02.467 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:39:02.471 [http-nio-9091-exec-2] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:39:02.471 [http-nio-9091-exec-3] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:39:02.471 [http-nio-9091-exec-2] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@5a5121ce, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:39:02.471 [http-nio-9091-exec-3] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@7d49d1b7, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:39:02.473 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:39:02.474 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:39:02.693 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:220 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:39:02.725 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:251 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:39:03.397 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:39:03.401 [http-nio-9091-exec-4] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:39:03.401 [http-nio-9091-exec-4] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@5a7e0e12, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:39:03.403 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt >= '2025-07-13' and warndt <= '2025-07-13' ","dt":["2025-07-13","2025-07-13"],"s":0,"pageSize":10,"vehicle_type":"公交车","pageNum":1,"n":10}
2025-07-13 13:39:03.595 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:192 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:40:34.363 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:40:34.363 [http-nio-9091-exec-13] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:40:34.402 [http-nio-9091-exec-7] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:40:34.402 [http-nio-9091-exec-13] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:40:34.402 [http-nio-9091-exec-7] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@6e740807, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:40:34.402 [http-nio-9091-exec-13] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@50c87c35, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:40:34.411 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:40:34.411 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:40:34.768 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:357 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:40:34.856 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:445 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:40:40.424 [http-nio-9091-exec-14] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:40:40.429 [http-nio-9091-exec-14] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:40:40.429 [http-nio-9091-exec-14] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@fd67536, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:40:40.432 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:40:40.716 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:284 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:40:41.087 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:40:41.089 [http-nio-9091-exec-2] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:40:41.089 [http-nio-9091-exec-2] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@4ffa75d4, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:40:41.090 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:40:41.316 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:226 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:40:41.386 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:40:41.388 [http-nio-9091-exec-3] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:40:41.388 [http-nio-9091-exec-3] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@1a6f197a, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:40:41.389 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:40:41.596 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:207 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:40:41.708 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:40:41.711 [http-nio-9091-exec-4] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:40:41.711 [http-nio-9091-exec-4] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@7f4b2785, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:40:41.712 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:40:41.918 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:206 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:40:41.971 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:40:41.974 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:40:41.974 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@212fa5ff, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:40:41.976 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:40:42.202 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:40:42.204 [http-nio-9091-exec-10] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:40:42.205 [http-nio-9091-exec-10] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@4c7555b4, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:40:42.206 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:230 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:40:42.206 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:40:42.453 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:247 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:40:43.209 [http-nio-9091-exec-15] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:40:43.213 [http-nio-9091-exec-15] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:40:43.213 [http-nio-9091-exec-15] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@4b26a7ea, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:40:43.217 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt >= '2025-07-13' and warndt <= '2025-07-13' ","dt":["2025-07-13","2025-07-13"],"s":0,"pageSize":10,"vehicle_type":"公交车","pageNum":1,"n":10}
2025-07-13 13:40:43.434 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:217 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:40:46.285 [http-nio-9091-exec-11] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:40:46.287 [http-nio-9091-exec-11] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:40:46.287 [http-nio-9091-exec-11] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@437b5cd9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:40:46.289 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:40:46.504 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:215 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:40:46.702 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:40:46.704 [http-nio-9091-exec-7] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:40:46.704 [http-nio-9091-exec-7] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@71fbba25, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:40:46.706 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:40:46.941 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:235 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:40:47.522 [http-nio-9091-exec-13] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:40:47.526 [http-nio-9091-exec-13] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:40:47.526 [http-nio-9091-exec-13] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@6db4ec2c, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:40:47.529 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:40:47.775 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:246 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:40:47.951 [http-nio-9091-exec-14] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:40:47.957 [http-nio-9091-exec-14] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:40:47.957 [http-nio-9091-exec-14] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@55d1705c, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:40:47.959 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:40:48.299 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:339 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:16.340 [http-nio-9091-exec-11] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:16.340 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:16.340 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnInfoUnreadCount [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:16.340 [http-nio-9091-exec-15] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:16.360 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/ws/info [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:16.391 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnInfoUnreadCount
2025-07-13 13:42:16.391 [http-nio-9091-exec-10] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:42:16.391 [http-nio-9091-exec-11] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBasicData
2025-07-13 13:42:16.391 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@20fce702, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:16.391 [http-nio-9091-exec-11] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@2ac63ca8, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:16.391 [http-nio-9091-exec-10] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@b3c0d26, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:16.399 [http-nio-9091-exec-15] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:42:16.400 [http-nio-9091-exec-15] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@4f41be2f, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:16.406 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '公交车' ","type":"公交车"}
2025-07-13 13:42:16.406 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"warndt = '2025-07-13' and flag = 0","flag":0,"s":0,"n":1}
2025-07-13 13:42:16.406 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:42:16.406 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:42:16.467 [http-nio-9091-exec-13] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/ws/012/atvboiyu/websocket [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:16.778 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:372 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:16.782 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:16.783 [http-nio-9091-exec-3] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOperationSummary
2025-07-13 13:42:16.784 [http-nio-9091-exec-3] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@774685fb, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:16.785 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null and company = null and line_name = null and type = '公交车' and dt = '2025-07-13' ","selectType":"1"}
2025-07-13 13:42:16.787 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:381 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:16.826 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:420 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:16.904 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:497 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:16.964 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:179 | http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:17.821 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusRouteDetail [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:17.823 [http-nio-9091-exec-4] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBusRouteDetail
2025-07-13 13:42:17.823 [http-nio-9091-exec-4] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@5e403806, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:17.824 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county != null and company != null and line_name != null","selectType":"2"}
2025-07-13 13:42:18.016 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:192 | http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:19.888 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/build-transport/invest/getInvestmentProgress [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:19.895 [http-nio-9091-exec-7] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/build-transport/invest/getInvestmentProgress
2025-07-13 13:42:19.895 [http-nio-9091-exec-7] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3eba7f5d, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:19.898 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100492/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"(year = '2025' or year = '2024' or year = '2023' or year = '2022' or year = '2021') order by year asc","s":0,"n":100}
2025-07-13 13:42:20.125 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:227 | http://yinshu.iok.la:8081/api/100492/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:20.421 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getArriverOutList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:20.421 [http-nio-9091-exec-14] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getAnalysisList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:20.421 [http-nio-9091-exec-13] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getTotalInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:20.422 [http-nio-9091-exec-11] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getPassengerList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:20.422 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getBusList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:20.424 [http-nio-9091-exec-10] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getBusList
2025-07-13 13:42:20.424 [http-nio-9091-exec-14] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getAnalysisList
2025-07-13 13:42:20.424 [http-nio-9091-exec-2] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getArriverOutList
2025-07-13 13:42:20.424 [http-nio-9091-exec-13] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getTotalInfo
2025-07-13 13:42:20.424 [http-nio-9091-exec-10] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@154e9a55, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:20.425 [http-nio-9091-exec-14] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@2f293a7, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:20.425 [http-nio-9091-exec-13] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3a7a4d9a, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:20.424 [http-nio-9091-exec-11] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getPassengerList
2025-07-13 13:42:20.425 [http-nio-9091-exec-2] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@72891e03, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:20.425 [http-nio-9091-exec-11] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3d823f44, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:20.427 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100453/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"dt='2025-07-13' and hour = null","dataType":"1"}
2025-07-13 13:42:20.427 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100446/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"id='2025'","s":0,"n":100}
2025-07-13 13:42:20.428 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100452/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"dt='2025-07-13' and hour = null and type = '机场'","dataType":"1","transportType":"1"}
2025-07-13 13:42:20.427 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100448/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" dt='2025-07-13' and hour = null","dataType":"1"}
2025-07-13 13:42:20.428 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100450/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"dt='2025-07-13' and city = null and type = '机场'","dataType":"1","transportType":"1"}
2025-07-13 13:42:20.586 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:158 | http://yinshu.iok.la:8081/api/100450/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:20.610 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:183 | http://yinshu.iok.la:8081/api/100448/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:20.611 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:184 | http://yinshu.iok.la:8081/api/100446/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:20.638 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:210 | http://yinshu.iok.la:8081/api/100452/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:20.859 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:432 | http://yinshu.iok.la:8081/api/100453/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:22.622 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:22.622 [http-nio-9091-exec-15] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getWarningTotal [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:22.626 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:42:22.626 [http-nio-9091-exec-15] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getWarningTotal
2025-07-13 13:42:22.626 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@e810349, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:22.626 [http-nio-9091-exec-15] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@9f9bfc5, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:22.628 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"locationtype='机场'","s":0,"locationtype":"机场","n":10000}
2025-07-13 13:42:22.628 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:42:22.863 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:235 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:25.611 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:2983 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:32.574 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getWarningList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:32.577 [http-nio-9091-exec-3] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getWarningList
2025-07-13 13:42:32.578 [http-nio-9091-exec-3] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@5e09b3a1, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:32.579 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"locationtype='机场'  order by warntime desc","s":0,"pageSize":10,"locationtype":"机场","pageNum":1,"n":10}
2025-07-13 13:42:32.779 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:200 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:33.719 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/exportXls [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:33.721 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"locationtype='机场'  order by warntime desc","s":0,"exportType":"5","locationtype":"机场","n":1000000}
2025-07-13 13:42:35.761 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:2040 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:48.105 [http-nio-9091-exec-13] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleClassification [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:48.105 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getCapacityClassification [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:48.105 [http-nio-9091-exec-14] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOverallSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:48.107 [http-nio-9091-exec-14] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOverallSummary
2025-07-13 13:42:48.107 [http-nio-9091-exec-14] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@336922df, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:48.107 [http-nio-9091-exec-13] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleClassification
2025-07-13 13:42:48.107 [http-nio-9091-exec-2] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getCapacityClassification
2025-07-13 13:42:48.107 [http-nio-9091-exec-13] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@32415219, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:48.107 [http-nio-9091-exec-2] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@65ad7a8, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:48.108 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null order by dt desc"}
2025-07-13 13:42:48.108 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null order by dt desc"}
2025-07-13 13:42:48.108 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null order by dt desc"}
2025-07-13 13:42:48.279 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:171 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:48.333 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:225 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:48.340 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:232 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:50.747 [http-nio-9091-exec-11] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:50.750 [http-nio-9091-exec-11] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBasicData
2025-07-13 13:42:50.750 [http-nio-9091-exec-11] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@117f54f5, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:50.751 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '公交车' ","type":"公交车"}
2025-07-13 13:42:50.952 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:201 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:50.963 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:50.971 [http-nio-9091-exec-10] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOperationSummary
2025-07-13 13:42:50.971 [http-nio-9091-exec-10] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@785e22a8, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:50.975 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null and company = null and line_name = null and type = '公交车' and dt = '2025-07-13' ","selectType":"1"}
2025-07-13 13:42:51.160 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:185 | http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:51.457 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:51.461 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBasicData
2025-07-13 13:42:51.461 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@5c616047, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:51.463 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-13 13:42:51.639 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:176 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:51.643 [http-nio-9091-exec-15] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:51.646 [http-nio-9091-exec-15] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOperationOverview
2025-07-13 13:42:51.646 [http-nio-9091-exec-15] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@572ef096, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:51.647 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" hour = null and vehicle_type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-13 13:42:51.866 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:219 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:52.879 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:52.882 [http-nio-9091-exec-3] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBasicData
2025-07-13 13:42:52.882 [http-nio-9091-exec-3] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@2f03ac2f, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:52.883 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-13 13:42:53.084 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:201 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:53.088 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:53.091 [http-nio-9091-exec-4] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOperationOverview
2025-07-13 13:42:53.092 [http-nio-9091-exec-4] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@79854c42, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:53.094 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" hour = null and vehicle_type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-13 13:42:53.312 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:218 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:53.608 [http-nio-9091-exec-14] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getSuspectedBlackCarData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:53.610 [http-nio-9091-exec-14] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getSuspectedBlackCarData
2025-07-13 13:42:53.610 [http-nio-9091-exec-14] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@718e5644, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:53.613 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"hour != null and type = '疑似黑车'  and dt = '2025-07-13' ","s":0,"pageSize":10,"pageNum":1,"n":10}
2025-07-13 13:42:53.831 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:218 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:54.989 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:54.992 [http-nio-9091-exec-2] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBasicData
2025-07-13 13:42:54.992 [http-nio-9091-exec-2] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@1dd06833, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:54.994 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '公交车' ","type":"公交车"}
2025-07-13 13:42:55.175 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:181 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:55.186 [http-nio-9091-exec-13] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:55.191 [http-nio-9091-exec-13] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOperationSummary
2025-07-13 13:42:55.191 [http-nio-9091-exec-13] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@7d7791c3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:55.194 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null and company = null and line_name = null and type = '公交车' and dt = '2025-07-13' ","selectType":"1"}
2025-07-13 13:42:55.438 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:244 | http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:56.199 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:56.199 [http-nio-9091-exec-11] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:56.203 [http-nio-9091-exec-11] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:42:56.203 [http-nio-9091-exec-10] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:42:56.204 [http-nio-9091-exec-11] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@5b57eda5, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:56.204 [http-nio-9091-exec-10] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@2fd62908, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:56.205 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:42:56.205 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:42:56.390 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:185 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:56.452 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:247 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:42:56.864 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBusRouteDetail [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:42:56.866 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBusRouteDetail
2025-07-13 13:42:56.867 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@13770c00, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:42:56.870 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county != null and company != null and line_name != null","selectType":"2"}
2025-07-13 13:42:57.042 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:172 | http://yinshu.iok.la:8081/api/100441/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:43:00.113 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getPassengerMapData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:43:00.117 [http-nio-9091-exec-3] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getPassengerMapData
2025-07-13 13:43:00.117 [http-nio-9091-exec-3] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@206e24a6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:43:00.120 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county != null and company != null and vehicle_type = '公交车'","selectType":"1"}
2025-07-13 13:43:00.708 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:588 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:43:21.723 [http-nio-9091-exec-13] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getMultiRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:43:21.728 [http-nio-9091-exec-13] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/vehicle/getMultiRealLocation
2025-07-13 13:43:21.728 [http-nio-9091-exec-13] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@1749661c, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:43:21.729 [http-nio-9091-exec-13] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getMultiRealLocation-----------------
2025-07-13 13:43:21.731 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋H0176X' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"ids":["晋H0176X"]}
2025-07-13 13:43:21.926 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:195 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋H0176X' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:43:26.708 [http-nio-9091-exec-11] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getMultiRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:43:26.709 [http-nio-9091-exec-11] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/vehicle/getMultiRealLocation
2025-07-13 13:43:26.709 [http-nio-9091-exec-11] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3840d763, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:43:26.709 [http-nio-9091-exec-11] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getMultiRealLocation-----------------
2025-07-13 13:43:26.710 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋H0176X' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"ids":["晋H0176X"]}
2025-07-13 13:43:26.891 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:181 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋H0176X' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:43:31.719 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getMultiRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:43:31.723 [http-nio-9091-exec-10] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/vehicle/getMultiRealLocation
2025-07-13 13:43:31.724 [http-nio-9091-exec-10] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@1037d815, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:43:31.725 [http-nio-9091-exec-10] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getMultiRealLocation-----------------
2025-07-13 13:43:31.726 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋H0176X' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"ids":["晋H0176X"]}
2025-07-13 13:43:31.912 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:187 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋H0176X' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:43:36.711 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getMultiRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:43:36.714 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/vehicle/getMultiRealLocation
2025-07-13 13:43:36.714 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@285e1c16, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:43:36.715 [http-nio-9091-exec-9] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getMultiRealLocation-----------------
2025-07-13 13:43:36.716 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋H0176X' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"ids":["晋H0176X"]}
2025-07-13 13:43:36.910 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:194 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋H0176X' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:43:41.716 [http-nio-9091-exec-15] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getMultiRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:43:41.719 [http-nio-9091-exec-15] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/vehicle/getMultiRealLocation
2025-07-13 13:43:41.719 [http-nio-9091-exec-15] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@62ef3d9e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:43:41.720 [http-nio-9091-exec-15] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getMultiRealLocation-----------------
2025-07-13 13:43:41.722 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋H0176X' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"ids":["晋H0176X"]}
2025-07-13 13:43:41.947 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:225 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋H0176X' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:43:46.710 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getMultiRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:43:46.715 [http-nio-9091-exec-3] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/vehicle/getMultiRealLocation
2025-07-13 13:43:46.715 [http-nio-9091-exec-3] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@439d4ef9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:43:46.716 [http-nio-9091-exec-3] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getMultiRealLocation-----------------
2025-07-13 13:43:46.717 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋H0176X' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"ids":["晋H0176X"]}
2025-07-13 13:43:46.935 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:218 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋H0176X' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:43:51.718 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getMultiRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:43:51.723 [http-nio-9091-exec-7] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/vehicle/getMultiRealLocation
2025-07-13 13:43:51.724 [http-nio-9091-exec-7] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@367fa2b4, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:43:51.724 [http-nio-9091-exec-7] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getMultiRealLocation-----------------
2025-07-13 13:43:51.726 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋H0176X' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"ids":["晋H0176X"]}
2025-07-13 13:43:51.996 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:270 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋H0176X' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:43:56.711 [http-nio-9091-exec-14] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getMultiRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:43:56.716 [http-nio-9091-exec-14] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/vehicle/getMultiRealLocation
2025-07-13 13:43:56.716 [http-nio-9091-exec-14] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@2e521aef, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:43:56.717 [http-nio-9091-exec-14] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getMultiRealLocation-----------------
2025-07-13 13:43:56.718 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋H0176X' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"ids":["晋H0176X"]}
2025-07-13 13:43:57.008 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:290 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋H0176X' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:44:01.718 [http-nio-9091-exec-13] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/vehicle/getMultiRealLocation [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:44:01.724 [http-nio-9091-exec-13] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/vehicle/getMultiRealLocation
2025-07-13 13:44:01.724 [http-nio-9091-exec-13] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@556866ef, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:44:01.726 [http-nio-9091-exec-13] INFO  com.yinshu.toms.service.impl.VehicleServiceImpl - ------------getMultiRealLocation-----------------
2025-07-13 13:44:01.727 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋H0176X' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"ids":["晋H0176X"]}
2025-07-13 13:44:01.951 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:223 | http://yinshu.iok.la:8081/api/100508/zPSvBrMftJFPzEoG1bRi?&tablename=ads_plane_fire_bus_location_info&filter=carid='晋H0176X' order by gps_timelong desc&s=0&n=1&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:44:03.426 [http-nio-9091-exec-11] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:44:03.429 [http-nio-9091-exec-11] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBasicData
2025-07-13 13:44:03.429 [http-nio-9091-exec-11] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@28552a69, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:44:03.430 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-13 13:44:03.787 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:357 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:44:03.794 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:44:03.798 [http-nio-9091-exec-10] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOperationOverview
2025-07-13 13:44:03.798 [http-nio-9091-exec-10] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@554c9e90, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:44:03.800 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" hour = null and vehicle_type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-13 13:44:04.236 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:436 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:44:04.329 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getPassengerMapData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:44:04.332 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getPassengerMapData
2025-07-13 13:44:04.332 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@6fcb491c, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:44:04.334 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county != null and company != null and vehicle_type = '巡游车'","selectType":"2"}
2025-07-13 13:44:04.723 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:389 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:44:46.610 [http-nio-9091-exec-15] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:44:46.613 [http-nio-9091-exec-15] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBasicData
2025-07-13 13:44:46.613 [http-nio-9091-exec-15] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@15af25a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:44:46.614 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '公交车' ","type":"公交车"}
2025-07-13 13:44:47.048 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:434 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:44:47.056 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:44:47.060 [http-nio-9091-exec-3] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOperationSummary
2025-07-13 13:44:47.061 [http-nio-9091-exec-3] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@9e14bc3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:44:47.064 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null and company = null and line_name = null and type = '公交车' and dt = '2025-07-13' ","selectType":"1"}
2025-07-13 13:44:47.238 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:174 | http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:44:47.797 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:44:47.800 [http-nio-9091-exec-4] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBasicData
2025-07-13 13:44:47.800 [http-nio-9091-exec-4] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@c3b384a, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:44:47.803 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-13 13:44:48.073 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:270 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:44:48.078 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:44:48.081 [http-nio-9091-exec-7] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOperationOverview
2025-07-13 13:44:48.081 [http-nio-9091-exec-7] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@25fd6c6b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:44:48.082 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" hour = null and vehicle_type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-13 13:44:48.314 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:232 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:44:48.608 [http-nio-9091-exec-14] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getPassengerMapData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:44:48.611 [http-nio-9091-exec-14] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getPassengerMapData
2025-07-13 13:44:48.611 [http-nio-9091-exec-14] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@5f5def2d, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:44:48.614 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county != null and company != null and vehicle_type = '网约车'","selectType":"3"}
2025-07-13 13:44:49.057 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:443 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:44:49.232 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:44:49.232 [http-nio-9091-exec-13] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:44:49.236 [http-nio-9091-exec-13] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:44:49.236 [http-nio-9091-exec-2] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:44:49.236 [http-nio-9091-exec-2] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@77fa7766, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:44:49.236 [http-nio-9091-exec-13] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@14b98967, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:44:49.239 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '网约车'  and warndt = '2025-07-13' ","vehicle_type":"网约车"}
2025-07-13 13:44:49.239 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:44:49.444 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:205 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:44:49.794 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:555 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:44:56.439 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getArriverOutList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:44:56.439 [http-nio-9091-exec-11] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getTotalInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:44:56.440 [http-nio-9091-exec-15] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getPassengerList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:44:56.440 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getBusList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:44:56.440 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getAnalysisList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:44:56.442 [http-nio-9091-exec-10] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getArriverOutList
2025-07-13 13:44:56.443 [http-nio-9091-exec-15] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getPassengerList
2025-07-13 13:44:56.443 [http-nio-9091-exec-10] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3aa713d1, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:44:56.443 [http-nio-9091-exec-15] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3d021eaa, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:44:56.443 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getBusList
2025-07-13 13:44:56.442 [http-nio-9091-exec-3] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getAnalysisList
2025-07-13 13:44:56.444 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@4f7086da, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:44:56.444 [http-nio-9091-exec-3] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@231d14e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:44:56.446 [http-nio-9091-exec-11] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getTotalInfo
2025-07-13 13:44:56.445 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100448/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" dt='2025-07-13' and hour = null","dataType":"1"}
2025-07-13 13:44:56.446 [http-nio-9091-exec-11] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@22b2f5dd, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:44:56.447 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100450/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"dt='2025-07-13' and city = null and type = '机场'","dataType":"1","transportType":"1"}
2025-07-13 13:44:56.448 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100453/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"dt='2025-07-13' and hour = null","dataType":"1"}
2025-07-13 13:44:56.448 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100446/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"id='2025'","s":0,"n":100}
2025-07-13 13:44:56.450 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100452/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"dt='2025-07-13' and hour = null and type = '机场'","dataType":"1","transportType":"1"}
2025-07-13 13:44:56.663 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:215 | http://yinshu.iok.la:8081/api/100453/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:44:56.670 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:225 | http://yinshu.iok.la:8081/api/100448/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:44:56.833 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:385 | http://yinshu.iok.la:8081/api/100446/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:44:56.840 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:390 | http://yinshu.iok.la:8081/api/100452/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:44:56.908 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:461 | http://yinshu.iok.la:8081/api/100450/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:44:57.829 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getVehicleList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:44:57.834 [http-nio-9091-exec-4] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getVehicleList
2025-07-13 13:44:57.835 [http-nio-9091-exec-4] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@1a9cc08a, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:44:57.838 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100479/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"order by station, flag desc","s":0,"stationType":"机场","n":100000}
2025-07-13 13:44:58.271 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:433 | http://yinshu.iok.la:8081/api/100479/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:45:08.955 [http-nio-9091-exec-13] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnInfoUnreadCount [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:45:08.955 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getArriverOutList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:45:08.955 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:45:08.955 [http-nio-9091-exec-14] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:45:08.955 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getTotalInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:45:08.955 [http-nio-9091-exec-11] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getAnalysisList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:45:08.957 [http-nio-9091-exec-10] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getArriverOutList
2025-07-13 13:45:08.957 [http-nio-9091-exec-2] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:45:08.957 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getTotalInfo
2025-07-13 13:45:08.957 [http-nio-9091-exec-2] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@43bb2f7d, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:45:08.957 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3c29da82, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:45:08.957 [http-nio-9091-exec-10] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@4acf054e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:45:08.964 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100448/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" dt='2025-07-13' and hour = null","dataType":"1"}
2025-07-13 13:45:08.964 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:45:08.964 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100446/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"id='2025'","s":0,"n":100}
2025-07-13 13:45:08.965 [http-nio-9091-exec-11] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getAnalysisList
2025-07-13 13:45:08.965 [http-nio-9091-exec-13] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnInfoUnreadCount
2025-07-13 13:45:08.965 [http-nio-9091-exec-14] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:45:08.965 [http-nio-9091-exec-13] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@49f7d2c3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:45:08.965 [http-nio-9091-exec-14] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@6317c91, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:45:08.965 [http-nio-9091-exec-11] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@753ef2bd, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:45:08.967 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100450/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"dt='2025-07-13' and city = null and type = '机场'","dataType":"1","transportType":"1"}
2025-07-13 13:45:08.967 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:45:08.967 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"warndt = '2025-07-13' and flag = 0","flag":0,"s":0,"n":1}
2025-07-13 13:45:08.979 [http-nio-9091-exec-15] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/ws/info [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:45:09.018 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/ws/489/npfo3gd0/websocket [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:45:09.133 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:166 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:45:09.133 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:169 | http://yinshu.iok.la:8081/api/100446/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:45:09.140 [http-nio-9091-exec-14] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getPassengerList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:45:09.141 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:177 | http://yinshu.iok.la:8081/api/100448/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:45:09.141 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getBusList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:45:09.144 [http-nio-9091-exec-14] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getPassengerList
2025-07-13 13:45:09.144 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getBusList
2025-07-13 13:45:09.144 [http-nio-9091-exec-14] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@de228bd, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:45:09.144 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@1edef84b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:45:09.145 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100453/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"dt='2025-07-13' and hour = null","dataType":"1"}
2025-07-13 13:45:09.145 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100452/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"dt='2025-07-13' and hour = null and type = '机场'","dataType":"1","transportType":"1"}
2025-07-13 13:45:09.195 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:231 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:45:09.198 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:231 | http://yinshu.iok.la:8081/api/100450/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:45:09.309 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:164 | http://yinshu.iok.la:8081/api/100453/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:45:09.324 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:357 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:45:09.324 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:179 | http://yinshu.iok.la:8081/api/100452/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:45:12.509 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getWarningTotal [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:45:12.509 [http-nio-9091-exec-15] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:45:12.514 [http-nio-9091-exec-15] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:45:12.514 [http-nio-9091-exec-3] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getWarningTotal
2025-07-13 13:45:12.514 [http-nio-9091-exec-15] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@7de334ba, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:45:12.514 [http-nio-9091-exec-3] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@98f8fcb, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:45:12.516 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:45:12.516 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"locationtype='机场'","s":0,"locationtype":"机场","n":10000}
2025-07-13 13:45:12.726 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:210 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:45:14.296 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:1780 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:45:15.100 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getVehicleList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:45:15.105 [http-nio-9091-exec-4] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getVehicleList
2025-07-13 13:45:15.105 [http-nio-9091-exec-4] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@706020d1, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:45:15.108 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100479/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"order by station, flag desc","s":0,"stationType":"机场","n":100000}
2025-07-13 13:45:15.544 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:436 | http://yinshu.iok.la:8081/api/100479/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:45:22.688 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:45:22.688 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/aviation-railway/aviation/getWarningTotal [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:45:22.692 [http-nio-9091-exec-7] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:45:22.692 [http-nio-9091-exec-10] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/aviation-railway/aviation/getWarningTotal
2025-07-13 13:45:22.693 [http-nio-9091-exec-7] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@66581fa5, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:45:22.693 [http-nio-9091-exec-10] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@29c289ae, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:45:22.695 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:45:22.695 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"locationtype='机场'","s":0,"locationtype":"机场","n":10000}
2025-07-13 13:45:22.904 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:209 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:45:24.677 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:1982 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:45:35.555 [http-nio-9091-exec-11] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/mcu/camera/tree [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:45:35.561 [http-nio-9091-exec-11] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/mcu/camera/tree
2025-07-13 13:45:35.561 [http-nio-9091-exec-11] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@6d9cd01a, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:45:37.739 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/road-transport/ads_basic_indicator_data [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:45:37.739 [http-nio-9091-exec-14] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/road-transport/ads_passenger_transport_station [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:45:37.740 [http-nio-9091-exec-15] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:45:37.740 [http-nio-9091-exec-13] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/road-transport/ads_daily_overview_trend_hourly [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:45:37.742 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/road-transport/ads_basic_indicator_data
2025-07-13 13:45:37.742 [http-nio-9091-exec-13] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/road-transport/ads_daily_overview_trend_hourly
2025-07-13 13:45:37.742 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@438289f8, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:45:37.742 [http-nio-9091-exec-14] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/road-transport/ads_passenger_transport_station
2025-07-13 13:45:37.742 [http-nio-9091-exec-15] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:45:37.742 [http-nio-9091-exec-13] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@2959fd23, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:45:37.742 [http-nio-9091-exec-14] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@34e8c72e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:45:37.742 [http-nio-9091-exec-15] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3900300d, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:45:37.744 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100449/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '客运'"}
2025-07-13 13:45:37.744 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100459/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"dt = '2025-07-13'"}
2025-07-13 13:45:37.744 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:45:37.744 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100461/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '客运' and dt =  '2025-07-13'"}
2025-07-13 13:45:37.935 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:191 | http://yinshu.iok.la:8081/api/100459/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:45:37.946 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:202 | http://yinshu.iok.la:8081/api/100449/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:45:37.946 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:202 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:45:37.972 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:228 | http://yinshu.iok.la:8081/api/100461/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:45:39.634 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/road-transport/ads_transport_map_vehicle_base_info [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:45:39.639 [http-nio-9091-exec-4] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/road-transport/ads_transport_map_vehicle_base_info
2025-07-13 13:45:39.639 [http-nio-9091-exec-4] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@272eeb5d, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:45:39.642 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100475/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '客运'","s":0,"n":1000000}
2025-07-13 13:45:39.856 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:214 | http://yinshu.iok.la:8081/api/100475/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:46:17.174 [http-nio-9091-exec-14] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleClassification [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:46:17.175 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOverallSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:46:17.175 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getCapacityClassification [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:46:17.185 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOverallSummary
2025-07-13 13:46:17.185 [http-nio-9091-exec-2] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getCapacityClassification
2025-07-13 13:46:17.185 [http-nio-9091-exec-14] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleClassification
2025-07-13 13:46:17.186 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@d83268f, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:46:17.186 [http-nio-9091-exec-14] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@4f68269b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:46:17.186 [http-nio-9091-exec-2] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@676b238a, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:46:17.187 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null order by dt desc"}
2025-07-13 13:46:17.187 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null order by dt desc"}
2025-07-13 13:46:17.187 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null order by dt desc"}
2025-07-13 13:46:17.532 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:344 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:46:17.558 [http-nio-9091-exec-14] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:371 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:46:17.635 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:448 | http://yinshu.iok.la:8081/api/100445/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:46:18.997 [http-nio-9091-exec-15] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:46:19.004 [http-nio-9091-exec-15] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBasicData
2025-07-13 13:46:19.004 [http-nio-9091-exec-15] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@3fcaa4ff, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:46:19.008 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '公交车' ","type":"公交车"}
2025-07-13 13:46:19.218 [http-nio-9091-exec-15] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:210 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:46:19.229 [http-nio-9091-exec-13] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:46:19.232 [http-nio-9091-exec-13] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOperationSummary
2025-07-13 13:46:19.233 [http-nio-9091-exec-13] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@652100e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:46:19.235 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null and company = null and line_name = null and type = '公交车' and dt = '2025-07-13' ","selectType":"1"}
2025-07-13 13:46:19.451 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:216 | http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:46:19.752 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:46:19.752 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:46:19.757 [http-nio-9091-exec-3] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:46:19.757 [http-nio-9091-exec-4] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:46:19.757 [http-nio-9091-exec-3] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@6f3bba18, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:46:19.757 [http-nio-9091-exec-4] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@12b8f3df, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:46:19.760 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:46:19.760 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:46:19.942 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:182 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:46:20.016 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:256 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:46:20.501 [http-nio-9091-exec-11] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:46:20.504 [http-nio-9091-exec-11] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:46:20.504 [http-nio-9091-exec-11] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@1fc6b9ea, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:46:20.507 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '公交车'  and warndt = '2025-07-13' ","vehicle_type":"公交车"}
2025-07-13 13:46:20.709 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:202 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:51:02.372 [http-nio-9091-exec-13] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:51:02.372 [http-nio-9091-exec-11] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:51:02.372 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:51:02.372 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnInfoUnreadCount [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:51:02.383 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/ws/info [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:51:02.388 [http-nio-9091-exec-4] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnInfoUnreadCount
2025-07-13 13:51:02.388 [http-nio-9091-exec-3] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:51:02.388 [http-nio-9091-exec-11] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBasicData
2025-07-13 13:51:02.388 [http-nio-9091-exec-13] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:51:02.389 [http-nio-9091-exec-13] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@17ba9671, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:51:02.389 [http-nio-9091-exec-3] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@61b757af, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:51:02.389 [http-nio-9091-exec-11] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@15bf16ad, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:51:02.389 [http-nio-9091-exec-4] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@b522d2a, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:51:02.403 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:51:02.403 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:51:02.403 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '公交车' ","type":"公交车"}
2025-07-13 13:51:02.404 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"warndt = '2025-07-13' and flag = 0","flag":0,"s":0,"n":1}
2025-07-13 13:51:02.421 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/ws/646/lsbujfw0/websocket [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:51:02.741 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:337 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:51:02.789 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:386 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:51:02.789 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:386 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:51:02.794 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationSummary [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:51:02.797 [http-nio-9091-exec-2] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOperationSummary
2025-07-13 13:51:02.798 [http-nio-9091-exec-2] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@7f0133ee, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:51:02.799 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county = null and company = null and line_name = null and type = '公交车' and dt = '2025-07-13' ","selectType":"1"}
2025-07-13 13:51:02.861 [http-nio-9091-exec-13] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:458 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:51:02.983 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:184 | http://yinshu.iok.la:8081/api/100470/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:52:22.484 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:52:22.490 [http-nio-9091-exec-4] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBasicData
2025-07-13 13:52:22.490 [http-nio-9091-exec-4] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@4578e20, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:52:22.491 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-13 13:52:22.629 [http-nio-9091-exec-4] ERROR com.yinshu.exception.GlobalExceptionHandler - 全局异常捕获>>>>>I/O error on POST request for "http://yinshu.iok.la:8081/api/100454/data.json": yinshu.iok.la:8081 failed to respond; nested exception is org.apache.http.NoHttpResponseException: yinshu.iok.la:8081 failed to respond
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://yinshu.iok.la:8081/api/100454/data.json": yinshu.iok.la:8081 failed to respond; nested exception is org.apache.http.NoHttpResponseException: yinshu.iok.la:8081 failed to respond
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:791)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:717)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:443)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:70)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:49)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:76)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:41)
	at com.yinshu.toms.service.impl.CityTrafficServiceImpl.getBasicData(CityTrafficServiceImpl.java:138)
	at com.yinshu.toms.controller.CityTrafficController.getBasicData(CityTrafficController.java:58)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.github.xiaoymin.knife4j.extend.filter.basic.ServletSecurityBasicAuthFilter.doFilter(ServletSecurityBasicAuthFilter.java:56)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.yinshu.sys.security.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:101)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.http.NoHttpResponseException: yinshu.iok.la:8081 failed to respond
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:141)
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:56)
	at org.apache.http.impl.io.AbstractMessageParser.parse(AbstractMessageParser.java:259)
	at org.apache.http.impl.DefaultBHttpClientConnection.receiveResponseHeader(DefaultBHttpClientConnection.java:163)
	at org.apache.http.impl.conn.CPoolProxy.receiveResponseHeader(CPoolProxy.java:157)
	at org.apache.http.protocol.HttpRequestExecutor.doReceiveResponse(HttpRequestExecutor.java:273)
	at org.apache.http.protocol.HttpRequestExecutor.execute(HttpRequestExecutor.java:125)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:272)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:782)
	... 106 common frames omitted
2025-07-13 13:52:23.290 [http-nio-9091-exec-11] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:52:23.290 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:52:23.293 [http-nio-9091-exec-11] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:52:23.293 [http-nio-9091-exec-3] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:52:23.293 [http-nio-9091-exec-3] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@f791392, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:52:23.293 [http-nio-9091-exec-11] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@4fa77673, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:52:23.294 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '巡游车'  and warndt = '2025-07-13' ","vehicle_type":"巡游车"}
2025-07-13 13:52:23.294 [http-nio-9091-exec-11] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:52:23.427 [http-nio-9091-exec-11] ERROR com.yinshu.exception.GlobalExceptionHandler - 全局异常捕获>>>>>I/O error on POST request for "http://yinshu.iok.la:8081/api/100586/data.json": yinshu.iok.la:8081 failed to respond; nested exception is org.apache.http.NoHttpResponseException: yinshu.iok.la:8081 failed to respond
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://yinshu.iok.la:8081/api/100586/data.json": yinshu.iok.la:8081 failed to respond; nested exception is org.apache.http.NoHttpResponseException: yinshu.iok.la:8081 failed to respond
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:791)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:717)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:443)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:70)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:49)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:76)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:41)
	at com.yinshu.toms.service.impl.WarnMsgServiceImpl.getWarnTypeInfo(WarnMsgServiceImpl.java:53)
	at com.yinshu.toms.controller.WarnMsgController.getWarnTypeInfo(WarnMsgController.java:51)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.github.xiaoymin.knife4j.extend.filter.basic.ServletSecurityBasicAuthFilter.doFilter(ServletSecurityBasicAuthFilter.java:56)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.yinshu.sys.security.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:101)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.http.NoHttpResponseException: yinshu.iok.la:8081 failed to respond
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:141)
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:56)
	at org.apache.http.impl.io.AbstractMessageParser.parse(AbstractMessageParser.java:259)
	at org.apache.http.impl.DefaultBHttpClientConnection.receiveResponseHeader(DefaultBHttpClientConnection.java:163)
	at org.apache.http.impl.conn.CPoolProxy.receiveResponseHeader(CPoolProxy.java:157)
	at org.apache.http.protocol.HttpRequestExecutor.doReceiveResponse(HttpRequestExecutor.java:273)
	at org.apache.http.protocol.HttpRequestExecutor.execute(HttpRequestExecutor.java:125)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:272)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:782)
	... 106 common frames omitted
2025-07-13 13:52:23.460 [http-nio-9091-exec-3] ERROR com.yinshu.exception.GlobalExceptionHandler - 全局异常捕获>>>>>I/O error on POST request for "http://yinshu.iok.la:8081/api/100588/data.json": yinshu.iok.la:8081 failed to respond; nested exception is org.apache.http.NoHttpResponseException: yinshu.iok.la:8081 failed to respond
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://yinshu.iok.la:8081/api/100588/data.json": yinshu.iok.la:8081 failed to respond; nested exception is org.apache.http.NoHttpResponseException: yinshu.iok.la:8081 failed to respond
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:791)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:717)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:443)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:70)
	at com.yinshu.http.HttpTemplateAbstract.post(HttpTemplateAbstract.java:49)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:76)
	at com.yinshu.sys.http.DvisualHttpTemplate.post(DvisualHttpTemplate.java:41)
	at com.yinshu.toms.service.impl.CityTrafficServiceImpl.getVehicleMonitorWarning(CityTrafficServiceImpl.java:378)
	at com.yinshu.toms.controller.CityTrafficController.getVehicleMonitorWarning(CityTrafficController.java:138)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.github.xiaoymin.knife4j.extend.filter.basic.ServletSecurityBasicAuthFilter.doFilter(ServletSecurityBasicAuthFilter.java:56)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.yinshu.sys.security.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:101)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.http.NoHttpResponseException: yinshu.iok.la:8081 failed to respond
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:141)
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:56)
	at org.apache.http.impl.io.AbstractMessageParser.parse(AbstractMessageParser.java:259)
	at org.apache.http.impl.DefaultBHttpClientConnection.receiveResponseHeader(DefaultBHttpClientConnection.java:163)
	at org.apache.http.impl.conn.CPoolProxy.receiveResponseHeader(CPoolProxy.java:157)
	at org.apache.http.protocol.HttpRequestExecutor.doReceiveResponse(HttpRequestExecutor.java:273)
	at org.apache.http.protocol.HttpRequestExecutor.execute(HttpRequestExecutor.java:125)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:272)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:782)
	... 106 common frames omitted
2025-07-13 13:52:32.970 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopping...
2025-07-13 13:52:32.971 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@7b9e7633]]
2025-07-13 13:52:32.971 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopped.
2025-07-13 13:52:32.994 [SpringApplicationShutdownHook] INFO  org.ehcache.core.EhcacheManager - Cache 's_setting' removed from EhcacheManager.
2025-07-13 13:52:32.994 [SpringApplicationShutdownHook] INFO  org.ehcache.core.EhcacheManager - Cache 'road_network' removed from EhcacheManager.
2025-07-13 13:52:33.005 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-13 13:52:33.007 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-13 13:52:33.008 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-13 13:52:33.008 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-13 13:52:35.464 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-13 13:52:35.473 [main] INFO  com.yinshu.StartApplication - Starting StartApplication using Java 21.0.6 on xiaoxiongxueJavadeMacBook-Air.local with PID 65001 (/Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot/tocc-admin-all-starter/target/classes started by javaxiaobear in /Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot)
2025-07-13 13:52:35.473 [main] INFO  com.yinshu.StartApplication - The following 1 profile is active: "dev"
2025-07-13 13:52:35.978 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-13 13:52:35.979 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-13 13:52:36.001 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-07-13 13:52:36.203 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-13 13:52:36.206 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$c906c355] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-13 13:52:36.215 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-13 13:52:36.404 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9091 (http)
2025-07-13 13:52:36.407 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9091"]
2025-07-13 13:52:36.407 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-13 13:52:36.407 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-13 13:52:36.463 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring embedded WebApplicationContext
2025-07-13 13:52:36.463 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 966 ms
2025-07-13 13:52:36.664 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [syncDb] success
2025-07-13 13:52:36.664 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [syncDb]
2025-07-13 13:52:38.428 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/**'] with [org.springframework.security.web.session.DisableEncodeUrlFilter@58c93be3, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5dd62f07, org.springframework.security.web.context.SecurityContextPersistenceFilter@1e205025, org.springframework.security.web.header.HeaderWriterFilter@1a598e30, org.springframework.web.filter.CorsFilter@6e669b5c, org.springframework.security.web.authentication.logout.LogoutFilter@********, com.yinshu.sys.security.JwtAuthenticationTokenFilter@615e7fe7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1c0a4f87, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7d50b4c4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3db3668, org.springframework.security.web.session.SessionManagementFilter@35d4fecf, org.springframework.security.web.access.ExceptionTranslationFilter@17d837ab, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@73eaae1e]
2025-07-13 13:52:38.623 [main] INFO  org.ehcache.core.EhcacheManager - Cache 's_setting' created in EhcacheManager.
2025-07-13 13:52:38.627 [main] INFO  org.ehcache.core.EhcacheManager - Cache 'road_network' created in EhcacheManager.
2025-07-13 13:52:38.697 [main] WARN  o.s.b.a.freemarker.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-13 13:52:38.753 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9091"]
2025-07-13 13:52:38.768 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9091 (http) with context path '/tocc'
2025-07-13 13:52:38.769 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Starting...
2025-07-13 13:52:38.769 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@466fd19b]]
2025-07-13 13:52:38.769 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Started.
2025-07-13 13:52:38.777 [main] INFO  com.yinshu.StartApplication - Started StartApplication in 3.539 seconds (JVM running for 4.057)
2025-07-13 13:52:38.821 [http-nio-9091-exec-1] INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_cd8218cd51f800ed2b73e5751cb3f4f9=1750299100,1751184993;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-07-13 13:52:38.824 [http-nio-9091-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-13 13:52:38.824 [http-nio-9091-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-13 13:52:38.825 [http-nio-9091-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-13 13:52:38.836 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/ws/info [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:52:39.019 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/ws/346/0o5rq4au/websocket [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:53:33.071 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:53:33.071 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:53:33.071 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:53:33.071 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnInfoUnreadCount [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:53:33.095 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/ws/info [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:53:33.096 [http-nio-9091-exec-7] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBasicData
2025-07-13 13:53:33.097 [http-nio-9091-exec-8] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnInfoUnreadCount
2025-07-13 13:53:33.100 [http-nio-9091-exec-8] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@f24db69, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:53:33.096 [http-nio-9091-exec-6] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:53:33.099 [http-nio-9091-exec-5] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:53:33.101 [http-nio-9091-exec-6] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@75848795, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:53:33.099 [http-nio-9091-exec-7] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@cbb21d0, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:53:33.102 [http-nio-9091-exec-5] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@46747122, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:53:33.205 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/ws/115/wsjpdw54/websocket [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:53:34.438 [http-nio-9091-exec-6] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1,syncDb} inited
2025-07-13 13:53:35.876 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:53:35.991 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"warndt = '2025-07-13' and flag = 0","flag":0,"s":0,"n":1}
2025-07-13 13:53:36.040 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:53:36.048 [http-nio-9091-exec-3] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBasicData
2025-07-13 13:53:36.048 [http-nio-9091-exec-3] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@4c16e5db, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:53:36.051 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-13 13:53:36.086 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-13 13:53:36.188 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:53:36.281 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:405 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:53:36.411 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:420 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:53:36.478 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:427 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:53:36.490 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:53:36.496 [http-nio-9091-exec-4] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOperationOverview
2025-07-13 13:53:36.497 [http-nio-9091-exec-4] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@18c42090, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:53:36.501 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" hour = null and vehicle_type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-13 13:53:36.507 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:421 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:53:36.512 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:53:36.515 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOperationOverview
2025-07-13 13:53:36.515 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@7cc5bb8c, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:53:36.517 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" hour = null and vehicle_type = '巡游车' ","selectType":"1","type":"巡游车"}
2025-07-13 13:53:36.648 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:460 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:53:36.701 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:201 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:53:36.736 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:219 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:53:36.782 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getPassengerMapData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:53:36.789 [http-nio-9091-exec-10] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getPassengerMapData
2025-07-13 13:53:36.789 [http-nio-9091-exec-10] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@5d87fe22, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:53:36.801 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county != null and company != null and vehicle_type = '网约车'","selectType":"3"}
2025-07-13 13:53:37.219 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:418 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:53:37.548 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:53:37.548 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnTypeInfo [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:53:37.554 [http-nio-9091-exec-2] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:53:37.554 [http-nio-9091-exec-1] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnTypeInfo
2025-07-13 13:53:37.555 [http-nio-9091-exec-1] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@5456d9e0, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:53:37.555 [http-nio-9091-exec-2] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@5806ab10, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:53:37.559 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {}
2025-07-13 13:53:37.559 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '网约车'  and warndt = '2025-07-13' ","vehicle_type":"网约车"}
2025-07-13 13:53:37.761 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:202 | http://yinshu.iok.la:8081/api/100586/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:53:37.915 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:356 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:53:38.252 [MessageBroker-5] INFO  o.s.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[1 current WS(1)-HttpStream(0)-HttpPoll(0), 2 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(2)-CONNECTED(2)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 20, active threads = 0, queued tasks = 0, completed tasks = 21], outboundChannel[pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 3], sockJsScheduler[pool size = 10, active threads = 1, queued tasks = 2, completed tasks = 14]
2025-07-13 13:53:46.456 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:53:46.461 [http-nio-9091-exec-5] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:53:46.461 [http-nio-9091-exec-5] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@4548d59b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:53:46.465 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '网约车'  and warndt >= '2025-07-13' and warndt <= '2025-07-13' ","dt":["2025-07-13","2025-07-13"],"s":0,"pageSize":10,"vehicle_type":"网约车","pageNum":1,"n":10}
2025-07-13 13:53:46.671 [http-nio-9091-exec-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:207 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:53:56.316 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:53:56.321 [http-nio-9091-exec-8] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:53:56.321 [http-nio-9091-exec-8] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@4fef8467, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:53:56.324 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '网约车'  and warndt >= '2025-07-13' and warndt <= '2025-07-13' ","dt":["2025-07-13","2025-07-13"],"s":10,"pageSize":10,"vehicle_type":"网约车","pageNum":2,"n":10}
2025-07-13 13:53:56.526 [http-nio-9091-exec-8] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:202 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:53:58.863 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:53:58.870 [http-nio-9091-exec-3] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:53:58.870 [http-nio-9091-exec-3] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@12f278a0, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:53:58.876 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '网约车'  and warndt >= '2025-07-13' and warndt <= '2025-07-13' ","dt":["2025-07-13","2025-07-13"],"s":20,"pageSize":10,"vehicle_type":"网约车","pageNum":3,"n":10}
2025-07-13 13:53:59.077 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:201 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:54:00.612 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:54:00.616 [http-nio-9091-exec-7] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:54:00.616 [http-nio-9091-exec-7] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@37900554, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:54:00.619 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '网约车'  and warndt >= '2025-07-13' and warndt <= '2025-07-13' ","dt":["2025-07-13","2025-07-13"],"s":10,"pageSize":10,"vehicle_type":"网约车","pageNum":2,"n":10}
2025-07-13 13:54:00.954 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:335 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:54:01.297 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getVehicleMonitorWarning [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:54:01.301 [http-nio-9091-exec-6] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getVehicleMonitorWarning
2025-07-13 13:54:01.302 [http-nio-9091-exec-6] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@2c74d73, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:54:01.305 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" cartype = '网约车'  and warndt >= '2025-07-13' and warndt <= '2025-07-13' ","dt":["2025-07-13","2025-07-13"],"s":0,"pageSize":10,"vehicle_type":"网约车","pageNum":1,"n":10}
2025-07-13 13:54:01.533 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:228 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:54:36.791 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/message/warnMsg/getWarnInfoList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:54:36.797 [http-nio-9091-exec-2] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/message/warnMsg/getWarnInfoList
2025-07-13 13:54:36.797 [http-nio-9091-exec-2] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@6576f1f4, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:54:36.802 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"warndt = '2025-07-13' and flag = 0","flag":0,"s":0,"n":1000}
2025-07-13 13:54:37.562 [http-nio-9091-exec-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:760 | http://yinshu.iok.la:8081/api/100588/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:54:51.991 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getPassengerMapData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:54:51.996 [http-nio-9091-exec-3] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getPassengerMapData
2025-07-13 13:54:51.996 [http-nio-9091-exec-3] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@1da2e3ae, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:54:51.999 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county != null and company != null and vehicle_type = '网约车'","selectType":"3"}
2025-07-13 13:54:52.382 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:383 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:54:52.526 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:54:52.529 [http-nio-9091-exec-7] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBasicData
2025-07-13 13:54:52.530 [http-nio-9091-exec-7] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@76d646a8, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:54:52.532 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-13 13:54:52.746 [http-nio-9091-exec-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:214 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:54:52.750 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:54:52.754 [http-nio-9091-exec-6] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOperationOverview
2025-07-13 13:54:52.754 [http-nio-9091-exec-6] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@586f48dd, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:54:52.756 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" hour = null and vehicle_type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-13 13:54:52.955 [http-nio-9091-exec-6] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:199 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:54:53.244 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getPassengerMapData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:54:53.249 [http-nio-9091-exec-4] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getPassengerMapData
2025-07-13 13:54:53.249 [http-nio-9091-exec-4] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@5561193c, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:54:53.251 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"county != null and company != null and vehicle_type = '网约车'","selectType":"3"}
2025-07-13 13:54:53.640 [http-nio-9091-exec-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:389 | http://yinshu.iok.la:8081/api/100476/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:55:03.172 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getBasicData [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:55:03.179 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getBasicData
2025-07-13 13:55:03.179 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@4ae05ffc, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:55:03.183 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":"type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-13 13:55:03.396 [http-nio-9091-exec-9] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:213 | http://yinshu.iok.la:8081/api/100454/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:55:03.402 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/toms/city-traffic/city/getOperationOverview [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ1NDQ5MDAsInVzZXJJZCI6IjEifQ.fHsGTBCrSOcy6z0FEL9koZ-HEJ4Fw3ISEZ0Qns6rg4Y
2025-07-13 13:55:03.405 [http-nio-9091-exec-10] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/toms/city-traffic/city/getOperationOverview
2025-07-13 13:55:03.405 [http-nio-9091-exec-10] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@2042ad47, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-07-13 13:55:03.407 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH | {"filter":" hour = null and vehicle_type = '网约车' ","selectType":"1","type":"网约车"}
2025-07-13 13:55:03.621 [http-nio-9091-exec-10] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:214 | http://yinshu.iok.la:8081/api/100482/data.json?&token=e5fdube5tb0aoOEjCagf9xU40qJ1eb9tH
2025-07-13 13:55:06.204 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopping...
2025-07-13 13:55:06.204 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@466fd19b]]
2025-07-13 13:55:06.204 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopped.
2025-07-13 13:55:06.218 [SpringApplicationShutdownHook] INFO  org.ehcache.core.EhcacheManager - Cache 's_setting' removed from EhcacheManager.
2025-07-13 13:55:06.218 [SpringApplicationShutdownHook] INFO  org.ehcache.core.EhcacheManager - Cache 'road_network' removed from EhcacheManager.
2025-07-13 13:55:06.226 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-13 13:55:06.227 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-13 13:55:06.228 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-13 13:55:06.228 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
