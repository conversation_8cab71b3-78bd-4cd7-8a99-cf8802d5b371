2025-08-01 09:36:59.794 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-01 09:36:59.803 [main] INFO  com.yinshu.StartApplication - Starting StartApplication using Java 21.0.6 on xiaoxiongxueJavadeMacBook-Air.local with PID 37491 (/Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot/tocc-admin-all-starter/target/classes started by javaxiaobear in /Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot)
2025-08-01 09:36:59.804 [main] INFO  com.yinshu.StartApplication - The following 1 profile is active: "dev"
2025-08-01 09:37:00.370 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 09:37:00.371 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 09:37:00.400 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
2025-08-01 09:37:00.606 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 09:37:00.610 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$17105194] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 09:37:00.619 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 09:37:00.818 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9091 (http)
2025-08-01 09:37:00.823 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9091"]
2025-08-01 09:37:00.823 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 09:37:00.823 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-01 09:37:00.871 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring embedded WebApplicationContext
2025-08-01 09:37:00.871 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1037 ms
2025-08-01 09:37:01.091 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [syncDb] success
2025-08-01 09:37:01.092 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [primary] success
2025-08-01 09:37:01.092 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [primary]
2025-08-01 09:37:01.263 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.yinshu.tcps.dao.TcpsFileDao.queryPageList] is ignored, because it exists, maybe from xml file
2025-08-01 09:37:01.264 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.yinshu.tcps.dao.TcpsFileDao.removeByFids] is ignored, because it exists, maybe from xml file
2025-08-01 09:37:03.533 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/**'] with [org.springframework.security.web.session.DisableEncodeUrlFilter@1fa8a4f7, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@54c1878b, org.springframework.security.web.context.SecurityContextPersistenceFilter@313a3af8, org.springframework.security.web.header.HeaderWriterFilter@281d3ce7, org.springframework.web.filter.CorsFilter@27f6e2c3, org.springframework.security.web.authentication.logout.LogoutFilter@51d26fed, com.yinshu.sys.security.JwtAuthenticationTokenFilter@69a3bf40, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@551976c2, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@65013d71, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3c89fdd8, org.springframework.security.web.session.SessionManagementFilter@53bc6492, org.springframework.security.web.access.ExceptionTranslationFilter@5e528476, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1e60890c]
2025-08-01 09:37:03.737 [main] WARN  o.s.b.a.freemarker.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-08-01 09:37:03.807 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9091"]
2025-08-01 09:37:03.816 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9091 (http) with context path '/tocc'
2025-08-01 09:37:03.816 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Starting...
2025-08-01 09:37:03.816 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@254a9f65]]
2025-08-01 09:37:03.817 [main] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Started.
2025-08-01 09:37:03.823 [main] INFO  com.yinshu.StartApplication - Started StartApplication in 4.223 seconds (JVM running for 5.047)
2025-08-01 09:37:03.972 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1,primary} inited
2025-08-01 09:37:04.092 [main] INFO  com.yinshu.tact.job.RegularCheckJob - 初始化定期自动查岗任务
2025-08-01 09:37:04.122 [main] INFO  com.yinshu.tact.job.RegularCheckJob - 定期自动查岗任务初始化完成，共加载 0 个规则
2025-08-01 09:37:30.179 [TaskSchedulerThreadPool-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aoWpXY1SFIyQPvMUjpyLFE | {"flag":0}
2025-08-01 09:37:34.693 [TaskSchedulerThreadPool-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:4516 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aoWpXY1SFIyQPvMUjpyLFE
2025-08-01 09:37:51.268 [http-nio-9091-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 09:37:51.268 [http-nio-9091-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 09:37:51.275 [http-nio-9091-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 7 ms
2025-08-01 09:37:51.289 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/getKaptchaCode [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTYxMTQ3MzgsInVzZXJJZCI6IjEifQ.xeOb5eTxBDu08AG3NkcBkaVekG0dGYZS2E2f_qtKycY
2025-08-01 09:37:57.140 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/login [jwt.token]null
2025-08-01 09:37:57.151 [http-nio-9091-exec-5] INFO  o.s.s.a.AnnotationAsyncExecutionInterceptor - More than one TaskExecutor bean found within the context, and none is named 'taskExecutor'. Mark one of them as primary or name it 'taskExecutor' (possibly as an alias) in order to use it for async processing: [clientInboundChannelExecutor, clientOutboundChannelExecutor, brokerChannelExecutor, taskScheduler]
2025-08-01 09:37:57.408 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/getKaptchaCode [jwt.token]null
2025-08-01 09:38:00.016 [TaskSchedulerThreadPool-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aoWpXY1SFIyQPvMUjpyLFE | {"flag":0}
2025-08-01 09:38:01.519 [TaskSchedulerThreadPool-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:1503 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aoWpXY1SFIyQPvMUjpyLFE
2025-08-01 09:38:02.334 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/getKaptchaCode [jwt.token]null
2025-08-01 09:38:03.357 [MessageBroker-1] INFO  o.s.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-08-01 09:38:06.893 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/login [jwt.token]null
2025-08-01 09:38:08.352 [http-nio-9091-exec-4] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/sys/dictionary/queryTreeAllList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTYxNzIyODcsInVzZXJJZCI6IjEifQ.fbHUx21SouzxLN_GzjtzFOikEy-NXOZJFXG7GLNqoLg
2025-08-01 09:38:08.352 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/tcps/industry-regulation/getTransportStatistics [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTYxNzIyODcsInVzZXJJZCI6IjEifQ.fbHUx21SouzxLN_GzjtzFOikEy-NXOZJFXG7GLNqoLg
2025-08-01 09:38:08.352 [http-nio-9091-exec-9] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/tcps/industry-regulation/getTransportStatistics [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTYxNzIyODcsInVzZXJJZCI6IjEifQ.fbHUx21SouzxLN_GzjtzFOikEy-NXOZJFXG7GLNqoLg
2025-08-01 09:38:08.352 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/tcps/industry-regulation/getHazardList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTYxNzIyODcsInVzZXJJZCI6IjEifQ.fbHUx21SouzxLN_GzjtzFOikEy-NXOZJFXG7GLNqoLg
2025-08-01 09:38:08.352 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/tcps/industry-regulation/getTransportStatistics [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTYxNzIyODcsInVzZXJJZCI6IjEifQ.fbHUx21SouzxLN_GzjtzFOikEy-NXOZJFXG7GLNqoLg
2025-08-01 09:38:08.353 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/tcps/industry-regulation/getHazardStatistics [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTYxNzIyODcsInVzZXJJZCI6IjEifQ.fbHUx21SouzxLN_GzjtzFOikEy-NXOZJFXG7GLNqoLg
2025-08-01 09:38:08.449 [http-nio-9091-exec-3] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/tcps/industry-regulation/getHazardStatistics
2025-08-01 09:38:08.449 [http-nio-9091-exec-9] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/tcps/industry-regulation/getTransportStatistics
2025-08-01 09:38:08.449 [http-nio-9091-exec-3] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@2729a4cc, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-08-01 09:38:08.449 [http-nio-9091-exec-9] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@eceaf46, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-08-01 09:38:08.449 [http-nio-9091-exec-8] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/tcps/industry-regulation/getTransportStatistics
2025-08-01 09:38:08.449 [http-nio-9091-exec-4] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/sys/dictionary/queryTreeAllList
2025-08-01 09:38:08.450 [http-nio-9091-exec-4] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@5f498f9e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-08-01 09:38:08.450 [http-nio-9091-exec-1] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/tcps/industry-regulation/getHazardList
2025-08-01 09:38:08.449 [http-nio-9091-exec-10] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/tcps/industry-regulation/getTransportStatistics
2025-08-01 09:38:08.450 [http-nio-9091-exec-10] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@19910c93, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-08-01 09:38:08.450 [http-nio-9091-exec-1] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@5690920a, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-08-01 09:38:08.450 [http-nio-9091-exec-8] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@50aa4550, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-08-01 09:38:08.462 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/100015/data.json?&token=e5fdube5tb0aoWpXY1SFIyQPvMUjpyLFE | {"date":"2025-08","filter":"month = '2025-08' ","dateType":"M","s":0,"n":1000}
2025-08-01 09:38:08.463 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/100017/data.json?&token=e5fdube5tb0aoWpXY1SFIyQPvMUjpyLFE | {"date":"2025-08","filter":"day like '2025-08' order by day","dateType":"M","s":0,"n":1000}
2025-08-01 09:38:08.765 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:303 | http://*************:8081/api/100015/data.json?&token=e5fdube5tb0aoWpXY1SFIyQPvMUjpyLFE
2025-08-01 09:38:08.765 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:302 | http://*************:8081/api/100017/data.json?&token=e5fdube5tb0aoWpXY1SFIyQPvMUjpyLFE
2025-08-01 09:38:08.773 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/auth | {"appid":"tocc_api","time":"DCiYL2n0IwnrvcJBmw/MLw=="}
2025-08-01 09:38:08.773 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/auth | {"appid":"tocc_api","time":"DCiYL2n0IwnrvcJBmw/MLw=="}
2025-08-01 09:38:08.999 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:226 | http://*************:8081/api/auth
2025-08-01 09:38:09.004 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/100015/data.json?&token=e5fdube5tb0aoWpXY1SFIyQPvMUjpyLFE&token=e5fdube5tb0aocJ4OrV4R2UaTUVeBWlRw | {"date":"2025-08","filter":"month = '2025-08' ","dateType":"M","s":0,"n":1000}
2025-08-01 09:38:09.036 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:263 | http://*************:8081/api/auth
2025-08-01 09:38:09.038 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/100017/data.json?&token=e5fdube5tb0aoWpXY1SFIyQPvMUjpyLFE&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"date":"2025-08","filter":"day like '2025-08' order by day","dateType":"M","s":0,"n":1000}
2025-08-01 09:38:09.217 [http-nio-9091-exec-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:213 | http://*************:8081/api/100015/data.json?&token=e5fdube5tb0aoWpXY1SFIyQPvMUjpyLFE&token=e5fdube5tb0aocJ4OrV4R2UaTUVeBWlRw
2025-08-01 09:38:09.258 [http-nio-9091-exec-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:220 | http://*************:8081/api/100017/data.json?&token=e5fdube5tb0aoWpXY1SFIyQPvMUjpyLFE&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:38:12.227 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/sys/unit/getUnitTree [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTYxNzIyODcsInVzZXJJZCI6IjEifQ.fbHUx21SouzxLN_GzjtzFOikEy-NXOZJFXG7GLNqoLg
2025-08-01 09:38:12.230 [http-nio-9091-exec-1] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/sys/user/pageList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTYxNzIyODcsInVzZXJJZCI6IjEifQ.fbHUx21SouzxLN_GzjtzFOikEy-NXOZJFXG7GLNqoLg
2025-08-01 09:38:12.245 [http-nio-9091-exec-8] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/sys/unit/getUnitTree
2025-08-01 09:38:12.245 [http-nio-9091-exec-8] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@524892dd, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-08-01 09:38:12.246 [http-nio-9091-exec-1] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/sys/user/pageList?keyword=&unitId=&userName=&pageNumber=1&pageSize=10&total=0
2025-08-01 09:38:12.246 [http-nio-9091-exec-1] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@1730eee, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-08-01 09:38:14.750 [http-nio-9091-exec-7] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/sys/setting/pageList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTYxNzIyODcsInVzZXJJZCI6IjEifQ.fbHUx21SouzxLN_GzjtzFOikEy-NXOZJFXG7GLNqoLg
2025-08-01 09:38:14.755 [http-nio-9091-exec-7] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/sys/setting/pageList?queryKeyword=&pageNumber=1&pageSize=10&total=0
2025-08-01 09:38:14.755 [http-nio-9091-exec-7] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@671be4bf, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-08-01 09:38:16.899 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/sys/setting/pageList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTYxNzIyODcsInVzZXJJZCI6IjEifQ.fbHUx21SouzxLN_GzjtzFOikEy-NXOZJFXG7GLNqoLg
2025-08-01 09:38:16.905 [http-nio-9091-exec-5] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/sys/setting/pageList?queryKeyword=&pageNumber=3&pageSize=10&total=29
2025-08-01 09:38:16.905 [http-nio-9091-exec-5] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@53464547, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-08-01 09:38:19.314 [http-nio-9091-exec-8] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/sys/setting/getNextSort [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTYxNzIyODcsInVzZXJJZCI6IjEifQ.fbHUx21SouzxLN_GzjtzFOikEy-NXOZJFXG7GLNqoLg
2025-08-01 09:38:19.319 [http-nio-9091-exec-8] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/sys/setting/getNextSort?loading=false
2025-08-01 09:38:19.319 [http-nio-9091-exec-8] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@f4510b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-08-01 09:38:30.010 [TaskSchedulerThreadPool-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:38:30.272 [TaskSchedulerThreadPool-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:262 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:39:00.027 [TaskSchedulerThreadPool-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:39:00.255 [TaskSchedulerThreadPool-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:228 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:39:05.059 [http-nio-9091-exec-2] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/sys/setting/create [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTYxNzIyODcsInVzZXJJZCI6IjEifQ.fbHUx21SouzxLN_GzjtzFOikEy-NXOZJFXG7GLNqoLg
2025-08-01 09:39:05.069 [http-nio-9091-exec-2] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/sys/setting/create
2025-08-01 09:39:05.070 [http-nio-9091-exec-2] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@5fe1343a, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-08-01 09:39:05.162 [http-nio-9091-exec-6] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/sys/setting/pageList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTYxNzIyODcsInVzZXJJZCI6IjEifQ.fbHUx21SouzxLN_GzjtzFOikEy-NXOZJFXG7GLNqoLg
2025-08-01 09:39:05.166 [http-nio-9091-exec-6] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/sys/setting/pageList?queryKeyword=&pageNumber=3&pageSize=10&total=29
2025-08-01 09:39:05.166 [http-nio-9091-exec-6] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@7e0ad5b9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-08-01 09:39:30.011 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:39:30.376 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:365 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:40:00.020 [TaskSchedulerThreadPool-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:40:01.749 [TaskSchedulerThreadPool-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:1730 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:40:30.051 [TaskSchedulerThreadPool-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:40:31.264 [TaskSchedulerThreadPool-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:1213 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:41:00.030 [TaskSchedulerThreadPool-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:41:00.329 [TaskSchedulerThreadPool-1] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:299 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:41:30.017 [TaskSchedulerThreadPool-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:41:30.316 [TaskSchedulerThreadPool-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:299 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:42:00.028 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:42:00.372 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:344 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:42:30.017 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:42:32.234 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:2217 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:43:00.058 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:43:03.099 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:3045 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:43:30.034 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:43:30.400 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:366 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:44:00.019 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:44:00.387 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:368 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:44:26.577 [http-nio-9091-exec-10] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/sync/syncData [jwt.token]null
2025-08-01 09:44:26.688 [http-nio-9091-exec-5] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/sync/syncData [jwt.token]null
2025-08-01 09:44:26.776 [http-nio-9091-exec-5] ERROR com.yinshu.exception.GlobalExceptionHandler - 全局异常捕获>>>>>
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'c44f1b4688f54fb3b7b78fb282ec8b31' for key 's_user.PRIMARY'
### The error may exist in com/yinshu/sys/dao/UserDao.java (best guess)
### The error may involve com.yinshu.sys.dao.UserDao.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO s_user  ( id, user_name, login_name, password, unit_id, create_time,     data_source )  VALUES (  ?, ?, ?, ?, ?, ?,     ?  )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'c44f1b4688f54fb3b7b78fb282ec8b31' for key 's_user.PRIMARY'
; Duplicate entry 'c44f1b4688f54fb3b7b78fb282ec8b31' for key 's_user.PRIMARY'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'c44f1b4688f54fb3b7b78fb282ec8b31' for key 's_user.PRIMARY'
org.springframework.dao.DuplicateKeyException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'c44f1b4688f54fb3b7b78fb282ec8b31' for key 's_user.PRIMARY'
### The error may exist in com/yinshu/sys/dao/UserDao.java (best guess)
### The error may involve com.yinshu.sys.dao.UserDao.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO s_user  ( id, user_name, login_name, password, unit_id, create_time,     data_source )  VALUES (  ?, ?, ?, ?, ?, ?,     ?  )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'c44f1b4688f54fb3b7b78fb282ec8b31' for key 's_user.PRIMARY'
; Duplicate entry 'c44f1b4688f54fb3b7b78fb282ec8b31' for key 's_user.PRIMARY'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'c44f1b4688f54fb3b7b78fb282ec8b31' for key 's_user.PRIMARY'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:244)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at jdk.proxy2/jdk.proxy2.$Proxy106.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy107.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.IService.save(IService.java:61)
	at com.baomidou.mybatisplus.extension.service.IService$$FastClassBySpringCGLIB$$f8525d18.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationInterceptor.invoke(DynamicDataSourceAnnotationInterceptor.java:50)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.yinshu.sys.service.impl.UserServiceImpl$$EnhancerBySpringCGLIB$$c662b23f.save(<generated>)
	at com.yinshu.sync.strategy.UserSyncDataStrategy.addUser(UserSyncDataStrategy.java:60)
	at com.yinshu.sync.strategy.UserSyncDataStrategy.handleByEnum(UserSyncDataStrategy.java:30)
	at com.yinshu.sync.strategy.UserSyncDataStrategy.execute(UserSyncDataStrategy.java:24)
	at com.yinshu.sync.SyncDataController.syncData(SyncDataController.java:32)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.github.xiaoymin.knife4j.extend.filter.basic.ServletSecurityBasicAuthFilter.doFilter(ServletSecurityBasicAuthFilter.java:56)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.yinshu.sys.security.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:64)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'c44f1b4688f54fb3b7b78fb282ec8b31' for key 's_user.PRIMARY'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:117)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3462)
	at com.alibaba.druid.wall.WallFilter.preparedStatement_execute(WallFilter.java:686)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at jdk.proxy3/jdk.proxy3.$Proxy331.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy329.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy328.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 119 common frames omitted
2025-08-01 09:44:30.012 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:44:30.297 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:285 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:45:00.047 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:45:00.390 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:344 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:45:30.020 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:45:30.447 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:427 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:46:00.047 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:46:00.272 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:225 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:46:30.030 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:46:30.338 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:308 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:47:00.009 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:47:00.378 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:369 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:47:30.016 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:47:30.425 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:409 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:48:00.025 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:48:00.421 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:396 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:48:30.007 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:48:30.737 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:730 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:49:00.010 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:49:05.145 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:5135 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:49:30.036 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:49:31.019 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:983 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:50:00.010 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:50:00.432 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:422 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:50:30.035 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:50:31.405 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:1370 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:51:00.026 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:51:01.172 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:1146 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:51:30.009 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:51:30.343 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:334 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:52:00.009 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:52:00.398 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:389 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:52:30.021 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:52:30.256 [TaskSchedulerThreadPool-3] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:235 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:53:00.009 [TaskSchedulerThreadPool-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:53:00.391 [TaskSchedulerThreadPool-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:382 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:53:30.008 [TaskSchedulerThreadPool-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:53:30.361 [TaskSchedulerThreadPool-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:353 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:54:00.012 [TaskSchedulerThreadPool-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:54:00.358 [TaskSchedulerThreadPool-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:346 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:54:30.020 [TaskSchedulerThreadPool-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:54:30.304 [TaskSchedulerThreadPool-4] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:284 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:55:00.011 [TaskSchedulerThreadPool-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:55:00.217 [TaskSchedulerThreadPool-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:206 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:55:30.024 [TaskSchedulerThreadPool-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:55:30.317 [TaskSchedulerThreadPool-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:293 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:56:00.014 [TaskSchedulerThreadPool-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:56:01.788 [TaskSchedulerThreadPool-5] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:1775 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:56:30.010 [TaskSchedulerThreadPool-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:56:31.315 [TaskSchedulerThreadPool-2] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:1305 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:57:00.020 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:57:00.481 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:461 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:57:30.007 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:57:32.775 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:2768 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:58:00.009 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:58:01.065 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:1056 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:58:30.014 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:58:32.200 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:2187 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:59:00.011 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:59:00.199 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:188 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 09:59:30.011 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 09:59:30.258 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:247 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:00:00.022 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:00:00.239 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:217 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:00:30.010 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:00:30.326 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:316 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:01:00.005 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:01:00.250 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:245 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:01:30.031 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:01:32.076 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:2046 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:02:00.007 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:02:00.223 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:216 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:02:30.050 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:02:30.418 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:368 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:03:00.015 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:03:00.362 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:347 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:03:30.008 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:03:30.285 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:277 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:04:00.010 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:04:00.280 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:270 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:04:30.006 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:04:30.367 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:361 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:05:00.007 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:05:00.381 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:374 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:05:30.012 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:05:30.302 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:290 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:06:00.027 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:06:00.611 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:584 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:06:30.012 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:06:31.998 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:1986 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:07:00.012 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:07:00.449 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:437 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:07:30.004 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:07:30.545 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:541 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:08:00.018 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:08:01.761 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:1743 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:08:03.373 [MessageBroker-1] INFO  o.s.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-08-01 10:08:30.013 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:08:30.333 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:320 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:09:00.007 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:09:00.412 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:405 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:09:30.009 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:09:31.800 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:1791 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:10:00.013 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:10:01.278 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:1266 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:10:30.022 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:10:30.643 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:621 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:11:00.012 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:11:01.073 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:1061 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:11:30.020 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:11:30.369 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:349 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:12:00.011 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:12:00.377 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:366 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:12:30.006 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:12:31.566 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:1560 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:13:00.022 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:13:00.418 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:396 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:13:30.016 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:13:30.306 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:290 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:14:00.015 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:14:00.479 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:465 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:14:30.008 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:14:30.395 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:387 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:15:00.024 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:15:00.593 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:569 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:15:30.018 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:15:30.316 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:299 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:16:00.032 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:16:00.495 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:463 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:16:30.015 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:16:30.228 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:213 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:17:00.022 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:17:00.404 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:383 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:17:30.007 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:17:30.415 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:408 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:18:00.037 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:18:00.934 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:897 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:18:30.014 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:18:30.244 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:230 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:19:00.012 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:19:00.548 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:536 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:19:30.023 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:19:30.250 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:228 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:20:00.009 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:20:00.328 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:319 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:20:30.027 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:20:30.266 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:241 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:21:00.022 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:21:00.456 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:434 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:21:30.026 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:21:30.317 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:292 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:22:00.056 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:22:01.144 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:1090 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:22:30.017 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:22:30.190 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:173 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:23:00.013 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:23:00.463 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:450 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:23:30.015 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:23:30.609 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:595 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:24:00.014 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:24:00.266 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:253 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:24:30.016 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:24:30.358 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:343 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:25:00.029 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:25:00.353 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:325 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:25:30.006 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:25:30.235 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:229 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:26:00.014 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:26:00.292 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:278 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:26:30.013 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:26:30.526 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:513 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:26:52.316 [http-nio-9091-exec-3] INFO  c.yinshu.sys.security.JwtAuthenticationTokenFilter - ----------(1)jwt.filter----------[URL]/tocc/api/sys/setting/pageList [jwt.token]eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTYxNzIyODcsInVzZXJJZCI6IjEifQ.fbHUx21SouzxLN_GzjtzFOikEy-NXOZJFXG7GLNqoLg
2025-08-01 10:26:52.349 [http-nio-9091-exec-3] INFO  c.y.s.s.UrlFilterInvocationSecurityMetadataSource - ----------(2)Filter.URL----------[URL]/api/sys/setting/pageList?queryKeyword=&pageNumber=1&pageSize=10&total=0
2025-08-01 10:26:52.349 [http-nio-9091-exec-3] INFO  com.yinshu.sys.security.UrlAccessDecisionManager - ----------(3)UrlAccess----------[auth]UsernamePasswordAuthenticationToken [Principal=com.yinshu.sys.entity.SessionUser@54826f31, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[ADMIN, APP]]
2025-08-01 10:27:00.009 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:27:00.618 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:609 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:27:30.022 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:27:30.477 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:455 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:28:00.010 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:28:00.322 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:312 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:28:30.009 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:28:30.288 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:279 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:29:00.018 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:29:01.289 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:1271 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:29:30.009 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:29:31.695 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:1686 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:30:00.022 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:30:00.835 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:814 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:30:30.019 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:30:31.224 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:1205 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:31:00.005 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:31:00.341 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:336 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:31:30.012 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:31:30.343 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:331 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:32:00.010 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:32:00.352 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:342 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:32:30.012 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:32:30.840 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:829 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:33:00.013 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:33:00.409 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:397 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:33:30.010 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:33:30.349 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:339 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:34:00.010 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:34:00.241 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:231 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:34:30.012 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:34:30.233 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:221 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:35:00.017 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:35:00.318 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:301 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:35:30.009 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:35:30.975 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:966 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:36:00.020 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:36:00.939 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:919 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:36:30.010 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:36:30.271 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:261 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:37:00.011 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:37:01.936 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:1925 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:37:30.006 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:37:31.279 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:1273 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:38:00.007 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:38:00.873 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:866 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:38:03.379 [MessageBroker-2] INFO  o.s.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 3, active threads = 1, queued tasks = 0, completed tasks = 2]
2025-08-01 10:38:30.031 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:38:30.263 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:233 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:39:00.007 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:39:00.425 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:418 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:39:30.012 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:39:30.698 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:687 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:40:00.022 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:40:00.715 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:694 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:40:30.011 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:40:30.812 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:801 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:41:00.029 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:41:00.289 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:260 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:41:30.009 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:41:30.420 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:412 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:42:00.003 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:42:00.343 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:340 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:42:30.014 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:42:30.339 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:325 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:43:00.008 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:43:00.249 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:241 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:43:30.004 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:43:30.329 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:326 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:44:00.004 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:44:00.417 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:413 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:44:30.006 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:44:31.829 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:1823 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:45:00.014 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:45:00.386 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:372 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:45:30.006 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:45:30.460 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:454 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:46:00.007 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:46:00.698 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:691 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:46:30.008 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:46:31.059 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:1051 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:47:00.007 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:47:00.289 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:282 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:47:30.008 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:47:30.185 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:177 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:48:00.005 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:48:00.305 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:300 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:48:30.007 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:48:30.264 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:257 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:49:00.005 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:49:00.324 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:319 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:49:30.004 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:49:30.259 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:255 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:50:00.007 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:50:00.516 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:509 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:50:30.007 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:50:30.253 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:246 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:51:00.010 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:51:00.271 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:261 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:51:30.013 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:51:30.315 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:302 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:52:00.019 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:52:00.363 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:344 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:52:30.013 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:52:30.276 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:263 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:53:00.007 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:53:00.342 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:335 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:53:30.008 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:53:30.270 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:262 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:54:00.015 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:54:00.357 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:343 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:54:30.008 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:54:30.225 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:217 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:55:00.007 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:55:00.298 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:291 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:55:30.006 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:55:30.290 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:284 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:56:00.015 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:56:00.229 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:214 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:56:30.005 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:56:30.272 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:267 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:57:00.005 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.start)-------->http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm | {"flag":0}
2025-08-01 10:57:00.273 [TaskSchedulerThreadPool-7] INFO  com.yinshu.http.HttpTemplateAbstract - -------------请求数据(post.end)-------->times:268 | http://*************:8081/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?&token=e5fdube5tb0aotNP8FPs2WG4HARADYwUm
2025-08-01 10:57:01.917 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopping...
2025-08-01 10:57:01.917 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@254a9f65]]
2025-08-01 10:57:01.917 [SpringApplicationShutdownHook] INFO  o.s.m.simp.broker.SimpleBrokerMessageHandler - Stopped.
2025-08-01 10:57:01.969 [SpringApplicationShutdownHook] INFO  com.yinshu.tact.job.RegularCheckJob - 正在关闭定期查岗任务...
2025-08-01 10:57:01.969 [SpringApplicationShutdownHook] INFO  com.yinshu.tact.job.RegularCheckJob - 定期查岗任务已关闭
2025-08-01 10:57:06.345 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-01 10:57:06.351 [main] INFO  com.yinshu.StartApplication - Starting StartApplication using Java 21.0.6 on xiaoxiongxueJavadeMacBook-Air.local with PID 51113 (/Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot/tocc-admin-all-starter/target/classes started by javaxiaobear in /Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot)
2025-08-01 10:57:06.351 [main] INFO  com.yinshu.StartApplication - The following 1 profile is active: "dev"
2025-08-01 10:57:06.913 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 10:57:06.915 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 10:57:06.949 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.
2025-08-01 10:57:07.173 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 10:57:07.177 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$e47de5d4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 10:57:07.186 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 10:57:07.393 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9091 (http)
2025-08-01 10:57:07.398 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9091"]
2025-08-01 10:57:07.399 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 10:57:07.399 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-01 10:57:07.449 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring embedded WebApplicationContext
2025-08-01 10:57:07.450 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1077 ms
2025-08-01 10:57:07.687 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [syncDb] success
2025-08-01 10:57:07.688 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [primary] success
2025-08-01 10:57:07.688 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [primary]
2025-08-01 10:57:07.850 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.yinshu.tcps.dao.TcpsFileDao.queryPageList] is ignored, because it exists, maybe from xml file
2025-08-01 10:57:07.851 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.yinshu.tcps.dao.TcpsFileDao.removeByFids] is ignored, because it exists, maybe from xml file
2025-08-01 11:08:19.349 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-01 11:08:19.355 [main] INFO  com.yinshu.StartApplication - Starting StartApplication using Java 21.0.6 on xiaoxiongxueJavadeMacBook-Air.local with PID 8474 (/Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot/tocc-admin-all-starter/target/classes started by javaxiaobear in /Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot)
2025-08-01 11:08:19.356 [main] INFO  com.yinshu.StartApplication - The following 1 profile is active: "dev"
2025-08-01 11:08:19.926 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 11:08:19.928 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 11:08:19.957 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-08-01 11:08:20.166 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 11:08:20.169 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$9d5f21f6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 11:08:20.178 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 11:08:20.382 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9091 (http)
2025-08-01 11:08:20.387 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9091"]
2025-08-01 11:08:20.387 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:08:20.387 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-01 11:08:20.437 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:08:20.437 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1056 ms
2025-08-01 11:08:20.659 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [syncDb] success
2025-08-01 11:08:20.660 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [primary] success
2025-08-01 11:08:20.660 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [primary]
2025-08-01 11:08:20.845 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.yinshu.tcps.dao.TcpsFileDao.queryPageList] is ignored, because it exists, maybe from xml file
2025-08-01 11:08:20.845 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.yinshu.tcps.dao.TcpsFileDao.removeByFids] is ignored, because it exists, maybe from xml file
2025-08-01 11:08:22.640 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'videoController': Unsatisfied dependency expressed through field 'videoService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'videoServiceImpl': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'sdk.api.base-url' in value "${sdk.api.base-url}"
2025-08-01 11:08:22.643 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-01 11:08:22.644 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-0} closing ...
2025-08-01 11:08:22.644 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-0} closing ...
2025-08-01 11:08:22.644 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-01 11:08:22.754 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-01 11:08:22.771 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-01 11:08:22.779 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'videoController': Unsatisfied dependency expressed through field 'videoService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'videoServiceImpl': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'sdk.api.base-url' in value "${sdk.api.base-url}"
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:713)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at com.yinshu.StartApplication.main(StartApplication.java:26)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'videoServiceImpl': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'sdk.api.base-url' in value "${sdk.api.base-url}"
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:414)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710)
	... 18 common frames omitted
Caused by: java.lang.IllegalArgumentException: Could not resolve placeholder 'sdk.api.base-url' in value "${sdk.api.base-url}"
	at org.springframework.util.PropertyPlaceholderHelper.parseStringValue(PropertyPlaceholderHelper.java:180)
	at org.springframework.util.PropertyPlaceholderHelper.replacePlaceholders(PropertyPlaceholderHelper.java:126)
	at org.springframework.core.env.AbstractPropertyResolver.doResolvePlaceholders(AbstractPropertyResolver.java:239)
	at org.springframework.core.env.AbstractPropertyResolver.resolveRequiredPlaceholders(AbstractPropertyResolver.java:210)
	at org.springframework.context.support.PropertySourcesPlaceholderConfigurer.lambda$processProperties$0(PropertySourcesPlaceholderConfigurer.java:191)
	at org.springframework.beans.factory.support.AbstractBeanFactory.resolveEmbeddedValue(AbstractBeanFactory.java:936)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1332)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408)
	... 29 common frames omitted
2025-08-01 11:42:34.973 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-01 11:42:34.981 [main] INFO  com.yinshu.StartApplication - Starting StartApplication using Java 21.0.6 on xiaoxiongxueJavadeMacBook-Air.local with PID 17562 (/Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot/tocc-admin-all-starter/target/classes started by javaxiaobear in /Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot)
2025-08-01 11:42:34.981 [main] INFO  com.yinshu.StartApplication - The following 1 profile is active: "dev"
2025-08-01 11:42:35.757 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 11:42:35.758 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 11:42:35.788 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-08-01 11:42:36.029 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 11:42:36.032 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$fdf1b9e9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 11:42:36.042 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 11:42:36.256 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9091 (http)
2025-08-01 11:42:36.259 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9091"]
2025-08-01 11:42:36.260 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:42:36.260 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-01 11:42:36.315 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:42:36.315 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1300 ms
2025-08-01 11:42:36.537 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [syncDb] success
2025-08-01 11:42:36.537 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [primary] success
2025-08-01 11:42:36.537 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [primary]
2025-08-01 11:42:36.711 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.yinshu.tcps.dao.TcpsFileDao.queryPageList] is ignored, because it exists, maybe from xml file
2025-08-01 11:42:36.711 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.yinshu.tcps.dao.TcpsFileDao.removeByFids] is ignored, because it exists, maybe from xml file
2025-08-01 11:42:38.503 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'videoController': Unsatisfied dependency expressed through field 'videoService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'videoServiceImpl': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'sdk.api.base-url' in value "${sdk.api.base-url}"
2025-08-01 11:42:38.506 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-01 11:42:38.507 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-0} closing ...
2025-08-01 11:42:38.507 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-0} closing ...
2025-08-01 11:42:38.507 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-01 11:42:38.618 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-01 11:42:38.635 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-01 11:42:38.643 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'videoController': Unsatisfied dependency expressed through field 'videoService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'videoServiceImpl': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'sdk.api.base-url' in value "${sdk.api.base-url}"
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:713)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at com.yinshu.StartApplication.main(StartApplication.java:26)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'videoServiceImpl': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'sdk.api.base-url' in value "${sdk.api.base-url}"
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:414)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710)
	... 18 common frames omitted
Caused by: java.lang.IllegalArgumentException: Could not resolve placeholder 'sdk.api.base-url' in value "${sdk.api.base-url}"
	at org.springframework.util.PropertyPlaceholderHelper.parseStringValue(PropertyPlaceholderHelper.java:180)
	at org.springframework.util.PropertyPlaceholderHelper.replacePlaceholders(PropertyPlaceholderHelper.java:126)
	at org.springframework.core.env.AbstractPropertyResolver.doResolvePlaceholders(AbstractPropertyResolver.java:239)
	at org.springframework.core.env.AbstractPropertyResolver.resolveRequiredPlaceholders(AbstractPropertyResolver.java:210)
	at org.springframework.context.support.PropertySourcesPlaceholderConfigurer.lambda$processProperties$0(PropertySourcesPlaceholderConfigurer.java:191)
	at org.springframework.beans.factory.support.AbstractBeanFactory.resolveEmbeddedValue(AbstractBeanFactory.java:936)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1332)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408)
	... 29 common frames omitted
2025-08-01 11:43:19.983 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-01 11:43:19.988 [main] INFO  com.yinshu.StartApplication - Starting StartApplication using Java 21.0.6 on xiaoxiongxueJavadeMacBook-Air.local with PID 17883 (/Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot/tocc-admin-all-starter/target/classes started by javaxiaobear in /Users/<USER>/Documents/javaxiaobear/work/湖南银数/忻州_交通运行监测平台/xinzhou-tocc-boot)
2025-08-01 11:43:19.988 [main] INFO  com.yinshu.StartApplication - The following 1 profile is active: "dev"
2025-08-01 11:43:20.549 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 11:43:20.550 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 11:43:20.580 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25 ms. Found 0 Redis repository interfaces.
2025-08-01 11:43:20.791 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 11:43:20.794 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$e47de5d4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 11:43:20.803 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 11:43:21.003 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9091 (http)
2025-08-01 11:43:21.007 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9091"]
2025-08-01 11:43:21.007 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:43:21.007 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-01 11:43:21.054 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/tocc] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:43:21.054 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1035 ms
2025-08-01 11:43:21.283 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [syncDb] success
2025-08-01 11:43:21.284 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [primary] success
2025-08-01 11:43:21.284 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [primary]
2025-08-01 11:43:21.455 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.yinshu.tcps.dao.TcpsFileDao.queryPageList] is ignored, because it exists, maybe from xml file
2025-08-01 11:43:21.456 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.yinshu.tcps.dao.TcpsFileDao.removeByFids] is ignored, because it exists, maybe from xml file
2025-08-01 11:43:23.186 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'videoController': Unsatisfied dependency expressed through field 'videoService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'videoServiceImpl': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'sdk.api.base-url' in value "${sdk.api.base-url}"
2025-08-01 11:43:23.189 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-01 11:43:23.190 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-0} closing ...
2025-08-01 11:43:23.190 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-0} closing ...
2025-08-01 11:43:23.190 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-01 11:43:23.301 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-01 11:43:23.321 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-01 11:43:23.329 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'videoController': Unsatisfied dependency expressed through field 'videoService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'videoServiceImpl': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'sdk.api.base-url' in value "${sdk.api.base-url}"
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:713)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at com.yinshu.StartApplication.main(StartApplication.java:26)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'videoServiceImpl': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'sdk.api.base-url' in value "${sdk.api.base-url}"
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:414)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710)
	... 18 common frames omitted
Caused by: java.lang.IllegalArgumentException: Could not resolve placeholder 'sdk.api.base-url' in value "${sdk.api.base-url}"
	at org.springframework.util.PropertyPlaceholderHelper.parseStringValue(PropertyPlaceholderHelper.java:180)
	at org.springframework.util.PropertyPlaceholderHelper.replacePlaceholders(PropertyPlaceholderHelper.java:126)
	at org.springframework.core.env.AbstractPropertyResolver.doResolvePlaceholders(AbstractPropertyResolver.java:239)
	at org.springframework.core.env.AbstractPropertyResolver.resolveRequiredPlaceholders(AbstractPropertyResolver.java:210)
	at org.springframework.context.support.PropertySourcesPlaceholderConfigurer.lambda$processProperties$0(PropertySourcesPlaceholderConfigurer.java:191)
	at org.springframework.beans.factory.support.AbstractBeanFactory.resolveEmbeddedValue(AbstractBeanFactory.java:936)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1332)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408)
	... 29 common frames omitted
