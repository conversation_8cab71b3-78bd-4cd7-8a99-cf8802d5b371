server:
  port: 5051
  http:
    port: 5050

spring:
  datasource:
    dynamic: #使用dynamic-datasource，基于springboot的快速集成多数据源的启动器
      primary: syncDb #设置默认的数据源
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      #类或方法上使用 @DS("dameng") 注解切换数据源，注意需要在@Transactional事务注解前使用，当事务开启后无法切换数据源
      datasource:
        syncDb:
          type: com.alibaba.druid.pool.DruidDataSource
          driverClassName : com.mysql.cj.jdbc.Driver
          #url: jdbc:mysql://**************:13306/xz_tocc?allowPublicKeyRetrieval=true&useSSL=false&serverTimezone=Asia/Shanghai
          url: **********************************************************************************************************
          username: root
          password: root_YS123456
          lazy: true   #设置数据源懒加载
      druid:
        initialSize: 1   #初始化连接个数
        minIdle: 1       #最小空闲连接个数
        maxActive: 10    #最大连接个数
        maxWait: 60000    #获取连接时最大等待时间，单位毫秒。
        keepAlive: true
        timeBetweenEvictionRunsMillis: 60000  #配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        minEvictableIdleTimeMillis: 30000     #配置一个连接在池中最小生存的时间，单位是毫秒
        validationQuery: select 'x' FROM DUAL #用来检测连接是否有效的sql，要求是一个查询语句。
        testWhileIdle: true       #建议配置为true，不影响性能，并且保证安全性。如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效。
        testOnBorrow: true        #申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
        testOnReturn: false       #归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
        poolPreparedStatements: false #是否缓存preparedStatement，也就是PSCache。PSCache对支持游标的数据库性能提升巨大，比如说oracle。在mysql下建议关闭。
        maxPoolPreparedStatementPerConnectionSize: -1 #要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true。在Druid中，不会存在Oracle下PSCache占用内存过多的问题，可以把这个数值配置大一些，比如说100
        filters: stat,wall #通过别名的方式配置扩展插件，常用的插件有：监控统计用的filter:stat，日志用的filter:log4j，防御sql注入的filter:wall
        connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000 # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
        useGlobalDataSourceStat: false # 合并多个DruidDataSource的监控数据

  redis:
    host: 127.0.0.1
    port: 6379
    password: 
    timeout: 60000
  minio:
    host: http://**********:9500
    access-key: amplesky
    secret-key: amplesky123
  local-storage:
    root-path: ./storage


mybatis-plus:
  mapper-locations:
    - classpath*:/mappers/mysql/*.xml
    - classpath*:/mappers/mysql/**/*.xml
    - classpath*:/mappers/dameng/*.xml
  global-config:
    refresh: true
    db-config:
      #主键类型  0:"数据库ID自增", 1:"用户输入ID",2:"全局唯一ID (数字类型唯一ID)", 3:"唯一ID";
      id-type: AUTO
  configuration:
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl


system:
  version: 1.4.34
  redis: true #是否使用redis
  # 文件路径 示例（ Windows配置D:/xinzhou-tdss/uploadPath，Linux配置 /home/<USER>/uploadPath
  profile: /home/<USER>/uploadPath
  oss-path: /prod-api/xinzhou-toms/vm/oss/
  
  
#====================附件设置=======================
attachment:
   default-path: /home/<USER>/attachment/

alarm:
  supervision:
    enabled: false  # 设置为 false 不启用报警督办定时任务

sdk:
  api:
    base-url: http://111.53.42.6:20551/gateway  # s17媒体服务地址
  user-key: xinzhou_operation_monitoring                            # 从平台API用户管理获取
  sign-key: 75c27d667ebd4e5f80d9dec8210caa56                             # 向研发获取
  auth-url: http://111.53.42.6:20551/gateway/alpha3/api/v1/user/sign/auth  # 鉴权接口地址