server:
  port: 9091
  http:
    port: 9090
  servlet:
    context-path: /tocc
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain,application/javascript,text/css
    min-response-size: 2048  #大于2K则压缩
spring:
  cache:
     type: redis
  servlet:
    multipart:
      max-file-size: 30MB
  profiles:
    active: dev
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    deserialization:
      # 当实体中没有属性的setter方法时是否抛出异常
      FAIL_ON_UNKNOWN_PROPERTIES: false
      # 字符串转允许List，否则导致 @RequestBody List<T> list 类型无法解析
      ACCEPT_SINGLE_VALUE_AS_ARRAY: true
#  resources:
    # 自springboot 2.5.5之后，该属性已经被废弃，使用web.resources.static-locations代替
    static-locations: classpath:/static/,classpath:/META-INF/resources/,classpath:/META-INF/resources/webjars/,file:${gc.starter.file.basePath}
  # 静态资源配置
  mvc:
#    throw-exception-if-no-handler-found: true
    # 静态资源访问接口前缀
#    static-path-pattern: /static/**
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  web:
    resources:
      static-locations: classpath:/static/,classpath:/META-INF/resources/,classpath:/META-INF/resources/webjars/,file:${gc.starter.file.basePath}
  task:
    scheduling:
      pool:
        size: 10       # 核心线程数
      thread-name-prefix: custom-scheduler-  # 线程名称前缀

snowflake:
  worker-id: ${random.int(1,31)}
  datacenter-id: ${random.int(1,31)}

# 安全忽略url
secure:
  ignored:
    urls:
      - /api/login
      - /api/ssoAuth
      - /api/updatePassword
      - /api/logout
      - /api/app/v1/auth
      - /api/sync/syncData
      - /export
      - /api/getKaptchaCode
      - /api/toms/message/warnMsg/pushMsg
      - /api/files
      - /api/tact
      - /api/getMathKaptchaCode
      - /api/file
      - /vm/oss
      - /api/toms/video
      - /api/sync/all
system:
  version: 1.4.34


# springdoc-openapi项目配置
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: '领导驾驶舱接口'
      paths-to-match: '/**'
      packages-to-scan: com.yinshu.tocc.cockpit.controller

knife4j:
  setting:
    language: zh_cn
  basic:
    enable: true
    username: admin
    password: admin
  enable: true
