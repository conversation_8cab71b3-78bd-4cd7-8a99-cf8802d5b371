package com.yinshu.login;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yinshu.annotation.OperLog;
import com.yinshu.exception.RestfulAPIException;
import com.yinshu.jwt.JWTUtil;
import com.yinshu.sys.entity.SessionUser;
import com.yinshu.sys.entity.Setting;
import com.yinshu.sys.entity.User;
import com.yinshu.sys.manager.MenuManager;
import com.yinshu.sys.manager.SettingManager;
import com.yinshu.sys.manager.UserManager;
import com.yinshu.sys.security.SecurityUtils;
import com.yinshu.sys.service.UserService;
import com.yinshu.sys.utils.PasswordValidator;
import com.yinshu.utils.*;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.geotools.util.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.*;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@RestController
@RequestMapping("/api")
public class LoginController {

    public static final int KAPTCHA_EXPIRE_MINUTES = 5;
    /**
     * 验证码保存
     */
    private static Map<String, String> kaptchaMap = new HashMap<>();
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private MenuManager menuManager;
    @Autowired
    private Environment environment;
    @Autowired
    private SettingManager settingManager;
    @Autowired
    private AuthenticationManager authenticationManager;
    @Autowired
    private UserManager userManager;
    @Autowired
    private UserService userService;
    @Resource
    private RestTemplate restTemplate;

    @PostMapping("/login")
    @OperLog(operModul = "系统操作", operType = "登录")
    public ResultVO<?> login(@RequestBody Map<String, Object> params) {

        Map<String, Object> resultMap = new HashMap<>();

        try {
            String loginName = params.getOrDefault("username1", "").toString();
            if (StringUtils.isEmpty(loginName) && params.containsKey("username")) {
                loginName = params.get("username").toString();
            }

            String password = params.get("password").toString();
            password = EncodeUtils.decodeBase64(password); /**前端base64密码解密*/
            /** 验证码校验*/
            if (params.get("uuid") == null || params.get("kaptcha") == null) {
                throw new RuntimeException("验证码不能为空");
            }
            if (!kaptchaMap.get(params.get("uuid").toString()).equals(params.get("kaptcha"))) {
                throw new RuntimeException("验证码错误");
            }

            /** 构造了一个基于用户名和密码的认证请求，并通过AuthenticationManager进行用户认证。 */
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginName, password);
            Authentication authenticate = authenticationManager.authenticate(authenticationToken);

            if (Objects.isNull(authenticate)) {
                throw new RuntimeException("用户名或密码有误");
            }

            SessionUser sessionUser = (SessionUser) authenticate.getPrincipal();
            String redis = environment.getProperty("system.redis");
            if (redis.equals("true")) {
                redisCache.setCacheObject("login:" + sessionUser.getUser().getLoginName(), sessionUser);
            }
            // 7.28 修改 新增参数类型字段 登录用户只允许获取 用户级别的系统设置
            Setting settingMap = new Setting();
            settingMap.setSetType("1");
            List<Setting> sysSetting = settingManager.queryList(settingMap);
            sessionUser.setSysSettingMap(sysSetting);

            String token = JWTUtil.createToken(sessionUser.getUser().getId(), sessionUser.getUser().getLoginName());
            resultMap.put("token", token);
            resultMap.put("sessionUser", sessionUser);
            resultMap.put("menuTree", menuManager.filterJsonTreeList(sessionUser.getMenuList()));

            kaptchaMap.remove(params.get("uuid"));

        } catch (Exception e) {
            resultMap.put("code", "-1");
            resultMap.put("msg", e.getMessage());
        }

        return new ResultVO<>(resultMap);

    }

    @GetMapping("/getSessionInfo")
    @OperLog(operModul = "系统操作", operType = "获取用户信息")
    public ResultVO<?> getSessionInfo(HttpServletRequest request) {
        // 获取请求token
        String token = request.getHeader("token");
        if (StringUtils.isEmpty(token)) {
            return new ResultVO<>(null);
        }
        // 获取用户信息
        Object cacheObject = redisCache.getCacheObject("login:session:" + token);
        if (Objects.isNull(cacheObject)) {
            throw new RestfulAPIException("用户信息已过期");
        }
        return (ResultVO<?>) cacheObject;
    }

    @GetMapping("/logout")
    @OperLog(operModul = "系统操作", operType = "退出")
    public ResultVO<?> loginOut(HttpServletRequest request) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Object obj = authentication.getPrincipal();
        if (obj instanceof UserDetails) {
            SessionUser sessionUser = (SessionUser) obj;
            String userAccount = sessionUser.getUser().getLoginName();
            String redis = environment.getProperty("system.redis");
            if ("true".equals(redis)) {
                redisCache.deleteObject("login:" + userAccount);
                // 删除用户会话信息
                String token = request.getHeader("token");
                if (StringUtils.isNotEmpty(token)) {
                    redisCache.deleteObject("login:session:" + token);
                }
            }
        }
        SecurityContextHolder.clearContext();
        return new ResultVO<>(null);
    }

    /**
     * 获取验证码
     *
     * @param response
     * @throws IOException
     */
    @GetMapping("/getKaptchaCode")
    public ResultVO<?> getKaptchaCode(HttpServletResponse response) throws IOException {
        Map<String, Object> resultMap = new HashMap<>();
        response.setDateHeader("Expires", 0);// 禁止缓存
        response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate");
        response.addHeader("Cache-Control", "post-check=0, pre-check=0");
        response.setHeader("Pragma", "no-cache");
        response.setContentType("image/png");// 设置响应格式为png图片
        BufferedImage image = new BufferedImage(130, 49, BufferedImage.TYPE_INT_RGB);// 生成图片验证码
        String randomText = KaptchaUtils.drawRandomText(image, 130, 49);
//        // 存入redis
//        redisUtils.set(ConstantCode.KAPTCHA_KEY, randomText, ConstantCode.KAPTCHA_EXP_TIME);
//        ServletOutputStream out = response.getOutputStream();
//        ImageIO.write(image, "jpg", out);

        // 转换流信息写出
        FastByteArrayOutputStream os = new FastByteArrayOutputStream();
        ImageIO.write(image, "jpg", os);
        String uuid = UUIDGenerator.uuid();
        resultMap.put("uuid", uuid);
        resultMap.put("img", Base64.encodeBytes(os.toByteArray()));

        kaptchaMap.put(uuid, randomText);/**暂存，为登录校验*/

        os.flush();
        os.close();

        return new ResultVO<>(resultMap);
    }

    @PostMapping("/updatePassword")
    @OperLog(operModul = "用户管理", operType = "修改密码")
    public ResultVO<?> updatePassword(@RequestBody Map<String, String> resetPassword) {
        String newPassword = resetPassword.get("newPassword");
        /** 密码解密 */
        newPassword = EncodeUtils.decodeBase64(newPassword);

        String userName = resetPassword.get("userName");
        User user = userManager.getUserByLoginName(userName);
        if (user == null) {
            return new ResultVO<>(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()),
                    "用户不存在");
        }
        String oldPassword = user.getPassword();
        if (SecurityUtils.matchesPassword(newPassword, oldPassword)) {
            return new ResultVO<>(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()),
                    "新密码不能与旧密码相同");
        }
        /** 密码强度校验*/
        if (!PasswordValidator.isValid(newPassword)) {
            return new ResultVO<>(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()),
                    "密码强度校验不通过,请重新输入！");
        }
        newPassword = SecurityUtils.encryptPassword(newPassword);

        if (userManager.updatePassword(user.getId(), newPassword) > 0) {
            return new ResultVO<>(userName);
        }
        return new ResultVO<>(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()),
                "修改密码异常，请联系管理员");
    }

    /**
     * 查岗问题
     *
     * @param response
     * @return
     * @throws IOException
     */
    @GetMapping("/getMathKaptchaCode")
    public ResultVO<?> getMathKaptchaCode(HttpServletResponse response) throws IOException {
        Map<String, Object> resultMap = new HashMap<>();
        // 设置响应头禁止缓存
        response.setDateHeader("Expires", 0);
        response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate");
        response.addHeader("Cache-Control", "post-check=0, pre-check=0");
        response.setHeader("Pragma", "no-cache");
        response.setContentType("image/png");
        // 创建图片对象
        BufferedImage image = new BufferedImage(130, 49, BufferedImage.TYPE_INT_RGB);
        // 生成数学题
        int a = (int) (Math.random() * 10);
        int b = (int) (Math.random() * 10);
        String question = a + " + " + b + " = ";//问题
        //String answer = String.valueOf(a + b);//答案
        // 绘制问题到图片上
        KaptchaUtils.drawText(image, question, 130, 49);
        // 转换流信息写出
        FastByteArrayOutputStream os = new FastByteArrayOutputStream();
        ImageIO.write(image, "jpg", os);
        String uuid = UUIDGenerator.uuid();
        resultMap.put("uuid", uuid);
        resultMap.put("img", Base64.encodeBytes(os.toByteArray()));
        resultMap.put("question", question);
        // 将答案存入 Redis，设置过期时间
//        redisCache.setCacheObject("kaptcha:" + uuid, answer, Long.parseLong("5"), TimeUnit.MINUTES);
        os.flush();
        os.close();

        return new ResultVO<>(resultMap);
    }

    /***
     * 单点登录进来的双向认证接口
     * @param model
     * @return
     */
    @PostMapping("/ssoAuth")
    public ResultVO<?> ssoAuth(@RequestBody TokenAuthDTO model) {
        String prefix = model.getPrefix();
        Setting settingAuthCode = settingManager.getByCode(prefix + "-portal-authCode");
        Setting settingAuthUrl = settingManager.getByCode("portal-authUrl");
        try {
        	String userName = getLoginUserByCodeAndTicket(model.getTicket(), settingAuthCode.getParmValue(), settingAuthUrl.getParmValue());
            return ResultVO.suc(loginByUserName(userName));
        } catch (Exception e) {
        	Map<String, Object> resultMap = new HashMap<>();
        	resultMap.put("errorMsg", "「统一门户」" + e.getMessage());
        	return ResultVO.suc(resultMap);
		}
    }

    /**
     * @param userName 用户名
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @description //通过用户名登录
     * @date 2025/7/30 17:40
     **/
    private Map<String, Object> loginByUserName(String userName) {
        User ownerUser = userService.getOne(new LambdaQueryWrapper<User>()
                .eq(User::getLoginName, userName));
        if (ownerUser == null) {
            throw new RestfulAPIException("用户不存在");
        }
        String loginName = ownerUser.getLoginName();
        String password = ownerUser.getPassword();
        UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginName, password);
        Authentication authenticate = authenticationManager.authenticate(authenticationToken);
        if (Objects.isNull(authenticate)) {
            throw new RuntimeException("用户名或密码有误");
        }

        SessionUser sessionUser = (SessionUser) authenticate.getPrincipal();
        String redis = environment.getProperty("system.redis");
        if ("true".equals(redis)) {
            redisCache.setCacheObject("login:" + sessionUser.getUser().getLoginName(), sessionUser);
        }
        List<Setting> sysSetting = settingManager.queryList(new Setting());
        sessionUser.setSysSettingMap(sysSetting);

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("token", JWTUtil.createToken(sessionUser.getUser().getId(), sessionUser.getUser().getLoginName()));
        resultMap.put("sessionUser", sessionUser);
        resultMap.put("menuTree", menuManager.filterJsonTreeList(sessionUser.getMenuList()));
        return resultMap;
    }

    /*
     * <AUTHOR>
     * @description //TODO 获取登录用户信息
     * @date 2025/7/30 17:12
     * @param ticket 票据信息
     * @param authCode 应用授权码
     * @param authUrl 请求地址
     * @return java.util.Map
     **/
    public String getLoginUserByCodeAndTicket(String ticket, String authCode, String authUrl) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("authCode", authCode);
        requestBody.put("ticket", ticket);
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

        ResponseEntity<String> response = restTemplate.postForEntity(authUrl, requestEntity, String.class);
        JSONObject respMap = JSONObject.parseObject(response.getBody());
        String codeObj = respMap.getString("code");
        if ("0".equals(codeObj)) {
            JSONObject userMap = respMap.getJSONObject("data");
            return userMap.get("loginName").toString();
        } else {
            throw new RuntimeException("" + respMap.getOrDefault("msg", "未知错误"));
        }
    }

    @GetMapping("/test")
//	@OperLog(operModul="系统操作", operType="退出")
    public ResultVO<?> test(HttpServletRequest request) {
        System.out.println("-----------test---------");
        return new ResultVO<>(null);
    }

    /**
     * 握手认证请求体
     */
    @Data
    public static class TokenAuthDTO {
        /**
         * 认证token，此处用code 命名
         */
        private String code;
        /**
         * 时间戳 String.valueOf(System.currentTimeMillis())
         */
        private String timestamp;
        /**
         * 认证令牌
         */
        private String ticket;
        /**
         * 系统前缀
         */
        private String prefix;
    }

}
