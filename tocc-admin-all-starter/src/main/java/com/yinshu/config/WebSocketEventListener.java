package com.yinshu.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.messaging.AbstractSubProtocolEvent;
import org.springframework.web.socket.messaging.SessionConnectedEvent;
import org.springframework.web.socket.messaging.SessionDisconnectEvent;

@Component
public class WebSocketEventListener {
    protected final static Logger logger = LoggerFactory.getLogger(WebSocketEventListener.class);

    /**
     * 监听Websocket连接建立
     * @param event
     */
    @EventListener
    public void handleWebSocketConnectListener(SessionConnectedEvent event) {
        //logger.info("Websocket----------connection----userName：{}", getUserName(event));
    }

    /**
     * 监听Websocket连接关闭
     * @param event
     */
    @EventListener
    public void handleWebSocketDisconnectListener(SessionDisconnectEvent event) {
        //logger.info("Websocket----------closed----userName：{}", getUserName(event));
    }

    private String getUserName(AbstractSubProtocolEvent event){
        if(event != null && event.getUser() != null){
            return event.getUser().getName();
        }
        return "";
    }

}
