package com.yinshu.sync.all.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.sync.all.entity.PortalUnit;
import com.yinshu.sync.all.enums.SyncDataFromEnum;
import com.yinshu.sync.all.mapper.PortalUnitMapper;
import com.yinshu.sync.all.service.AllSyncUnitService;
import com.yinshu.sys.entity.Unit;
import com.yinshu.sys.service.UnitService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AllSyncRoleServiceImpl
 * @description TODO 单位同步
 * @date 2025/7/25 14:52
 **/
@Service
public class AllSyncUnitServiceImpl extends ServiceImpl<PortalUnitMapper, PortalUnit> implements AllSyncUnitService {

    @Resource
    UnitService unitService;

    @Resource
    PortalUnitMapper portalUnitMapper;

    @Override
    public Integer syncAll() {
        // 删除主库数据
        unitService.remove(new LambdaQueryWrapper<Unit>()
                .eq(Unit::getDataSource, SyncDataFromEnum.INTERIOR_PORTAL_USER.getType()));
        // 获取来源库数据
        List<PortalUnit> list = portalUnitMapper.selectList(new LambdaQueryWrapper<PortalUnit>());
        // 新增到主库中
        boolean saved = unitService.saveOrUpdateBatch(list.stream().map(val -> {
            Unit unit = new Unit();
            BeanUtil.copyProperties(val, unit);
            unit.setDataSource(SyncDataFromEnum.INTERIOR_PORTAL_USER.getType());
            return unit;
        }).collect(Collectors.toList()));
        return saved ? list.size() : 0;
    }

    @Override
    public Integer syncAll(List<PortalUnit> list) {
        // 删除主库数据
        unitService.remove(new LambdaQueryWrapper<Unit>()
                .eq(Unit::getDataSource, SyncDataFromEnum.INTERIOR_PORTAL_USER.getType()));
        // 新增到主库中
        boolean saved = unitService.saveOrUpdateBatch(list.stream().map(val -> {
            Unit unit = new Unit();
            BeanUtil.copyProperties(val, unit);
            unit.setDataSource(SyncDataFromEnum.INTERIOR_PORTAL_USER.getType());
            return unit;
        }).collect(Collectors.toList()));
        return saved ? list.size() : 0;
    }
}
