package com.yinshu.sync.all.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.sync.all.entity.PortalUser;
import com.yinshu.sync.all.enums.SyncDataFromEnum;
import com.yinshu.sync.all.mapper.PortalUserMapper;
import com.yinshu.sync.all.service.AllSyncUserService;
import com.yinshu.sys.dao.UserDao;
import com.yinshu.sys.entity.User;
import com.yinshu.sys.service.UserService;
import org.apache.ibatis.executor.BatchResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AllSyncRoleServiceImpl
 * @description TODO 用户同步
 * @date 2025/7/25 14:52
 **/
@Service
public class AllSyncUserServiceImpl implements AllSyncUserService {

    @Resource
    UserService userService;

    @Resource
    PortalUserMapper portalUserMapper;

    @Override
    public Integer syncAll() {
        // 删除主库数据
        userService.remove(new LambdaQueryWrapper<User>()
                .eq(User::getDataSource, SyncDataFromEnum.INTERIOR_PORTAL_USER.getType()));
        // 获取来源库数据
        List<PortalUser> list = portalUserMapper.selectList(new LambdaQueryWrapper<PortalUser>());
        // 新增到主库中
        List<User> users = list.stream().map(val -> {
            User user = new User();
            BeanUtil.copyProperties(val, user);
            user.setDataSource(SyncDataFromEnum.INTERIOR_PORTAL_USER.getType());
            return user;
        }).collect(Collectors.toList());
        boolean saved = userService.saveOrUpdateBatch(users);
        return  saved ? list.size() : 0;
    }

    @Override
    public Integer syncAll(List<PortalUser> list) {
        // 删除主库数据
        userService.remove(new LambdaQueryWrapper<User>()
                .eq(User::getDataSource, SyncDataFromEnum.INTERIOR_PORTAL_USER.getType()));
        // 新增到主库中
        boolean saved = userService.saveOrUpdateBatch(list.stream().map(val -> {
            User user = new User();
            BeanUtil.copyProperties(val, user);
            user.setDataSource(SyncDataFromEnum.INTERIOR_PORTAL_USER.getType());
            return user;
        }).collect(Collectors.toList()));
        return saved ? list.size() : 0;
    }
}
