package com.yinshu.sync.all.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 门户角色实体
 *
 * <AUTHOR>
 */
@Data
@TableName("portal_role")
public class PortalRole implements Serializable {
    /** 角色ID */
    @TableId(type = IdType.INPUT)
    private String id;
    /** 角色代码 */
    private String roleCode;
    /** 角色名称 */
    private String roleName;
    /** 角色描述 */
    private String roleDescription;
    /** 创建时间 */
    private Date createTime;
    /** 更新时间 */
    private Date updateTime;
    /** 所属机构ID */
    private String unitid;
} 