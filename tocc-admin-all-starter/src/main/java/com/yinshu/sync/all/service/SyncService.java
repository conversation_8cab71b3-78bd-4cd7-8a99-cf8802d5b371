package com.yinshu.sync.all.service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SyncService
 * @description TODO 同步服务
 * @date 2025/7/25 14:59
 **/
public interface SyncService<T> {
    /*
     * <AUTHOR>
     * @description //TODO 同步所有数据
     * @date 2025/7/25 15:00
     * @return java.lang.Long
     **/
    Integer syncAll();

    /*
     * <AUTHOR>
     * @description //TODO 同步所有数据
     * @date 2025/7/25 15:00
     * @return java.lang.Long
     **/
    Integer syncAll(List<T> list);
}
