package com.yinshu.sync.all.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.sync.all.entity.PortalRole;
import com.yinshu.sync.all.enums.SyncDataFromEnum;
import com.yinshu.sync.all.mapper.PortalRoleMapper;
import com.yinshu.sync.all.service.AllSyncRoleService;
import com.yinshu.sys.entity.Role;
import com.yinshu.sys.service.RoleService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AllSyncRoleServiceImpl
 * @description TODO 角色同步
 * @date 2025/7/25 14:52
 **/
@Service
public class AllSyncRoleServiceImpl extends ServiceImpl<PortalRoleMapper, PortalRole> implements AllSyncRoleService {

    @Resource
    RoleService roleService;

    @Resource
    PortalRoleMapper portalRoleMapper;

    @Override
    public Integer syncAll() {
        // 删除主库数据
        roleService.remove(new LambdaQueryWrapper<Role>()
                .eq(Role::getDataSource, SyncDataFromEnum.INTERIOR_PORTAL_USER.getType()));
        // 获取来源库数据
        List<PortalRole> list = portalRoleMapper.selectList(new LambdaQueryWrapper<PortalRole>());
        // 新增到主库中
        boolean saved = roleService.saveOrUpdateBatch(list.stream().map(val -> {
            Role role = new Role();
            BeanUtil.copyProperties(val, role);
            role.setDataSource(SyncDataFromEnum.INTERIOR_PORTAL_USER.getType());
            return role;
        }).collect(Collectors.toList()));
        return saved ? list.size() : 0;
    }

    @Override
    public Integer syncAll(List<PortalRole> list) {
        // 删除主库数据
        roleService.remove(new LambdaQueryWrapper<Role>()
                .eq(Role::getDataSource, SyncDataFromEnum.INTERIOR_PORTAL_USER.getType()));
        // 新增到主库中
        boolean saved = roleService.saveOrUpdateBatch(list.stream().map(val -> {
            Role role = new Role();
            BeanUtil.copyProperties(val, role);
            role.setDataSource(SyncDataFromEnum.INTERIOR_PORTAL_USER.getType());
            return role;
        }).collect(Collectors.toList()));
        return saved ? list.size() : 0;
    }
}
