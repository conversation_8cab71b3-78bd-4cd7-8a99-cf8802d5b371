package com.yinshu.sync.all.controller;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.sync.all.entity.PortalRole;
import com.yinshu.sync.all.entity.PortalUnit;
import com.yinshu.sync.all.entity.PortalUser;
import com.yinshu.sync.all.service.AllSyncRoleService;
import com.yinshu.sync.all.service.AllSyncUnitService;
import com.yinshu.sync.all.service.AllSyncUserService;
import com.yinshu.sys.entity.Setting;
import com.yinshu.sys.manager.SettingManager;
import com.yinshu.utils.Md5Utils;
import com.yinshu.utils.ResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AllSyncControoler
 * @description TODO 全量同步接口
 * @date 2025/7/25 14:58
 **/
@RestController
@RequestMapping("api/sync/all")
public class AllSyncController {

    private static final Logger logger = LoggerFactory.getLogger(AllSyncController.class);

    @Resource
    AllSyncRoleService roleService;

    @Resource
    AllSyncUserService userService;

    @Resource
    AllSyncUnitService unitService;
    @Resource
    RestTemplate restTemplate;

    @Autowired
    private SettingManager settingManager;

    @PostMapping("/syncRole")
    public ResultVO<Integer> syncRole() {
        return ResultVO.suc(roleService.syncAll());
    }

    @PostMapping("/syncUnit")
    public ResultVO<Integer> syncUnit() {
        return ResultVO.suc(unitService.syncAll());
    }

    @PostMapping("/syncUser")
    public ResultVO<Integer> syncUser() {
        return ResultVO.suc(userService.syncAll());
    }

    /*
     * <AUTHOR>
     * @description //TODO api同步用户接口
     * @date 2025/7/29 11:50
     * dev；http://localhost:9081/portal/api/portal/application/
     * test：http://127.0.0.1:5041/portal/api/portal/application/
     * @param suffix 请求地址后缀
     * @param body 请求参数
     * @param clazz 返回数据类型
     * @return T 返回数据
     **/
    public <T> T postJson(String suffix, Object body, Class<T> clazz) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> request = new HttpEntity<>(body, headers);
        Setting portalBaseUrl = settingManager.getByCode("portal-base-url");
        return restTemplate.postForObject(portalBaseUrl.getParmValue() + suffix, request, clazz);
    }

    /**
     * 构建认证请求体
     *
     * @return 包含认证信息的HashMap
     */
    private HashMap<String, Object> buildAuthBody() {
        Setting settingAuthCode = settingManager.getByCode("portal-authCode");
        Setting settingAuthPsw = settingManager.getByCode("portal-authPsw");
        // 获取当前时间戳
        String timestamp = String.valueOf(System.currentTimeMillis());
        // 认证ticket
        String authWayTicket = Md5Utils.authSyncDataCode(settingAuthCode.getParmValue(), settingAuthPsw.getParmValue(), timestamp);
        HashMap<String, Object> body = new HashMap<String, Object>() {{
            put("authWayTicket", authWayTicket);
            put("authCode", settingAuthCode.getParmValue());
            put("timestamp", timestamp);
        }};
        return body;
    }

    @PostMapping("/apiSyncUser")
    public ResultVO<Integer> apiSyncUser() {
        HashMap<String, Object> body = buildAuthBody();
        String result = postJson("getFullDoseUserList", body, String.class);
        ResultVO resultVO = JSONObject.parseObject(result, ResultVO.class);
        logger.info("getFullDoseUserListResult:{},{}", result, resultVO);
        if (resultVO.getData() != null && "0".equals(resultVO.getCode())) {
            String json = JSONObject.toJSONString(resultVO.getData());
            List<PortalUser> portalUsers = JSONObject.parseArray(json, PortalUser.class);
            return ResultVO.suc(userService.syncAll(portalUsers));
        }
        return ResultVO.suc(0);
    }

    @PostMapping("/apiSyncUnit")
    public ResultVO<Integer> apiSyncUnit() {
        HashMap<String, Object> body = buildAuthBody();
        String result = postJson( "getFullDoseUnitList", body, String.class);
        ResultVO resultVO = JSONObject.parseObject(result, ResultVO.class);
        logger.info("getFullDoseUnitListResult:{},{}", result, resultVO);
        if (resultVO.getData() != null && "0".equals(resultVO.getCode())) {
            String json = JSONObject.toJSONString(resultVO.getData());
            List<PortalUnit> portalUnits = JSONObject.parseArray(json, PortalUnit.class);
            return ResultVO.suc(unitService.syncAll(portalUnits));
        }
        return ResultVO.suc(0);
    }

    @PostMapping("/apiSyncRole")
    public ResultVO<Integer> apiSyncRole() {
        HashMap<String, Object> body = buildAuthBody();
        String result = postJson("getFullDoseRoleList", body, String.class);
        ResultVO resultVO = JSONObject.parseObject(result, ResultVO.class);
        if (resultVO.getData() != null && "0".equals(resultVO.getCode())) {
            String json = JSONObject.toJSONString(resultVO.getData());
            List<PortalRole> portalUsers = JSONObject.parseArray(json, PortalRole.class);
            return ResultVO.suc(roleService.syncAll(portalUsers));
        }
        return ResultVO.suc(0);
    }
}
