package com.yinshu.sync.strategy;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yinshu.enums.SyncOperationTypeEnum;
import com.yinshu.sync.SyncDataController.PortalSyncDataDTO;
import com.yinshu.sys.entity.Role;
import com.yinshu.sys.service.RoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("ROLE")
public class RoleSyncDataStrategy implements SyncDataStrategy {

    @Autowired
    private RoleService roleManager;

    @Override
    public void execute(PortalSyncDataDTO dto) {
        SyncOperationTypeEnum typeEnum = SyncOperationTypeEnum.fromCode(dto.getOperationType());
        handleByEnum(typeEnum, dto);
    }

    private void handleByEnum(SyncOperationTypeEnum typeEnum, PortalSyncDataDTO dto) {
        switch (typeEnum) {
            case ADD:
                addRole(dto);
                break;
            case UPDATE:
                updateRole(dto);
                break;
            case DELETE:
                deleteRole(dto);
                break;
            case DELETES:
                batchDeleteRole(dto);
                break;
            default:
                throw new IllegalArgumentException("不支持的操作类型: " + typeEnum.getCode());
        }
    }

    private void addRole(PortalSyncDataDTO dto) {
        System.out.println("新增角色: " + dto.getEntityContent());
        // 创建ObjectMapper实例
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            Role entity = objectMapper.readValue(dto.getEntityContent(), Role.class);
            entity.setDataSource(dto.getDataFrom());
            roleManager.save(entity);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

    }

    private void updateRole(PortalSyncDataDTO dto) {
        System.out.println("修改角色: " + dto.getEntityContent());
        // 创建ObjectMapper实例
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            Role entity = objectMapper.readValue(dto.getEntityContent(), Role.class);
            roleManager.updateById(entity);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    private void deleteRole(PortalSyncDataDTO dto) {
        System.out.println("删除角色: " + dto.getEntityContent());
        roleManager.removeById(dto.getEntityContent());
    }

    private void batchDeleteRole(PortalSyncDataDTO dto) {
        System.out.println("批量删除角色: " + dto.getEntityContent());
        ObjectMapper objectMapper = new ObjectMapper();
        List<String> ids = null;
        try {
            ids = objectMapper.readValue(dto.getEntityContent(), List.class);
            roleManager.removeByIds(ids);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

    }
} 