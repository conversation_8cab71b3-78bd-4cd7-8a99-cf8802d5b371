package com.yinshu.sync.strategy;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yinshu.enums.SyncOperationTypeEnum;
import com.yinshu.sync.SyncDataController.PortalSyncDataDTO;
import com.yinshu.sys.entity.ResetPassword;
import com.yinshu.sys.entity.User;
import com.yinshu.sys.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("USER")
public class UserSyncDataStrategy implements SyncDataStrategy {

    @Autowired
    private UserService userManager;

    @Override
    public void execute(PortalSyncDataDTO dto) {
        SyncOperationTypeEnum typeEnum = SyncOperationTypeEnum.fromCode(dto.getOperationType());
        handleByEnum(typeEnum, dto);
    }

    private void handleByEnum(SyncOperationTypeEnum typeEnum, PortalSyncDataDTO dto) {
        switch (typeEnum) {
            case ADD:
                addUser(dto);
                break;
            case UPDATE:
                updateUser(dto);
                break;
            case DELETE:
                deleteUser(dto);
                break;
            case DELETES:
                batchDeleteUser(dto);
                break;
            case UPDATEPSW:
                updatePswUser(dto);
                break;
            case RESETPSW:
                resetPswUser(dto);
                break;
            default:
                throw new IllegalArgumentException("不支持的操作类型: " + typeEnum.getCode());
        }
    }

    private void addUser(PortalSyncDataDTO dto) {
        System.out.println("新增用户: " + dto.getEntityContent());
        // 创建ObjectMapper实例
        ObjectMapper objectMapper = new ObjectMapper();
        User entity = null;
        try {
            entity = objectMapper.readValue(dto.getEntityContent(), User.class);
            entity.setDataSource(dto.getDataFrom());
            userManager.save(entity);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }


    }

    private void updateUser(PortalSyncDataDTO dto) {
        System.out.println("修改用户: " + dto.getEntityContent());
        ObjectMapper objectMapper = new ObjectMapper();
        User entity = null;
        try {
            entity = objectMapper.readValue(dto.getEntityContent(), User.class);
            entity.setPassword(null);
            userManager.updateById(entity);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }


    }

    private void deleteUser(PortalSyncDataDTO dto) {

        System.out.println("删除用户: " + dto.getEntityContent());
        userManager.removeById(dto.getEntityContent());

    }

    private void batchDeleteUser(PortalSyncDataDTO dto) {
        System.out.println("批量删除用户: " + dto.getEntityContent());
        ObjectMapper objectMapper = new ObjectMapper();
        List<String> ids = null;
        try {
            ids = objectMapper.readValue(dto.getEntityContent(), List.class);
            userManager.removeByIds(ids);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    private void updatePswUser(PortalSyncDataDTO dto) {

        System.out.println("修改用户密码: " + dto.getEntityContent());
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            ResetPassword resetPassword = objectMapper.readValue(dto.getEntityContent(), ResetPassword.class);
            userManager.updatePassword(resetPassword.getId(), resetPassword.getNewPassword());

        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }


    }

    private void resetPswUser(PortalSyncDataDTO dto) {
        System.out.println("重置用户密码: " + dto.getEntityContent());
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            ResetPassword resetPassword = objectMapper.readValue(dto.getEntityContent(), ResetPassword.class);
            userManager.updatePassword(resetPassword.getId(), resetPassword.getNewPassword());

        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }
} 