package com.yinshu.sync.strategy;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yinshu.enums.SyncOperationTypeEnum;
import com.yinshu.sync.SyncDataController.PortalSyncDataDTO;
import com.yinshu.sys.entity.Unit;
import com.yinshu.sys.service.UnitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("ORG")
public class OrgSyncDataStrategy implements SyncDataStrategy {

    @Autowired
    private UnitService unitManager;


    @Override
    public void execute(PortalSyncDataDTO dto) {
        SyncOperationTypeEnum typeEnum = SyncOperationTypeEnum.fromCode(dto.getOperationType());
        handleByEnum(typeEnum, dto);
    }

    private void handleByEnum(SyncOperationTypeEnum typeEnum, PortalSyncDataDTO dto) {
        switch (typeEnum) {
            case ADD:
                addOrg(dto);
                break;
            case UPDATE:
                updateOrg(dto);
                break;
            case DELETE:
                deleteOrg(dto);
                break;
            case DELETES:
                batchDeleteOrg(dto);
                break;
            default:
                throw new IllegalArgumentException("不支持的操作类型: " + typeEnum.getCode());
        }
    }

    private void addOrg(PortalSyncDataDTO dto) {
        System.out.println("新增机构: " + dto.getEntityContent());
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            Unit entity = objectMapper.readValue(dto.getEntityContent(), Unit.class);
            entity.setDataSource(dto.getDataFrom());
            unitManager.save(entity);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }


    }

    private void updateOrg(PortalSyncDataDTO dto) {
        System.out.println("修改机构: " + dto.getEntityContent());
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            Unit entity = objectMapper.readValue(dto.getEntityContent(), Unit.class);
            unitManager.updateById(entity);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    private void deleteOrg(PortalSyncDataDTO dto) {
        System.out.println("删除机构: " + dto.getEntityContent());
        unitManager.removeById(dto.getEntityContent());
    }

    private void batchDeleteOrg(PortalSyncDataDTO dto) {
        System.out.println("批量删除机构: " + dto.getEntityContent());
        ObjectMapper objectMapper = new ObjectMapper();
        List<String> ids = null;
        try {
            ids = objectMapper.readValue(dto.getEntityContent(), List.class);
            unitManager.removeByIds(ids);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

    }
} 