package com.yinshu.sync.strategy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class SyncDataStrategyFactory {
    private final Map<String, SyncDataStrategy> strategyMap;

    @Autowired
    public SyncDataStrategyFactory(Map<String, SyncDataStrategy> strategyMap) {
        this.strategyMap = strategyMap;
    }

    public SyncDataStrategy getStrategy(String operationName) {
        SyncDataStrategy strategy = strategyMap.get(operationName);
        if (strategy == null) {
            throw new IllegalArgumentException("未找到对应的同步策略: " + operationName);
        }
        return strategy;
    }
} 