package com.yinshu.sync;

import com.yinshu.sync.all.enums.SyncDataFromEnum;
import com.yinshu.sync.strategy.SyncDataStrategyFactory;
import com.yinshu.utils.ResultVO;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/****
 * 外部推送过来的信息，客户端接收同步到本系统
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/sync")
public class SyncDataController {

    @Autowired
    private SyncDataStrategyFactory syncDataStrategyFactory;

    /***
     * 数据同步
     * @return
     */
    @PostMapping("/syncData")
    public ResultVO syncData(@RequestBody PortalSyncDataDTO model) {
        model.setDataFrom(SyncDataFromEnum.INTERIOR_PORTAL_USER.getType());
        // 根据operationName选择策略
        syncDataStrategyFactory.getStrategy(model.getOperationName()).execute(model);
        return ResultVO.suc("同步成功");
    }

    /***
     * 数据同步请求DTO
     */
    @Data
    public static class PortalSyncDataDTO {
        /**
         * 操作类型（新增/修改/删除）
         */
        private String operationType;
        /**
         * 操作名称
         */
        private String operationName;
        /**
         * 实体内容（JSON等）
         */
        private String entityContent;
        /**
         * 数据来源
         **/
        private Integer dataFrom;
    }

}
