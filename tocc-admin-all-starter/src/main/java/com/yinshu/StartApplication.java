package com.yinshu;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;


/**
 * @program: spring-boot
 * @description: 启动类
 * @author:
 **/
@SpringBootApplication
@EnableCaching
@EnableAsync
@EnableScheduling
@MapperScan({"com.yinshu.sys.dao", "com.yinshu.tdss.dao", "com.yinshu.mcu.dao", "com.yinshu.tcps.dao", "com.yinshu.tiss.dao", "com.yinshu.portal.dao", "com.yinshu.sync.all.mapper"
        , "com.yinshu.tact.dao"})
public class StartApplication {

    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplication(StartApplication.class);
        springApplication.run(args);
    }
}
