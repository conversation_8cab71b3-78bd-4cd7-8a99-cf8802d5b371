<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
         
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.yinshu.tocc</groupId>
		<artifactId>project</artifactId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>
	<artifactId>tocc-admin-all-starter</artifactId>
	<!-- <packaging>war</packaging> -->
	<packaging>jar</packaging>
	<version>1.0</version>

	<dependencies>
		<!--在建交通工程监管系统-->
		<dependency>
			<groupId>com.yinshu.tcps</groupId>
			<artifactId>xinzhou-tcps</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		<!--运行监测系统-->
		<dependency>
			<groupId>com.yinshu.toms</groupId>
			<artifactId>xinzhou-toms</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		<!--辅助决策系统-->
		<dependency>
			<groupId>com.yinshu.tdss</groupId>
			<artifactId>xinzhou-tdss</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		<!--	行业监管系统	-->
		<dependency>
			<groupId>com.yinshu.tiss</groupId>
			<artifactId>xinzhou-tiss</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>

<!--			公用监管系统-->
		<dependency>
			<groupId>com.yinshu.tact</groupId>
			<artifactId>xinzhou-tact</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>

		<!-- <dependency>
			<groupId>com.yinshu.portal</groupId>
			<artifactId>yinshu-portal</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency> -->


		<!--导出bug-->
		<!-- <dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.15.1</version>
		</dependency> -->
		<!-- 将内嵌 Tomcat 改为 provided -->
<!--		<dependency>-->
<!--			<groupId>org.springframework.boot</groupId>-->
<!--			<artifactId>spring-boot-starter-tomcat</artifactId>-->
<!--			<version>${spring-boot.version}</version>-->
<!--			<scope>provided</scope>-->
<!--		</dependency>-->

<!-- 		<dependency>
            <groupId>com.yinshu</groupId>
            <artifactId>yinshu-activiti</artifactId>
            <version>1.0</version>
        </dependency>
		<dependency>
			<groupId>com.yinshu</groupId>
			<artifactId>yinshu-dataroom</artifactId>
			<version>1.0</version>
		</dependency> -->

	</dependencies>

	<build>
        <plugins>
            <plugin>
                <!--打包为jar包插件-->
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.4.3</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>