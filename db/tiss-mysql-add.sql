
DROP TABLE IF EXISTS `tiss_strategy_config`;
CREATE TABLE `tiss_strategy_config` (
                                        `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                        `type` tinyint NOT NULL COMMENT '类型(1:区县 2:运营商 3:企业)',
                                        `indicator` varchar(100) NOT NULL COMMENT '指标项',
                                        `weight` tinyint DEFAULT NULL COMMENT '权重',
                                        `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='策略配置表';

DROP TABLE IF EXISTS `tiss_score_config`;
CREATE TABLE tiss_score_config (
                                   id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
                                   indicator_id INT COMMENT '指标ID',
                                   rating_level VARCHAR(50) COMMENT '评分等级说明',
                                   score INT COMMENT '评分',
                                   left_rule VARCHAR(10) COMMENT '左边规则(gt:大于,gte:大于等于,lt:小于,lte:小于等于,eq:等于,neq:不等于)',
                                   left_rule_value INT COMMENT '左边规则值',
                                   right_rule VARCHAR(10) COMMENT '右边规则(gt:大于,gte:大于等于,lt:小于,lte:小于等于,eq:等于,neq:不等于)',
                                   right_rule_value INT COMMENT '右边规则值',
                                   create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评分配置表';