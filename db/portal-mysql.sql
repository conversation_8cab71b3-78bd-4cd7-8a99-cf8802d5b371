
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;


DROP TABLE IF EXISTS `portal_app_ticket`;
CREATE TABLE `portal_app_ticket` (
                                     `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                     `ticket` text COMMENT '令牌',
                                     `app_id` bigint unsigned DEFAULT NULL COMMENT '应用ID',
                                     `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-未使用 1-已使用',
                                     `use_time` datetime DEFAULT NULL COMMENT '使用时间',
                                     `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `creator` varchar(50) DEFAULT NULL COMMENT '创建人',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='应用令牌表';


DROP TABLE IF EXISTS `portal_application`;
CREATE TABLE `portal_application` (
                                      `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                      `app_name` varchar(100) NOT NULL COMMENT '门户子系统名称',
                                      `app_icon` varchar(255) DEFAULT NULL COMMENT '应用图标URL',
                                      `auth_code` varchar(64) DEFAULT NULL COMMENT 'API授权码',
                                      `auth_psw` varchar(128) DEFAULT NULL COMMENT 'API授权密码',
                                      `app_home_url` varchar(1024) DEFAULT NULL COMMENT '子系统路径',
                                      `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-禁用 1-启用',
                                      `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                      `creator` varchar(50) DEFAULT NULL COMMENT '创建人',
                                      `updater` varchar(50) DEFAULT NULL COMMENT '更新人',
                                      `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                                      `sort` int DEFAULT NULL COMMENT '排序字段',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='应用表';


BEGIN;
INSERT INTO `portal_application` (`id`, `app_name`, `app_icon`, `auth_code`, `auth_psw`, `app_home_url`, `status`, `create_time`, `update_time`, `creator`, `updater`, `remark`) VALUES (3, '3', '123', '87d193cb2df64e6f', '5e1a17dd71e347998b49a7f7', 'http://127.0.0.1:8080/#/transfer', 1, '2025-07-16 09:41:33', '2025-07-17 17:26:01', NULL, '1', '这是交通辅助决策测试');
INSERT INTO `portal_application` (`id`, `app_name`, `app_icon`, `auth_code`, `auth_psw`, `app_home_url`, `status`, `create_time`, `update_time`, `creator`, `updater`, `remark`) VALUES (4, '6', '123', '362480845d484b2e', 'b9337398a7034eff921258b8', 'http://127.0.0.1:8000/home/<USER>', 1, '2025-07-16 10:29:28', '2025-07-18 14:20:20', '1', '1', '这是测试应用00002123');
COMMIT;


DROP TABLE IF EXISTS `portal_menu`;
CREATE TABLE `portal_menu` (
                               `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                               `parent_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '父级菜单',
                               `menu_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '菜单名称',
                               `menu_url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '路径',
                               `component` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件',
                               `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '权限标识',
                               `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '状态',
                               `is_visible` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否可视(1:可见 2: 不可见)',
                               `menu_icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图标',
                               `menu_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '菜单类型',
                               `menu_sort` int DEFAULT NULL COMMENT '排序',
                               `lang_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC;


BEGIN;
INSERT INTO `portal_menu` (`id`, `parent_id`, `menu_name`, `menu_url`, `component`, `perms`, `status`, `is_visible`, `menu_icon`, `menu_type`, `menu_sort`, `lang_key`) VALUES ('2b1e1e3051a74f7ba4fec7770653d66c', 'a86af28e9ed04faba6e378ac09308a20', '字典管理', '/sys/dictionary', '', NULL, '1', '1', 'Memo', '1', 45, NULL);
INSERT INTO `portal_menu` (`id`, `parent_id`, `menu_name`, `menu_url`, `component`, `perms`, `status`, `is_visible`, `menu_icon`, `menu_type`, `menu_sort`, `lang_key`) VALUES ('396c09ae850b4a61b68de5ced07a3120', 'a86af28e9ed04faba6e378ac09308a20', '角色管理', '/sys/role', '', NULL, '1', '1', 'Avatar', '1', 30, NULL);
INSERT INTO `portal_menu` (`id`, `parent_id`, `menu_name`, `menu_url`, `component`, `perms`, `status`, `is_visible`, `menu_icon`, `menu_type`, `menu_sort`, `lang_key`) VALUES ('4a624510d78f4a1d88d259fc81d3f79a', 'a86af28e9ed04faba6e378ac09308a20', '文件管理', '/sys/fileInfo', '', NULL, '1', '1', 'Folder', '1', NULL, NULL);
INSERT INTO `portal_menu` (`id`, `parent_id`, `menu_name`, `menu_url`, `component`, `perms`, `status`, `is_visible`, `menu_icon`, `menu_type`, `menu_sort`, `lang_key`) VALUES ('4f02f2adfce4406ab1d705bb2ca456c6', 'a86af28e9ed04faba6e378ac09308a20', '菜单管理', '/sys/menu', '', NULL, '1', '1', 'Menu', '1', 40, NULL);
INSERT INTO `portal_menu` (`id`, `parent_id`, `menu_name`, `menu_url`, `component`, `perms`, `status`, `is_visible`, `menu_icon`, `menu_type`, `menu_sort`, `lang_key`) VALUES ('5a08ff58c4c14b5ca016b65814761243', 'a86af28e9ed04faba6e378ac09308a20', '文件存储服务', '/sys/file', '', NULL, '1', '2', '', '1', NULL, NULL);
INSERT INTO `portal_menu` (`id`, `parent_id`, `menu_name`, `menu_url`, `component`, `perms`, `status`, `is_visible`, `menu_icon`, `menu_type`, `menu_sort`, `lang_key`) VALUES ('5beda365ada4443eaa576cc527385090', 'a86af28e9ed04faba6e378ac09308a20', '日志管理', '/sys/log', '', NULL, '1', '1', 'Notebook', '1', 50, NULL);
INSERT INTO `portal_menu` (`id`, `parent_id`, `menu_name`, `menu_url`, `component`, `perms`, `status`, `is_visible`, `menu_icon`, `menu_type`, `menu_sort`, `lang_key`) VALUES ('744e84d86dad4202818aa2f8bd3ce135', 'ae6b108e042d46aca828ae5de8b20d27', '应用管理', '/portal/application', '', NULL, '1', '1', 'Mouse', '1', 1, NULL);
INSERT INTO `portal_menu` (`id`, `parent_id`, `menu_name`, `menu_url`, `component`, `perms`, `status`, `is_visible`, `menu_icon`, `menu_type`, `menu_sort`, `lang_key`) VALUES ('877455b7e2bd4ef585f294936b4178a7', 'a86af28e9ed04faba6e378ac09308a20', '机构管理', '/sys/unit', '', NULL, '1', '1', 'OfficeBuilding', '1', 10, NULL);
INSERT INTO `portal_menu` (`id`, `parent_id`, `menu_name`, `menu_url`, `component`, `perms`, `status`, `is_visible`, `menu_icon`, `menu_type`, `menu_sort`, `lang_key`) VALUES ('a86af28e9ed04faba6e378ac09308a20', '', '系统管理', '', '', NULL, '1', '1', 'Setting', '1', 2, NULL);
INSERT INTO `portal_menu` (`id`, `parent_id`, `menu_name`, `menu_url`, `component`, `perms`, `status`, `is_visible`, `menu_icon`, `menu_type`, `menu_sort`, `lang_key`) VALUES ('ae6b108e042d46aca828ae5de8b20d27', '', '门户管理', '', '', NULL, '1', '1', 'Monitor', '1', 3, NULL);
INSERT INTO `portal_menu` (`id`, `parent_id`, `menu_name`, `menu_url`, `component`, `perms`, `status`, `is_visible`, `menu_icon`, `menu_type`, `menu_sort`, `lang_key`) VALUES ('d4027bd10f2546d3b77fc4d929302d63', 'a86af28e9ed04faba6e378ac09308a20', '系统设置', '/sys/setting', '', NULL, '1', '1', 'Operation', '1', 98, NULL);
INSERT INTO `portal_menu` (`id`, `parent_id`, `menu_name`, `menu_url`, `component`, `perms`, `status`, `is_visible`, `menu_icon`, `menu_type`, `menu_sort`, `lang_key`) VALUES ('e18d109198ec4c189452c119774858cd', 'a86af28e9ed04faba6e378ac09308a20', '用户管理', '/sys/user', '', NULL, '1', '1', 'User', '1', 19, NULL);
INSERT INTO `portal_menu` (`id`, `parent_id`, `menu_name`, `menu_url`, `component`, `perms`, `status`, `is_visible`, `menu_icon`, `menu_type`, `menu_sort`, `lang_key`) VALUES ('e7f2efef2f5e4ed8ad2d4c1163276a74', 'd33ef224d86c44398934d6a8650bcf03', '系统配置', '/sys/settingDm', '', NULL, '1', '1', '', '1', NULL, NULL);
COMMIT;


DROP TABLE IF EXISTS `portal_menu_permission`;
CREATE TABLE `portal_menu_permission` (
                                          `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                          `menu_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '菜单ID',
                                          `action_type` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '动作类型',
                                          `action_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '动作名称',
                                          `action_url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '动作路径',
                                          `create_time` timestamp NULL DEFAULT NULL,
                                          `create_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                                          `update_time` timestamp NULL DEFAULT NULL,
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC;


BEGIN;
INSERT INTO `portal_menu_permission` (`id`, `menu_id`, `action_type`, `action_name`, `action_url`, `create_time`, `create_user`, `update_time`) VALUES ('01d97902942c4ec49f30d358c60ec7ee', 'e7f2efef2f5e4ed8ad2d4c1163276a74', NULL, '通用', '**', '2024-12-18 15:47:01', NULL, NULL);
INSERT INTO `portal_menu_permission` (`id`, `menu_id`, `action_type`, `action_name`, `action_url`, `create_time`, `create_user`, `update_time`) VALUES ('1da87b1ccf124a1382b40eb27a389cca', 'e18d109198ec4c189452c119774858cd', NULL, '通用', '**', '2024-12-13 14:54:37', NULL, NULL);
INSERT INTO `portal_menu_permission` (`id`, `menu_id`, `action_type`, `action_name`, `action_url`, `create_time`, `create_user`, `update_time`) VALUES ('24ce4b9686064367aae5d77061f26055', '877455b7e2bd4ef585f294936b4178a7', NULL, '通用', '**', '2024-12-13 15:07:36', NULL, NULL);
INSERT INTO `portal_menu_permission` (`id`, `menu_id`, `action_type`, `action_name`, `action_url`, `create_time`, `create_user`, `update_time`) VALUES ('2655220f44f7405693de025110eb3cab', NULL, NULL, '修改', 'update', NULL, NULL, NULL);
INSERT INTO `portal_menu_permission` (`id`, `menu_id`, `action_type`, `action_name`, `action_url`, `create_time`, `create_user`, `update_time`) VALUES ('2763cc2d10b648dc86bbc4048377b6cd', '4f02f2adfce4406ab1d705bb2ca456c6', NULL, '通用', '**', '2024-12-13 15:40:41', NULL, NULL);
INSERT INTO `portal_menu_permission` (`id`, `menu_id`, `action_type`, `action_name`, `action_url`, `create_time`, `create_user`, `update_time`) VALUES ('2d59c3594350436b8b5dd5751e5acc37', '5beda365ada4443eaa576cc527385090', NULL, '查看', 'getById', '2024-12-13 15:42:46', NULL, NULL);
INSERT INTO `portal_menu_permission` (`id`, `menu_id`, `action_type`, `action_name`, `action_url`, `create_time`, `create_user`, `update_time`) VALUES ('35d23fbf72b149a88d2b327c3f209047', NULL, NULL, '通用', '**', NULL, NULL, NULL);
INSERT INTO `portal_menu_permission` (`id`, `menu_id`, `action_type`, `action_name`, `action_url`, `create_time`, `create_user`, `update_time`) VALUES ('4938a762b5444a00bb05232d7eb73ffc', '2b1e1e3051a74f7ba4fec7770653d66c', NULL, '通用', '**', '2024-12-13 15:41:46', NULL, NULL);
INSERT INTO `portal_menu_permission` (`id`, `menu_id`, `action_type`, `action_name`, `action_url`, `create_time`, `create_user`, `update_time`) VALUES ('952a6ca5815b455780c1e28d7d86cb20', '4a624510d78f4a1d88d259fc81d3f79a', NULL, '通用', '**', '2024-12-31 16:26:21', NULL, NULL);
INSERT INTO `portal_menu_permission` (`id`, `menu_id`, `action_type`, `action_name`, `action_url`, `create_time`, `create_user`, `update_time`) VALUES ('9c04986c84a941089a3f1795236ca096', '744e84d86dad4202818aa2f8bd3ce135', NULL, '通用', '**', '2025-07-15 11:48:40', NULL, NULL);
INSERT INTO `portal_menu_permission` (`id`, `menu_id`, `action_type`, `action_name`, `action_url`, `create_time`, `create_user`, `update_time`) VALUES ('baca3a39aa80407cb5a67beaac86e20b', '396c09ae850b4a61b68de5ced07a3120', NULL, '通用', '**', '2024-12-13 15:06:05', NULL, NULL);
INSERT INTO `portal_menu_permission` (`id`, `menu_id`, `action_type`, `action_name`, `action_url`, `create_time`, `create_user`, `update_time`) VALUES ('dbf6c9313a354e3d96d890c232792e69', '5beda365ada4443eaa576cc527385090', NULL, '通用', '**', '2024-12-13 15:42:46', NULL, NULL);
INSERT INTO `portal_menu_permission` (`id`, `menu_id`, `action_type`, `action_name`, `action_url`, `create_time`, `create_user`, `update_time`) VALUES ('e2b84b6e07cf4c0ba4b2f2fa14930ed9', 'd4027bd10f2546d3b77fc4d929302d63', NULL, '通用', '**', '2024-12-13 15:46:39', NULL, NULL);
INSERT INTO `portal_menu_permission` (`id`, `menu_id`, `action_type`, `action_name`, `action_url`, `create_time`, `create_user`, `update_time`) VALUES ('e6d90483c23d4a398c4f33002193df96', '5a08ff58c4c14b5ca016b65814761243', NULL, '通用', '**', '2025-03-24 14:32:28', NULL, NULL);
COMMIT;


DROP TABLE IF EXISTS `portal_role`;
CREATE TABLE `portal_role` (
                               `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色id',
                               `role_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '角色代码',
                               `role_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '角色名称',
                               `role_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '角色描述',
                               `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
                               `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
                               `unitid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '机构',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC;


BEGIN;
INSERT INTO `portal_role` (`id`, `role_code`, `role_name`, `role_description`, `create_time`, `update_time`, `unitid`) VALUES ('5320602b53554f62b48e28f4972e8823', 'APP', 'APP', '', '2024-11-20 17:26:16', NULL, NULL);
INSERT INTO `portal_role` (`id`, `role_code`, `role_name`, `role_description`, `create_time`, `update_time`, `unitid`) VALUES ('7570b7c9baa24a1193bc69d9ccfb4b84', 'COCKPIT_ADMIN', '驾驶舱管理员', 'http://***************:7070/cockpit/highway-network', '2025-05-19 16:09:13', '2025-05-19 17:11:26', NULL);
INSERT INTO `portal_role` (`id`, `role_code`, `role_name`, `role_description`, `create_time`, `update_time`, `unitid`) VALUES ('900edb5c8d3b4952becfd2811bd98d5b', 'ADMIN', '业务管理员', '管理员权限', '2024-08-14 14:58:00', '2024-08-30 16:02:32', NULL);
INSERT INTO `portal_role` (`id`, `role_code`, `role_name`, `role_description`, `create_time`, `update_time`, `unitid`) VALUES ('bd2f7196dae446ee8523c6f976a815a4', 'SUPER_ADMIN', '超级管理员', '', '2024-08-30 10:01:58', NULL, NULL);
INSERT INTO `portal_role` (`id`, `role_code`, `role_name`, `role_description`, `create_time`, `update_time`, `unitid`) VALUES ('c478bc7a772a4f4d820654196748ae43', '123', '监控中心', '', '2025-07-01 11:38:20', NULL, NULL);
COMMIT;


DROP TABLE IF EXISTS `portal_role_app`;
CREATE TABLE `portal_role_app` (
                                   `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                   `role_id` varchar(32) DEFAULT NULL COMMENT '角色ID',
                                   `app_id` bigint unsigned DEFAULT NULL COMMENT '应用ID',
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='角色应用授权表';


BEGIN;
INSERT INTO `portal_role_app` (`id`, `role_id`, `app_id`) VALUES (2, '900edb5c8d3b4952becfd2811bd98d5b', 3);
INSERT INTO `portal_role_app` (`id`, `role_id`, `app_id`) VALUES (3, '900edb5c8d3b4952becfd2811bd98d5b', 4);
COMMIT;


DROP TABLE IF EXISTS `portal_role_menu`;
CREATE TABLE `portal_role_menu` (
                                    `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
                                    `role_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '角色id',
                                    `menu_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '菜单id',
                                    `perms` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '权限标识',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC;


BEGIN;
INSERT INTO `portal_role_menu` (`id`, `role_id`, `menu_id`, `perms`) VALUES ('05a0b48991234d64be5f43c23c6274bb', '900edb5c8d3b4952becfd2811bd98d5b', '396c09ae850b4a61b68de5ced07a3120', '[\"baca3a39aa80407cb5a67beaac86e20b\"]');
INSERT INTO `portal_role_menu` (`id`, `role_id`, `menu_id`, `perms`) VALUES ('409109a69df64cb7baa2dd144c8dc62c', '900edb5c8d3b4952becfd2811bd98d5b', 'd4027bd10f2546d3b77fc4d929302d63', '[\"e2b84b6e07cf4c0ba4b2f2fa14930ed9\"]');
INSERT INTO `portal_role_menu` (`id`, `role_id`, `menu_id`, `perms`) VALUES ('4b107b5693db4d019663c74b54111cbd', '900edb5c8d3b4952becfd2811bd98d5b', 'ae6b108e042d46aca828ae5de8b20d27', '[]');
INSERT INTO `portal_role_menu` (`id`, `role_id`, `menu_id`, `perms`) VALUES ('75489879e2bb46478ee02a69c5cc2155', '900edb5c8d3b4952becfd2811bd98d5b', '5beda365ada4443eaa576cc527385090', '[\"2d59c3594350436b8b5dd5751e5acc37\",\"dbf6c9313a354e3d96d890c232792e69\"]');
INSERT INTO `portal_role_menu` (`id`, `role_id`, `menu_id`, `perms`) VALUES ('895b4b92b4c044a58b76049bc3b4daad', '900edb5c8d3b4952becfd2811bd98d5b', '877455b7e2bd4ef585f294936b4178a7', '[\"24ce4b9686064367aae5d77061f26055\"]');
INSERT INTO `portal_role_menu` (`id`, `role_id`, `menu_id`, `perms`) VALUES ('8d4c122a206340d4bf8725b4b38a6027', '900edb5c8d3b4952becfd2811bd98d5b', 'a86af28e9ed04faba6e378ac09308a20', '[]');
INSERT INTO `portal_role_menu` (`id`, `role_id`, `menu_id`, `perms`) VALUES ('8da809cbbd764f6781e9402221d8d23a', '900edb5c8d3b4952becfd2811bd98d5b', 'e18d109198ec4c189452c119774858cd', '[\"1da87b1ccf124a1382b40eb27a389cca\"]');
INSERT INTO `portal_role_menu` (`id`, `role_id`, `menu_id`, `perms`) VALUES ('90749548001848f0b362ac104d9e9a42', '900edb5c8d3b4952becfd2811bd98d5b', '4f02f2adfce4406ab1d705bb2ca456c6', '[\"2763cc2d10b648dc86bbc4048377b6cd\"]');
INSERT INTO `portal_role_menu` (`id`, `role_id`, `menu_id`, `perms`) VALUES ('bd2452a342e04966b47f2871ca3b781b', '900edb5c8d3b4952becfd2811bd98d5b', '2b1e1e3051a74f7ba4fec7770653d66c', '[\"4938a762b5444a00bb05232d7eb73ffc\"]');
INSERT INTO `portal_role_menu` (`id`, `role_id`, `menu_id`, `perms`) VALUES ('d4761f34a8c44d35a13ef3e7937a650d', '900edb5c8d3b4952becfd2811bd98d5b', '744e84d86dad4202818aa2f8bd3ce135', '[\"9c04986c84a941089a3f1795236ca096\"]');
COMMIT;


DROP TABLE IF EXISTS `portal_role_menu_permission`;
CREATE TABLE `portal_role_menu_permission` (
                                               `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
                                               `role_menu_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '角色菜单ID',
                                               `permisson_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '菜单权限ID',
                                               PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC;


BEGIN;
INSERT INTO `portal_role_menu_permission` (`id`, `role_menu_id`, `permisson_id`) VALUES ('7351502273696415744', '895b4b92b4c044a58b76049bc3b4daad', '24ce4b9686064367aae5d77061f26055');
INSERT INTO `portal_role_menu_permission` (`id`, `role_menu_id`, `permisson_id`) VALUES ('7351502273704804352', '8da809cbbd764f6781e9402221d8d23a', '1da87b1ccf124a1382b40eb27a389cca');
INSERT INTO `portal_role_menu_permission` (`id`, `role_menu_id`, `permisson_id`) VALUES ('7351502273713192960', '05a0b48991234d64be5f43c23c6274bb', 'baca3a39aa80407cb5a67beaac86e20b');
INSERT INTO `portal_role_menu_permission` (`id`, `role_menu_id`, `permisson_id`) VALUES ('7351502273721581568', '90749548001848f0b362ac104d9e9a42', '2763cc2d10b648dc86bbc4048377b6cd');
INSERT INTO `portal_role_menu_permission` (`id`, `role_menu_id`, `permisson_id`) VALUES ('7351502273729970176', 'bd2452a342e04966b47f2871ca3b781b', '4938a762b5444a00bb05232d7eb73ffc');
INSERT INTO `portal_role_menu_permission` (`id`, `role_menu_id`, `permisson_id`) VALUES ('7351502273734164480', '75489879e2bb46478ee02a69c5cc2155', '2d59c3594350436b8b5dd5751e5acc37');
INSERT INTO `portal_role_menu_permission` (`id`, `role_menu_id`, `permisson_id`) VALUES ('7351502273738358784', '75489879e2bb46478ee02a69c5cc2155', 'dbf6c9313a354e3d96d890c232792e69');
INSERT INTO `portal_role_menu_permission` (`id`, `role_menu_id`, `permisson_id`) VALUES ('7351502273742553088', '409109a69df64cb7baa2dd144c8dc62c', 'e2b84b6e07cf4c0ba4b2f2fa14930ed9');
INSERT INTO `portal_role_menu_permission` (`id`, `role_menu_id`, `permisson_id`) VALUES ('7351502273750941696', 'd4761f34a8c44d35a13ef3e7937a650d', '9c04986c84a941089a3f1795236ca096');
COMMIT;


DROP TABLE IF EXISTS `portal_role_user`;
CREATE TABLE `portal_role_user` (
                                    `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
                                    `role_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '角色id',
                                    `user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '菜单id',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC;


BEGIN;
INSERT INTO `portal_role_user` (`id`, `role_id`, `user_id`) VALUES ('1ba686dc1a9a40b8a63a05f5cfb8e37c', 'c478bc7a772a4f4d820654196748ae43', 'd2ca0e0f1a1845fea94793b92479dd95');
INSERT INTO `portal_role_user` (`id`, `role_id`, `user_id`) VALUES ('5969d3cde91241488fc939fb23fc520b', '900edb5c8d3b4952becfd2811bd98d5b', '1');
INSERT INTO `portal_role_user` (`id`, `role_id`, `user_id`) VALUES ('cd02f397f7ee40b5a97065ba5d19cd29', 'bd2f7196dae446ee8523c6f976a815a4', 'b7498f65020f4fd0aae86d2fcd89dc7f');
INSERT INTO `portal_role_user` (`id`, `role_id`, `user_id`) VALUES ('d7f256d188f649d5919ae331ace5ed36', '5320602b53554f62b48e28f4972e8823', 'e56a2c93309b43b9a706ed1952981e7e');
INSERT INTO `portal_role_user` (`id`, `role_id`, `user_id`) VALUES ('f5622b2ba02d457f8995dd8bf9e4f1af', '900edb5c8d3b4952becfd2811bd98d5b', 'ad227b2d5a524673b55063d6417ba01c');
INSERT INTO `portal_role_user` (`id`, `role_id`, `user_id`) VALUES ('f59c97143fe74762aa1cd55d25942eec', '7570b7c9baa24a1193bc69d9ccfb4b84', '4f3dc7fd2e70405eb974740a3335b3a9');
INSERT INTO `portal_role_user` (`id`, `role_id`, `user_id`) VALUES ('f971e9cf6f1242f4a27e9f1dd5ae7ac2', '5320602b53554f62b48e28f4972e8823', '1');
COMMIT;


DROP TABLE IF EXISTS `portal_unit`;
CREATE TABLE `portal_unit` (
                               `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'id',
                               `ucode` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '机构代码',
                               `uname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '机构名称',
                               `parent_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '父ID',
                               `unit_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                               `usort` int DEFAULT NULL COMMENT '排序',
                               `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
                               `camera_unit` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '对应监控列表目录',
                               `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '机构类型: Account 集团、单位 Department 部门',
                               `is_group` smallint DEFAULT NULL COMMENT '是否是集团 1:是 0:否',
                               `path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '路径',
                               `data_source` tinyint DEFAULT NULL COMMENT '数据来源 1=系统内部 0=第三方同步',
                               PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC;


BEGIN;
INSERT INTO `portal_unit` (`id`, `ucode`, `uname`, `parent_id`, `unit_path`, `usort`, `create_time`, `camera_unit`, `type`, `is_group`, `path`, `data_source`) VALUES ('001de20275b74a4aa914c8864dfdd8eb', '', '产品部门', '', '001de20275b74a4aa914c8864dfdd8eb', 3, '2024-12-10 16:16:32', NULL, NULL, NULL, NULL, 1);
INSERT INTO `portal_unit` (`id`, `ucode`, `uname`, `parent_id`, `unit_path`, `usort`, `create_time`, `camera_unit`, `type`, `is_group`, `path`, `data_source`) VALUES ('1a24c32dd69a4860bc840f25861be57c', '', '销售一部', '7df9251ef8a94b4685b3f001a3624c6c', 'fd7e2028ec2a44a9b1fc068b3a61382b/7df9251ef8a94b4685b3f001a3624c6c/1a24c32dd69a4860bc840f25861be57c', 1, '2024-09-13 11:42:44', NULL, NULL, NULL, NULL, 1);
INSERT INTO `portal_unit` (`id`, `ucode`, `uname`, `parent_id`, `unit_path`, `usort`, `create_time`, `camera_unit`, `type`, `is_group`, `path`, `data_source`) VALUES ('5f9d6959509b41888821a174356f9f5f', '', '研发一部', '97777e565f1a4e21a1e05053e7e27cbc', 'fd7e2028ec2a44a9b1fc068b3a61382b/97777e565f1a4e21a1e05053e7e27cbc/5f9d6959509b41888821a174356f9f5f', 1, '2024-12-10 16:16:05', NULL, NULL, NULL, NULL, 1);
INSERT INTO `portal_unit` (`id`, `ucode`, `uname`, `parent_id`, `unit_path`, `usort`, `create_time`, `camera_unit`, `type`, `is_group`, `path`, `data_source`) VALUES ('7df9251ef8a94b4685b3f001a3624c6c', 'A002', '销售部门', 'fd7e2028ec2a44a9b1fc068b3a61382b', 'fd7e2028ec2a44a9b1fc068b3a61382b/7df9251ef8a94b4685b3f001a3624c6c', 2, '2024-08-09 10:33:31', NULL, NULL, NULL, NULL, 1);
INSERT INTO `portal_unit` (`id`, `ucode`, `uname`, `parent_id`, `unit_path`, `usort`, `create_time`, `camera_unit`, `type`, `is_group`, `path`, `data_source`) VALUES ('97777e565f1a4e21a1e05053e7e27cbc', 'A003', '研发部门', 'fd7e2028ec2a44a9b1fc068b3a61382b', 'fd7e2028ec2a44a9b1fc068b3a61382b/97777e565f1a4e21a1e05053e7e27cbc', 3, '2024-08-09 10:33:43', NULL, NULL, NULL, NULL, 1);
INSERT INTO `portal_unit` (`id`, `ucode`, `uname`, `parent_id`, `unit_path`, `usort`, `create_time`, `camera_unit`, `type`, `is_group`, `path`, `data_source`) VALUES ('a7a4db084fcf4aec84055c8ca9e1a942', '', '销售二部', '7df9251ef8a94b4685b3f001a3624c6c', 'fd7e2028ec2a44a9b1fc068b3a61382b/7df9251ef8a94b4685b3f001a3624c6c/a7a4db084fcf4aec84055c8ca9e1a942', 2, '2024-09-13 11:43:53', NULL, NULL, NULL, NULL, 1);
INSERT INTO `portal_unit` (`id`, `ucode`, `uname`, `parent_id`, `unit_path`, `usort`, `create_time`, `camera_unit`, `type`, `is_group`, `path`, `data_source`) VALUES ('fd7e2028ec2a44a9b1fc068b3a61382b', 'A001', '山西交通厅', NULL, 'fd7e2028ec2a44a9b1fc068b3a61382b', 1, '2024-08-09 10:25:01', NULL, NULL, NULL, NULL, 1);
COMMIT;


DROP TABLE IF EXISTS `portal_user`;
CREATE TABLE `portal_user` (
                               `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                               `user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                               `login_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                               `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                               `unit_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所属机构',
                               `create_time` timestamp NULL DEFAULT NULL,
                               `create_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                               `update_time` timestamp NULL DEFAULT NULL,
                               `phone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机号',
                               `dept_id` bigint DEFAULT NULL COMMENT '部门ID(组织ID)',
                               `dept_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '部门名称',
                               `unit_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '单位名称',
                               `data_source` tinyint DEFAULT NULL COMMENT '数据来源 1=系统内部 0=第三方同步',
                               `state` smallint DEFAULT NULL COMMENT '在职/离职 1：在职 2：离职',
                               PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC;


BEGIN;
INSERT INTO `portal_user` (`id`, `user_name`, `login_name`, `password`, `unit_id`, `create_time`, `create_user`, `update_time`, `phone`, `dept_id`, `dept_name`, `unit_name`, `data_source`, `state`) VALUES ('1', 'admin', 'admin', '$2a$10$CyRWIEgxxI7IS8838U8RV.Zb8UFrnAow7WeYGjJGDgzbTfI3GABoK', 'fd7e2028ec2a44a9b1fc068b3a61382b', '2025-03-05 14:11:01', NULL, NULL, '', NULL, '', '', NULL, 1);
INSERT INTO `portal_user` (`id`, `user_name`, `login_name`, `password`, `unit_id`, `create_time`, `create_user`, `update_time`, `phone`, `dept_id`, `dept_name`, `unit_name`, `data_source`, `state`) VALUES ('53ec8dd7557045659be04823d3f19c00', 'yangcan', 'yangcan', '$2a$10$CtTLNzVQ3u2KErgVbrkyReeKlNdo6JPxMkDZ1E44L6sdFqyx8tzj2', 'fd7e2028ec2a44a9b1fc068b3a61382b', '2024-09-24 15:36:03', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);
INSERT INTO `portal_user` (`id`, `user_name`, `login_name`, `password`, `unit_id`, `create_time`, `create_user`, `update_time`, `phone`, `dept_id`, `dept_name`, `unit_name`, `data_source`, `state`) VALUES ('ad227b2d5a524673b55063d6417ba01c', '超级管理员', 'superadmin', '$2a$10$xgl.jNx0qeKjn3hVip/9s.to0icN3ExPFBVYOkUJAWTho.bsZxB16', '', '2024-08-30 10:01:39', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);
INSERT INTO `portal_user` (`id`, `user_name`, `login_name`, `password`, `unit_id`, `create_time`, `create_user`, `update_time`, `phone`, `dept_id`, `dept_name`, `unit_name`, `data_source`, `state`) VALUES ('d2ca0e0f1a1845fea94793b92479dd95', '许华强', 'xu', '$2a$10$0ptxI9fxilTDCxV714TdLuRX3sEUZVa613nTA9S1vZgGGUYW7NMq6', '', '2025-07-01 10:51:19', NULL, NULL, '', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `portal_user` (`id`, `user_name`, `login_name`, `password`, `unit_id`, `create_time`, `create_user`, `update_time`, `phone`, `dept_id`, `dept_name`, `unit_name`, `data_source`, `state`) VALUES ('e56a2c93309b43b9a706ed1952981e7e', 'app测试用户', 'apptest1', '$2a$10$iHbbFLzfpzwWGzesOyLrVuUc42DxBRh4cpHU2mCEQdhQXwIiEQzq6', 'fd7e2028ec2a44a9b1fc068b3a61382b', '2024-11-20 17:25:49', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);
COMMIT;

