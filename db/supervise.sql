CREATE TABLE `tocc_supervise_auto_rule`
(
    `id`              BIGINT PRIMARY KEY COMMENT '主键ID',
    `alarm_type_name` VARCHAR(255) NOT NULL COMMENT '报警类型',
    `waiting_minutes` INT          NOT NULL COMMENT '自动督办等待时长（分钟）'
) COMMENT ='自动督办规则表';

CREATE TABLE `tocc_supervise_manual_rule`
(
    `id`            BIGINT PRIMARY KEY COMMENT '主键ID',
    `alarm_id`      BIGINT NOT NULL COMMENT '关联的报警信息ID',
    `limit_minutes` INT    NOT NULL COMMENT '督办截止时长（分钟）'
) COMMENT ='手动督办规则表';