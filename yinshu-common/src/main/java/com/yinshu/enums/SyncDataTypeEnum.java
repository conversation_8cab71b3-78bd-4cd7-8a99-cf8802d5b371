package com.yinshu.enums;

/**
 * 同步数据类型枚举
 * <AUTHOR>
 * @date 2024-07-17
 */
public enum SyncDataTypeEnum {
    USER("USER", "用户"),
    ROLE("ROLE", "角色"),
    ORG("ORG", "机构");

    private final String code;
    private final String label;

    SyncDataTypeEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }

    public static SyncDataTypeEnum fromCode(String code) {
        for (SyncDataTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("不支持的数据类型: " + code);
    }
} 