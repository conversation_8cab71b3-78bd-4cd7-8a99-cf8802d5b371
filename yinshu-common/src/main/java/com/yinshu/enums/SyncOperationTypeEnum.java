package com.yinshu.enums;

/**
 * 同步操作类型枚举
 * <AUTHOR>
 * @date 2024-07-17
 */
public enum SyncOperationTypeEnum {
    ADD("ADD", "新增"),
    DELETE("DELETE", "删除"),
    DELETES("DELETES", "批量删除"),
    UPDATE("UPDATE", "修改"),
    UPDATEPSW("UPDATEPSW", "修改密码"),
    RESETPSW("RESETPSW", "重置密码");

    private final String code;
    private final String label;

    SyncOperationTypeEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }

    public static SyncOperationTypeEnum fromCode(String code) {
        for (SyncOperationTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("不支持的操作类型: " + code);
    }
} 