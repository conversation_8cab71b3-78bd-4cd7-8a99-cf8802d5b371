package com.yinshu.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: c
 * @Date: 2024/10/22/9:19
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum LeaveStateEnums {
    //状态（0：草稿 1：审批中 9：完成 2:审批驳回）
    UN_SUBMIT(0, "未提交"),
    SUBMIT(1, "审批中"),
    APPROVAL_SUCCESS(9, "审批通过"),
    APPROVAL_FAIL(2, "审批驳回");

    private Integer code;
    private String desc;

    public static LeaveStateEnums getLeaveByCode(Integer code) {
        for (LeaveStateEnums value : LeaveStateEnums.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
