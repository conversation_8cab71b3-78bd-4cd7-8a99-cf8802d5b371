package com.yinshu.interceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import com.yinshu.exception.TokenRuntimeException;
import com.yinshu.jwt.JWTUtil;


@Component
public class TokenInterceptor implements HandlerInterceptor {
	
	private static final Logger logger = LoggerFactory.getLogger(TokenInterceptor.class.getName());
	
	@Autowired
	private Environment environment;
	
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object o) throws Exception {
		logger.info("API.Token------------->" + request.getRequestURI());
		
//		response.setHeader("Access-Control-Allow-Origin", "*");
//        response.setHeader("Access-Control-Allow-Credentials", "true");
//        response.setHeader("Access-Control-Allow-Methods", "GET, HEAD, POST, PUT, PATCH, DELETE, OPTIONS");
//        response.setHeader("Access-Control-Max-Age", "86400");
//        response.setHeader("Access-Control-Allow-Headers", "*");
//
//        // 如果是OPTIONS则结束请求
//        if (HttpMethod.OPTIONS.toString().equals(request.getMethod())) {
//            response.setStatus(HttpStatus.NO_CONTENT.value());
//            return false;
//        }
		
		if(request.getRequestURI().contains("jscode2session")) {
			return true;
		}
		
		/**
		 * 如果不走token，验证密钥
		 */
	    String secret = request.getHeader("secret");
//	    if(StringUtils.isEmpty(secret) || !secret.equals(environment.getProperty("system.secret"))) {
//	    	throw new TokenRuntimeException("密钥为空或者错误");
//	    }
	    
	    /**
	     * 验证token（JWT）
	     */
	    String token = request.getHeader("token");
	    if(StringUtils.isEmpty(token)){
            throw new TokenRuntimeException("token不能为空");
        }
	    boolean verifyToken = JWTUtil.verify(token);
		if(!verifyToken){
            throw new TokenRuntimeException("无效的token，请重新登录。");
        }
	    return true;
	}
}
