package com.yinshu.http;

import javax.net.ssl.SSLContext;

import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;

@Configuration
public class RestTemplateConfig {

	@Bean
	public RestTemplate restTemplate() throws Exception {
		// 1. 创建SSL配置（这里使用信任所有证书的方式，生产环境不应这样配置）
		SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, (chain, authType) -> true).build();

		SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE);

		// 2. 注册HTTP和HTTPS的Socket工厂
		Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
				.register("http", PlainConnectionSocketFactory.getSocketFactory()).register("https", socketFactory)
				.build();

		// 3. 创建连接池管理器
		PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(
				socketFactoryRegistry);

		// 4. 配置连接池参数
		connectionManager.setMaxTotal(200); // 最大连接数
		connectionManager.setDefaultMaxPerRoute(50); // 每个路由的最大连接数

		// 5. 配置请求参数
//		RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(5 * 1000) // 连接超时时间（毫秒）
//				.setSocketTimeout(10 * 1000) // 读取超时时间（毫秒）
//				.setConnectionRequestTimeout(10 * 1000) // 从连接池获取连接的超时时间
//				.build();

		// 6. 创建HttpClient
		//CloseableHttpClient httpClient = HttpClientBuilder.create().setConnectionManager(connectionManager).setDefaultRequestConfig(requestConfig).build();
		CloseableHttpClient httpClient = HttpClientBuilder.create().setConnectionManager(connectionManager).build();

		// 7. 创建RestTemplate并使用连接池
		HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(httpClient);
		factory.setHttpClient(httpClient);
		factory.setConnectTimeout(15 * 1000);// 设置连接超时时间（毫秒）
		factory.setReadTimeout(60 * 1000);// 设置读取超时时间（毫秒）
		factory.setConnectionRequestTimeout(10 * 1000);// 设置从连接池获取连接的超时时间（毫秒）


		RestTemplate restTemplate = new RestTemplate(factory);

		// 添加对text/html类型的支持
		restTemplate.getMessageConverters().add(0, new StringHttpMessageConverter(StandardCharsets.UTF_8));
		restTemplate.getMessageConverters().add(1, new MappingJackson2HttpMessageConverter());

		return restTemplate;
	}

}
