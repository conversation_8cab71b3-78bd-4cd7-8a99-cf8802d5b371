package com.yinshu.http;

import java.util.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.exception.APIException;
import com.yinshu.exception.DvisualTokenException;
import com.yinshu.utils.StringHelper;

/**
 * 统一封装http请求
 * <AUTHOR>
 *
 */
public abstract class HttpTemplateAbstract {

	protected final static Logger logger = LoggerFactory.getLogger(HttpTemplateAbstract.class); 
	
	private static final String DEFAULT_CONTENT_TYPE = "application/json;charset=UTF-8";
	
	@Autowired
    private RestTemplate restTemplate;

    public static final Set<String> EXCLUDE_API_LOG =
            new HashSet<>(Arrays.asList(
                    "/api/113/",
                    "/api/23/",
                    "/api/100247/",
                    "/api/16/",
                    "/api/152/"));

	
	/**
     * post 请求
     *
     * @param url        请求地址
     * @param jsonObject 请求参数 JSON
     * @return JSONObject	
     */
    public JSONObject post(String url, JSONObject jsonObject) {
    	Map<String, String> headerMap = new HashMap<String, String>();
    	JSONObject result = post(url, headerMap, jsonObject);
        return result;
    }
    
	/**
     * post 请求
     *
     * @param url        请求地址
     * @param headerMap  头信息
     * @param jsonObject 请求参数 JSON
     * @return JSONObject
     */
    public JSONObject post(String url, Map<String, String> headerMap, JSONObject jsonObject) {
    	long start = System.currentTimeMillis();
    	logger.info("-------------请求数据(post.start)-------->" + url + " | " + jsonObject.toString());
        HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.add("Content-Type", DEFAULT_CONTENT_TYPE);
        for (Map.Entry<String, String> stringStringEntry : headerMap.entrySet()) {
            httpHeaders.add(stringStringEntry.getKey(), stringStringEntry.getValue());
        }
        HttpEntity httpEntity = new HttpEntity(jsonObject, httpHeaders);
        JSONObject result = restTemplate.postForObject(url, httpEntity, JSONObject.class);
        //if(isLog(url)) {//百度路网数据量太大
        	//logger.info("-------------请求数据(post.end)-------->times:" + (System.currentTimeMillis() - start) + " | "  + url +  " | " + result);        	
        	logger.info("-------------请求数据(post.end)-------->times:" + (System.currentTimeMillis() - start) + " | "  + url);
        //}
        if(result == null){
            throw new DvisualTokenException("大数据平台接口result返回null");
        }else if(!result.getString("state").equals("200")) {
        	if(result.getString("error") != null && result.getString("error").contains("Token信息错误")) {
        		throw new DvisualTokenException(result.getString("error"));
        	}
        	throw new APIException("「大数据平台」" + StringHelper.toString(result.getString("message")) + "-" + result.getString("error"));
        }
        return result;
    }

    private static boolean isLog(String url){
        for(String excUrl : EXCLUDE_API_LOG){
            if(url.contains(excUrl)){
                return false;
            }
        }
        return true;
    }
    
//    /**
//     * get 请求
//     *
//     * @param url        请求地址
//     * @param jsonObject 请求参数 JSON
//     * @return JSONObject
//     */
//    public JSONObject get(String url, JSONObject jsonObject) {
//    	Map<String, String> headerMap = new HashMap<String, String>();
//    	JSONObject result = this.get(url, headerMap, jsonObject);
//        return result;
//    }
//    
//    /**
//     * get 请求
//     *
//     * @param url        请求地址
//     * @param headerMap  头信息
//     * @param jsonObject 请求参数 JSON
//     * @return JSONObject
//     */
//    public JSONObject get(String url, Map<String, String> headerMap, JSONObject jsonObject) {
//    	long start = System.currentTimeMillis();
//    	logger.info("-------------请求数据(get.start)-------->" + url + " | " + jsonObject.toString());
//        HttpHeaders httpHeaders = new HttpHeaders();
//		httpHeaders.add("Content-Type", DEFAULT_CONTENT_TYPE);
//        for (Map.Entry<String, String> stringStringEntry : headerMap.entrySet()) {
//            httpHeaders.add(stringStringEntry.getKey(), stringStringEntry.getValue());
//        }
//        HttpEntity httpEntity = new HttpEntity(jsonObject, httpHeaders);
//        ResponseEntity<JSONObject> response = restTemplate.exchange(url, HttpMethod.GET, httpEntity, JSONObject.class);
//        JSONObject result = response.getBody();
//        logger.info("-------------请求数据(get.end)-------->" + result.toString(), " | times:" + (System.currentTimeMillis() - start));
//        if(!result.getString("state").equals("200")) {
//        	throw new APIException("「大数据平台」" + result.getString("message"));
//        }
//        return result;
//    }
	
}
