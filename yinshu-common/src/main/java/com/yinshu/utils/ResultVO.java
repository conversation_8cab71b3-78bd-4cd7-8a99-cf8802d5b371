package com.yinshu.utils;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "返回结果")
public class ResultVO<T> {

    public static final String SUCCESS_CODE = "200";

    public static final String ERROR_CODE = "101";

    /**
     * 状态码，比如1000代表响应成功
     */
    @Schema(description = "状态码，比如1000代表响应成功")
    private String code;
    /**
     * 响应信息，用来说明响应情况
     */
    @Schema(description = "响应信息，用来说明响应情况")
    private String msg;
    /**
     * 响应的具体数据
     */
    @Schema(description = "响应的具体数据")
    private T data;

    public ResultVO(T data) {
        this("0", "success", data);
    }

    public ResultVO(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public ResultVO(String code, T data) {
        this.code = code;
        this.data = data;
    }

    public ResultVO(String code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public static <T> ResultVO<T> suc(T data) {
        return new ResultVO<>(data);
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
