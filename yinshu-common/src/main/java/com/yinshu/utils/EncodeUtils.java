package com.yinshu.utils;

import java.security.SecureRandom;
import java.util.Base64;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

/**
 * 大数据平台加密和解密
 * 
 * <AUTHOR>
 *
 */
public class EncodeUtils {

	/**
	 * 加密
	 * 
	 * @param data
	 * @param secret
	 * @return
	 */
	public static String _encode(String data, String secret) {
		try {
			byte[] bb = aes_encode(data.getBytes(), secret);
			return Base64.getEncoder().encodeToString(bb);
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
	}
	
	public static String encode(String data, String secret) {
		try {
			byte[] bb = aes_encrypt(data.getBytes(), secret);
			return Base64.getEncoder().encodeToString(bb);
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
	}

	public static byte[] aes_encrypt(byte[] content, String code) throws Exception {
		// 生成key
		KeyGenerator kgen = KeyGenerator.getInstance("AES");
		// 用随机数作为密钥，使用SHA1PRNG规则初始化密钥生成器
		SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
		// 设置种子数
		random.setSeed(code.getBytes());
		// 固定长度
		kgen.init(128, random);
		// 产生秘钥
		SecretKey secretKey = kgen.generateKey();
		// 获取秘钥
		byte[] enCodeFormat = secretKey.getEncoded();
		// 还原秘钥
		SecretKeySpec key = new SecretKeySpec(enCodeFormat, "AES");
		// 获取AES加密算法
		Cipher cipher = Cipher.getInstance("AES");
		// 设置加密Key
		cipher.init(Cipher.ENCRYPT_MODE, key);
		// 加密
		byte[] result = cipher.doFinal(content);
		return result;
	}

	private static byte[] aes_encode(byte[] content, String seed) throws Exception {
		KeyGenerator kgen = KeyGenerator.getInstance("AES");
		SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
		random.setSeed(seed.getBytes());
		kgen.init(128, random);
		SecretKey secretKey = kgen.generateKey();
		byte[] enCodeFormat = secretKey.getEncoded();
		SecretKeySpec key = new SecretKeySpec(enCodeFormat, "AES");
		Cipher cipher = Cipher.getInstance("AES");// CBC/PKCS5Padding");
		cipher.init(Cipher.ENCRYPT_MODE, key);
		byte[] result = cipher.doFinal(content);
		return result;
	}

	/**
	 * 解密
	 * 
	 * @param data
	 * @param secret
	 * @return
	 */
	public static String _decode(String data, String secret) {
		try {
			byte[] bb = Base64.getDecoder().decode(data);
			return new String(aes_decode(bb, secret));
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
	}

	private static byte[] aes_decode(byte[] content, String seed) throws Exception {
		KeyGenerator kgen = KeyGenerator.getInstance("AES");
		SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
		random.setSeed(seed.getBytes());
		kgen.init(128, random);
		SecretKey secretKey = kgen.generateKey();
		byte[] enCodeFormat = secretKey.getEncoded();
		SecretKeySpec key = new SecretKeySpec(enCodeFormat, "AES");

		Cipher cipher = Cipher.getInstance("AES");// CBC/PKCS5Padding");
		cipher.init(Cipher.DECRYPT_MODE, key);
		byte[] result = cipher.doFinal(content);
		return result;
	}
	
	/**
	 * 将前端传过来的密码进行base64解密（为了让密码不进行明文传输）
	 * @param password
	 * @return
	 */
	public static String decodeBase64(String password) {
		byte[] decodedBytes = Base64.getDecoder().decode(password);
        return new String(decodedBytes);
	}
	
	public static boolean isStrictBase64(String str) {
        // 移除所有空白字符
        String cleanStr = str.replaceAll("\\s", "");
        
        // 检查长度是否为4的倍数
        if (cleanStr.length() % 4 != 0) {
            return false;
        }
        
        // 检查字符集是否合法
        if (!cleanStr.matches("^[A-Za-z0-9+/]*={0,2}$")) {
            return false;
        }
        
        // 检查填充字符'='的位置是否正确
        int padIndex = cleanStr.indexOf('=');
        if (padIndex != -1) {
            // 如果有'='，必须全部在末尾
            String padPart = cleanStr.substring(padIndex);
            if (!padPart.matches("=+$")) {
                return false;
            }
            // 填充字符只能是1或2个
            if (padPart.length() > 2) {
                return false;
            }
        }
        
        // 尝试解码验证
        try {
            Base64.getDecoder().decode(cleanStr);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}
