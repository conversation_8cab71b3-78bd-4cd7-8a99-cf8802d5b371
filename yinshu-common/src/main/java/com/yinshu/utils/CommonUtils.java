package com.yinshu.utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;


public class CommonUtils {
	
	/**
	 * 获取参数
	 * 
	 * @param request
	 * @return
	 */
	public static Map<String, Object> getRequestParams(HttpServletRequest request) {
		Map<String, Object> result = new HashMap<>();
		Map<String, String[]> paramArr = request.getParameterMap();
		for (String key : paramArr.keySet()) {
			String[] values = paramArr.get(key);
			if (values.length == 1) {
				result.put(key, values[0]);
			} else {
				result.put(key, values);
			}
		}

		return result;

	}

	/**
	 * LinkedHashMap的key转驼峰小写
	 * @param listMap
	 * @return
	 */
	public static List<LinkedHashMap<String,Object>> underlineToCamel(List<LinkedHashMap<String,Object>> listMap) {
		List<LinkedHashMap<String, Object>> mapList = new ArrayList<>(listMap.size());
		for (LinkedHashMap<String, Object> map : listMap) {
			LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>(map.size());
			for (Map.Entry<String, Object> entry : map.entrySet()) {
				resultMap.put(StringUtils.underlineToCamel(entry.getKey()), entry.getValue());
			}
			mapList.add(resultMap);
		}
		return mapList;
	}
	
//	public static Map<String, String> uploadFiles(HttpServletRequest req) {
//		String filePath = null; 
//		String bathPath = environment.getProperty("attachment.goods-path");
//		return uploadFiles(req, filePath);
//	}
	
	

}
