package com.yinshu.utils;

import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSONArray;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class StringHelper {

    public static String toString(Object obj) {
        return obj == null ? "" : obj.toString();
    }

    public static String toString0(Object obj) {
        return obj == null ? "0" : obj.toString();
    }

    /**
     * 将结果除以 100/1000/10000等
     *
     * @param dataList
     * @param _divisor
     * @return
     */
    public static List<String> formatNumber(List<String> dataList, int _divisor) {
        BigDecimal divisor = new BigDecimal(_divisor);
        List<String> resultList = new ArrayList<String>();
        for (String str : dataList) {
            BigDecimal number = new BigDecimal(str).divide(divisor).setScale(1, RoundingMode.DOWN);
            resultList.add(number.toString());
        }

        return resultList;

    }

    /**
     * 将结果除以 100/1000/10000等
     *
     * @param dataList
     * @param _divisor
     * @return
     */
    public static String formatNumber(String str, int _divisor) {
        BigDecimal divisor = new BigDecimal(_divisor);
        BigDecimal number = new BigDecimal(str).divide(divisor).setScale(1, RoundingMode.DOWN);
        return number.toString();
    }

    /**
     * 将结果除以 100/1000/10000等
     *
     * @param dataList
     * @param _divisor
     * @return
     */
    public static String formatNumber(Object str, int _divisor) {
        if (str == null) {
            return null;
        }
        BigDecimal divisor = new BigDecimal(_divisor);
        BigDecimal number = new BigDecimal(str.toString()).divide(divisor).setScale(1, RoundingMode.DOWN);
        return number.toString();
    }

    /**
     * 年显示
     *
     * @param date
     * @return
     */
    public static String formatDateForYY(String date) {
        return date.substring(0, 4);
    }

    /**
     * 年月显示
     *
     * @param date
     * @return
     */
    public static String formatDateForYYMM(String date) {
        return date.substring(0, 4) + "/" + date.substring(5, 7);
    }

    /**
     * 按天显示
     *
     * @param date
     * @return
     */
    public static String formatDateForDD(String date) {
        return date.substring(5, 7) + "/" + date.substring(8, 10);
    }

    /**
     * 春节日期 2024-12-01~2025-01-31
     *
     * @param date
     */
    public static String[] dateRange(String date) {
        return date.split("~");
    }

    /**
     * 截取字符串长度
     *
     * @param str
     * @param length
     * @return
     */
    public static String getString(String str, int length) {
        if (str.length() < length) {
            return str;
        }
        return str.substring(0, length);
    }

    /**
     * sql去掉最前面的and
     *
     * @param str
     * @param length
     * @return
     */
    public static String removeAndBySql(String sql) {
        if (StringUtils.hasText(sql) && sql.substring(0, 4).contains("and")) {
            return sql.substring(4);
        }
        return sql;
    }

    /*
     * <AUTHOR>
     * @description //TODO 判断是否为科学计数法
     * @date 2025/5/19 17:44
     * @param string 字符串
     * @return boolean true or false
     **/
    public static boolean isScientificNotation(String string) {
        return string.matches("[+-]?\\d+(\\.\\d*)?[Ee][+-]?\\d+");
    }

    /*
     * <AUTHOR>
     * @description //TODO 判断是否为科学计数法
     * @date 2025/5/19 17:44
     * @param string 字符串
     * @return java.lang.Object
     **/
    public static Object toDouble(String string) {
        if (isScientificNotation(string)) {
            return Double.parseDouble(string);
        } else {
            return string;
        }
    }

    /*
     * <AUTHOR>
     * @description //TODO 转换为普通字符串
     * @date 2025/5/19 17:44
     * @param d double
     * @return java.lang.String
     **/
    public static String toPlainString(Double d) {
        if (d == null) return null;
        return BigDecimal.valueOf(d).toPlainString();
    }
    
    /**
     * 拼接时间段的sql语句
     * @param dateType
     * @param dateRange
     * @param column
     * @return
     */
    public static String toDateRange(String dateType, JSONArray dateRange, String column) {
    	String dt = StringUtils.hasText(column) ? column : "dt";
    	if(dateType.equals("1")) {
    		return dt + "='" + DateUtils.getDate() + "'"; 
    	} else if(dateType.equals("month")) {
    		return dt + ">='" + DateUtils.getDateTime("yyyy-MM") + "-01" + "' and " + dt + "<='" + DateUtils.getDateTime("yyyy-MM") + "-31" + "'";
    	} else if(dateType.equals("quarter")) {
    		return dt + ">='" + DateUtils.getDateTime("yyyy-MM") + "-01" + "' and " + dt + "<='" + DateUtils.getDateTime("yyyy-MM") + "-31" + "'";
    	} else if(dateType.equals("year")) {
    		return dt + ">='" + DateUtils.getDateTime("yyyy-MM") + "-01" + "' and " + dt + "<='" + DateUtils.getDateTime("yyyy-MM") + "-31" + "'";
    	} else {
    		String startDate = dateRange.getString(0);
        	String endDate = dateRange.getString(1);
    		return dt + ">='" + startDate + "' and " + dt + "<='" + endDate + "'";
    	}
    }
    
    public static Map<String, Object> setMapData(String name, BigDecimal value, BigDecimal sum) {
		Map<String, Object> dataMap = new HashMap<String, Object>();
		dataMap.put("name", name);
		dataMap.put("value", value);
		return dataMap;
	}
    
    public static String[] getQuarterList(String quarter) {
    	String[] strs = new String[2];
    	if(quarter.equals("01")) {
    		strs[0] = "01";
    		strs[1] = "03";
    	} else if(quarter.equals("02")) {
    		strs[0] = "04";
    		strs[1] = "06";
    	} else if(quarter.equals("03")) {
    		strs[0] = "07";
    		strs[1] = "09";
    	} else if(quarter.equals("04")) {
    		strs[0] = "10";
    		strs[1] = "12";
    	}
    	return strs;
    }
    
    /**
     * 根据文件名称获取文件类型
     * @param fileName
     * @return
     */
    public static String getFileType(String fileName) {
    	String suffix = fileName.substring(fileName.lastIndexOf("."));
    	if(suffix.contains("png") || suffix.contains("jpg")) {
    		return "1";
    	} else if(suffix.contains("mp4") || suffix.contains("mp3")) {
    		return "2";
    	} else if(suffix.contains("doc") || suffix.contains("pdf")) {
    		return "3";
    	}
    	return fileName;
    }
}
