package com.yinshu.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class DateUtils {

    public static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";
    public static final String DEFAULT_DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";
    public static String YYYY_MM_DD = "yyyy-MM-dd";
    public static String YYYY_MM = "yyyy-MM";

    private DateUtils() {
    }

    public static Date getNow() {
        return Calendar.getInstance().getTime();
    }

    public static Date getDate(String pattern) {
        return formatDate(getNow(), pattern);
    }

    public static String getDate() {
        return getDateTime("yyyy-MM-dd");
    }

    public static String getYM() {
        return getDateTime("yyyy-MM");
    }

    public static String getDateTime() {
        return getDateTime("yyyy-MM-dd HH:mm:ss");
    }

    public static String getPreDate() {
        Date cur = Calendar.getInstance().getTime();
        Date pre = new Date(cur.getTime() - 24 * 60 * 60 * 1000);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return dateFormat.format(pre);
    }

    /**
     * 获取指定日期 的前n天
     *
     * @param day
     * @return
     */
    public static String getSpecifiedPreDate(Integer day) {
        Date cur = Calendar.getInstance().getTime();
        Date pre = new Date(cur.getTime() - day * 24 * 60 * 60 * 1000);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return dateFormat.format(pre);
    }

    /**
     * 获取去年今日
     *
     * @param date
     * @return
     */
    public static String getLastYearNowDay(String date) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 将字符串日期转换为LocalDate对象
        LocalDate today = LocalDate.parse(date, formatter);
        // 计算去年的今天
        LocalDate lastYearToday = today.minusYears(1);
        return lastYearToday.format(formatter);
    }

    public static Date getLastDate(Date date, String pattern) {
        Date pre = new Date(date.getTime() + 24 * 60 * 60 * 1000);
        return pre;
    }

    public static Date getLastDate(String datestr, String pattern) {
        Date date = parse(datestr, pattern);
        Date pre = new Date(date.getTime() + 24 * 60 * 60 * 1000);
        return pre;
    }

    public static String getPreDateTime() {
        Date cur = Calendar.getInstance().getTime();
        Date pre = new Date(cur.getTime() - 24 * 60 * 60 * 1000);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return dateFormat.format(pre);
    }

    public static String getPreDateTime(Date date) {
        Date pre = new Date(date.getTime() - 24 * 60 * 60 * 1000);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return dateFormat.format(pre);
    }

    public static String getFirstDayOfMonth() {
        Calendar c = Calendar.getInstance();
        Calendar calfirst = Calendar.getInstance();
        int now = c.get(Calendar.DAY_OF_MONTH);
        calfirst.add(Calendar.DATE, 1 - now);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return dateFormat.format(calfirst.getTime());
    }

    public static String getDateTime(String pattern) {
        Date datetime = Calendar.getInstance().getTime();
        return getDateTime(datetime, pattern);
    }

    public static String getDateTime(Date date, String pattern) {
        if (pattern == null || "".equals(pattern))
            pattern = "yyyy-MM-dd HH:mm:ss";
        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
        return dateFormat.format(date);
    }

    public static int getCurrentYear() {
        return Calendar.getInstance().get(1);
    }

    public static int getCurrentMonth() {
        return Calendar.getInstance().get(2) + 1;
    }

    public static int getCurrentDay() {
        return Calendar.getInstance().get(5);
    }

    public static int getCurrentHour() {
        return Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
    }

    public static Date addDays(int days) {
        return add(getNow(), days, 5);
    }

    public static Date addDays(Date date, int days) {
        return add(date, days, 5);
    }

    public static Date addMinutes(Date date, int minutes) {
        return add(date, minutes, Calendar.MINUTE);
    }

    public static Date addSecond(Date date, int second) {
        return add(date, second, Calendar.SECOND);
    }

    public static Date addMonths(int months) {
        return add(getNow(), months, 2);
    }

    public static Date addMonths(Date date, int months) {
        return add(date, months, 2);
    }

    public static String getPreYear(String curYear) {
        int curY = Integer.parseInt(curYear);
        int preY = curY - 1;
        return preY + "";
    }

    private static Date add(Date date, int amount, int field) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(field, amount);
        return calendar.getTime();
    }

    public static int diffYears(Date one, Date two) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(one);
        int yearOne = calendar.get(1);
        calendar.setTime(two);
        int yearTwo = calendar.get(1);
        return yearOne - yearTwo;
    }

    public static int diffMonths(Date one, Date two) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(one);
        int yearOne = calendar.get(1);
        int monthOne = calendar.get(2);
        calendar.setTime(two);
        int yearTwo = calendar.get(1);
        int monthTwo = calendar.get(2);
        return (yearOne - yearTwo) * 12 + (monthOne - monthTwo);
    }

    public static long diffDays(Date one, Date two) {
        return (one.getTime() - two.getTime()) / 0x5265c00L;
    }

    public static long diffHours(Date one, Date two) {
        return (one.getTime() - two.getTime()) / 1000 / 60 / 60;
    }

    public static long diffMinutes(Date one, Date two) {
        return (one.getTime() - two.getTime()) / 1000 / 60;
    }

    public static long diffSeconds(Date one, Date two) {
        return (one.getTime() - two.getTime()) / 1000;
    }

    public static Date parse(String datestr, String pattern) {
        if (datestr == null || "".equals(datestr))
            return null;
        Date date = null;
        String p = pattern;
        if (pattern == null || "".equals(pattern))
            p = "yyyy-MM-dd";
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat(p);
            date = dateFormat.parse(datestr);
        } catch (ParseException parseexception) {
        }
        return date;
    }

    public static String parseString(String datestr, String pattern) {
        Date datetime = parse(datestr, pattern);
        return format(datetime, pattern);
    }

    public static String format(Date date, String pattern) {
        String p;
        p = pattern;
        if (pattern == null || "".equals(pattern))
            p = "yyyy-MM-dd";
        SimpleDateFormat dateFormat = new SimpleDateFormat(p);
        try {
            return dateFormat.format(date);
        } catch (Exception e) {
            return "";
        }
    }

    public static Date formatDate(Date date, String pattern) {
        String datetime = format(date, pattern);
        return parse(datetime, pattern);
    }

    public static Date getMonthLastDay() {
        return getMonthLastDay(getNow());
    }

    public static Date getMonthLastDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(calendar.get(1), calendar.get(2) + 1, 1);
        calendar.add(5, -1);
        return calendar.getTime();
    }

    public static int getQuarter() {
        int quarter = (getCurrentMonth() - 1) / 3 + 1;
        return quarter;
    }

    public static int compareTo(Date d1, Date d2) {
        if (d1.compareTo(d2) > 0) {
            System.out.println("Date1 时间在 Date2 之后");
            return 1;
        } else if (d1.compareTo(d2) < 0) {
            System.out.println("Date1 时间在 Date2 之前");
            return -1;
        } else if (d1.compareTo(d2) == 0) {
            System.out.println("Date1 时间与 Date2 相等");
            return 0;
        } else {
            return 9;
        }
    }

    /**
     * 计算时间差
     *
     * @param endDate   最后时间
     * @param startTime 开始时间
     * @return 时间差（天/小时/分钟）
     */
    public static String timeDistance(Date endDate, Date startTime) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - startTime.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    public static void main(String[] args) {
        String test = "2003-1-31";
        try {
//			Date date = parse(test, "");
//			System.out
//					.println("\u5F97\u5230\u5F53\u524D\u65E5\u671F \uFF0D getDate():" + getDate());
//			System.out
//					.println("\u5F97\u5230\u5F53\u524D\u65E5\u671F\u65F6\u95F4 \uFF0D getDateTime():"
//							+ getDateTime());
//			System.out.println("\u5F97\u5230\u5F53\u524D\u5E74\u4EFD \uFF0D getCurrentYear():"
//					+ getCurrentYear());
//			System.out.println("\u5F97\u5230\u5F53\u524D\u6708\u4EFD \uFF0D getCurrentMonth():"
//					+ getCurrentMonth());
//			System.out.println("\u5F97\u5230\u5F53\u524D\u65E5\u5B50 \uFF0D getCurrentDay():"
//					+ getCurrentDay());
//			System.out.println("\u89E3\u6790 \uFF0D parse(" + test + "):"
//					+ getDateTime(date, "yyyy-MM-dd"));
//			System.out.println("\u81EA\u589E\u6708\u4EFD \uFF0D addMonths(3):"
//					+ getDateTime(addMonths(3), "yyyy-MM-dd"));
//			System.out.println("\u589E\u52A0\u6708\u4EFD \uFF0D addMonths(" + test + ",3):"
//					+ getDateTime(addMonths(date, 3), "yyyy-MM-dd"));
//			System.out.println("\u589E\u52A0\u65E5\u671F \uFF0D addDays(" + test + ",3):"
//					+ getDateTime(addDays(date, 3), "yyyy-MM-dd"));
//			System.out.println("\u81EA\u589E\u65E5\u671F \uFF0D addDays(3):"
//					+ getDateTime(addDays(3), "yyyy-MM-dd"));
//			System.out.println("\u6BD4\u8F83\u65E5\u671F \uFF0D diffDays():"
//					+ diffDays(getNow(), date));
//			System.out.println("\u6BD4\u8F83\u6708\u4EFD \uFF0D diffMonths():"
//					+ diffMonths(getNow(), date));
//			System.out.println("\u5F97\u5230" + test
//					+ "\u6240\u5728\u6708\u4EFD\u7684\u6700\u540E\u4E00\u5929:"
//					+ getDateTime(getMonthLastDay(date), "yyyy-MM-dd"));
//			System.out.println(getPreDate());

//		   System.out.println(format(addDays(7),"yyyy-MM-dd hh:mm:ss"));;

//			Date curTime = DateUtils.getNow();
//			Date registerEndTime = DateUtils.parse("2020-02-06 20:00", "yyyy-MM-dd HH:mm:ss");
//			System.out.println(curTime.before(registerEndTime));

            long times = 1733217033270L;
            System.out.println(DateUtils.format(new Date(times), "yyyy-MM-dd hh:mm:ss"));


        } catch (Exception e) {
            System.out.println(e.getStackTrace());
        }
    }

    /**
     * 获取月份的最后一天
     *
     * @param date
     * @return
     */
    public static Date lastDayOfMonth(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int value = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        cal.set(Calendar.DAY_OF_MONTH, value);
        return cal.getTime();
    }

    /*
     * <AUTHOR>
     * @description //TODO 获取时间列表
     * @date 2025/5/8 10:49
     * @param start 开始时间 YYYY-MM-DD
     * @param stop 结束时间 YYYY-MM-DD
     * @return java.util.List<java.lang.String>
     **/
    public static List<String> getTimeList(String start, String stop) {
        List<String> list = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        LocalDate startDate = LocalDate.parse(start, formatter);
        LocalDate endDate = LocalDate.parse(stop, formatter);

        while (!startDate.isAfter(endDate)) {
            list.add(startDate.format(formatter));
            startDate = startDate.plusDays(1);
        }

        return list;
    }
}
