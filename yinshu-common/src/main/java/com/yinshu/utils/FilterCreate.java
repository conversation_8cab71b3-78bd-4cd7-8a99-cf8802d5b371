package com.yinshu.utils;

public class FilterCreate {
    private StringBuilder stringBuilder = new StringBuilder();

    public FilterCreate and(String part){
        return concat(true, part, "and");
    }
    public FilterCreate and(boolean isConcat, String part){
        return concat(isConcat, part, "and");
    }
    public FilterCreate or(String part){
        return concat(true, part, "or");
    }
    public FilterCreate or(boolean isConcat, String part){
        return concat(isConcat, part, "or");
    }
    public FilterCreate concat(String part){
        if(part != null){
            stringBuilder.append(" ").append(part);
        }
        return this;
    }
    public FilterCreate concat(boolean flag, String part){
        return concat(flag, part, null);
    }
    public FilterCreate concat(boolean flag, String part, String symbol){
        if(flag && part != null){
            stringBuilder.append(" ");
            if(symbol != null){
                stringBuilder.append(symbol).append(" ");
            }
            stringBuilder.append(part);
        }
        return this;
    }
    @Override
    public String toString(){
        String filter = stringBuilder.toString().trim();
        if(filter.startsWith("and ")){
            filter = filter.replaceFirst("and", "");
        }
        if(filter.startsWith("or ")){
            filter = filter.replaceFirst("or", "");
        }
        return filter.trim();
    }

}
