package com.yinshu.utils;

import java.awt.AlphaComposite;
import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.util.Random;

/**
 * 验证码生成
 * <AUTHOR>
 *
 */
public class KaptchaUtils {
	/**
     * 传入BufferedImage对象，并将生成好的验证码保存到BufferedImage中
     */
    public static String drawRandomText(BufferedImage bufferedImage, int width, int height) {
        Graphics2D graphics = (Graphics2D) bufferedImage.getGraphics();
        graphics.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 1f));
        //graphics.setColor(new Color(6, 23, 64));// 验证码背景色
        graphics.setColor(Color.WHITE);
        graphics.fillRect(0, 0, width, height);// 填充线条背景
        graphics.setFont(new Font("宋体,楷体,微软雅黑", Font.PLAIN, 32));
        // 数字和字母的组合
        //String baseNumLetter = "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        String baseNumLetter = "0123456789";
        StringBuilder builder = new StringBuilder();
        // 旋转原点的 x 坐标
        int x = 20;
        String ch;
        Random random = new Random();
        for (int i = 0; i < 4; i++) {
            graphics.setColor(getRandomColor());
        	//graphics.setColor(new Color(0xBCCDEB));
            //设置字体旋转角度,角度小于30度
            int degree = random.nextInt() % 20;
            int dot = random.nextInt(baseNumLetter.length());
            ch = baseNumLetter.charAt(dot) + "";
            builder.append(ch);
            //正向旋转
            graphics.rotate(degree * Math.PI / 180, x, 45);
            graphics.drawString(ch, x, 38);
            //反向旋转
            graphics.rotate(-degree * Math.PI / 180, x, 45);
            // 字母间距记录
            x += 25;
        }
        // 画干扰线
        for (int i = 0; i < 6; i++) {
            // 设置随机颜色
            graphics.setColor(getRandomColor());
            // 随机画线
            graphics.drawLine(random.nextInt(width), random.nextInt(height), random.nextInt(width), random.nextInt(height));
        }
        // 添加噪点
        for (int i = 0; i < 30; i++) {
            int x1 = random.nextInt(width);
            int y1 = random.nextInt(height);
            graphics.setColor(getRandomColor());
            graphics.fillRect(x1, y1, 2, 2);
        }
        return builder.toString();
    }

    /**
     * 在图片上绘制指定文本（可用于绘制数学表达式）
     *
     * @param bufferedImage 图片对象
     * @param text 要绘制的文本内容（例如 "4 + 3 ="）
     * @param width 图片宽度
     * @param height 图片高度
     */
    public static void drawText(BufferedImage bufferedImage, String text, int width, int height) {
        Graphics2D graphics = (Graphics2D) bufferedImage.getGraphics();
        graphics.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 1f));
        // 设置背景色和字体
        graphics.setColor(Color.WHITE);
        graphics.fillRect(0, 0, width, height);
        graphics.setFont(new Font("宋体,楷体,微软雅黑", Font.PLAIN, 32));

        // 初始化绘制参数
        int x = 20;
        Random random = new Random();

        // 绘制每个字符
        for (int i = 0; i < text.length(); i++) {
            char ch = text.charAt(i);

            // 随机颜色和旋转角度
            graphics.setColor(getRandomColor());
            int degree = random.nextInt() % 20;

            // 正向旋转
            graphics.rotate(degree * Math.PI / 180, x, 45);
            graphics.drawString(String.valueOf(ch), x, 38);
            // 反向旋转
            graphics.rotate(-degree * Math.PI / 180, x, 45);

            // 字符间距
            x += 15;
        }

        // 添加干扰线
        for (int i = 0; i < 6; i++) {
            graphics.setColor(getRandomColor());
            graphics.drawLine(random.nextInt(width), random.nextInt(height), random.nextInt(width), random.nextInt(height));
        }

        // 添加噪点
        for (int i = 0; i < 30; i++) {
            int x1 = random.nextInt(width);
            int y1 = random.nextInt(height);
            graphics.setColor(getRandomColor());
            graphics.fillRect(x1, y1, 2, 2);
        }
    }


    /**
     * 随机取色
     */
    private static Color getRandomColor() {
        Random ran = new Random();
        return new Color(ran.nextInt(256), ran.nextInt(256), ran.nextInt(256));
    }

}
