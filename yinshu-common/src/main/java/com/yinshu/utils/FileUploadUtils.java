package com.yinshu.utils;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;

public class FileUploadUtils {
	
	private final static Logger logger = LoggerFactory.getLogger(CommonUtils.class);

	/**
	 * 批量文件上传
	 * 
	 * @param req
	 * @param filePath
	 * @return
	 */
	public synchronized static Map<String, String> uploadFiles(HttpServletRequest req, String bathPath) {
		String date = DateUtils.getDateTime("yyyyMMdd");
		String filePath = bathPath + File.separator + date;
		Map<String, String> result = new HashMap<>();
		String fileName = "";
		MultipartHttpServletRequest murequest = (MultipartHttpServletRequest) req;
		Map<String, MultipartFile> files = murequest.getFileMap();// 得到文件map对象
		createMkdir(filePath);
		for (String key : files.keySet()) {
			MultipartFile file = files.get(key);
			if (StringUtils.isEmpty(file.getOriginalFilename())) {
				continue;
			}
			String originalFilename = file.getOriginalFilename();
			SnowflakeIdGenerator snowflakeIdGenerator = new SnowflakeIdGenerator(0, 0);
			fileName = snowflakeIdGenerator.nextIdStr() + originalFilename.substring(originalFilename.lastIndexOf("."));
			File newfile = new File(filePath, fileName);
			try {
				file.transferTo(newfile);
				result.put(key, fileName);
				// result.put("name", originalFilename.substring(0,
				// originalFilename.indexOf(".")));
				result.put("path", date + File.separator + fileName);
			} catch (Exception e) {
				logger.error("上传文件出错，原因：" + e.getMessage());
				throw new RuntimeException(e);
			}
		}

		return result;

	}

	public static void createMkdir(String path) {
		File file = new File(path);
		if (!file.exists() && !file.isDirectory()) {
			file.mkdirs();
		}
	}
}
