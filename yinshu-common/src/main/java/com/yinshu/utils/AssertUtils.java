package com.yinshu.utils;

import com.yinshu.exception.APIException;
import org.springframework.util.ObjectUtils;

/**
 * 断言实用程序
 *
 * <AUTHOR>
 */
public class AssertUtils {

    /**
     * 断言对象不为空
     *
     * @param obj 对象
     */
    public static void notEmpty(Object obj) {
        notEmpty(obj, "对象不能为空");
    }

    /**
     * 断言对象不为空
     *
     * @param obj 对象
     * @param message 消息
     */
    public static void notEmpty(Object obj, String message) {
        if (ObjectUtils.isEmpty(obj)) {
            throw new APIException(message);
        }
    }

}
