package com.yinshu.utils;

import org.springframework.util.DigestUtils;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/****
 * MD5摘要
 * <AUTHOR>
 */
public class Md5Utils {

    /***
     * 保持不变即可
     */
    public static final String MD5_KEY="fda0f583f78ccb08df512b00e0b2f32b";

    /***
     * 生成摘要(根据字符串)
     * @param str
     * @return
     */
    public static String generateMd5(String str) throws NoSuchAlgorithmException, UnsupportedEncodingException {
        MessageDigest digest = MessageDigest.getInstance("MD5");
        byte[] bs = digest.digest(str.getBytes("UTF-8"));
        StringBuilder sb = new StringBuilder(40);
        for (byte x : bs) {
            if ((x & 0xff) >> 4 == 0) {
                sb.append("0").append(Integer.toHexString(x & 0xff));
            } else {
                sb.append(Integer.toHexString(x & 0xff));
            }
        }
        return sb.toString();
    }

    /***
     * 生成摘要(根据字节码)
     * @param bytes
     * @return
     */
    public static String generateMd5(byte[] bytes) throws NoSuchAlgorithmException, UnsupportedEncodingException {
        MessageDigest digest = MessageDigest.getInstance("MD5");
        byte[] bs = digest.digest(bytes);
        StringBuilder sb = new StringBuilder(40);
        for (byte x : bs) {
            if ((x & 0xff) >> 4 == 0) {
                sb.append("0").append(Integer.toHexString(x & 0xff));
            } else {
                sb.append(Integer.toHexString(x & 0xff));
            }
        }
        return sb.toString();
    }


    /***
     * 将code结合时间戳执行加密生成ticket摘要
     * @param code
     * @param timestamp 时间戳
     * @return 加密后的结果
     */
    public static String createTicketValue(String code,String timestamp) {
        String salt=MD5_KEY;//使用sm4对称加密得key作为盐值给密码加密
        // 拼接原密码与盐值
        String str = salt + code+ timestamp+ salt;
        // 循环加密5次
        for (int i = 0; i < 5; i++) {
            // DigestUtils：springboot提供的工具类
            str = DigestUtils.md5DigestAsHex(str.getBytes()).toUpperCase();
        }
        // 返回结果
        return str;
    }

    /***
     * 将授权码，授权密码结合时间戳执行加密生成ticket摘要
     * @param authCode
     * @param authPsw
     * @param ticket
     * @param timestamp 时间戳
     * @return 加密后的结果
     */
    public static String createTicketValue(String authCode, String authPsw,String ticket, String timestamp) {
        String salt = MD5_KEY;//使用sm4对称加密得key作为盐值给密码加密
        // 拼接原密码与盐值
        String str = salt + authCode + authPsw +ticket + timestamp + salt;
        // 循环加密5次
        for (int i = 0; i < 5; i++) {
            // DigestUtils：springboot提供的工具类
            str = DigestUtils.md5DigestAsHex(str.getBytes()).toUpperCase();
        }
        // 返回结果
        return str;
    }

    /***
     * 认证同步数据的摘要方法
     * @param authCode
     * @param authPsw
     * @param timestamp
     * @return
     */
    public static String authSyncDataCode(String authCode, String authPsw,String timestamp){
        String salt = MD5_KEY;//使用sm4对称加密得key作为盐值给密码加密
        // 拼接原密码与盐值
        String str = salt + authCode + authPsw  + timestamp + salt;
        // 循环加密5次
        for (int i = 0; i < 5; i++) {
            // DigestUtils：springboot提供的工具类
            str = DigestUtils.md5DigestAsHex(str.getBytes()).toUpperCase();
        }
        // 返回结果
        return str;
    }


    public static void main(String[] args) throws Exception {
        String code= "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxIiwibG9naW5OYW1lIjoiYWRtaW4iLCJleHAiOjE3NTQ3ODk3ODYsInVzZXJJZCI6IjEifQ.PmGiPklfEovPVpu7Wyq0bFYnPBQzjrFuCqfVldaM_0s";
        String timestamp = String.valueOf(System.currentTimeMillis());
        System.out.println("前code："+code+"\n timestamp:" + timestamp);
        String md5Str=Md5Utils.createTicketValue(code,timestamp);
        System.out.println("摘要后："+ md5Str);

    }
}
