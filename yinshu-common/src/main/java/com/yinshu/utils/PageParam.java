package com.yinshu.utils;

import java.io.Serializable;

import cn.idev.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

public class PageParam<T> implements Serializable {
	
	private static final long serialVersionUID = 1L;
	private static final Integer PAGE_NUMBER = 1;
    private static final Integer PAGE_SIZE = 10;


    //页面编号
    @TableField(exist = false)
    @ExcelIgnore
    private Integer pageNumber = PAGE_NUMBER;
    //页面大小
    @TableField(exist = false)
    @ExcelIgnore
    private Integer pageSize = PAGE_SIZE;
    //是否升序
    @TableField(exist = false)
    @ExcelIgnore
    private Boolean asc = true;
    //排序方式
    @TableField(exist = false)
    @ExcelIgnore
    private String sortBy;
    //查询关键字
    @TableField(exist = false)
    @ExcelIgnore
    private String queryKeyword;
    
    /**
     * 关键字查询
     */
    @TableField(exist = false)
    @ExcelIgnore
    private String keyword;


    public static Integer getPageSize() {
        return PAGE_SIZE;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Boolean getAsc() {
        return asc;
    }

    public void setAsc(Boolean asc) {
        this.asc = asc;
    }

    public String getSortBy() {
        return sortBy;
    }

    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }

    public String getQueryKeyword() {
        return queryKeyword;
    }

    public void setQueryKeyword(String queryKeyword) {
        this.queryKeyword = queryKeyword;
    }

    public String getKeyword() {
		return keyword;
	}

	public void setKeyword(String keyword) {
		this.keyword = keyword;
	}

	public Page<T> toPage(){
        Page<T> page = new Page<T>(this.pageNumber, this.pageSize);
        // 前端是否有排序字段
        if (StringUtils.isNotEmpty(sortBy)){
            OrderItem orderItem = new OrderItem();
            orderItem.setAsc(asc);
            orderItem.setColumn(sortBy);
            page.addOrder(orderItem);
        }
        return page;
    }
}
