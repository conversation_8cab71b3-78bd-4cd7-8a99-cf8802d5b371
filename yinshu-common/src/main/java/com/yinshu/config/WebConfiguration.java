package com.yinshu.config;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.web.servlet.config.annotation.CorsRegistration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * WEB环境配置，拦截器注册
 *
 * <AUTHOR>
 */
@Configuration
public class WebConfiguration implements WebMvcConfigurer {

	@Autowired
	private Environment environment; 

//	@Autowired
//	private TokenInterceptor tokenInterceptor;
//	
//	@Override
//	public void addInterceptors(InterceptorRegistry registry) {
//        InterceptorRegistration tokenRegistry = registry.addInterceptor(tokenInterceptor);
//        tokenRegistry.addPathPatterns("/api/**");
//        tokenRegistry.excludePathPatterns("/api/login");
//        tokenRegistry.excludePathPatterns("/api/loginOut");
//        tokenRegistry.excludePathPatterns("/api/upload/image");
//        tokenRegistry.excludePathPatterns("/api/upload/image2");
//	}
//	
//	public void addResourceHandlers(ResourceHandlerRegistry registry) {
//		registry.addResourceHandler("/vm/oss/**").addResourceLocations("file:" + environment.getProperty("attachment.banner-path"))
//												 .addResourceLocations("file:" + environment.getProperty("attachment.goods-path"))
//												 .addResourceLocations("file:" + environment.getProperty("attachment.orders-path"))
//												 .addResourceLocations("file:" + environment.getProperty("attachment.activitie-path"))
//												 .addResourceLocations("file:" + environment.getProperty("attachment.wangEditor-path"))
//												 .addResourceLocations("file:" + environment.getProperty("attachment.cert-template-path"))
//												 .addResourceLocations("file:" + environment.getProperty("attachment.cert-file-path"))
//												 .addResourceLocations("file:" + environment.getProperty("attachment.pdfReport-path"));
//    }

    /**
     * 文件存储路径
     */
    @Value("${system.profile}")
    private String rootPath;

    /**
     * 页面跨域访问Controller过滤
     *
     * @return
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
//        WebMvcConfigurer.super.addCorsMappings(registry);
//        registry.addMapping("/**")
//                .allowedHeaders("*")
//                .allowedMethods("*")
//                .allowedOrigins("*");
        CorsEntity cors = new CorsEntity();
        CorsRegistration corsRegistration = registry.addMapping(cors.getMapping());
        corsRegistration
                // change 由allowedOrigins改为allowedOriginPatterns，springboot新版本中allowedOrigins不允许设置*，只能设置具体的域名
                .allowedOriginPatterns(cors.getAllowedOrigins().toArray(new String[cors.getAllowedOrigins().size()]))
                .allowCredentials(cors.getAllowCredentials())
                .allowedMethods(cors.getAllowedMethods().toArray(new String[cors.getAllowedMethods().size()]))
                .maxAge(cors.getMaxAge());
        if (cors.getAllowedHeaders() != null && !cors.getAllowedHeaders().isEmpty()) {
            corsRegistration.allowedHeaders(cors.getAllowedHeaders().toArray(new String[cors.getAllowedHeaders().size()]));
        }
        List<String> exposedHeaders = cors.getExposedHeaders();
        if (exposedHeaders != null && !exposedHeaders.isEmpty()) {
            corsRegistration.exposedHeaders(exposedHeaders.toArray(new String[exposedHeaders.size()]));
        }
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 前端访问演示：http://localhost:9091/tocc/api/files/tcps/tcps_1752122346144_222.jpg
//        registry.addResourceHandler("/api/files/**")
//                .addResourceLocations("file:" + rootPath + "/");
    	registry.addResourceHandler("/vm/oss/**")
    			.addResourceLocations("file:" + environment.getProperty("attachment.default-path"))
    			.addResourceLocations("file:" + environment.getProperty("attachment.cert-file-path"));
    }

}
