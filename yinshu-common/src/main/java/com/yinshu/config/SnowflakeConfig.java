package com.yinshu.config;

import com.yinshu.utils.SnowflakeIdGenerator;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SnowflakeConfig {
    @Value("${snowflake.worker-id}")
    private long workerId;

    @Value("${snowflake.datacenter-id}")
    private long datacenterId;
    /**
     * 添加雪花算法id生成器
     */
    @Bean
    public SnowflakeIdGenerator snowflakeIdGenerator() {
        // 创建一个雪花算法生成器，传入机器ID和数据中心ID
        SnowflakeIdGenerator idGenerator = new SnowflakeIdGenerator(workerId, datacenterId);
        return idGenerator;
    }
}
