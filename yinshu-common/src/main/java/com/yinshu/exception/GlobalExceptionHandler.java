package com.yinshu.exception;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.ModelAndView;

import com.yinshu.utils.ResultVO;


/**
 * 全局异常捕获
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

	private final static Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);
	
	@ExceptionHandler(value = Exception.class)
	public Object jsonHandler(HttpServletRequest request, Exception e) {
		//log(e, request);
		Map<String, Object> errResult = new HashMap<>();
		logger.error("全局异常捕获>>>>>" + e.getMessage(), e);
		if(e instanceof SessionTimeOutException) {
			errResult.put("code", "-9");
			errResult.put("msg", e.getMessage());
			return errResult;
		} 
		else if(e instanceof APIException) {
			APIException exception = (APIException)e;
			errResult.put("code", exception.getCode());
			errResult.put("msg", exception.getMessage() == null ? "后台处理失败" : exception.getMessage());
			return errResult;
		}
		else if (isAjax(request)) {
			errResult.put("code", "-1");
			errResult.put("msg", e.getMessage());
			return errResult;
        } 
		else if (e instanceof RuntimeException) {
			errResult.put("code", "-1");
			errResult.put("msg", e.getMessage() == null ? e.getClass().toString() : e.getMessage());
			return errResult;
        } 
		else {// 系统异常
//			errResult.put("code", "500");
//			errResult.put("msg", "出现未知异常，请联系管理员");
			ModelAndView modelAndView = new ModelAndView();
            modelAndView.setViewName("error"); //这里需要在templates文件夹下新建一个error.html文件用作错误页面
            return modelAndView;
		}
		//return errResult;
	}
	
	@ExceptionHandler(value = TokenRuntimeException.class)  
    @ResponseBody 
	public Map<String, Object> bizExceptionHandler(HttpServletRequest req, TokenRuntimeException e){
    	logger.error("发生业务异常！原因是：{}", e.getMsg());
    	Map<String, Object> errResult = new HashMap<>();
    	errResult.put("code", "201");
		errResult.put("msg", e.getMsg());
		return errResult;
    }
	
	/**
	 * 参数错误异常
	 * @param req
	 * @param e
	 * @return
	 */
	@ExceptionHandler(value = MissingServletRequestParameterException.class)  
	public ResultVO<?> missingServletRequestParameterException(HttpServletRequest req, MissingServletRequestParameterException e){
		return new ResultVO<>("99", e.getMessage());
    }
	
	
	@ExceptionHandler(value = RestfulAPIException.class)  
	public ResultVO<?> bizExceptionHandler(HttpServletRequest req, RestfulAPIException e){
    	logger.error("Restful接口调用异常！原因是：{}", e);
    	return new ResultVO<>("999999", e.getMsg() == null ? e.getMessage() : e.getMsg());
    }

	private void log(Exception ex, HttpServletRequest request) {
//		log.error("请求地址：" + request.getRequestURL(), ex);
//		Enumeration<String> enumeration = request.getParameterNames();
//		log.error("请求参数：");
//		while (enumeration.hasMoreElements()) {
//			String name = enumeration.nextElement().toString();
//			log.error(name + "---" + request.getParameter(name));
//		}
//		StackTraceElement[] error = ex.getStackTrace();
//		for (StackTraceElement stackTraceElement : error) {
//			log.error(stackTraceElement.toString());
//		}
	}
	
	/**
     * 判断是否是Ajax请求
     *
     * @param request
     * @return
     */
    public boolean isAjax(HttpServletRequest request) {
        return (request.getHeader("X-Requested-With") != null && "XMLHttpRequest".equals(request.getHeader("X-Requested-With")));
    }

}