package com.yinshu.exception;

public class TokenRuntimeException extends RuntimeException {

	private static final long serialVersionUID = 1L;

	private String msg;
	
	private Integer code = 201;
	 
	public TokenRuntimeException(String msg) {
		this.msg = msg;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

}
