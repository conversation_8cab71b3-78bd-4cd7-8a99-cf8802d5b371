package com.yinshu.exception;

/**
 * 用户登录超时
 * <AUTHOR>
 *
 */
public class APIException extends RuntimeException {

	private static final long serialVersionUID = 1L;
	
	/**
     * 状态码，比如1000代表响应成功
     */
    private String code;
    /**
     * 响应信息，用来说明响应情况
     */
    private String msg;

    public APIException() {
        this("999999", "接口错误");
    }
    
    public APIException(Exception exception) {
        this("999999", exception);
    }
    
    public APIException(Exception exception, String msg) {
        this("999999", exception, msg);
    }
    
    public APIException(String msg) {
        this("999999", msg);
    }
    
    public APIException(String code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
    }
    
    public APIException(String code, Exception exception) {
        super(exception.getMessage(), exception);
        this.code = code;
        this.msg = exception.getMessage();
    }
    
    public APIException(String code, Exception exception, String msg) {
        super(exception.getMessage(), exception);
        this.code = code;
        this.msg = msg;
    }

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}
    
    
}
