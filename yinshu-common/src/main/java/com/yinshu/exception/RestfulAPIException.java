package com.yinshu.exception;

/**
 * 用户登录超时
 * <AUTHOR>
 *
 */
public class RestfulAPIException extends RuntimeException {

	private static final long serialVersionUID = 1L;
	
	/**
     * 状态码，比如1000代表响应成功
     */
    private String code;
    /**
     * 响应信息，用来说明响应情况
     */
    private String msg;
    
    public RestfulAPIException(Throwable cause) {
        super(cause);
        this.msg = cause.getMessage();
    }

    public RestfulAPIException() {
        this("99", "接口错误");
    }
    
    public RestfulAPIException(String msg) {
        this("99", msg);
    }
    
    public RestfulAPIException(String code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
    }

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}
    
    
}
