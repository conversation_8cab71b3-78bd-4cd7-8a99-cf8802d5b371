package com.yinshu.common;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ApiConstant
 * @description TODO API接口请求地址前缀
 * @date 2025/6/4 10:45
 **/
public interface ApiConstant {

    /**
     * 接口通用前缀
     **/
    String API_PREFIX = "/api";
    /**
     * 辅助决策系统通用前缀
     **/
    String API_TDSS_PREFIX = API_PREFIX + "/tdss";
    /**
     * 运行监测系统通用前缀
     **/
    String API_TOMS_PREFIX = API_PREFIX + "/toms";
    /**
     * 行业监管系统通用前缀
     **/
    String API_TISS_PREFIX = API_PREFIX + "/tiss";
    /**
     * 在建交通工程监管系统
     **/
    String API_TCPS_PREFIX = API_PREFIX + "/tcps";

    /**
     * 在建交通工程监管系统
     **/
    String API_TACP_PREFIX = API_PREFIX + "/tact";

    /***
     * 门户
     */
    String API_PORTAL_PREFIX = API_PREFIX + "/portal";
}
