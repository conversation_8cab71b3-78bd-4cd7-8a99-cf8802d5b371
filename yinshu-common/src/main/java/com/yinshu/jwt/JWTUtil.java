package com.yinshu.jwt;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.yinshu.utils.DateUtils;

public class JWTUtil {
	
	private static final String SECRET = "yinshu@2024!";//自定义修改
	
	private static final Logger logger = LoggerFactory.getLogger(JWTUtil.class.getName());

	/**
	 * 创建jwt
	 * 
	 * @param user
	 * @return
	 */
	public static String createToken(String userId) {
		return JWT.create().withAudience(userId) // 设置载荷
				.withClaim("userId", userId)
				.withClaim("userName", "zhangsan")
				.withExpiresAt(DateUtils.addMinutes(DateUtils.getNow(), 120)) //设置签名过期的时间:24小时后
				.sign(Algorithm.HMAC256(SECRET)); //签名
	}
	
	/**
	 * 创建jwt
	 * 
	 * @param user
	 * @return
	 */
	public static String createToken(String userId, String loginName) {
		return JWT.create().withAudience(userId) // 设置载荷
				.withClaim("userId", userId)
				.withClaim("loginName", loginName)
				.withExpiresAt(DateUtils.addMinutes(DateUtils.getNow(), 36000)) //设置签名过期的时间:24小时后
				.sign(Algorithm.HMAC256(SECRET)); //签名
	}
	

	/**
	 * 校验token
	 * @param token
	 * @return
	 */
	public static boolean verify(String token) {
		try {
			JWT.require(Algorithm.HMAC256(SECRET)).build().verify(token);
			return true;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return false;
	}

	// 从token中获取用户id
	public static String getUserId(String token) {
		try {
			DecodedJWT jwt = JWT.decode(token);
			return jwt.getClaim("userId").asString();
		} catch (JWTDecodeException e) {
			return null;
		}
	}

	// 从token中获取定义的荷载信息
	public static String getTokenClaim(String token, String key) {
		try {
			DecodedJWT jwt = JWT.decode(token);
			return jwt.getClaim(key).asString();
		} catch (JWTDecodeException e) {
			return null;
		}
	}

	// 判断 token 是否过期
	public static boolean isExpire(String token) {
		DecodedJWT jwt = JWT.decode(token);
		// 如果token的过期时间小于当前时间，则表示已过期，为true
		return jwt.getExpiresAt().getTime() < System.currentTimeMillis();
	}
}
