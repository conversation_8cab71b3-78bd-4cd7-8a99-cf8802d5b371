package com.yinshu.toms.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.fast.excel.util.ExcelUtils;
import com.yinshu.toms.service.BasicResourceService;
import com.yinshu.toms.service.RoadEventService;
import com.yinshu.toms.vo.highway.RoadRealEventExportVo;
import com.yinshu.toms.vo.road.transport.RoadHighwayFacilitiesExportVo;
import com.yinshu.toms.vo.road.transport.RoadNetworkExportVO;
import com.yinshu.toms.vo.road.transport.RoadNetworkProjectExportVO;
import com.yinshu.toms.vo.road.transport.RoadStructureExportVO;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 公共接口
 *
 */
@RestController
@RequestMapping(ApiConstant.API_TOMS_PREFIX + "/basic-resource")
public class BasicResourceController {

    @Autowired
    private BasicResourceService basicResourceService;

    @Autowired
    private RoadEventService roadEventService;


    /**
     * 获取公路事件地图
     */
    @PostMapping("/getRoadEventMap")
    public ResultVO<?> getRoadEventMap(@RequestBody JSONObject query) {
        return ResultVO.suc(roadEventService.getRoadEventMap(query));
    }

    /**
     * 获取公路事件地图导出
     */
    @PostMapping("/getRoadEventMapExport")
    public void getRoadEventMapExport(@RequestBody JSONObject query) {
        JSONObject object = roadEventService.getRoadEventMap(query);
        ExcelUtils.exportExcelSheet(RoadRealEventExportVo.class, object);
    }

    /**
     * 获取地市行政区划
     * @param query
     * @return
     */
    @PostMapping("/getCountyList")
    public ResultVO<?> getCountyList(@RequestBody JSONObject query) {
        return ResultVO.suc(basicResourceService.getCountyList(query));
    }

    /**
     * 获取公路路网
     * @param query
     * @return
     */
    @PostMapping("/getRoadNetwork")
    public ResultVO<?> getRoadNetwork(@RequestBody JSONObject query) {
        return ResultVO.suc(basicResourceService.getRoadNetwork(query));
    }

    /**
     * 获取路网详细信息
     * @param query
     * @return
     */
    @PostMapping("/getRoadNetworkInfo")
    public ResultVO<?> getRoadNetworkInfo(@RequestBody JSONObject query) {
        return ResultVO.suc(basicResourceService.getRoadNetworkInfo(query));
    }

    /**
     * 导出公路路网
     * @param query
     * @return
     */
    @PostMapping("/exportRoadNetworkInfo")
    public void exportRoadNetworkInfo(@RequestBody JSONObject query) {
        JSONObject object = basicResourceService.getRoadNetworkInfo(query);
        ExcelUtils.exportExcelSheet(RoadNetworkExportVO.class, object);
    }

    /**
     * 获取构造物
     * @param query
     * @return
     */
    @PostMapping("/getStructureList")
    public ResultVO<?> getStructureList(@RequestBody JSONObject query) {
        return ResultVO.suc(basicResourceService.getStructureList(query));
    }

    /**
     * 导出构造物
     * @param query
     * @return
     */
    @PostMapping("/exportStructureList")
    public void exportStructureList(@RequestBody JSONObject query) {
        JSONObject object = basicResourceService.getStructureList(query);
        ExcelUtils.exportExcelSheet(RoadStructureExportVO.class, object);
    }

    /**
     * 获取公路设施
     * @param query
     * @return
     */
    @PostMapping("/getHighwayFacilities")
    public ResultVO<?> getHighwayFacilities(@RequestBody JSONObject query) {
        return ResultVO.suc(basicResourceService.getHighwayFacilities(query));
    }

    /**
     * 导出公路设施
     * @param query
     * @return
     */
    @PostMapping("/exportHighwayFacilities")
    public void exportHighwayFacilities(@RequestBody JSONObject query) {
        JSONObject object = basicResourceService.getHighwayFacilities(query);
        ExcelUtils.exportExcelSheet(RoadHighwayFacilitiesExportVo.class, object);
    }
    
    /**
     * 获取路网工程
     * @param query
     * @return
     */
    @PostMapping("/getRoadNetworkProjectList")
    public ResultVO<?> getRoadNetworkProjectList(@RequestBody JSONObject query) {
        return ResultVO.suc(basicResourceService.getRoadNetworkProjectList(query));
    }

    /**
     * 导出构造物
     * @param query
     * @return
     */
    @PostMapping("/exportRoadNetworkProjectList")
    public void exportRoadNetworkProjectList(@RequestBody JSONObject query) {
        JSONObject object = basicResourceService.getRoadNetworkProjectList(query);
        ExcelUtils.exportExcelSheet(RoadNetworkProjectExportVO.class, object);
    }


}
