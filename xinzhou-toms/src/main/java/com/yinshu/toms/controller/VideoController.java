package com.yinshu.toms.controller;

import com.yinshu.common.ApiConstant;
import com.yinshu.toms.common.ApiResult;
import com.yinshu.toms.service.VideoService;
import com.yinshu.toms.vo.video.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 视频
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping(ApiConstant.API_TOMS_PREFIX + "/video")
public class VideoController {

    @Autowired
    private VideoService videoService;

    /**
     * 1. 通过车牌或设备编号获取车辆ID
     */
    @GetMapping("/vehicle/simple-info")
    public ApiResult<VehicleSimpleInfo> getSimpleInfo(
            @RequestParam(required = false) String vehicleNo,
            @RequestParam(required = false) String deviceNo) {
        return videoService.getVehicleSimpleInfo(vehicleNo, deviceNo);
    }

    /**
     * 2. 查询通道名称和编号
     */
    @GetMapping("/vehicle/channel-names")
    public ApiResult<List<ChannelName>> queryChannelNames() {
        return videoService.queryChannelNames();
    }

    /**
     * 3. 通过车辆ID查询设备详情（含devId和channelNo）
     */
    @GetMapping("/vehicle/detail")
    public ApiResult<VehicleDetail> getVehicleDetail(@RequestParam Long vehicleId) {
        return videoService.getVehicleDetail(vehicleId);
    }

    /**
     * 4. 查询设备回放月历
     */
    @GetMapping("/device/calendar")
    public ApiResult<List<CalendarInfo>> queryCalendar(
            @RequestParam String devId,
            @RequestParam String channels,
            @RequestParam String beginDate, // 格式：yyyy-MM
            @RequestParam String endTime) { // 格式：yyyy-MM
        return videoService.queryDeviceCalendar(devId, channels, beginDate, endTime);
    }

    /**
     * 5. 查询设备媒体文件（建议时间范围≤1天）
     */
    @GetMapping("/device/files")
    public ApiResult<List<MediaFile>> queryFiles(
            @RequestParam String devId,
            @RequestParam String chan,
            @RequestParam String beginTime, // 格式：yyyy-MM-dd HH:mm:ss
            @RequestParam String endTime,   // 格式：yyyy-MM-dd HH:mm:ss
            @RequestParam Integer timeout) { // 10~60秒
        return videoService.queryDeviceFiles(devId, chan, beginTime, endTime, timeout);
    }

    /**
     * 6. 创建视频下载任务
     */
    @PostMapping("/device/task/create")
    public ApiResult<TaskInfo> createDownloadTask(@RequestBody TaskCreateParam param) {
        return videoService.createVideoDownloadTask(param);
    }

    /**
     * 7. 查询下载任务详情
     */
    @GetMapping("/device/task/detail")
    public ApiResult<TaskDetail> getTaskDetail(@RequestParam String taskId) {
        return videoService.getDownloadTaskDetail(taskId);
    }
}
