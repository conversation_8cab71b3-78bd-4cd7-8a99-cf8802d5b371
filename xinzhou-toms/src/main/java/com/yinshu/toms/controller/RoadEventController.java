package com.yinshu.toms.controller;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.fast.excel.util.ExcelUtils;
import com.yinshu.toms.service.RoadEventService;
import com.yinshu.toms.vo.road.transport.RoadEventDetailExportVO;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 公路路网-事件
 *
 */
@RestController
@RequestMapping(ApiConstant.API_TOMS_PREFIX + "/road-network/event")
public class RoadEventController {

    @Autowired
    private RoadEventService roadEventService;

    @PostMapping("/getRoadEvent")
    public ResultVO<?> getRoadEvent(@RequestBody JSONObject query) {
        return ResultVO.suc(roadEventService.getRoadEvent(query));
    }


    @PostMapping("/getRoadEventRank")
    public ResultVO<?> getRoadEventRank(@RequestBody JSONObject query) {
        return ResultVO.suc(roadEventService.getRoadEventRank(query));
    }

    @PostMapping("/getRoadEventDetail")
    public ResultVO<?> getRoadEventDetail(@RequestBody JSONObject query) {
        return ResultVO.suc(roadEventService.getRoadEventDetail(query));
    }

    @PostMapping("/exportRoadEventDetail")
    public void exportRoadDisasterDetail(@RequestBody JSONObject query) {
        JSONObject object = roadEventService.getRoadEventDetail(query);
        ExcelUtils.exportExcelSheet(RoadEventDetailExportVO.class, object);
    }

    @PostMapping("/getRoadEventProportion")
    public ResultVO<?> getRoadEventProportion(@RequestBody JSONObject query) {
        return ResultVO.suc(roadEventService.getRoadEventProportion(query));
    }

}
