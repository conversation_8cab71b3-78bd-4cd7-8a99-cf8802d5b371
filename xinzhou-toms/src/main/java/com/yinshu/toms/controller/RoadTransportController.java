package com.yinshu.toms.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.fast.excel.util.ExcelUtils;
import com.yinshu.toms.service.RoadTransportService;
import com.yinshu.toms.vo.road.transport.*;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 道路运输专题
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(ApiConstant.API_TOMS_PREFIX + "/road-transport")
public class RoadTransportController {

    @Resource
    private RoadTransportService service;

    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 基础指标
     * @date 2025/6/16 11:20
     **/
    @PostMapping("/ads_basic_indicator_data")
    public ResultVO<JSONObject> adsBasicIndicatorData(@RequestBody JSONObject query) {
        JSONObject object = service.adsBasicIndicatorData(query);
        return ResultVO.suc(object);
    }

    /**
     * @param query 查询类
     * <AUTHOR>
     * @description //TODO 基础指标导出
     * @date 2025/6/16 11:20
     **/
    @PostMapping("/ads_basic_indicator_data_export")
    public void adsBasicIndicatorDataExport(@RequestBody JSONObject query) {
        JSONObject object = service.adsBasicIndicatorData(query);
        if (query.containsKey("type")) {
            switch (query.getString("type")) {
                case "客运":
                    ExcelUtils.exportExcelSheet(DepartureScheduleExportVO.class, object);
                    break;
                case "货运":
                    break;
                case "危货运":
                    ExcelUtils.exportExcelSheet(DangerousDepartureScheduleExportVO.class, object);
                    break;
            }
        }
    }


    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 客运站点监测
     * @date 2025/6/16 11:20
     **/
    @PostMapping("/ads_passenger_transport_station")
    public ResultVO<JSONObject> adsPassengerTransportStation(@RequestBody JSONObject query) {
        JSONObject object = service.adsPassengerTransportStation(query);
        return ResultVO.suc(object);
    }

    /**
     * @param query 查询类
     * <AUTHOR>
     * @description //TODO 客运站点监测导出
     * @date 2025/6/16 11:20
     **/
    @PostMapping("/ads_passenger_transport_station_export")
    public void adsPassengerTransportStationExport(@RequestBody JSONObject query) {
        JSONObject object = service.adsPassengerTransportStation(query);
        ExcelUtils.exportExcelSheet(TransportStationExportVO.class, object);
    }


    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 今日概览趋势小时
     * @date 2025/6/16 11:20
     **/
    @PostMapping("/ads_daily_overview_trend_hourly")
    public ResultVO<JSONObject> adsDailyOverviewTrendHourly(@RequestBody JSONObject query) {
        JSONObject object = service.adsDailyOverviewTrendHourly(query);
        return ResultVO.suc(object);
    }


    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 每日概览趋势
     * @date 2025/6/16 11:20
     **/
    @PostMapping("/ads_daily_overview_trend")
    public ResultVO<JSONObject> adsDailyOverviewTrend(@RequestBody JSONObject query) {
        JSONObject object = service.adsDailyOverviewTrend(query);
        return ResultVO.suc(object);
    }

    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 每日概览趋势导出
     * @date 2025/6/16 11:20
     **/
    @PostMapping("/ads_daily_overview_trend_export")
    public void adsDailyOverviewTrendExport(@RequestBody JSONObject query) {
        JSONObject object = service.adsDailyOverviewTrend(query);
        if (query.containsKey("type")) {
            switch (query.getString("type")) {
                case "客运":
                    ExcelUtils.exportExcelSheet(PassengerStationInOutCityExportVO.class, object);
                    break;
                case "危货运":
                    ExcelUtils.exportExcelSheet(DangerousGoodsOperationVehiclesExportVO.class, object);
                    break;
            }
        }
    }


    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 危货运量周变化趋势
     * @date 2025/6/16 11:20
     **/
    @PostMapping("/ads_dangerous_goods_vol_weekly_trend")
    public ResultVO<JSONObject> adsDangerousGoodsVolWeeklyTrend(@RequestBody JSONObject query) {
        JSONObject object = service.adsDangerousGoodsVolWeeklyTrend(query);
        return ResultVO.suc(object);
    }


    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 道路运输概览占比
     * @date 2025/6/16 11:20
     **/
    @PostMapping("/ads_road_transport_overview_ratio")
    public ResultVO<JSONObject> adsRoadTransportOverviewRatio(@RequestBody JSONObject query) {
        JSONObject object = service.adsRoadTransportOverviewRatio(query);
        return ResultVO.suc(object);
    }


    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 普通货运车数量详情
     * @date 2025/6/16 11:20
     **/
    @PostMapping("/ads_general_freight_truck_quantity_detail")
    public ResultVO<JSONObject> adsGeneralFreightTruckQuantityDetail(@RequestBody JSONObject query) {
        JSONObject object = service.adsGeneralFreightTruckQuantityDetail(query);
        return ResultVO.suc(object);
    }

    /**
     * @param query 查询类
     * <AUTHOR>
     * @description //TODO 普通货运车数量详情导出
     * @date 2025/6/16 11:20
     **/
    @PostMapping("/ads_general_freight_truck_quantity_detail_export")
    public void adsGeneralFreightTruckQuantityDetailExport(@RequestBody JSONObject query) {
        JSONObject object = service.adsGeneralFreightTruckQuantityDetail(query);
        ExcelUtils.exportExcelSheet(OrdinaryFreightVehiclesExportVO.class, object);
    }


    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 运输地图车辆基本信息
     * @date 2025/6/16 11:20
     **/
    @PostMapping("/ads_transport_map_vehicle_base_info")
    public ResultVO<JSONObject> adsTransportMapVehicleBaseInfo(@RequestBody JSONObject query) {
        JSONObject object = service.adsTransportMapVehicleBaseInfo(query);
        return ResultVO.suc(object);
    }


    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 预警车辆详情
     * @date 2025/6/16 11:20
     **/
    @PostMapping("/ads_warning_vehicle_detail")
    public ResultVO<JSONObject> adsWarningVehicleDetail(@RequestBody JSONObject query) {
        JSONObject object = service.adsWarningVehicleDetail(query);
        return ResultVO.suc(object);
    }

    /**
     * @param query 查询类
     * <AUTHOR>
     * @description //TODO 预警车辆详情导出
     * @date 2025/6/16 11:20
     **/
    @PostMapping("/ads_warning_vehicle_detail_export")
    public void adsWarningVehicleDetailExport(@RequestBody JSONObject query) {
        JSONObject object = service.adsWarningVehicleDetail(query);
        JSONArray list = object.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject jsonObject = list.getJSONObject(i);
            if (jsonObject.containsKey("warnlevel")) {
                switch (jsonObject.getString("warnlevel")) {
                    case "0":
                        jsonObject.put("warnlevel", "无");
                        break;
                    case "1":
                        jsonObject.put("warnlevel", "一级");
                        break;
                    case "2":
                        jsonObject.put("warnlevel", "二级");
                        break;
                    case "3":
                        jsonObject.put("warnlevel", "三级");
                        break;
                    case "4":
                        jsonObject.put("warnlevel", "四级");
                        break;
                    case "5":
                        jsonObject.put("warnlevel", "五级");
                }
            }
        }
        ExcelUtils.exportExcelSheet(WarningVehicleDetailExportVO.class, object);
    }


    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 危货运数据排名
     * @date 2025/6/16 11:20
     **/
    @PostMapping("/ads_dg_data_rank")
    public ResultVO<JSONObject> adsDgDataRank(@RequestBody JSONObject query) {
        JSONObject object = service.adsDgDataRank(query);
        return ResultVO.suc(object);
    }


}
