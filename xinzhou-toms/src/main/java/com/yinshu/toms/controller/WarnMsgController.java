package com.yinshu.toms.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.fast.excel.util.ExcelUtils;
import com.yinshu.toms.service.WarnMsgService;
import com.yinshu.toms.vo.road.transport.ProjectInfoExportVO;
import com.yinshu.toms.vo.road.transport.WarningVehicleDetailExportVO;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping(ApiConstant.API_TOMS_PREFIX + "/message/warnMsg")
public class WarnMsgController {
    @Resource
    private SimpMessagingTemplate simpMessagingTemplate;

    @Autowired
    private WarnMsgService warnMsgService;

    @MessageMapping("/sendMsg")
    public void sendMsg(String message) {
        System.out.println(message);
        simpMessagingTemplate.convertAndSend("/topic/warnMsg", message);
    }

    @RequestMapping("/pushMsg")
    public ResultVO<?> pushMsg(@RequestParam Map<String, Object> params,
                                             @RequestBody Map<String, Object> data) {
        Map<String, Object> msgMap = new HashMap<>();
        msgMap.putAll(params);
        msgMap.putAll(data);
        String msg = JSON.toJSONString(msgMap);
        simpMessagingTemplate.convertAndSend("/topic/warnMsg", msg);
        return ResultVO.suc("消息发送成功。");
    }

    /**
     *获取告警类型信息
     * @param query
     * @return
     */
    @PostMapping("/getWarnTypeInfo")
    public ResultVO<?> getWarnTypeInfo(@RequestBody JSONObject query) {
        return ResultVO.suc(warnMsgService.getWarnTypeInfo(query));
    }

    /**
     *根据id获取告警配置
     * @param query
     * @return
     */
    @PostMapping("/getWarnConfig")
    public ResultVO<?> getWarnConfig(@RequestBody JSONObject query) {
        return ResultVO.suc(warnMsgService.getWarnConfig(query));
    }

    /**
     *根据id修改告警配置
     * @param query
     * @return
     */
    @PostMapping("/updateWarnConfig")
    public ResultVO<?> updateWarnConfig(@RequestBody JSONObject query) {
        return ResultVO.suc(warnMsgService.updateWarnConfig(query));
    }

    /**
     *获取告警数量统计
     * @param query
     * @return
     */
    @PostMapping("/getWarnInfoUnreadCount")
    public ResultVO<?> getWarnInfoUnreadCount(@RequestBody JSONObject query) {
        return ResultVO.suc(warnMsgService.getWarnInfoUnreadCount(query));
    }

    /**
     *获取告警记录
     * @param query
     * @return
     */
    @PostMapping("/getWarnInfoList")
    public ResultVO<?> getWarnInfoList(@RequestBody JSONObject query) {
        return ResultVO.suc(warnMsgService.getWarnInfoList(query));
    }

    /**
     *导出告警记录
     * @param query
     * @return
     */
    @PostMapping("/exportWarnInfoList")
    public void exportWarnInfoList(@RequestBody JSONObject query) {
        JSONObject object = warnMsgService.getWarnInfoList(query);
        JSONArray list = object.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject jsonObject = list.getJSONObject(i);
            if (jsonObject.containsKey("warnlevel")) {
                switch (jsonObject.getString("warnlevel")) {
                    case "0":
                        jsonObject.put("warnlevel", "无");
                        break;
                    case "1":
                        jsonObject.put("warnlevel", "一级");
                        break;
                    case "2":
                        jsonObject.put("warnlevel", "二级");
                        break;
                    case "3":
                        jsonObject.put("warnlevel", "三级");
                        break;
                    case "4":
                        jsonObject.put("warnlevel", "四级");
                        break;
                    case "5":
                        jsonObject.put("warnlevel", "五级");
                }
            }
        }
        ExcelUtils.exportExcelSheet(WarningVehicleDetailExportVO.class, object);
    }

    /**
     *根据id设置为已读
     * @param query
     * @return
     */
    @PostMapping("/updateWarInfoRead")
    public ResultVO<?> updateWarInfoRead(@RequestBody JSONObject query) {
        return ResultVO.suc(warnMsgService.updateWarInfoRead(query));
    }


}

