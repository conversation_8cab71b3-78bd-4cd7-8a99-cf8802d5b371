package com.yinshu.toms.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.toms.service.AviationService;
import com.yinshu.toms.service.VehicleService;
import com.yinshu.utils.ResultVO;

/**
 * 车辆检测（实时定位/实时轨迹/历史轨迹等）
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping(ApiConstant.API_TOMS_PREFIX + "/vehicle")
public class VehicleController {

    @Autowired
    private VehicleService vehicleService;
    
    /**
	 * 根据车牌号码获取实时定位
	 */
    @PostMapping("/getRealLocation")
    public ResultVO<?> getRealLocation(@RequestBody JSONObject query) {
        JSONObject object = vehicleService.getRealLocation(query);
        return ResultVO.suc(object);
    }

    /**
     * 获取历史轨迹
     */
    @PostMapping("/getHistoryLocation")
    public ResultVO<?> getHistoryLocation(@RequestBody JSONObject query) {
        return ResultVO.suc(vehicleService.getHistoryLocation(query));
    }
    
    /**
	 * 获取多辆车的实时定位
	 * 
	 * @return
	 */
    @PostMapping("/getMultiRealLocation")
    public ResultVO<?> getMultiRealLocation(@RequestBody JSONObject query) {
        JSONObject object = vehicleService.getMultiRealLocation(query);
        return ResultVO.suc(object);
    }

}
