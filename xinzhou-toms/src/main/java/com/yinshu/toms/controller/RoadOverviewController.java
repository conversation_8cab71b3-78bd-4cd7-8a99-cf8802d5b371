package com.yinshu.toms.controller;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.toms.service.AviationService;
import com.yinshu.toms.service.RoadOverviewService;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 公路路网-路网概况
 *
 */
@RestController
@RequestMapping(ApiConstant.API_TOMS_PREFIX + "/road-network/overview")
public class RoadOverviewController {

    @Autowired
    private RoadOverviewService overviewService;

    @PostMapping("/getRoadBaseInfo")
    public ResultVO<?> getRoadBaseInfo(@RequestBody JSONObject query) {
        JSONObject object = overviewService.getRoadBaseInfo(query);
        return ResultVO.suc(object);
    }

    @PostMapping("/getTrafficInfo")
    public ResultVO<?> getTrafficInfo(@RequestBody JSONObject query) {
        JSONObject object = overviewService.getTrafficInfo(query);
        return ResultVO.suc(object);
    }

    @PostMapping("/getRoadMileage")
    public ResultVO<?> getRoadMileage(@RequestBody JSONObject query) {
        JSONObject object = overviewService.getRoadMileage(query);
        return ResultVO.suc(object);
    }


}
