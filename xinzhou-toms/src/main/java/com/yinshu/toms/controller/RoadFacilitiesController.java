package com.yinshu.toms.controller;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.fast.excel.util.ExcelUtils;
import com.yinshu.toms.service.RoadFacilitiesService;
import com.yinshu.toms.vo.road.transport.DeviceDetailsExportVO;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 公路路网-设施
 *
 */
@RestController
@RequestMapping(ApiConstant.API_TOMS_PREFIX + "/road-network/facilities")
public class RoadFacilitiesController {

    @Autowired
    private RoadFacilitiesService roadFacilitiesService;

    @PostMapping("/getBasicInfo")
    public ResultVO<?> getBasicInfo(@RequestBody JSONObject query) {
        return ResultVO.suc(roadFacilitiesService.getBasicInfo(query));
    }

    @PostMapping("/getDeviceDetails")
    public ResultVO<?> getDeviceDetails(@RequestBody JSONObject query) {
        return ResultVO.suc(roadFacilitiesService.getDeviceDetails(query));
    }

    @PostMapping("/exportDeviceDetails")
    public void exportDeviceDetails(@RequestBody JSONObject query) {
        JSONObject object = roadFacilitiesService.getDeviceDetails(query);
        ExcelUtils.exportExcelSheet(DeviceDetailsExportVO.class, object);
    }

    @PostMapping("/getDeviceOfflineRank")
    public ResultVO<?> getDeviceOfflineRank(@RequestBody JSONObject query) {
        return ResultVO.suc(roadFacilitiesService.getDeviceOfflineRank(query));
    }


}
