package com.yinshu.toms.controller;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.fast.excel.util.ExcelUtils;
import com.yinshu.toms.service.RoadMaintainService;
import com.yinshu.toms.service.RoadOverviewService;
import com.yinshu.toms.vo.road.transport.RoadDisasterExportVO;
import com.yinshu.toms.vo.road.transport.RoadMQIRankExportVO;
import com.yinshu.toms.vo.road.transport.TransportStationExportVO;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 公路路网-养护
 *
 */
@RestController
@RequestMapping(ApiConstant.API_TOMS_PREFIX + "/road-network/maintain")
public class RoadMaintainController {

    @Autowired
    private RoadMaintainService roadMaintainService;

    @PostMapping("/getRoadDisasterTrend")
    public ResultVO<?> getRoadDisasterTrend(@RequestBody JSONObject query) {
        JSONObject object = roadMaintainService.getRoadDisasterTrend(query);
        return ResultVO.suc(object);
    }

    @PostMapping("/getRoadDisasterDetail")
    public ResultVO<?> getRoadDisasterDetail(@RequestBody JSONObject query) {
        JSONObject object = roadMaintainService.getRoadDisasterDetail(query);
        return ResultVO.suc(object);
    }

    @PostMapping("/exportRoadDisasterDetail")
    public void exportRoadDisasterDetail(@RequestBody JSONObject query) {
        JSONObject object = roadMaintainService.getRoadDisasterDetail(query);
        ExcelUtils.exportExcelSheet(RoadDisasterExportVO.class, object);
    }

    @PostMapping("/getRoadTechnicalCondition")
    public ResultVO<?> getRoadTechnicalCondition(@RequestBody JSONObject query) {
        JSONObject object = roadMaintainService.getRoadTechnicalCondition(query);
        return ResultVO.suc(object);
    }

    @PostMapping("/getRoadMQIRank")
    public ResultVO<?> getRoadMQIRank(@RequestBody JSONObject query) {
        JSONObject object = roadMaintainService.getRoadMQIRank(query);
        return ResultVO.suc(object);
    }

    @PostMapping("/exportRoadMQIRank")
    public void exportRoadMQIRank(@RequestBody JSONObject query) {
        JSONObject object = roadMaintainService.getRoadMQIRank(query);
        ExcelUtils.exportExcelSheet(RoadMQIRankExportVO.class, object);
    }

}
