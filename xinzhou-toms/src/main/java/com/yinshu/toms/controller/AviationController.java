package com.yinshu.toms.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.toms.service.AviationService;
import com.yinshu.utils.DateUtils;
import com.yinshu.utils.ResultVO;

/**
 * 民航/铁路专题
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping(ApiConstant.API_TOMS_PREFIX + "/aviation-railway/aviation")
public class AviationController {

    @Autowired
    private AviationService aviationService;
    
    /**
	 * 基础指标
	 */
    @PostMapping("/getTotalInfo")
    public ResultVO<?> getTotalInfo(@RequestBody JSONObject query) {
        JSONObject object = aviationService.getTotalInfo(query);
        return ResultVO.suc(object);
    }
    
    /**
	 * 航班数
	 */
    @PostMapping("/getArriverOutList")
    public ResultVO<?> getArriverOutList(@RequestBody JSONObject query) {
        JSONObject object = aviationService.getArriverOutList(query);
        return ResultVO.suc(object);
    }
    
    /**
	 * 航班统计情况
	 */
    @PostMapping("/getAnalysisList")
    public ResultVO<?> getAnalysisList(@RequestBody JSONObject query) {
        JSONObject object = aviationService.getAnalysisList(query);
        return ResultVO.suc(object);
    }
    
    /**
	 * 民航客运量监测
	 */
    @PostMapping("/getPassengerList")
    public ResultVO<?> getPassengerList(@RequestBody JSONObject query) {
        JSONObject object = aviationService.getPassengerList(query);
        return ResultVO.suc(object);
    }
    
    /**
	 * 民航客运量监测分页列表
	 */
    @PostMapping("/getPassengerPageList")
    public ResultVO<?> getPassengerPageList(@RequestBody JSONObject query) {
        JSONObject object = aviationService.getPassengerPageList(query);
        return ResultVO.suc(object);
    }
    
    /**
	 * 民航货运量监测
	 */
    @PostMapping("/getCargoList")
    public ResultVO<?> getCargoList(@RequestBody JSONObject query) {
        JSONObject object = aviationService.getCargoList(query);
        return ResultVO.suc(object);
    }
    
    /**
	 * 机场大巴运行
	 */
    @PostMapping("/getBusList")
    public ResultVO<?> getBusList(@RequestBody JSONObject query) {
        JSONObject object = aviationService.getBusList(query);
        return ResultVO.suc(object);
    }
    
    /**
     * 获取车辆信息
     */
    @PostMapping("/getVehicleList")
    public ResultVO<?> getVehicleList(@RequestBody JSONObject query) {
        JSONObject object = aviationService.getVehicleList(query);
        return ResultVO.suc(object);
    }
    
    
    @PostMapping("/getWarningTotal")
    public ResultVO<?> getWarningTotal(@RequestBody JSONObject query) {
    	JSONObject object = aviationService.getWarningTotal(query);
        return ResultVO.suc(object);
    }
    /**
     * 执法研判
     */
    @PostMapping("/getWarningList")
    public ResultVO<?> getWarningList(@RequestBody JSONObject query) {
        JSONObject object = aviationService.getWarningList(query);
        return ResultVO.suc(object);
    }
    
    /**
     * 导出excel
     */
    @PostMapping("/exportXls")
    public void exportXls(@RequestBody JSONObject query) {
    	aviationService.exportXls(query);
    }
    
    
}
