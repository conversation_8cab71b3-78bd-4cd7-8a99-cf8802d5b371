package com.yinshu.toms.controller;


import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.toms.service.CityTrafficService;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(ApiConstant.API_TOMS_PREFIX + "/city-traffic/city")
public class CityTrafficController {

    @Autowired
    private CityTrafficService cityTrafficService;

    /**
     * 获取城市交通概览
     * @param jsonObject
     * @return
     */
    @PostMapping("/getOverallSummary")
    public ResultVO<?> getOverallSummary(@RequestBody JSONObject jsonObject){
        return ResultVO.suc(cityTrafficService.getOverallSummary(jsonObject));
    }

    /**
     * 获取车辆分类
     * @param jsonObject
     * @return
     */
    @PostMapping("/getVehicleClassification")
    public ResultVO<?> getVehicleClassification(@RequestBody JSONObject jsonObject){
        return ResultVO.suc(cityTrafficService.getVehicleClassification(jsonObject));
    }

    /**
     * 获取运力分类
     * @param jsonObject
     * @return
     */
    @PostMapping("/getCapacityClassification")
    public ResultVO<?> getCapacityClassification(@RequestBody JSONObject jsonObject){
        return ResultVO.suc(cityTrafficService.getCapacityClassification(jsonObject));
    }

    /**
     *  公交车、巡游车、网约车基础数据
     * @param jsonObject
     * @return
     */
    @PostMapping("/getBasicData")
    public ResultVO<?> getBasicData(@RequestBody JSONObject jsonObject){
        return ResultVO.suc(cityTrafficService.getBasicData(jsonObject));
    }

    /**
     * 营运速度
     * @param jsonObject
     * @return
     */
    @PostMapping("/getOperationSummary")
    public ResultVO<?> getOperationSummary(@RequestBody JSONObject jsonObject){
        return ResultVO.suc(cityTrafficService.getOperationSummary(jsonObject));
    }

    /**
     * 公交车燃料类型占比
     * @param jsonObject
     * @return
     */
    @PostMapping("/getBusFuelType")
    public ResultVO<?> getBusFuelType(@RequestBody JSONObject jsonObject){
        return ResultVO.suc(cityTrafficService.getBusFuelType(jsonObject));
    }

    /**
     * 公交车线路详情
     * @param jsonObject
     * @return
     */
    @PostMapping("/getBusRouteDetail")
    public ResultVO<?> getBusRouteDetail(@RequestBody JSONObject jsonObject){
        return ResultVO.suc(cityTrafficService.getBusRouteDetail(jsonObject));
    }

    /**
     * 公交车站点详情
     * @param jsonObject
     * @return
     */
    @PostMapping("/getBusStationDetail")
    public ResultVO<?> getBusStationDetail(@RequestBody JSONObject jsonObject){
        return ResultVO.suc(cityTrafficService.getBusStationDetail(jsonObject));
    }

    /**
     * 客运地图的数据 公交车、巡游车、网约车
     * @param jsonObject
     * @return
     */
    @PostMapping("/getPassengerMapData")
    public ResultVO<?> getPassengerMapData(@RequestBody JSONObject jsonObject){
        return ResultVO.suc(cityTrafficService.getPassengerMapData(jsonObject));
    }

    /**
     * 巡游车/网约车 运营数据
     * @param jsonObject
     * @return
     */
    @PostMapping("/getOperationOverview")
    public ResultVO<?> getOperationOverview(@RequestBody JSONObject jsonObject){
        return ResultVO.suc(cityTrafficService.getOperationOverview(jsonObject));
    }

    /**
     * 营业时长排名
     * @param jsonObject
     * @return
     */
    @PostMapping("/getBusinessHoursRanking")
    public ResultVO<?> getBusinessHoursRanking(@RequestBody JSONObject jsonObject){
        return ResultVO.suc(cityTrafficService.getBusinessHoursRanking(jsonObject));
    }

    /**
     * 公交车、巡游车、网约车 监测预警
     * @param jsonObject
     * @return
     */
    @PostMapping("/getVehicleMonitorWarning")
    public ResultVO<?> getVehicleMonitorWarning(@RequestBody JSONObject jsonObject){
        return ResultVO.suc(cityTrafficService.getVehicleMonitorWarning(jsonObject));
    }

    /**
     * 疑似黑车的数据
     * @param jsonObject
     * @return
     */
    @PostMapping("/getSuspectedBlackCarData")
    public ResultVO<?> getSuspectedBlackCarData(@RequestBody JSONObject jsonObject){
        return ResultVO.suc(cityTrafficService.getSuspectedBlackCarData(jsonObject));
    }

    /**
     * OD分析
     * @param jsonObject
     * @return
     */
    @PostMapping("/getODAnalysis")
    public  ResultVO<?> getODAnalysis(@RequestBody JSONObject jsonObject){
        return ResultVO.suc(cityTrafficService.getODAnalysis(jsonObject));
    }

    /**
     * 导出
     * @param jsonObject
     */
    @PostMapping("/getExport")
    public void getExport(@RequestBody JSONObject jsonObject){
        cityTrafficService.getExport(jsonObject);
    }

}
