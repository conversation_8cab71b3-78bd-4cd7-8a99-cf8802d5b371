package com.yinshu.toms.controller;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.fast.excel.util.ExcelUtils;
import com.yinshu.toms.service.RoadConstructService;
import com.yinshu.toms.vo.road.transport.ProjectInfoExportVO;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 公路路网-建设工程指标
 *
 */
@RestController
@RequestMapping(ApiConstant.API_TOMS_PREFIX + "/road-network/construct")
public class RoadConstructController {

    @Autowired
    private RoadConstructService roadConstructService;

    @PostMapping("/getIndicator")
    public ResultVO<?> getIndicator(@RequestBody JSONObject query) {
        return ResultVO.suc(roadConstructService.getIndicator(query));
    }

    @PostMapping("/getProjectInfo")
    public ResultVO<?> getProjectInfo(@RequestBody JSONObject query) {
        return ResultVO.suc(roadConstructService.getProjectInfo(query));
    }

    @PostMapping("/exportProjectInfo")
    public void exportProjectInfo(@RequestBody JSONObject query) {
        JSONObject object = roadConstructService.getProjectInfo(query);
        ExcelUtils.exportExcelSheet(ProjectInfoExportVO.class, object);
    }

    @PostMapping("/getConstructionProgress")
    public ResultVO<?> getConstructionProgress(@RequestBody JSONObject query) {
        return ResultVO.suc(roadConstructService.getConstructionProgress(query));
    }

}
