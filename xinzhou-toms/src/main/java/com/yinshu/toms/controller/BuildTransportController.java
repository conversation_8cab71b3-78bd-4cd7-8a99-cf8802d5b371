package com.yinshu.toms.controller;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.fast.excel.util.ExcelUtils;
import com.yinshu.toms.service.BuildTransportService;
import com.yinshu.toms.vo.road.transport.DangerousDepartureScheduleExportVO;
import com.yinshu.toms.vo.road.transport.ProjectInfoVO;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 在建交通
 */
@RestController
@RequestMapping(ApiConstant.API_TOMS_PREFIX + "/build-transport/invest")
public class BuildTransportController {

    @Autowired
    private BuildTransportService buildTransportService;

    /**
     * 获取投资进度在建工程
     * @param query
     * @return
     */
    @PostMapping("/getInvestmentProgress")
    public ResultVO<?> getInvestmentProgress(@RequestBody JSONObject query) {
        JSONObject object = buildTransportService.getInvestmentProgress(query);
        return ResultVO.suc(object);
    }

    /**
     * 获取工程项目信息录入
     * @param query
     * @return
     */
    @PostMapping("/getProjectInfo")
    public ResultVO<?> getProjectInfo(@RequestBody JSONObject query) {
        JSONObject object = buildTransportService.getProjectInfo(query);
        return ResultVO.suc(object);
    }

    /**
     * 工程项目信息导出
     * @param query
     * @return
     */
    @PostMapping("/exportProjectInfo")
    public void exportProjectInfo(@RequestBody JSONObject query) {
        JSONObject object = buildTransportService.getProjectInfo(query);
        ExcelUtils.exportExcelSheet(ProjectInfoVO.class, object);
    }
}
