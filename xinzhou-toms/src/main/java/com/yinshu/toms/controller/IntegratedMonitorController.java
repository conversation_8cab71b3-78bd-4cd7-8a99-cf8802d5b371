package com.yinshu.toms.controller;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.toms.service.IntegratedMonitorService;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 综合监测
 */
@RestController
@RequestMapping(ApiConstant.API_TOMS_PREFIX + "/integrated-monitor")
public class IntegratedMonitorController {

    @Resource
    private IntegratedMonitorService service;

    /**
     * 营运车辆概览、经营业户概览、从业人员概览
     * @param query
     * @return
     */
    @PostMapping("/getBaseInfo")
    public ResultVO<?> getBaseInfo(@RequestBody JSONObject query) {
        JSONObject object = service.getBaseInfo(query);
        return ResultVO.suc(object);
    }

    /**
     * 实时对接数据
     * @param query
     * @return
     */
    @PostMapping("/getDockingData")
    public ResultVO<?> getDockingData(@RequestBody JSONObject query) {
        JSONObject object = service.getDockingData(query);
        return ResultVO.suc(object);
    }

    /**
     * 公路概况
     * @param query
     * @return
     */
    @PostMapping("/getRoadMileage")
    public ResultVO<?> getRoadMileage(@RequestBody JSONObject query) {
        JSONObject object = service.getRoadMileage(query);
        return ResultVO.suc(object);
    }

    /**
     * 桥梁
     * @param query
     * @return
     */
    @PostMapping("/getRoadBridge")
    public ResultVO<?> getRoadBridge(@RequestBody JSONObject query) {
        JSONObject object = service.getRoadBridge(query);
        return ResultVO.suc(object);
    }

    /**
     * 隧道
     * @param query
     * @return
     */
    @PostMapping("/getRoadTunnel")
    public ResultVO<?> getRoadTunnel(@RequestBody JSONObject query) {
        JSONObject object = service.getRoadTunnel(query);
        return ResultVO.suc(object);
    }

}
