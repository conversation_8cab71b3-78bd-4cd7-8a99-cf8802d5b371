package com.yinshu.toms.vo.road.transport;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TransportStationExportVO
 * @description TODO 客运站点监测导出
 * @date 2025/6/16 16:55
 **/
@Data
public class TransportStationExportVO {
    @ExcelProperty(value = "序号")
    @ColumnWidth(15)
    private Integer no;
    @ExcelProperty(value = "时间")
    @ColumnWidth(25)
    private LocalDate dt;
    @ExcelProperty(value = "客运站名称")
    private String name;
    @ExcelProperty(value = "计划发车班次")
    private Integer planNum;
    @ExcelProperty(value = "实际发车班次")
    private Integer ActualNum;
}
