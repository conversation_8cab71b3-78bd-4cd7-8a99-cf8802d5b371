package com.yinshu.toms.vo.road.transport;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import cn.idev.excel.annotation.write.style.ContentStyle;
import cn.idev.excel.annotation.write.style.HeadStyle;
import cn.idev.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

import java.math.BigDecimal;

@Data
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 表头居中
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 内容居中
public class RoadMQIRankExportVO {
    @ExcelProperty(value = "序号")
    @ColumnWidth(15)
    private Integer no;

    @ExcelProperty(value = "行政区")
    private String county;

    @ExcelProperty(value = "路线编号")
    private String roadCode;

    @ExcelProperty(value = "路线名称")
    private String roadName;

    @ExcelProperty(value = "MQI")
    private BigDecimal mqi;

    @ExcelProperty(value = "PQI")
    private BigDecimal pqi;

    @ExcelProperty(value = "SCI")
    private BigDecimal sci;

    @ExcelProperty(value = "BCI")
    private BigDecimal bci;

    @ExcelProperty(value = "TCI")
    private String tci;
}
