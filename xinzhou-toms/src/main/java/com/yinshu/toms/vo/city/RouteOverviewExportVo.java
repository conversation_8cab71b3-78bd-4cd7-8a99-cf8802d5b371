package com.yinshu.toms.vo.city;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import cn.idev.excel.annotation.write.style.ContentStyle;
import cn.idev.excel.annotation.write.style.HeadStyle;
import cn.idev.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

import java.math.BigDecimal;

@Data
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 表头居中
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 内容居中
public class RouteOverviewExportVo {

    @ExcelProperty(value = "序号")
    @ColumnWidth(15)
    private Integer no;

    @ExcelProperty(value = "路线名称")
    @ColumnWidth(15)
    private String lineName;

    @ExcelProperty(value = "路线类型")
    @ColumnWidth(15)
    private String lineType;

    @ExcelProperty(value = "路线长度")
    @ColumnWidth(15)
    private String routeLength;

    @ExcelProperty(value = "站点数量")
    @ColumnWidth(15)
    private BigDecimal stationNum;

    @ExcelProperty(value = "平均站距")
    @ColumnWidth(15)
    private double averageStopDistance;
}
