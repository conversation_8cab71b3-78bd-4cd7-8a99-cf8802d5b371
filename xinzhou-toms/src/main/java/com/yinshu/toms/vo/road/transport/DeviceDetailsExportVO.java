package com.yinshu.toms.vo.road.transport;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import cn.idev.excel.annotation.write.style.ContentStyle;
import cn.idev.excel.annotation.write.style.HeadStyle;
import cn.idev.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

@Data
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 表头居中
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 内容居中
public class DeviceDetailsExportVO {
    @ExcelProperty(value = "序号")
    @ColumnWidth(15)
    private Integer no;

    @ExcelProperty(value = "行政区")
    @ColumnWidth(25)
    private String county;

    @ExcelProperty(value = "设备名称")
    private String deviceName;

    @ExcelProperty(value = "类型")
    private String type;

    @ExcelProperty(value = "设备编码")
    private String deviceCode;

    @ExcelProperty(value = "设备地址")
    private String deviceAddress;

    @ExcelProperty(value = "运行状态")
    private String status;

    @ExcelProperty(value = "质保状态")
    private String warrantyStatus;

    @ExcelProperty(value = "维护单位")
    private String maintenanceUnit;
}
