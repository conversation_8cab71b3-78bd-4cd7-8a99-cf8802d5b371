package com.yinshu.toms.vo.road.transport;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
public class ProjectInfoVO {

    @ExcelProperty(value = "排行")
    @ColumnWidth(15)
    private Integer no;

    @ExcelProperty(value = "合同日期")
    private String dt;

    @ExcelProperty(value = "项目名称")
    private String projectName;

    @ExcelProperty(value = "立项批文")
    private String approvalDocument;

    @ExcelProperty(value = "公路等级")
    private String highwayClassification;

    @ExcelProperty(value = "项目规模")
    private String projectScale;

    @ExcelProperty(value = "项目法人单位")
    private String projectLegalEntity;

    @ExcelProperty(value = "施工单位名称")
    private String constructionUnitName;

}
