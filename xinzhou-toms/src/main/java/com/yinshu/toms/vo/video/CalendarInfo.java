package com.yinshu.toms.vo.video;

import lombok.Data;
import java.util.List;

/**
 * 设备回放月历检索结果模型
 * 对应接口：/alpha3/api/v1/h5/sdk/device/calendar/query
 * <AUTHOR>
 */
@Data
public class CalendarInfo {
    /**
     * 日期（格式：yyyy-MM-dd）
     */
    private String date;

    /**
     * 录像文件类型
     * 1-普通录像，2-报警录像
     */
    private List<Integer> fileTypes;

    /**
     * 存储类型
     * 1-设备存储
     */
    private List<Integer> storeTypes;
}
