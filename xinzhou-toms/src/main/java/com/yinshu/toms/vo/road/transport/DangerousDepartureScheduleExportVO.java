package com.yinshu.toms.vo.road.transport;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DepartureSchedule
 * @description TODO 危货运详情导出vo
 * @date 2025/6/16 14:38
 **/
@Data
public class DangerousDepartureScheduleExportVO {
    @ExcelProperty(value = "序号")
    @ColumnWidth(10)
    private Integer no;
    @ExcelProperty(value = "时间")
    @ColumnWidth(20)
    private LocalDate dt;
    @ExcelProperty(value = "危货运输车辆")
    private Integer dgTransportVehicles;
    @ExcelProperty(value = "入网率")
    @ColumnWidth(20)
    private Double nar;
    @ExcelProperty(value = "上线率")
    @ColumnWidth(20)
    private Double or;
}
