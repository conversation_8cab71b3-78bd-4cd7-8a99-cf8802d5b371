package com.yinshu.toms.vo.road.transport;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import cn.idev.excel.annotation.write.style.ContentStyle;
import cn.idev.excel.annotation.write.style.HeadStyle;
import cn.idev.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

import java.math.BigDecimal;

@Data
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 表头居中
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 内容居中
public class RoadNetworkExportVO {
    @ExcelProperty(value = "序号")
    @ColumnWidth(15)
    private Integer no;

    @ExcelProperty(value = "行政区")
    private String county;

    @ColumnWidth(30)
    @ExcelProperty(value = "路线编号")
    private String roadCode;

    @ExcelProperty(value = "路线名称")
    private String roadName;

    @ExcelProperty(value = "技术等级")
    private String technicalLevel;

    @ExcelProperty(value = "行政等级")
    private String administrativeLevel;

    @ExcelProperty(value = "里程")
    private BigDecimal mileage;

    @ExcelProperty(value = "管理单位")
    private String managementUnit;

    @ExcelProperty(value = "养护单位")
    private String maintenanceUnit;

}
