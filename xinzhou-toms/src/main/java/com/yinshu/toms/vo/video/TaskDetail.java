package com.yinshu.toms.vo.video;

import lombok.Data;

import java.util.List;
/**
 * 任务详情（detail接口响应）
 * <AUTHOR>
 *
 */
@Data
public class TaskDetail {
    private String taskId;
    private Integer status; // 3-成功，其他状态见文档4.错误码说明
    private List<FileDetail> fileList;

    @Data
    public static class FileDetail {
        private Integer chan;
        private String fileId;
        private Long fileSize;
        private String fileUrl;
        private String streamType;
    }
}
