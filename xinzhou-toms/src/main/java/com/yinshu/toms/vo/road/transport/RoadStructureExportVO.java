package com.yinshu.toms.vo.road.transport;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import cn.idev.excel.annotation.write.style.ContentStyle;
import cn.idev.excel.annotation.write.style.HeadStyle;
import cn.idev.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

import java.math.BigDecimal;

@Data
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 表头居中
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 内容居中
public class RoadStructureExportVO {
    @ExcelProperty(value = "序号")
    @ColumnWidth(15)
    private Integer no;

    @ExcelProperty(value = "行政区")
    private String county;

    @ColumnWidth(30)
    @ExcelProperty(value = "构造物类型")
    private String type;

    @ExcelProperty(value = "编号")
    private String structureCode;

    @ExcelProperty(value = "名称")
    private String name;

    @ExcelProperty(value = "所属路线")
    private String route;

    @ExcelProperty(value = "里程")
    private BigDecimal mileage;

    @ExcelProperty(value = "管理单位")
    private String managementUnit;

    @ExcelProperty(value = "养护单位")
    private String maintenanceUnit;

}
