package com.yinshu.toms.vo.road.transport;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DepartureSchedule
 * @description TODO 客运站发车班次详情导出vo
 * @date 2025/6/16 14:38
 **/
@Data
public class DepartureScheduleExportVO {
    @ExcelProperty(value = "序号")
    @ColumnWidth(10)
    private Integer no;
    @ExcelProperty(value = "时间")
    @ColumnWidth(20)
    private LocalDate dt;
    @ExcelProperty(value = "长途客运")
    private Integer ldPassengerTransport;
    @ExcelProperty(value = "旅游大巴")
    private Integer tourBus;
    @ExcelProperty(value = "长途客运入网率")
    private Double narLdPassengerTransport;
    @ExcelProperty(value = "旅游大巴入网率")
    private Double narTourBus;
    @ExcelProperty(value = "长途客运上线率")
    private Double orLdPassengerTransport;
    @ExcelProperty(value = "旅游大巴上线率")
    private Double orTourBus;
}
