package com.yinshu.toms.vo.highway;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class RoadRealEventExportVo {

    @ExcelProperty(value = "序号")
    @ColumnWidth(10)
    private Integer no;

    @ExcelProperty(value = "时间")
    @ColumnWidth(30)
    private LocalDateTime occurrenceTime;

    @ExcelProperty(value = "线路名称")
    @ColumnWidth(15)
    private String roadName;

    @ExcelProperty(value = "线路编号")
    @ColumnWidth(15)
    private String roadCode;

    @ExcelProperty(value = "事件内容")
    @ColumnWidth(15)
    private String eventName;

    @ExcelProperty(value = "事件类型")
    @ColumnWidth(15)
    private String eventType;

    @ExcelProperty(value = "事件级别")
    @ColumnWidth(15)
    private String eventLevel;

}
