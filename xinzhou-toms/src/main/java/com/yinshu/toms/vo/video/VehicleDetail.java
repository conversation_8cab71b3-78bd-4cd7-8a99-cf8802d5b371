package com.yinshu.toms.vo.video;

import lombok.Data;

import java.util.List;
/**
 * 车辆设备详情（detail接口响应）
 * <AUTHOR>
 *
 */
@Data
public class VehicleDetail {
    private List<DeviceInfo> deviceList;

    @Data
    public static class DeviceInfo {
        private Long vehicleId;
        private String deviceNo;
        private Integer onlineState; // 0-离线，1-在线
        private String authId; // 对应H5 SDK的devId
        private List<DeviceChannel> deviceChannelList;

        @Data
        public static class DeviceChannel {
            private Integer channelNo;
            private Integer enable; // 是否可用
        }
    }
}
