package com.yinshu.toms.vo.city;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import cn.idev.excel.annotation.write.style.ContentStyle;
import cn.idev.excel.annotation.write.style.HeadStyle;
import cn.idev.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

@Data
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 表头居中
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 内容居中
public class BusOperationExportVo {

    @ExcelProperty(value = "序号")
    @ColumnWidth(10)
    private Integer no;

    @ExcelProperty(value = "线路名称")
    @ColumnWidth(15)
    private String fLineName;

    @ExcelProperty(value = "行驶里程")
    @ColumnWidth(15)
    private String mileage;

    @ExcelProperty(value = "客流量")
    @ColumnWidth(15)
    private String passengerVolume;

    @ExcelProperty(value = "平均车速")
    @ColumnWidth(15)
    private String currentOperatingSpeed;

    @ExcelProperty(value = "高峰均速")
    @ColumnWidth(15)
    private String peakSpeed;

    @ExcelProperty(value = "准点率")
    @ColumnWidth(15)
    private String onTimeRate;

    @ExcelProperty(value = "高峰准点率")
    @ColumnWidth(15)
    private String peakTimeRate;
}
