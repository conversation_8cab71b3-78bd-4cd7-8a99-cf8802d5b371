package com.yinshu.toms.vo.aviation;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import cn.idev.excel.annotation.write.style.ContentStyle;
import cn.idev.excel.annotation.write.style.HeadStyle;
import cn.idev.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 交通执法判研记录
 * <AUTHOR>
 *
 */
@Data
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 表头居中
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 内容居中
public class TrafficAlarmExportVo {

	@ExcelProperty(value = "序号")
    @ColumnWidth(15)
    private Integer no;

    @ExcelProperty(value = "所属企业")
    @ColumnWidth(15)
    private String company;

    @ExcelProperty(value = "报警时间")
    @ColumnWidth(30)
    private String warntime;

    @ExcelProperty(value = "车牌号码")
    @ColumnWidth(15)
    private String carid;

    @ExcelProperty(value = "驾驶员")
    @ColumnWidth(15)
    private String driver;

    @ExcelProperty(value = "报警类型")
    @ColumnWidth(15)
    private String warntype;
    
    @ExcelProperty(value = "报警级别")
    @ColumnWidth(15)
    private String warnlevel;
    
}
