package com.yinshu.toms.vo.road.transport;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TransportStationExportVO
 * @description TODO 普通货运车数量详情导出
 * @date 2025/6/16 16:55
 **/
@Data
public class OrdinaryFreightVehiclesExportVO {
    @ExcelProperty(value = "序号")
    @ColumnWidth(15)
    private Integer no;
    @ExcelProperty(value = "时间")
    @ColumnWidth(25)
    private LocalDate dt;
    @ExcelProperty(value = "车辆在线总数")
    private Integer tnov;
    @ExcelProperty(value = "在线同比")
    @ColumnWidth(15)
    private Double tnovPre;
    @ExcelProperty(value = "在线环比")
    @ColumnWidth(15)
    private Double tnovPhr;
    @ExcelProperty(value = "新办数量")
    private Integer nrq;
    @ExcelProperty(value = "新办同比")
    private Double nrqPre;
    @ExcelProperty(value = "新办环比")
    private Double nrqPhr;
    @ExcelProperty(value = "注销数量")
    private Integer cancelNum;
    @ExcelProperty(value = "注销同比")
    private Double cancelPre;
    @ExcelProperty(value = "注销环比")
    private Double cancelPhr;
    @ExcelProperty(value = "迁出数量")
    private Integer reloc;
    @ExcelProperty(value = "迁出同比")
    private Double relocPre;
    @ExcelProperty(value = "迁出环比")
    private Double relocPhr;
}
