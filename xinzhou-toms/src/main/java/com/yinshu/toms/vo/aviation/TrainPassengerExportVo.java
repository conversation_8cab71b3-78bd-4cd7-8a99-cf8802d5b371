package com.yinshu.toms.vo.aviation;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import cn.idev.excel.annotation.write.style.ContentStyle;
import cn.idev.excel.annotation.write.style.HeadStyle;
import cn.idev.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

import java.math.BigDecimal;

@Data
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 表头居中
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 内容居中
public class TrainPassengerExportVo {

	@ExcelProperty(value = "序号")
    @ColumnWidth(15)
    private Integer no;

    @ExcelProperty(value = "日期")
    @ColumnWidth(15)
    private String dt;

    @ExcelProperty(value = "总客运量")
    @ColumnWidth(15)
    private String totalnum;

    @ExcelProperty(value = "同比")
    @ColumnWidth(15)
    private String totalnumPrepre;

    @ExcelProperty(value = "环比")
    @ColumnWidth(15)
    private BigDecimal totalnumPhrpre;

    @ExcelProperty(value = "到站客运量")
    @ColumnWidth(15)
    private BigDecimal innum;
    
    @ExcelProperty(value = "同比")
    @ColumnWidth(15)
    private BigDecimal innumPrepre;
    
    @ExcelProperty(value = "环比")
    @ColumnWidth(15)
    private BigDecimal innumPhrpre;
    
    @ExcelProperty(value = "离站客运量")
    @ColumnWidth(15)
    private BigDecimal outnum;
    
    @ExcelProperty(value = "同比")
    @ColumnWidth(15)
    private BigDecimal outnumPrepre;
    
    @ExcelProperty(value = "环比")
    @ColumnWidth(15)
    private BigDecimal outnumPhrpre;
}
