package com.yinshu.toms.vo.road.transport;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TransportStationExportVO
 * @description TODO 普通货运车数量详情导出
 * @date 2025/6/16 16:55
 **/
@Data
public class WarningVehicleDetailExportVO {
    @ExcelProperty(value = "序号")
    @ColumnWidth(15)
    private Integer no;
    @ExcelProperty(value = "报警时间")
    @ColumnWidth(30)
    private LocalDateTime warntime;
    @ExcelProperty(value = "所属企业")
    private String company;
    @ExcelProperty(value = "车牌号")
    @ColumnWidth(15)
    private String carid;
    @ExcelProperty(value = "驾驶员")
    @ColumnWidth(15)
    private String driver;
    @ExcelProperty(value = "车辆类型")
    private String cartype;
    @ExcelProperty(value = "报警类型")
    private String warntype;
    @ExcelProperty(value = "报警级别")
    private String warnlevel;
    @ExcelProperty(value = "报警信息")
    private String warnmsg;
}
