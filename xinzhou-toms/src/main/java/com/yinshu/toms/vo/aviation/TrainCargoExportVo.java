package com.yinshu.toms.vo.aviation;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import cn.idev.excel.annotation.write.style.ContentStyle;
import cn.idev.excel.annotation.write.style.HeadStyle;
import cn.idev.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

import java.math.BigDecimal;

@Data
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 表头居中
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 内容居中
public class TrainCargoExportVo {

	@ExcelProperty(value = "序号")
    @ColumnWidth(15)
    private Integer no;

    @ExcelProperty(value = "日期")
    @ColumnWidth(15)
    private String dt;

    @ExcelProperty(value = "总货运量")
    @ColumnWidth(15)
    private String huototalnum;

    @ExcelProperty(value = "同比")
    @ColumnWidth(15)
    private String huototalnumPrepre;

    @ExcelProperty(value = "环比")
    @ColumnWidth(15)
    private BigDecimal huototalnumPhrpre;

    @ExcelProperty(value = "到站货运量")
    @ColumnWidth(15)
    private BigDecimal huoinnum;
    
    @ExcelProperty(value = "同比")
    @ColumnWidth(15)
    private BigDecimal huoinnumPrepre;
    
    @ExcelProperty(value = "环比")
    @ColumnWidth(15)
    private BigDecimal huoinnumPhrpre;
    
    @ExcelProperty(value = "离站货运量")
    @ColumnWidth(15)
    private BigDecimal huooutnum;
    
    @ExcelProperty(value = "同比")
    @ColumnWidth(15)
    private BigDecimal huooutnumPrepre;
    
    @ExcelProperty(value = "环比")
    @ColumnWidth(15)
    private BigDecimal huooutnumPhrpre;
}
