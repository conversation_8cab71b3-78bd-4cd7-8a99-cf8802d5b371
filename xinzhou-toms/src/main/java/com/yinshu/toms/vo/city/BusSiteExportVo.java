package com.yinshu.toms.vo.city;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import cn.idev.excel.annotation.write.style.ContentStyle;
import cn.idev.excel.annotation.write.style.HeadStyle;
import cn.idev.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

@Data
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 表头居中
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 内容居中
public class BusSiteExportVo {

    @ExcelProperty(value = "序号")
    @ColumnWidth(10)
    private Integer no;

    @ExcelProperty(value = "站点名称")
    @ColumnWidth(15)
    private String stationName;

    @ExcelProperty(value = "站点类型")
    @ColumnWidth(15)
    private String stationType;

    @ExcelProperty(value = "途经路线")
    @ColumnWidth(15)
    private String passingRoutes;
}
