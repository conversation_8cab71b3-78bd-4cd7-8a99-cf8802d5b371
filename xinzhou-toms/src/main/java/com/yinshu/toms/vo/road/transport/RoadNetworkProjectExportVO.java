package com.yinshu.toms.vo.road.transport;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import cn.idev.excel.annotation.write.style.ContentStyle;
import cn.idev.excel.annotation.write.style.HeadStyle;
import cn.idev.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

import java.math.BigDecimal;

@Data
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 表头居中
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 内容居中
public class RoadNetworkProjectExportVO {
    @ExcelProperty(value = "序号")
    @ColumnWidth(15)
    private Integer no;

    @ExcelProperty(value = "行政区")
    private String county;

    @ColumnWidth(30)
    @ExcelProperty(value = "项目类型")
    private String projectType;

    @ExcelProperty(value = "合同日期")
    private String contractTime;

    @ExcelProperty(value = "项目名称")
    private String projectName;

    @ExcelProperty(value = "立项批文")
    private String projectDocument;

    @ExcelProperty(value = "公路等级")
    private String roadLevel;

    @ExcelProperty(value = "项目规模")
    private String projectScale;

    @ExcelProperty(value = "项目法人单位")
    private String projectLegalEntity;

    @ExcelProperty(value = "施工单位名称")
    private String constructionUnit;

}
