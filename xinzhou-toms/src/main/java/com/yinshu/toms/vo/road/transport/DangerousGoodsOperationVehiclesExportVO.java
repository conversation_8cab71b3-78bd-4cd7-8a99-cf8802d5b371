package com.yinshu.toms.vo.road.transport;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TransportStationExportVO
 * @description TODO 危货运行车辆详情导出
 * @date 2025/6/16 16:55
 **/
@Data
public class DangerousGoodsOperationVehiclesExportVO {
    @ExcelProperty(value = "序号")
    @ColumnWidth(15)
    private Integer no;
    @ExcelProperty(value = "日期")
    @ColumnWidth(25)
    private LocalDate dt;
    @ExcelProperty(value = "危货累计总数")
    private Long dangerNum;
    @ExcelProperty(value = "危货到达总量")
    private Long dangerArrive;
    @ExcelProperty(value = "危货出发总量")
    private Long dangerLeave;
}
