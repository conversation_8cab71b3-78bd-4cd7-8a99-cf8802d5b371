package com.yinshu.toms.vo.road.transport;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import cn.idev.excel.annotation.write.style.ContentStyle;
import cn.idev.excel.annotation.write.style.HeadStyle;
import cn.idev.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

import java.math.BigDecimal;

@Data
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 表头居中
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 内容居中
public class RoadEventDetailExportVO {
    @ExcelProperty(value = "排名")
    @ColumnWidth(15)
    private Integer no;

    @ExcelProperty(value = "行政区")
    private String county;

    @ColumnWidth(30)
    @ExcelProperty(value = "日期")
    private String date;

    @ExcelProperty(value = "交通事故")
    private BigDecimal trafficAccident;

    @ExcelProperty(value = "道路施工")
    private BigDecimal roadConstruction;

    @ExcelProperty(value = "地质灾害")
    private BigDecimal geologicalDisaster;

    @ExcelProperty(value = "恶劣天气")
    private BigDecimal badWeather;

    @ExcelProperty(value = "重大活动")
    private BigDecimal majorEvents;

    @ExcelProperty(value = "其他")
    private BigDecimal other;

}
