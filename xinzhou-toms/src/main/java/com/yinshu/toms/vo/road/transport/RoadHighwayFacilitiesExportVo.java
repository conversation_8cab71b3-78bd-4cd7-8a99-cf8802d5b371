package com.yinshu.toms.vo.road.transport;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import cn.idev.excel.annotation.write.style.ContentStyle;
import cn.idev.excel.annotation.write.style.HeadStyle;
import cn.idev.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

@Data
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 表头居中
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 内容居中
public class RoadHighwayFacilitiesExportVo {

    @ExcelProperty(value = "序号")
    @ColumnWidth(15)
    private String no;

    @ExcelProperty(value = "设施名称")
    @ColumnWidth(25)
    private String deviceName;

    @ExcelProperty(value = "类型")
    @ColumnWidth(25)
    private String type;

    @ExcelProperty(value = "设备编号")
    @ColumnWidth(25)
    private String deviceCode;

    @ExcelProperty(value = "设备地址")
    @ColumnWidth(25)
    private String deviceAddress;

    @ExcelProperty(value = "设备状态")
    @ColumnWidth(25)
    private String status;

    @ExcelProperty(value = "质保状态")
    @ColumnWidth(25)
    private String warrantyStatus;

    @ExcelProperty(value = "维护单位")
    @ColumnWidth(25)
    private String maintenanceUnit;
}
