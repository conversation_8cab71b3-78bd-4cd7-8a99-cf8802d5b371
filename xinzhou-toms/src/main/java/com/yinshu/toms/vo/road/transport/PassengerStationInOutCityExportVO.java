package com.yinshu.toms.vo.road.transport;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TransportStationExportVO
 * @description TODO 客运量出入市详情导出
 * @date 2025/6/16 16:55
 **/
@Data
public class PassengerStationInOutCityExportVO {
    @ExcelProperty(value = "序号")
    @ColumnWidth(15)
    private Integer no;
    @ExcelProperty(value = "时间")
    @ColumnWidth(25)
    private LocalDate dt;
    @ExcelProperty(value = "客运站名称")
    private String shoals;
    @ExcelProperty(value = "入市")
    @ColumnWidth(15)
    private Double inCity;
    @ExcelProperty(value = "出市")
    @ColumnWidth(15)
    private Double outCity;
}
