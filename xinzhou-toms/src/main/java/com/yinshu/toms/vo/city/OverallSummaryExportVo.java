package com.yinshu.toms.vo.city;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
public class OverallSummaryExportVo {

    @ExcelProperty(value = "序号")
    @ColumnWidth(15)
    private String no;

    @ExcelProperty(value = "地区")
    @ColumnWidth(15)
    private String county;

    @ExcelProperty(value = "公交车车辆")
    @ColumnWidth(15)
    private String busVehicles;

    @ExcelProperty(value = "巡游车车辆")
    @ColumnWidth(15)
    private String cruisingVehicles;

    @ExcelProperty(value = "网约车车辆")
    @ColumnWidth(15)
    private String ridehailingVehicles;

    @ExcelProperty(value = "公交车运力")
    @ColumnWidth(15)
    private String busCapacity;

    @ExcelProperty(value = "巡游车运力")
    @ColumnWidth(15)
    private String cruisingCapacity;

    @ExcelProperty(value = "网约车运力")
    @ColumnWidth(15)
    private String ridehailingCapacity;

}
