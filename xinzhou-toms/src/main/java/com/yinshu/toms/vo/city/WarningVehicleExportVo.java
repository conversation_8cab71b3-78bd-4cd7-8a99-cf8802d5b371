package com.yinshu.toms.vo.city;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import cn.idev.excel.annotation.write.style.ContentStyle;
import cn.idev.excel.annotation.write.style.HeadStyle;
import cn.idev.excel.enums.poi.HorizontalAlignmentEnum;
import com.yinshu.fast.excel.util.WarnLevelConverter;
import lombok.Data;

@Data
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 表头居中
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 内容居中
public class WarningVehicleExportVo {

    @ExcelProperty(value = "序号")
    @ColumnWidth(15)
    private Integer no;

    @ExcelProperty(value = "时间")
    @ColumnWidth(25)
    private String warndt;

    @ExcelProperty(value = "所属企业")
    @ColumnWidth(25)
    private String company;

    @ExcelProperty(value = "车牌号")
    @ColumnWidth(15)
    private String carid;

    @ExcelProperty(value = "驾驶员")
    @ColumnWidth(15)
    private String driver;

    @ExcelProperty(value = "报警类型")
    @ColumnWidth(15)
    private String warntype;

    @ExcelProperty(value = "报警级别", converter = WarnLevelConverter.class)
    @ColumnWidth(15)
    private String warnlevel;

    @ExcelProperty(value = "报警信息")
    @ColumnWidth(15)
    private String warnmsg;

}
