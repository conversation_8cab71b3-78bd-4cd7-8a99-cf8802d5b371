package com.yinshu.toms.task;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.toms.service.WarnMsgService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@EnableScheduling
@Component
public class WebSocketWarnTask {
	protected final static Logger logger = LoggerFactory.getLogger(WebSocketWarnTask.class);

    @Resource
    private SimpMessagingTemplate simpMessagingTemplate;
    @Autowired
    private WarnMsgService warnMsgService;



    // 延迟10秒执行第一次，后续每30秒执行一次
    //@Scheduled(initialDelay = 10 * 1000, fixedRate = 30 * 1000)
    public void task(){
        JSONObject query = new JSONObject();
        query.put("flag", 0);
        Integer count = warnMsgService.getWarnInfoUnreadCount(query);
        JSONObject countObj = new JSONObject();
        countObj.put("warnCount", count);
        simpMessagingTemplate.convertAndSend("/topic/warnCount", JSON.toJSONString(countObj));
    }


    public void testTask(){
        logger.info("testTask执行----------------------------");
    }

}
