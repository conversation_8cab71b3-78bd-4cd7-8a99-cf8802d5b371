package com.yinshu.toms.service;


import com.yinshu.toms.common.ApiResult;
import com.yinshu.toms.vo.video.*;

import java.util.List;
/**
 * 视频
 * <AUTHOR>
 *
 */
public interface VideoService {

    /**
     * 通过车牌号或设备编号获取车辆简单信息（含vehicleId）
     */
    ApiResult<VehicleSimpleInfo> getVehicleSimpleInfo(String vehicleNo, String deviceNo);

    /**
     * 查询通道名称和编号列表
     */
    ApiResult<List<ChannelName>> queryChannelNames();

    /**
     * 通过vehicleId查询车辆设备详情（含devId、channelNo等）
     */
    ApiResult<VehicleDetail> getVehicleDetail(Long vehicleId);

    /**
     * 设备回放月历检索（查询指定时间范围内的录像存储信息）
     */
    ApiResult<List<CalendarInfo>> queryDeviceCalendar(String devId, String channels, String beginDate, String endTime);

    /**
     * 设备文件检索（查询指定时间范围内的媒体文件）
     */
    ApiResult<List<MediaFile>> queryDeviceFiles(String devId, String chan, String beginTime, String endTime, Integer timeout);

    /**
     * 创建视频下载任务
     */
    ApiResult<TaskInfo> createVideoDownloadTask(TaskCreateParam param);

    /**
     * 查询视频下载任务详情
     */
    ApiResult<TaskDetail> getDownloadTaskDetail(String taskId);
}
