package com.yinshu.toms.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.toms.service.WarnMsgService;
import com.yinshu.toms.util.DataComparisonUtils;
import com.yinshu.toms.util.UtilService;
import com.yinshu.utils.DateUtils;
import com.yinshu.utils.FilterCreate;
import com.yinshu.utils.ReplaceUtils;
import com.yinshu.utils.server.Sys;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 预警消息
 */
@Service
public class WarnMsgServiceImpl implements WarnMsgService {

    /***
     * 交通运输预警阈值配置表//dim_traffic_warn_config
     */
    public static final String DIM_TRAFFIC_WARN_CONFIG = "/api/100585/data.json?";
    /***
     * 交通运输预警阈值配置表//dim_traffic_warn_config
     */
    public static final String MODEL_DIM_TRAFFIC_WARN_CONFIG = "/api/100508/zPSvBrMftJFPzEoG1bRi?tablename=dim_traffic_warn_config";
    /***
     * 修改-交通运输预警阈值配置表
     */
    public static final String UPDATE_DIM_TRAFFIC_WARN_CONFIG = "/api/dim_traffic_warn_config/EauiEp0OmAmGqfFt1bh4?";
    /***
     * 预警类型字段维表//dim_warntype_info_analysis
     */
    public static final String DIM_WARNTYPE_INFO_ANALYSIS = "/api/100586/data.json?";
    /***
     * 自定义报警明细表//ads_warn_info_self
     */
    public static final String ADS_WARN_INFO_SELF_ = "/api/100588/data.json?";
    /***
     * 自定义报警明细表//ads_warn_info_self
     */
    public static final String MODEL_ADS_WARN_INFO_SELF = "/api/100508/zPSvBrMftJFPzEoG1bRi?";
    /***
     * 获取未读告警记录数
     */
    public static final String COUNT_ADS_WARN_INFO_SELF = "/api/ads_warn_info_self_fast/q10SuQtYPv9el7WFmTWl?";
    /***
     * 修改-自定义报警明细表//ads_warn_info_self
     */
    public static final String UPDATE_ADS_WARN_INFO_SELF = "/api/ads_warn_info_self/90W7Q5LsErvfLNgVoo5K?";

    @Resource
    DvisualHttpTemplate template;

    /**
     * 获取告警类型信息
     * @param query
     * @return
     */
    public JSONArray getWarnTypeInfo(JSONObject query) {
        JSONObject resultData = template.post(DIM_WARNTYPE_INFO_ANALYSIS, query);
        return DataComparisonUtils.getResultList(resultData);
    }

    /**
     * 根据id获取告警配置
     * @param query
     * @return
     */
    public JSONObject getWarnConfig(JSONObject query) {
        String id = query.getString("id");
        String filterTemplate = "id = '{id}'";
        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                "id", id));
        JSONObject resultData = template.post(MODEL_DIM_TRAFFIC_WARN_CONFIG, query);
        return DataComparisonUtils.getResultFirst(resultData);
    }

    /**
     * 根据id修改告警配置
     * @param query
     * @return
     */
    public JSONObject updateWarnConfig(JSONObject query) {
        query.put("updatetype", "u");
        JSONObject resultData = template.post(UPDATE_DIM_TRAFFIC_WARN_CONFIG, query);
        return resultData;
    }

    /**
     * 获取告警数量统计
     * @param query
     * @return
     */
    public Integer getWarnInfoUnreadCount(JSONObject query) {
        JSONObject resultData = template.post(COUNT_ADS_WARN_INFO_SELF, query);
        Integer total = resultData.getInteger("total");
        return total;
    }

    /**
     * 获取告警数量统计
     * @param query
     * @return
     */
    public Integer findWarnInfoUnreadCount(JSONObject query) {
        String flag = query.getString("flag");
        String day = DateUtils.getDate();
        String filterTemplate = new FilterCreate()
                .and("warndt = '{day}'")
                .and(StringUtils.isNotBlank(flag), "flag = {flag}")
                .toString();
        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                "day", day, "flag", flag));
        query.put("s", 0);
        query.put("n", 1);
        query.put("tablename", "ads_warn_info_self");
        JSONObject resultData = template.post(MODEL_ADS_WARN_INFO_SELF, query);
        Integer total = resultData.getInteger("total");
        return total;
    }

    /**
     * 获取告警记录
     * @param query
     * @return
     */
    public JSONObject getWarnInfoList(JSONObject query) {
        String flag = query.getString("flag");
        JSONArray dateRange = query.getJSONArray("dateRange");
        String carid = query.getString("carid");
        String driver = query.getString("driver");
        String startDay = null, endDay = null;
        if(dateRange != null){
            startDay = dateRange.getString(0);
            endDay = dateRange.getString(1);
        }
        String day = DateUtils.getDate();
        String filterTemplate = new FilterCreate()
                .and(StringUtils.isBlank(startDay), "warndt = '{day}'")
                .and(StringUtils.isNotBlank(startDay), "warndt >= '{startDay}' and warndt <= '{endDay}'")
                .and(StringUtils.isNotBlank(carid), "carid like '{carid}'")
                .and(StringUtils.isNotBlank(driver), "driver like '{driver}'")
                .and(StringUtils.isNotBlank(flag), "flag = {flag}")
                .concat("order by warntime desc")
                .toString();
        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                "day", day, "startDay", startDay, "endDay", endDay,
                "carid", carid, "driver", driver, "flag", flag));
        query.put("tablename", "ads_warn_info_self");
        JSONObject resultData = template.post(MODEL_ADS_WARN_INFO_SELF, query);
        return resultData;
    }

    /**
     * 根据id设置为已读
     * @param query
     * @return
     */
    public Integer updateWarInfoRead(JSONObject query) {
        String id = query.getString("id");
        if(StringUtils.isNotBlank(id)){
            query.put("id", id);
            JSONObject resultData = template.post(UPDATE_ADS_WARN_INFO_SELF, query);
            return resultData.getInteger("total");
        }else{
            return getWarnInfoUnreadCount(query);
        }
    }


}
