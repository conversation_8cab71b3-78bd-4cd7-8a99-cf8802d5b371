package com.yinshu.toms.service;

import com.alibaba.fastjson.JSONObject;

/**
 * 民航专题
 * <AUTHOR>
 *
 */
public interface AviationService {
	
	/**
	 * 基础指标
	 */
    JSONObject getTotalInfo(JSONObject query);
    
    /**
	 * 航班数
	 */
    JSONObject getArriverOutList(JSONObject query);
    
    /**
	 * 航班统计情况
	 */
    JSONObject getAnalysisList(JSONObject query);
    
    /**
	 * 民航客运量监测
	 */
    JSONObject getPassengerList(JSONObject query);
    
    /**
	 * 民航客运量监测分页列表
	 */
    JSONObject getPassengerPageList(JSONObject query);
    
    /**
	 * 民航货运量监测
	 */
    JSONObject getCargoList(JSONObject query);
    
    /**
	 * 机场大巴运行
	 */
    JSONObject getBusList(JSONObject query);
    
	/**
	 * 获取车辆信息
	 */
    JSONObject getVehicleList(JSONObject query);
    
    /**
	 * 执法判研分类汇总
	 */
    JSONObject getWarningTotal(JSONObject query);
    
    /**
   	 * 执法判研
   	 */
    JSONObject getWarningList(JSONObject query);
    
    /**
     *  导出
     * @param jsonObject
     */
    void exportXls(JSONObject jsonObject);

}
