package com.yinshu.toms.service;

import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RoadTransportService
 * @description TODO 道路运输服务
 * @date 2025/6/16 11:04
 **/
public interface RoadTransportService {
    /**
     * @param query 查询参数
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @description //TODO 基础指标
     * @date 2025/6/16 11:20
     **/
    JSONObject adsBasicIndicatorData(JSONObject query);

    /*
     * <AUTHOR>
     * @description //TODO 客运站点监测
     * @date 2025/6/16 16:37
     * @param query
     * @return com.alibaba.fastjson.JSONObject
     **/
    JSONObject adsPassengerTransportStation(JSONObject query);


    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 今日概览趋势小时
     * @date 2025/6/16 11:20
     **/
    JSONObject adsDailyOverviewTrendHourly(JSONObject query);

    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 每日概览趋势
     * @date 2025/6/16 11:20
     **/
    JSONObject adsDailyOverviewTrend(JSONObject query);

    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 危货运量周变化趋势
     * @date 2025/6/16 11:20
     **/
    JSONObject adsDangerousGoodsVolWeeklyTrend(JSONObject query);


    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 道路运输概览占比
     * @date 2025/6/16 11:20
     **/
    JSONObject adsRoadTransportOverviewRatio(JSONObject query);

    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 普通货运车数量详情
     * @date 2025/6/16 11:20
     **/
    JSONObject adsGeneralFreightTruckQuantityDetail(JSONObject query);

    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 运输地图车辆基本信息
     * @date 2025/6/16 11:20
     **/
    JSONObject adsTransportMapVehicleBaseInfo(JSONObject query);

    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 预警车辆详情
     * @date 2025/6/16 11:20
     **/
    JSONObject adsWarningVehicleDetail(JSONObject query);

    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 危货运数据排名
     * @date 2025/6/16 11:20
     **/
    JSONObject adsDgDataRank(JSONObject query);
}
