package com.yinshu.toms.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * 预警消息
 *
 */
public interface WarnMsgService {
	/**
	 * 获取告警类型信息
	 * @param query
	 * @return
	 */
	JSONArray getWarnTypeInfo(JSONObject query);

	/**
	 * 根据id获取告警配置
	 * @param query
	 * @return
	 */
	JSONObject getWarnConfig(JSONObject query);

	/**
	 * 根据id修改告警配置
	 * @param query
	 * @return
	 */
	JSONObject updateWarnConfig(JSONObject query);

	/**
	 * 获取告警数量统计
	 * @param query
	 * @return
	 */
	Integer getWarnInfoUnreadCount(JSONObject query);

	/**
	 * 获取告警记录
	 * @param query
	 * @return
	 */
	JSONObject getWarnInfoList(JSONObject query);

	/**
	 * 根据id设置为已读
	 * @param query
	 * @return
	 */
	Integer updateWarInfoRead(JSONObject query);

}
