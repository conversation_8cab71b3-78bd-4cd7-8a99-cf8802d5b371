package com.yinshu.toms.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.toms.service.RoadOverviewService;
import com.yinshu.toms.util.DataComparisonUtils;
import com.yinshu.toms.util.LngLatUtil;
import com.yinshu.toms.util.UtilService;
import com.yinshu.utils.DateUtils;
import com.yinshu.utils.ReplaceUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 公路路网-路网概况
 *
 */
@Service
public class RoadOverviewServiceImpl implements RoadOverviewService {

    @Resource
    DvisualHttpTemplate template;

    /***
     * 公路路网与养护基础指标//ads_xinzhou_road_net_and_maintain_base_info
     */
    public static final String ADS_XINZHOU_ROAD_NET_AND_MAINTAIN_BASE_INFO = "/api/100447/data.json?";

    /***
     * 路网交通量信息//ads_xinzhou_road_net_traffic_info
     */
    public static final String ADS_XINZHOU_ROAD_NET_TRAFFIC_INFO = "/api/100451/data.json?";

    /***
     * 路网公路里程占比//ads_xinzhou_road_mileage_proportion
     */
    public static final String ADS_XINZHOU_ROAD_MILEAGE_PROPORTION = "/api/100458/data.json?";


    /**
	 * 获取基础信息
	 * @param query
	 * @return
	 */
    public JSONObject getRoadBaseInfo(JSONObject query) {
        String year = String.valueOf(DateUtils.getCurrentYear());
        String filterTemplate = "id = '{year}'";
        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                "year", year));
        JSONObject resultData = template.post(ADS_XINZHOU_ROAD_NET_AND_MAINTAIN_BASE_INFO, query);
        return DataComparisonUtils.getResultFirst(resultData);
    }

    /**
     * 获取路网交通量信息
     * @param query
     * @return
     */
    public JSONObject getTrafficInfo(JSONObject query) {
        String ym = UtilService.RealOrTest(DateUtils.getYM(), "2025-06");
        String filterTemplate = "id like '{ym}' order by id";
        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                "ym", ym));
        JSONObject resultData = template.post(ADS_XINZHOU_ROAD_NET_TRAFFIC_INFO, query);
        JSONObject toDay = DataComparisonUtils.getResultLast(resultData);
        resultData.put("toDay", toDay);
        DataComparisonUtils.assemblingMoreBarOrLineData(resultData, "id", "total_today", "overrun_vehicle");
        return resultData;
    }

    /**
     * 获取公路总里程占比
     * @param query
     * @return
     */
    public JSONObject getRoadMileage(JSONObject query) {
        String day = UtilService.RealOrTest(DateUtils.getDate(), "2025-06-11");
        String area = query.getString("area");
        String filterTemplate = "city = '{area}' and dt = '{day}'";
        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                "area", area, "day", day));
        JSONObject resultData = template.post(ADS_XINZHOU_ROAD_MILEAGE_PROPORTION, query);
        return DataComparisonUtils.getResultFirst(resultData);
    }



}
