package com.yinshu.toms.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.toms.service.RoadConstructService;
import com.yinshu.toms.util.DataComparisonUtils;
import com.yinshu.toms.util.UtilService;
import com.yinshu.utils.DateUtils;
import com.yinshu.utils.FilterCreate;
import com.yinshu.utils.ReplaceUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 公路路网-建设工程指标
 *
 */
@Service
public class RoadConstructServiceImpl implements RoadConstructService {

    @Resource
    DvisualHttpTemplate template;

    /***
     * 建设基础构造物指标//ads_xinzhou_construct_structure_index
     */
    public static final String ADS_XINZHOU_CONSTRUCT_STRUCTURE_INDEX = "/api/100488/data.json?";
    /***
     * 项目信息详情//ads_xinzhou_project_info_details
     */
    public static final String ADS_XINZHOU_PROJECT_INFO_DETAILS = "/api/100489/data.json?";
    /***
     * 建设工程进度//ads_xinzhou_build_project_progress
     */
    public static final String ADS_XINZHOU_BUILD_PROJECT_PROGRESS = "/api/100490/data.json?";


    /**
     * 获取建设工程指标
     * @param query
     * @return
     */
    public JSONObject getIndicator(JSONObject query) {
        String year = String.valueOf(DateUtils.getCurrentYear());
        String filterTemplate = "dt = '{year}'";
        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                "year", year));
        JSONObject resultData = template.post(ADS_XINZHOU_CONSTRUCT_STRUCTURE_INDEX, query);
        return DataComparisonUtils.getResultFirst(resultData);
    }

    /**
     * 获取项目信息详情
     * @param query
     * @return
     */
    public JSONObject getProjectInfo(JSONObject query) {
        String projectName = query.getString("projectName");
        String projectDocument = query.getString("projectDocument");
        String filterTemplate = new FilterCreate().or(StringUtils.isNotBlank(projectName),"f_project_name like '{projectName}'")
                .or(StringUtils.isNotBlank(projectDocument), "f_project_document like '{projectDocument}'")
                .concat("order by contract_date desc").toString();
        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                "projectName", projectName, "projectDocument", projectDocument));
        JSONObject resultData = template.post(ADS_XINZHOU_PROJECT_INFO_DETAILS, query);
        return resultData;
    }

    /**
     * 获取建设工程进度
     * @param query
     * @return
     */
    public JSONObject getConstructionProgress(JSONObject query) {
        String year = String.valueOf(DateUtils.getCurrentYear());
        String filterTemplate = "id = '{year}'";
        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                "year", year));
        JSONObject resultData = template.post(ADS_XINZHOU_BUILD_PROJECT_PROGRESS, query);
        return DataComparisonUtils.getResultFirst(resultData);
    }
}
