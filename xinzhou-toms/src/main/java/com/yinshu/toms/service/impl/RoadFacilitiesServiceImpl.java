package com.yinshu.toms.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.toms.service.RoadFacilitiesService;
import com.yinshu.toms.util.DataComparisonUtils;
import com.yinshu.toms.util.UtilService;
import com.yinshu.utils.DateUtils;
import com.yinshu.utils.FilterCreate;
import com.yinshu.utils.ReplaceUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 公路路网-设施
 *
 */
@Service
public class RoadFacilitiesServiceImpl implements RoadFacilitiesService {

    @Resource
    DvisualHttpTemplate template;

    /***
     * 信息化设备基础指标//ads_xinzhou_information_device_base_index
     */
    public static final String ADS_XINZHOU_INFORMATION_DEVICE_BASE_INDEX = "/api/100491/data.json?";

    /***
     * 信息化设备详情//ads_xinzhou_information_device_details
     */
    public static final String ADS_XINZHOU_INFORMATION_DEVICE_DETAILS = "/api/100503/data.json?";

    /***
     * 信息化设备离线率排行//ads_xinzhou_information_device_offline_rate_rank
     */
    public static final String ADS_XINZHOU_INFORMATION_DEVICE_OFFLINE_RATE_RANK = "/api/100504/data.json?";


    /**
     * 获取基础指标
     * @param query
     * @return
     */
    public JSONObject getBasicInfo(JSONObject query) {
        String day = UtilService.RealOrTest(DateUtils.getDate(), "2025-06-09");
        String filterTemplate = "id = '{day}' ";
        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                "day", day));
        JSONObject resultData = template.post(ADS_XINZHOU_INFORMATION_DEVICE_BASE_INDEX, query);
        return DataComparisonUtils.getResultFirst(resultData);
    }

    /**
     * 信息化设备详情
     * @param query
     * @return
     */
    public JSONObject getDeviceDetails(JSONObject query) {
        String area = query.getString("area");
        String deviceName = query.getString("deviceName");
        String filterTemplate = new FilterCreate()
                .and(!"忻州市".equals(area), "county = '{area}'")
                .and(StringUtils.isNotBlank(deviceName), "device_name like '{deviceName}'")
                .toString();
        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                "area", area, "deviceName", deviceName));
        JSONObject resultData = template.post(ADS_XINZHOU_INFORMATION_DEVICE_DETAILS, query);
        return resultData;
    }

    /**
     * 获取信息化设备离线率排行
     * @param query
     * @return
     */
    public JSONArray getDeviceOfflineRank(JSONObject query) {
        String day = UtilService.RealOrTest(DateUtils.getDate(), "2025-06-09");
        String filterTemplate = "dt = '{day}' order by offline_rate desc";
        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                "day", day));
        query.put("s", 0);
        query.put("n", 10);
        JSONObject resultData = template.post(ADS_XINZHOU_INFORMATION_DEVICE_OFFLINE_RATE_RANK, query);
        return DataComparisonUtils.getResultList(resultData);
    }
}
