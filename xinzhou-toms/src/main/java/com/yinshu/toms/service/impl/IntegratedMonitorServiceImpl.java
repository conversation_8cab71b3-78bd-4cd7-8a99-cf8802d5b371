package com.yinshu.toms.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.toms.service.IntegratedMonitorService;
import com.yinshu.toms.util.DataComparisonUtils;
import com.yinshu.utils.DateUtils;
import com.yinshu.utils.FilterCreate;
import com.yinshu.utils.ReplaceUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class IntegratedMonitorServiceImpl implements IntegratedMonitorService {
    @Resource
    DvisualHttpTemplate template;

    /***
     * 车辆业户人员概览//ads_xinzhou_car_enterprise_people_overview
     */
    public static final String ADS_XINZHOU_CAR_ENTERPRISE_PEOPLE_OVERVIEW = "/api/100494/data.json?";

    /***
     * 实时对接数据//ads_xinzhou_docking_data_real
     */
    public static final String ADS_XINZHOU_DOCKING_DATA_REAL = "/api/100495/data.json?";

    /***
     * 路网公路里程占比//ads_xinzhou_road_mileage_proportion
     */
    public static final String ADS_XINZHOU_ROAD_MILEAGE_PROPORTION = "/api/100458/data.json?";

    /**
     * 公路概况-桥梁//ads_xinzhou_road_overview_birdge
     */
    public static final String ADS_XINZHOU_ROAD_VOERVIEW_BIRDGE = "/api/100496/data.json?";

    /**
     * 公路概况-隧道//ads_xinzhou_road_overview_tunnel
     */
    public static final String ADS_XINZHOU_ROAD_OVERVIEW_TUNNEL = "/api/100497/data.json?";

    /**
     * 营运车辆概览、经营业户概览、从业人员概览
     * @param query
     * @return
     */
    @Override
    public JSONObject getBaseInfo(JSONObject query) {
        String ym = DateUtils.getYM();
        String filterTemplate = "id = '"+ym+"'";
        query.put("filter", filterTemplate);
        JSONObject resultData = template.post(ADS_XINZHOU_CAR_ENTERPRISE_PEOPLE_OVERVIEW, query);
        return resultData;
    }

    /**
     * 实时对接数据
     * @param query
     * @return
     */
    @Override
    public JSONObject getDockingData(JSONObject query) {
        SimpleDateFormat sdft = new SimpleDateFormat("yyyy-MM-dd_HH");
        String time = sdft.format(new Date());
        String filterTemplate = "id = '"+time+"'";
        query.put("filter","id = '2025-06-12_10'");
        JSONObject resultData = template.post(ADS_XINZHOU_DOCKING_DATA_REAL, query);
        return resultData;
    }

    /**
     * 公路概况
     * @param query
     * @return
     */
    @Override
    public JSONObject getRoadMileage(JSONObject query) {
        //String today = DateUtils.getDate();
        String today = "";
        String city = StringUtils.isEmpty(query.getString("city")) ? "" : query.getString("city");
        String filterTemplate = new FilterCreate().and(StringUtils.isNotBlank(city),"city like '{city}'")
                .and(StringUtils.isNotBlank(today),"dt = '{today}'")
                .toString();
        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,"city",city,"today",today));
        JSONObject resultData = template.post(ADS_XINZHOU_ROAD_MILEAGE_PROPORTION, query);

        JSONArray list = resultData.getJSONArray("list");
        List<JSONObject> jsonList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            jsonList.add(list.getJSONObject(i));
        }
        Integer national_highway = 0;
        Integer provincial_highway = 0;
        Integer county_highway = 0;
        Integer township_road = 0;
        Integer village_road = 0;
        Integer other = 0;
        for (int i = 0; i < jsonList.size(); i++) {
            national_highway += jsonList.get(i).getIntValue("national_highway");
            provincial_highway += jsonList.get(i).getIntValue("provincial_highway");
            county_highway += jsonList.get(i).getIntValue("county_highway");
            township_road += jsonList.get(i).getIntValue("township_road");
            village_road += jsonList.get(i).getIntValue("village_road");
            other += jsonList.get(i).getIntValue("other");
        }
        JSONObject map = new JSONObject();
        map.put("national_highway",national_highway);
        map.put("provincial_highway",provincial_highway);
        map.put("county_highway",county_highway);
        map.put("township_road",township_road);
        map.put("village_road",village_road);
        map.put("other",other);
        map.put("total", national_highway + provincial_highway + county_highway + township_road + village_road + other);
        return map;
    }

    /**
     * 桥梁
     * @param query
     * @return
     */
    @Override
    public JSONObject getRoadBridge(JSONObject query) {
        String ym = DateUtils.getYM();
        //query.put("filter","id = '"+ym+"'");
        query.put("filter","id = '2025-06'");
        JSONObject resultData = template.post(ADS_XINZHOU_ROAD_VOERVIEW_BIRDGE, query);
        return resultData;
    }

    /**
     * 隧道
     * @param query
     * @return
     */
    @Override
    public JSONObject getRoadTunnel(JSONObject query) {
        String ym = DateUtils.getYM();
        //query.put("filter","id = '"+ym+"'");
        query.put("filter","id = '2025-06'");
        JSONObject resultData = template.post(ADS_XINZHOU_ROAD_OVERVIEW_TUNNEL, query);
        return resultData;
    }
}
