package com.yinshu.toms.service.impl;

import com.alibaba.fastjson.JSON;
import com.yinshu.toms.common.ApiResult;
import com.yinshu.toms.service.VideoService;
import com.yinshu.toms.util.SignUtil;
import com.yinshu.toms.vo.video.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.List;
import java.util.Optional;

/**
 * 视频服务实现类（基于签名文档和H5 SDK接口文档更新）
 */
@Service
@Slf4j
public class VideoServiceImpl implements VideoService {

    @Autowired
    private RestTemplate restTemplate;

    // 第三方API基础地址（s17媒体服务地址）
    @Value("${sdk.api.base-url}")
    private String baseUrl;

    @Value("${sdk.user-key}") // API用户管理中获取的userKey
    private String userKey;
    @Value("${sdk.sign-key}") // 签名密钥
    private String signKey;
    @Value("${sdk.auth-url}") // 鉴权接口地址
    private String authUrl;

    // 缓存鉴权信息（后续用Redis缓存并设置过期时间）
    private AuthInfo cachedAuthInfo;

    /**
     * 构建SDK接口专用请求头（包含签名、token等必填参数）
     */
    private HttpHeaders buildSdkHeaders() {
        // 1. 确保已获取鉴权信息（token、appId、tenantId）
        AuthInfo authInfo = getAuthInfo();
        if (authInfo == null) {
            throw new RuntimeException("获取鉴权信息失败");
        }

        // 3. 构建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.add("_tenantId", authInfo.getTenantId().toString());
        headers.add("_appId", authInfo.getAppId().toString());
        headers.add("_token", authInfo.getToken()); // 从鉴权接口获取的token
        headers.add("_sign", authInfo.getSign()); // 实时生成的签名
        headers.add("_langType", "zh_CN"); // 语言类型（默认中文）
        return headers;
    }

    /**
     * 获取鉴权信息（token、appId、tenantId）
     * 优先使用缓存，缓存过期或不存在则重新调用鉴权接口
     */
    private AuthInfo getAuthInfo() {
        // 简单判断缓存是否有效（实际项目中需根据token有效期判断）
        if (cachedAuthInfo != null) {
            return cachedAuthInfo;
        }

        // 调用鉴权接口获取新的鉴权信息
        try {
            // 生成鉴权所需的sign
            String sign = SignUtil.getSign(userKey, signKey);
            // AES加密密码
            String userPwd = "123456";
            String encrypt = SignUtil.encrypt(userPwd, sign);
            // 构建请求参数
            AuthRequest authRequest = new AuthRequest();
            authRequest.setUserKey(userKey);
            authRequest.setSign(signKey);
            // 发送请求
            HttpEntity<AuthRequest> request = new HttpEntity<>(authRequest);

            ResponseEntity<ApiResult<AuthInfo>> response = restTemplate.exchange(
                    authUrl, HttpMethod.POST, request, new ParameterizedTypeReference<ApiResult<AuthInfo>>() {}
            );

            // 解析响应
            ApiResult<AuthInfo> result = response.getBody();
            if (result == null || !result.isSuccess() || result.getData() == null) {
                log.error("鉴权接口返回失败：{}", result);
                return null;
            }

            // 缓存鉴权信息
            cachedAuthInfo = result.getData();
            cachedAuthInfo.setSign(encrypt);
            return cachedAuthInfo;
        } catch (Exception e) {
            log.error("调用鉴权接口失败", e);
            return null;
        }
    }


    // ------------------------------ 接口实现 ------------------------------

    @Override
    public ApiResult<VehicleSimpleInfo> getVehicleSimpleInfo(String vehicleNo, String deviceNo) {
        String url = UriComponentsBuilder.fromHttpUrl(baseUrl + "/alpha3/api/v1/h5/sdk/vehicle/simpleInfo")
                .queryParamIfPresent("vehicleNo", Optional.ofNullable(vehicleNo))
                .queryParamIfPresent("deviceNo", Optional.ofNullable(deviceNo))
                .toUriString();

        HttpEntity<Void> request = new HttpEntity<>(buildSdkHeaders());
        ResponseEntity<ApiResult<VehicleSimpleInfo>> response = restTemplate.exchange(
                url, HttpMethod.GET, request, (Class<ApiResult<VehicleSimpleInfo>>) (Class<?>) ApiResult.class
        );
        return response.getBody();
    }

    @Override
    public ApiResult<List<ChannelName>> queryChannelNames() {
        String url = baseUrl + "/alpha3/api/v1/h5/sdk/vehicle/queryChannelNames";
        HttpEntity<Void> request = new HttpEntity<>(buildSdkHeaders());
        ResponseEntity<ApiResult<List<ChannelName>>> response = restTemplate.exchange(
                url, HttpMethod.GET, request, (Class<ApiResult<List<ChannelName>>>) (Class<?>) ApiResult.class
        );
        return response.getBody();
    }

    @Override
    public ApiResult<VehicleDetail> getVehicleDetail(Long vehicleId) {
        String url = UriComponentsBuilder.fromHttpUrl(baseUrl + "/alpha3/api/v1/h5/sdk/vehicle/detail")
                .queryParam("vehicleId", vehicleId)
                .toUriString();
        HttpEntity<Void> request = new HttpEntity<>(buildSdkHeaders());
        ResponseEntity<ApiResult<VehicleDetail>> response = restTemplate.exchange(
                url, HttpMethod.GET, request, (Class<ApiResult<VehicleDetail>>) (Class<?>) ApiResult.class
        );
        return response.getBody();
    }

    @Override
    public ApiResult<List<CalendarInfo>> queryDeviceCalendar(String devId, String channels, String beginDate, String endTime) {
        String url = UriComponentsBuilder.fromHttpUrl(baseUrl + "/alpha3/api/v1/h5/sdk/device/calendar/query")
                .queryParam("devId", devId)
                .queryParam("channels", channels)
                .queryParam("beginDate", beginDate)
                .queryParam("endTime", endTime)
                .toUriString();
        HttpEntity<Void> request = new HttpEntity<>(buildSdkHeaders());
        ResponseEntity<ApiResult<List<CalendarInfo>>> response = restTemplate.exchange(
                url, HttpMethod.GET, request, (Class<ApiResult<List<CalendarInfo>>>) (Class<?>) ApiResult.class
        );
        return response.getBody();
    }

    @Override
    public ApiResult<List<MediaFile>> queryDeviceFiles(String devId, String chan, String beginTime, String endTime, Integer timeout) {
        String url = UriComponentsBuilder.fromHttpUrl(baseUrl + "/alpha3/api/v1/h5/sdk/device/file/query")
                .queryParam("devId", devId)
                .queryParam("chan", chan)
                .queryParam("beginTime", beginTime)
                .queryParam("endTime", endTime)
                .queryParam("timeout", timeout)
                .toUriString();
        HttpEntity<Void> request = new HttpEntity<>(buildSdkHeaders());
        ResponseEntity<ApiResult<List<MediaFile>>> response = restTemplate.exchange(
                url, HttpMethod.GET, request, (Class<ApiResult<List<MediaFile>>>) (Class<?>) ApiResult.class
        );
        return response.getBody();
    }

    @Override
    public ApiResult<TaskInfo> createVideoDownloadTask(TaskCreateParam param) {
        String url = baseUrl + "/alpha3/api/v1/h5/sdk/device/task/create";
        HttpEntity<TaskCreateParam> request = new HttpEntity<>(param, buildSdkHeaders());
        ResponseEntity<ApiResult<TaskInfo>> response = restTemplate.postForEntity(
                url, request, (Class<ApiResult<TaskInfo>>) (Class<?>) ApiResult.class
        );
        return response.getBody();
    }

    @Override
    public ApiResult<TaskDetail> getDownloadTaskDetail(String taskId) {
        String url = UriComponentsBuilder.fromHttpUrl(baseUrl + "/alpha3/api/v1/h5/sdk/device/task/detail")
                .queryParam("taskId", taskId)
                .toUriString();
        HttpEntity<Void> request = new HttpEntity<>(buildSdkHeaders());
        ResponseEntity<ApiResult<TaskDetail>> response = restTemplate.exchange(
                url, HttpMethod.GET, request, (Class<ApiResult<TaskDetail>>) (Class<?>) ApiResult.class
        );
        return response.getBody();
    }

    // ------------------------------ 内部辅助类 ------------------------------


    /**
     * 鉴权接口请求参数
     */
    @Data
    private static class AuthRequest {
        private String userKey;
        private String sign;
    }

    /**
     * 鉴权接口返回信息
     */
    @Data
    private static class AuthInfo {
        private Integer appId;
        private Long tenantId;
        private String token;
        private String lang;
        private Long userId;

        private String sign;
    }
}