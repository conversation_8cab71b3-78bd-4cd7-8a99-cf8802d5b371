package com.yinshu.toms.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.fast.excel.util.ExcelUtils;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.toms.service.CityTrafficService;
import com.yinshu.toms.util.DataComparisonUtils;
import com.yinshu.toms.vo.city.*;
import com.yinshu.utils.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
public class CityTrafficServiceImpl implements CityTrafficService {

    @Resource
    DvisualHttpTemplate httpTemplate;

    /**
     * 忻州交通城市客运营运车辆按日//ads_xinzhou_city_bus_daily
     */
    private static final String ADS_XINZHOU_CITY_BUS_DAILY = "/api/100445/data.json?";

    /**
     * 忻州交通城市客运基础数据//ads_xinzhou_basic_data
     */
    private static final String ADS_XINZHOU_BASIC_DATA = "/api/100454/data.json?";

    /**
     * 忻州交通城市客运车辆运营概况按日//ads_xinzhou_bus_daily_operation_summary
     */
    private static final String ADS_XINZHOU_BUS_DAILY_OPERATION_SUMMARY = "/api/100470/data.json?";

    /**
     * 忻州交通城市客运公交车线路详情//ads_xinzhou_bus_route_detail
     */
    private static final String ADS_XINZHOU_BUS_ROUTE_DETAIL = "/api/100441/data.json?";

    /**
     * 忻州交通城市客运公交车站点详情//ads_xinzhou_bus_station_detail
     */
    private static final String ADS_XINZHOU_BUS_STATION_DETAIL = "/api/100474/data.json?";

    /**
     * 忻州交通城市客运车辆监测预警//ads_xinzhou_vehicle_monitor_warning
     */
    private static final String ADS_XINZHOU_VEHICLE_MONITOR_WARNING = "/api/100476/data.json?";

    /**
     * 忻州交通城市客运巡游网约营运概况按小时//ads_xinzhou_cruise_taxi_hourly_operation_summary
     */
    private static final String ADS_XINZHOU_CRUISE_TAXI_HOURLY_OPERATION_SUMMARY = "/api/100482/data.json?";

    /**
     * 忻州交通城市客运巡游网约营业时长排名按日//ads_xinzhou_cruise_taxi_daily_hours_rank
     */
    private static final String ADS_XINZHOU_CRUISE_TAXI_DAILY_HOURS_RANK = "/api/100486/data.json?";

    /**
     * 忻州交通城市客运巡游网约区域客运量排行按小时//ads_xinzhou_cruise_taxi_hour_area_passenger_rank
     */
    private static final String ADS_XINZHOU_CRUISE_TAXI_HOUR_AREA_PASSENGER_RANK = "/api/100500/data.json?";

    /***
     * 忻州车辆报警数据明细表//ads_warn_info_self
     */
    public static final String ADS_WARN_INFO_SELF = "/api/100588/data.json?";

    /**
     * 获取城市交通概览
     * @param jsonObject
     * @return
     */
    @Override
    public JSONObject getOverallSummary(JSONObject jsonObject) {
        jsonObject.put("filter","county = null order by dt desc");
        if (null != jsonObject.getJSONArray("dt") && jsonObject.getJSONArray("dt").size() == 2){
            jsonObject.put("filter","dt >= '" + jsonObject.getJSONArray("dt").getString(0) + "' and dt <= '" + jsonObject.getJSONArray("dt").getString(1) + "' and county != null order by dt desc");
        }
        JSONObject post = httpTemplate.post(ADS_XINZHOU_CITY_BUS_DAILY, jsonObject);
        if (null != post) {
            JSONArray list = post.getJSONArray("list");
            if (null != list && list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject obj = list.getJSONObject(i);
                    obj.put("county", "忻州市");
                }
                post.put("list", list);
            }
        }
        return post;
    }

    /**
     * 车辆分类
     * @param jsonObject
     * @return
     */
    @Override
    public JSONObject getVehicleClassification(JSONObject jsonObject) {
        jsonObject.put("filter","county = null and dt = '" + DateUtils.getDate() + "' ");
//        jsonObject.put("filter","county = null order by dt desc");
        JSONObject post = httpTemplate.post(ADS_XINZHOU_CITY_BUS_DAILY, jsonObject);
        Map<String, String> nameValuePairs = new HashMap<>();
        nameValuePairs.put("公交车", "bus_vehicles");
        nameValuePairs.put("巡游车", "cruising_vehicles");
        nameValuePairs.put("网约车", "ridehailing_vehicles");
        return DataComparisonUtils.assemblingPieData(post, nameValuePairs);
    }

    /**
     * 运力分类
     * @param jsonObject
     * @return
     */
    @Override
    public JSONObject getCapacityClassification(JSONObject jsonObject) {
        jsonObject.put("filter","county = null and dt = '" + DateUtils.getDate() + "' ");
//        jsonObject.put("filter","county = null order by dt desc");
        JSONObject post = httpTemplate.post(ADS_XINZHOU_CITY_BUS_DAILY, jsonObject);
        Map<String, String> nameValuePairs = new HashMap<>();
        nameValuePairs.put("公交车", "bus_capacity");
        nameValuePairs.put("巡游车", "cruising_capacity");
        nameValuePairs.put("网约车", "ridehailing_capacity");
        return DataComparisonUtils.assemblingPieData(post, nameValuePairs);
    }

    @Override
    public JSONObject getBasicData(JSONObject jsonObject) {
        jsonObject.put("filter","type = '" + jsonObject.getString("type") + "' ");
        return httpTemplate.post(ADS_XINZHOU_BASIC_DATA, jsonObject);
    }

    /**
     * 运营情况
     * @param jsonObject
     * @return
     */
    @Override
    public JSONObject getOperationSummary(JSONObject jsonObject) {

        String filter = "";
        // 1 公交车运营情况
        if ("1".equals(jsonObject.getString("selectType"))){
            filter = "county = null and company = null and line_name = null and type = '公交车' and dt = '" + DateUtils.getDate() + "' ";
        }
        // 2 公交车运营情况 更多的查询
        else if ("2".equals(jsonObject.getString("selectType"))){
            filter = "county != null and company = null and line_name != null and type = '公交车'";
            String fLineName = jsonObject.getString("f_line_name");
            if (null != fLineName && !fLineName.isEmpty()){
                filter += " and f_line_name like '%" + fLineName + "' ";
            }
            String county = jsonObject.getString("county");
            if (null!= county &&!county.isEmpty() && !"忻州市".equals(county)){
                filter += " and county = '" + county + "' ";
            }
            JSONArray dateRange = jsonObject.getJSONArray("date_range");
            if (null != dateRange && dateRange.size() == 2) {
                filter += " and dt >= '" + dateRange.getString(0) + "' and dt <= '" + dateRange.getString(1) + "' ";
            }
        }
        // 3 公交车行业详情
        else if ("3".equals(jsonObject.getString("selectType"))){
            filter = "county != null and company != null and line_name = null and type = '公交车'";
        }
        // 4 客运地图
        else if ("4".equals(jsonObject.getString("selectType"))){
            filter = "county != null and company != null and line_name = null ";
            String type = jsonObject.getString("type");
            if (null!= type && !type.isEmpty()){
                filter += " and type = '" + type + "' ";
            }
        }
        jsonObject.put("filter",filter);
        return httpTemplate.post(ADS_XINZHOU_BUS_DAILY_OPERATION_SUMMARY,jsonObject);
    }

    /**
     * 公交车燃料类型占比
     * @param jsonObject
     * @return
     */
    @Override
    public JSONObject getBusFuelType(JSONObject jsonObject) {
        jsonObject.put("filter","type = '公交车'");
        JSONObject post = httpTemplate.post(ADS_XINZHOU_BASIC_DATA, jsonObject);
        Map<String, String> nameValuePairs = new HashMap<>();
        nameValuePairs.put("柴油", "diesel_oil");
        nameValuePairs.put("天然气", "natural_gas");
        nameValuePairs.put("纯电动", "electric_power");
        nameValuePairs.put("汽油", "gasoline_oil");
        nameValuePairs.put("其他类型", "others_type");
        return DataComparisonUtils.assemblingPieData(post, nameValuePairs);
    }

    /**
     * 客运地图
     * @param jsonObject
     * @return
     */
    @Override
    public JSONObject getBusRouteDetail(JSONObject jsonObject) {
        String filter = "";
        // 1 公交车 线路更多
        if("1".equals(jsonObject.getString("selectType"))){
            filter = "county!= null and line_name != null";
            String fLineName = jsonObject.getString("f_line_name");
            if (null != fLineName && !fLineName.isEmpty()){
                filter += " and line_name like '" + fLineName + "' ";
            }
            String county = jsonObject.getString("county");
            if (null!= county &&!county.isEmpty() && !"忻州市".equals(county)){
                filter += " and county = '" + county + "' ";
            }
        }
        // 2 公交车 客运地图 线路
        else if("2".equals(jsonObject.getString("selectType"))){
            filter = "county != null and company != null and line_name != null";
        }
        jsonObject.put("filter",filter);
        return httpTemplate.post(ADS_XINZHOU_BUS_ROUTE_DETAIL, jsonObject);
    }

    /**
     *  公交车站点详情
     *  @param jsonObject
     * @return
     */
    @Override
    public JSONObject getBusStationDetail(JSONObject jsonObject) {
        String filter = "";
        // 1 公交车 站点更多
        if("1".equals(jsonObject.getString("selectType"))){
            filter = "county != null and station_name != null";
            String fLineName = jsonObject.getString("f_station_name");
            if (null != fLineName && !fLineName.isEmpty()){
                filter += " and station_name like '" + fLineName + "' ";
            }
            String county = jsonObject.getString("county");
            if (null!= county &&!county.isEmpty() && !"忻州市".equals(county)){
                filter += " and county = '" + county + "' ";
            }
        }
        // 2 公交车 客运地图 站点
        else if("2".equals(jsonObject.getString("selectType"))){
            filter = "county != null and station_name != null";
        }
        jsonObject.put("filter",filter);
        return httpTemplate.post(ADS_XINZHOU_BUS_STATION_DETAIL, jsonObject);
    }

    /**
     *  客运地图的数据 公交车、巡游车、网约车
     * @param jsonObject
     * @return
     */
    @Override
    public JSONObject getPassengerMapData(JSONObject jsonObject) {
        String filter = "type = '实时车辆' and ";
        // 1 公交车 客运地图
        if("1".equals(jsonObject.getString("selectType"))){
            filter = "county != null and company != null and vehicle_type = '公交车'";
        }
        // 2 巡游车 客运地图
        else if("2".equals(jsonObject.getString("selectType"))){
            filter = "county != null and company != null and vehicle_type = '巡游车'";
        }
        // 3 网约车 客运地图
        else if("3".equals(jsonObject.getString("selectType"))){
            filter = "county != null and company != null and vehicle_type = '网约车'";
        }
        jsonObject.put("filter",filter);
        return httpTemplate.post(ADS_XINZHOU_VEHICLE_MONITOR_WARNING, jsonObject);
    }

    /**
     *  运营概览
     * @param jsonObject
     * @return
     */
    @Override
    public JSONObject getOperationOverview(JSONObject jsonObject) {
        String filter;
        //获取卡片数据
        if ("1".equals(jsonObject.getString("selectType"))){
            filter = " hour = null and vehicle_type = '" + jsonObject.getString("type") + "' " + " and dt = '" + DateUtils.getDate() + "' ";
//            filter = " hour = null and vehicle_type = '" + jsonObject.getString("type") + "' ";
            jsonObject.put("filter",filter);
            return httpTemplate.post(ADS_XINZHOU_CRUISE_TAXI_HOURLY_OPERATION_SUMMARY, jsonObject);
        }
        else if ("2".equals(jsonObject.getString("selectType"))){
            String field = jsonObject.getString("field");
            filter = " hour != null and vehicle_type = '" + jsonObject.getString("type") + "' and " + field + " != null order by hour";
            jsonObject.put("filter",filter);
            JSONObject post = httpTemplate.post(ADS_XINZHOU_CRUISE_TAXI_HOURLY_OPERATION_SUMMARY, jsonObject);
            if ("operation_mileage".equals(field)) {
                // 运营里程 / 运行里程
                JSONObject jsonObject1 = DataComparisonUtils.assemblingMoreBarOrLineData(post, "hour", "operation_mileage", "run_mileage");
                // 计算实载率
                JSONArray opArr = jsonObject1.getJSONArray("operation_mileageData");
                JSONArray runArr = jsonObject1.getJSONArray("run_mileageData");
                JSONArray resultArray = new JSONArray();

                for (int i = 0; i < opArr.size() && i < runArr.size(); i++) {
                    BigDecimal op = new BigDecimal(opArr.getBigDecimal(i).toString());
                    BigDecimal run = new BigDecimal(runArr.getBigDecimal(i).toString());
                    BigDecimal value = (run.compareTo(BigDecimal.ZERO) != 0)
                            ? op.divide(run, 2, RoundingMode.HALF_UP)
                            : BigDecimal.ZERO;
                    resultArray.add(value.multiply(new BigDecimal(100)));
                }
                jsonObject1.put("seriesData", resultArray);
                return jsonObject1;
            }else {
                return DataComparisonUtils.assemblingBarOrLineData(post, "hour", field);
            }
        }
        return new JSONObject();
    }

    /**
     *  营业时长排名
     * @param jsonObject
     * @return
     */
    @Override
    public JSONObject getBusinessHoursRanking(JSONObject jsonObject) {
        String vehicleType = jsonObject.getString("vehicle_type");
//        String filter = "vehicle_type = '" + vehicleType + "' ";
        String filter = "vehicle_type = '" + vehicleType + "' and dt = '" + DateUtils.getDate() + "' ";
        jsonObject.put("filter",filter);
        jsonObject.put("n",6);
        JSONObject post = httpTemplate.post(ADS_XINZHOU_CRUISE_TAXI_DAILY_HOURS_RANK, jsonObject);
        return DataComparisonUtils.assemblingMoreBarOrLineData(post, "carid", "operation_duration","online_duration");
    }

    /**
     *  公交车、巡游车、网约车 监测预警
     * @param jsonObject
     * @return
     */
    @Override
    public JSONObject getVehicleMonitorWarning(JSONObject jsonObject) {
        String vehicleType = jsonObject.getString("vehicle_type");
        String warnType = jsonObject.getString("warn_type");
        String filter = " cartype = '" + vehicleType + "' ";
        if (null != warnType && !warnType.isEmpty()){
            filter += " and warntype = '" + warnType + "' ";
        }
        // 日期范围
        JSONArray dtRange = jsonObject.getJSONArray("dt");
        if (dtRange != null && dtRange.size() == 2) {
            String startDate = dtRange.getString(0);
            String endDate = dtRange.getString(1);
            filter += " and warndt >= '" + startDate + "' and warndt <= '" + endDate + "' ";
        }else {
            filter += " and warndt = '" + DateUtils.getDate() + "' ";
        }
        //车牌号
        String carid = jsonObject.getString("carid");
        if (null!= carid &&!carid.isEmpty()){
            filter += " and carid like '%" + carid + "' ";
        }
        //驾驶员
        String name = jsonObject.getString("name");
        if (null!= name &&!name.isEmpty()){
            filter += " and driver like '%" + name + "' ";
        }
        jsonObject.put("filter",filter);
        return httpTemplate.post(ADS_WARN_INFO_SELF, jsonObject);
    }

    /**
     *  疑似黑车的数据
     * @param jsonObject
     * @return
     */
    @Override
    public JSONObject getSuspectedBlackCarData(JSONObject jsonObject) {
        String filter = "hour != null and type = '疑似黑车' ";
        Date dt = jsonObject.getDate("dt");
        if (null != dt){
            filter += " and dt = '" + DateUtils.format(dt, DateUtils.YYYY_MM_DD) + "' ";
        }else {
            filter += " and dt = '" + DateUtils.getDate() + "' ";
        }
        String carid = jsonObject.getString("carid");
        if (null!= carid &&!carid.isEmpty()){
            filter += " and carid = '" + carid + "' ";
        }
        jsonObject.put("filter",filter);
        return httpTemplate.post(ADS_XINZHOU_VEHICLE_MONITOR_WARNING, jsonObject);
    }

    /**
     *  OD分析
     * @param jsonObject
     * @return
     */
    @Override
    public JSONObject getODAnalysis(JSONObject jsonObject) {
        String dt = jsonObject.getString("dt");
        String filter = "";
        if (null != dt && !dt.isEmpty()){
            filter = "dt = '" + dt + "' ";
        }
        String type = jsonObject.getString("type");
        if (null != type && !type.isEmpty()){
            filter += " and type = '" + type + "' ";
        }
        jsonObject.put("filter",filter + " order by passenger_volume desc");
        return httpTemplate.post(ADS_XINZHOU_CRUISE_TAXI_HOUR_AREA_PASSENGER_RANK, jsonObject);
    }

    /**
     *  导出
     * @param jsonObject
     * @return
     */
    @Override
    public void getExport(JSONObject jsonObject) {
        // 1 公交线路详情导出
        if ("1".equals(jsonObject.getString("exportType"))) {
            JSONObject operationSummary = getBusRouteDetail(jsonObject);
            if (operationSummary != null && !operationSummary.getJSONArray("list").isEmpty()) {
                JSONArray list = operationSummary.getJSONArray("list");

                for (int i = 0; i < list.size(); i++) {
                    JSONObject item = list.getJSONObject(i);

                    BigDecimal averageStopDistance = item.getBigDecimal("average_stop_distance");
                    BigDecimal stationNum = item.getBigDecimal("station_num");

                    // 处理 null 值为 0（防御性写法）
                    if (averageStopDistance == null) {
                        averageStopDistance = BigDecimal.ZERO;
                    }
                    if (stationNum == null || stationNum.compareTo(BigDecimal.ZERO) == 0) {
                        item.put("averageStopDistance", BigDecimal.ZERO);
                    } else {
                        BigDecimal result = averageStopDistance
                                .multiply(BigDecimal.valueOf(2))
                                .divide(stationNum, 2, RoundingMode.HALF_UP);
                        item.put("averageStopDistance", result);
                    }
                }
            }
            ExcelUtils.exportExcelSheet(RouteOverviewExportVo.class, operationSummary);
        }
        // 2 公交站点详情导出
        else if ("2".equals(jsonObject.getString("exportType"))) {
            JSONObject busStationDetail = getBusStationDetail(jsonObject);
            ExcelUtils.exportExcelSheet(BusSiteExportVo.class, busStationDetail);
        }
        // 3 公交车运营详情
        else if ("3".equals(jsonObject.getString("exportType"))) {
            JSONObject busRouteDetail = getOperationSummary(jsonObject);
            ExcelUtils.exportExcelSheet(BusOperationExportVo.class, busRouteDetail);
        }
        // 4 预警车辆详情
        else if ("4".equals(jsonObject.getString("exportType"))) {
            JSONObject operationOverview = getVehicleMonitorWarning(jsonObject);
            ExcelUtils.exportExcelSheet(WarningVehicleExportVo.class, operationOverview);
        }
        // 5 运营概览
        else  if ("5".equals(jsonObject.getString("exportType"))) {
            JSONObject operationOverview = getOverallSummary(jsonObject);
            ExcelUtils.exportExcelSheet(OverallSummaryExportVo.class, operationOverview);
        }
    }
}