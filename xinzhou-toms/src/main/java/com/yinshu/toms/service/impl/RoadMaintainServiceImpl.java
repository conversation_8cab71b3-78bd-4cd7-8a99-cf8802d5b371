package com.yinshu.toms.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.toms.service.RoadMaintainService;
import com.yinshu.toms.util.DataComparisonUtils;
import com.yinshu.utils.DateUtils;
import com.yinshu.utils.FilterCreate;
import com.yinshu.utils.ReplaceUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 公路路网-养护
 *
 */
@Service
public class RoadMaintainServiceImpl implements RoadMaintainService {

    @Resource
    DvisualHttpTemplate template;

    /***
     * 养护路面灾害趋势//ads_xinzhou_road_disaster_trend
     */
    public static final String ADS_XINZHOU_ROAD_DISASTER_TREND = "/api/100460/data.json?";
    /***
     * 养护路面灾害详情//ads_xinzhou_road_disaster_details
     */
    public static final String ADS_XINZHOU_ROAD_DISASTER_DETAILS = "/api/100462/data.json?";
    /***
     * 养护公路技术状况//ads_xinzhou_road_technical_condition
     */
    public static final String ADS_XINZHOU_ROAD_TECHNICAL_CONDITION = "/api/100468/data.json?";
    /***
     * 养护mqi指标排名//ads_xinzhou_mqi_index_rank
     */
    public static final String ADS_XINZHOU_MQI_INDEX_RANK = "/api/100469/data.json?";

    /**
	 * 获取养护路面灾害趋势
	 * @param query
	 * @return
	 */
    public JSONObject getRoadDisasterTrend(JSONObject query) {
        String year = String.valueOf(DateUtils.getCurrentYear());
        String filterTemplate = "id like '{year}' order by id";
        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                "year", year));
        JSONObject resultData = template.post(ADS_XINZHOU_ROAD_DISASTER_TREND, query);
        DataComparisonUtils.assemblingBarOrLineData(resultData, "id", "num");
        return resultData;
    }

    /**
     * 获取养护路面灾害详情
     * @param query
     * @return
     */
    public JSONObject getRoadDisasterDetail(JSONObject query) {
        String area = query.getString("area");
        String filterTemplate = new FilterCreate()
                .and(StringUtils.isNotBlank(area) && !"忻州市".equals(area), "county = '{area}'")
                .concat("order by time desc").toString();
        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                "area", area));
        JSONObject resultData = template.post(ADS_XINZHOU_ROAD_DISASTER_DETAILS, query);
        return resultData;
    }

    /**
     * 获取养护公路技术状况
     * @param query
     * @return
     */
    public JSONObject getRoadTechnicalCondition(JSONObject query) {
        String year = String.valueOf(DateUtils.getCurrentYear());
        String filterTemplate = "id = '{year}'";
        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                "year", year));
        JSONObject resultData = template.post(ADS_XINZHOU_ROAD_TECHNICAL_CONDITION, query);
        return DataComparisonUtils.getResultFirst(resultData);
    }

    /**
     * 获取MQI指标排名
     * @param query
     * @return
     */
    public JSONObject getRoadMQIRank(JSONObject query) {
        String area = query.getString("area");
        String roadName = query.getString("roadName");
        String year = String.valueOf(DateUtils.getCurrentYear());
        String filterTemplate = new FilterCreate()
                .and("year = '{year}'")
                .and(!"忻州市".equals(area), "county = '{area}'")
                .and(StringUtils.isNotBlank(roadName), "road_name like '{roadName}'")
                .concat("order by mqi desc").toString();
        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                "year", year, "area", area, "roadName", roadName));
        JSONObject resultData = template.post(ADS_XINZHOU_MQI_INDEX_RANK, query);
        return resultData;
    }



}
