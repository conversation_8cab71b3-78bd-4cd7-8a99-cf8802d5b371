package com.yinshu.toms.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * 公共方法
 *
 */
public interface BasicResourceService {
    
	/**
	 * 获取地市行政区划
	 * @param query
	 * @return
	 */
	JSONArray getCountyList(JSONObject query);

	/**
	 * 获取公路路网
	 * @param query
	 * @return
	 */
	JSONArray getRoadNetwork(JSONObject query);

	/**
	 * 获取路网详细信息
	 * @param query
	 * @return
	 */
	JSONObject getRoadNetworkInfo(JSONObject query);

	/**
	 * 获取构造物
	 * @param query
	 * @return
	 */
	JSONObject getStructureList(JSONObject query);

	/**
	 * 获取公路设施
	 * @param query
	 * @return
	 */
	JSONObject getHighwayFacilities(JSONObject query);
	
	/**
     * 获取路网工程
     * @param query
     * @return
     */
    JSONObject getRoadNetworkProjectList(JSONObject query);
}
