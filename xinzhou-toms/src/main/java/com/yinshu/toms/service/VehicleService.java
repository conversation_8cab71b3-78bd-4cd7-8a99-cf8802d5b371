package com.yinshu.toms.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * 车辆
 * <AUTHOR>
 *
 */
public interface VehicleService {
	
	/**
	 * 根据车牌号码获取实时定位
	 */
    JSONObject getRealLocation(JSONObject query);
	/**
	 * 获取历史轨迹
	 *
	 * @return
	 */
	JSONArray getHistoryLocation(JSONObject query);
	
	/**
	 * 获取多辆车的实时定位
	 * 
	 * @return
	 */
	JSONObject getMultiRealLocation(JSONObject query);
}
