package com.yinshu.toms.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.toms.service.RoadEventService;
import com.yinshu.toms.util.DataComparisonUtils;
import com.yinshu.toms.util.UtilService;
import com.yinshu.utils.DateUtils;
import com.yinshu.utils.ReplaceUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 公路路网-事件
 */
@Service
public class RoadEventServiceImpl implements RoadEventService {

    /***
     * 公路实时事件//ads_xinzhou_road_real_event
     */
    public static final String ADS_XINZHOU_ROAD_REAL_EVENT = "/api/100481/data.json?";
    /***
     * 公路实时事件排名//ads_xinzhou_road_real_event_rank
     */
    public static final String ADS_XINZHOU_ROAD_REAL_EVENT_RANK = "/api/100483/data.json?";
    /***
     * 公路事件详情按月//ads_xinzhou_road_event_details_mth
     */
    public static final String ADS_XINZHOU_ROAD_EVENT_DETAILS_MTH = "/api/100484/data.json?";
    /***
     * 公路事件详情按年季度//ads_xinzhou_road_event_details_year_quarter
     */
    public static final String ADS_XINZHOU_ROAD_EVENT_DETAILS_YEAR_QUARTER = "/api/100485/data.json?";
    /***
     * 公路事件分类占比//ads_xinzhou_road_event_classify_proportion
     */
    public static final String ADS_XINZHOU_ROAD_EVENT_CLASSIFY_PROPORTION = "/api/100487/data.json?";
    @Resource
    DvisualHttpTemplate template;

    /**
     * 获取公路实时事件
     *
     * @param query
     * @return
     */
    public JSONArray getRoadEvent(JSONObject query) {
        String day = UtilService.RealOrTest(DateUtils.getDate(), "2025-06-06");
        String filterTemplate = "day = '{day}' order by occurrence_time desc";
        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                "day", day));
        query.put("s", 0);
        query.put("n", 10);
        JSONObject resultData = template.post(ADS_XINZHOU_ROAD_REAL_EVENT, query);
        return DataComparisonUtils.getResultList(resultData);
    }

    /**
     * 获取公路实时事件排名
     *
     * @param query
     * @return
     */
    public JSONArray getRoadEventRank(JSONObject query) {
        String filterTemplate = "order by event_num desc";
        query.put("filter", filterTemplate);
        query.put("s", 0);
        query.put("n", 10);
        JSONObject resultData = template.post(ADS_XINZHOU_ROAD_REAL_EVENT_RANK, query);
        return DataComparisonUtils.getResultList(resultData);
    }

    /**
     * 获取公路事件详情
     *
     * @param query
     * @return
     */
    public JSONObject getRoadEventDetail(JSONObject query) {
        String dateType = query.getString("dateExtraActive");
        String date = query.getJSONArray("date").getString(0);
        String filterTemplate = "";
        JSONObject resultData = new JSONObject();
        if ("月".equals(dateType)) {
            filterTemplate = "month = '{date}' order by traffic_accident desc";
            query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                    "date", date));
            resultData = template.post(ADS_XINZHOU_ROAD_EVENT_DETAILS_MTH, query);
            JSONArray resultList = DataComparisonUtils.getResultList(resultData);
            for (int i = 0; i < resultList.size(); i++) {
                JSONObject item = resultList.getJSONObject(i);
                item.put("date", item.get("month"));
            }
        } else if ("季度".equals(dateType)) {
            String year = query.getString("dateExtraYear");
            String quarter = "0" + query.getString("dateExtraQuarter");
            filterTemplate = "year = '{year}' and quarter = '{quarter}' order by traffic_accident desc";
            query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                    "year", year, "quarter", quarter));
            resultData = template.post(ADS_XINZHOU_ROAD_EVENT_DETAILS_YEAR_QUARTER, query);
            JSONArray resultList = DataComparisonUtils.getResultList(resultData);
            for (int i = 0; i < resultList.size(); i++) {
                JSONObject item = resultList.getJSONObject(i);
                item.put("date", item.get("year") + "年" + item.get("quarter") + "季度");
            }
        } else if ("年".equals(dateType)) {
            filterTemplate = "year = '{date}' and quarter = null order by traffic_accident desc";
            query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                    "date", date.split("-")[0]));
            resultData = template.post(ADS_XINZHOU_ROAD_EVENT_DETAILS_YEAR_QUARTER, query);
            JSONArray resultList = DataComparisonUtils.getResultList(resultData);
            for (int i = 0; i < resultList.size(); i++) {
                JSONObject item = resultList.getJSONObject(i);
                item.put("date", item.get("year"));
            }
        }
        return resultData;
    }

    /**
     * 获取公路事件占比
     *
     * @param query
     * @return
     */
    public JSONObject getRoadEventProportion(JSONObject query) {
        String day = UtilService.RealOrTest(DateUtils.getDate(), "2025-06-06");
        String filterTemplate = "dt = '{day}' ";
        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                "day", day));
        JSONObject resultData = template.post(ADS_XINZHOU_ROAD_EVENT_CLASSIFY_PROPORTION, query);
        return DataComparisonUtils.getResultFirst(resultData);
    }

    @Override
    public JSONObject getRoadEventMap(JSONObject query) {
        return template.post(ADS_XINZHOU_ROAD_REAL_EVENT, query);
    }


}
