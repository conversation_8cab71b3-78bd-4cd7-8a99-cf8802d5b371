package com.yinshu.toms.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.toms.common.RemoteUrlConstants;
import com.yinshu.toms.service.RoadTransportService;
import org.locationtech.jts.geom.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RoadTransportServiceImpl
 * @description TODO 道路运输服务接口
 * @date 2025/6/16 11:04
 **/
@Service
public class RoadTransportServiceImpl implements RoadTransportService {

    @Resource
    DvisualHttpTemplate template;

    /**
     * @param query 查询参数
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @description //TODO 基础指标
     * @date 2025/6/16 11:20
     **/
    @Override
    public JSONObject adsBasicIndicatorData(JSONObject query) {
        return template.post(RemoteUrlConstants.ADS_BASIC_INDICATOR_DATA, query);
    }

    /*
     * <AUTHOR>
     * @description //TODO 客运站点监测
     * @date 2025/6/16 16:37
     * @param query
     * @return com.alibaba.fastjson.JSONObject
     **/
    @Override
    public JSONObject adsPassengerTransportStation(JSONObject query) {
        return template.post(RemoteUrlConstants.ADS_PASSENGER_TRANSPORT_STATION, query);
    }

    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 今日概览趋势小时
     * @date 2025/6/16 11:20
     **/
    @Override
    public JSONObject adsDailyOverviewTrendHourly(JSONObject query) {
        return template.post(RemoteUrlConstants.ADS_DAILY_OVERVIEW_TREND_HOURLY, query);
    }

    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 每日概览趋势
     * @date 2025/6/16 11:20
     **/
    @Override
    public JSONObject adsDailyOverviewTrend(JSONObject query) {
        return template.post(RemoteUrlConstants.ADS_DAILY_OVERVIEW_TREND, query);
    }

    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 危货运量周变化趋势
     * @date 2025/6/16 11:20
     **/
    @Override
    public JSONObject adsDangerousGoodsVolWeeklyTrend(JSONObject query) {
        return template.post(RemoteUrlConstants.ADS_DANGEROUS_GOODS_VOL_WEEKLY_TREND, query);
    }

    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 道路运输概览占比
     * @date 2025/6/16 11:20
     **/
    @Override
    public JSONObject adsRoadTransportOverviewRatio(JSONObject query) {
        return template.post(RemoteUrlConstants.ADS_ROAD_TRANSPORT_OVERVIEW_RATIO, query);
    }

    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 普通货运车数量详情
     * @date 2025/6/16 11:20
     **/
    @Override
    public JSONObject adsGeneralFreightTruckQuantityDetail(JSONObject query) {
        return template.post(RemoteUrlConstants.ADS_GENERAL_FREIGHT_TRUCK_QUANTITY_DETAIL, query);
    }

    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 运输地图车辆基本信息
     * @date 2025/6/16 11:20
     **/
    @Override
    public JSONObject adsTransportMapVehicleBaseInfo(JSONObject query) {
        JSONObject post = template.post(RemoteUrlConstants.ADS_TRANSPORT_MAP_VEHICLE_BASE_INFO, query);
        // 是否需要根据范围来过滤
        if (query.containsKey("location")) {
            JSONObject copy = new JSONObject(post.getInnerMap());
            // todo 目标点
            // 1、转区域数组
            String location = query.getString("location");
            String[] split = location.split(",");
            List<Coordinate> points = new LinkedList<>();
            for (int i = 0; i < split.length; i += 2) {
                if (i + 1 < split.length) {
                    points.add(new Coordinate(Double.parseDouble(split[i]), Double.parseDouble(split[i + 1])));
                }
            }
            GeometryFactory geometryFactory = new GeometryFactory();
            LinearRing shell = geometryFactory.createLinearRing(points.toArray(new Coordinate[0]));
            Polygon polygon = geometryFactory.createPolygon(shell);
            // todo 区域点
            JSONArray list = post.getJSONArray("list");
            List<LinkedHashMap<String, Object>> data = new ArrayList<>();
            for (Object o : list) {
                if (o instanceof LinkedHashMap) {
                    LinkedHashMap<String, Object> map = (LinkedHashMap<String, Object>) o;
                    if (map.containsKey("lng") && map.containsKey("lat")) {
                        // 目标点有多个，只要有一个包含就追加
                        List<Coordinate> targetCoords = new ArrayList<>();
                        targetCoords.add(new Coordinate((Double) map.get("lng"), (Double) map.get("lat")));
                        // 过滤出在多边形内的点
                        for (Coordinate coord : targetCoords) {
                            Point point = geometryFactory.createPoint(coord);
                            if (polygon.contains(point)) {
                                data.add(map);
                                break;
                            }
                        }
                    }
                }
            }
            // 3、修改数据
            copy.put("total", data.size());
            copy.put("list", data);
        }
        return post;
    }

    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 预警车辆详情
     * @date 2025/6/16 11:20
     **/
    @Override
    public JSONObject adsWarningVehicleDetail(JSONObject query) {
        return template.post(RemoteUrlConstants.ads_warn_info_self, query);
    }

    /**
     * @param query 查询类
     * @return com.yinshu.utils.ResultVO<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @description //TODO 危货运数据排名
     * @date 2025/6/16 11:20
     **/
    @Override
    public JSONObject adsDgDataRank(JSONObject query) {
        return template.post(RemoteUrlConstants.ADS_DG_DATA_RANK, query);
    }
}
