package com.yinshu.toms.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.toms.common.RemoteUrlConstants;
import com.yinshu.toms.common.RequestFilterBuilder;
import com.yinshu.toms.service.BuildTransportService;
import com.yinshu.toms.vo.road.transport.ChartVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
public class BuildTransportServiceImpl implements BuildTransportService {

    @Resource
    DvisualHttpTemplate template;

    /**
     * 获取投资进度在建工程
     *
     * @param query
     * @return
     */
    @Override
    public JSONObject getInvestmentProgress(JSONObject query) {
//        获取今年至前五年的数据
        int currentYear = LocalDate.now().getYear();
        query.put("filter", "(year = '" + currentYear + "' " + "or year = '" + (currentYear - 1) + "' "
                + "or year = '" + (currentYear - 2) + "' " + "or year = '" + (currentYear - 3)
                + "' " + "or year = '" + (currentYear - 4) + "') order by year asc");
        JSONObject post = template.post(RemoteUrlConstants.ads_invest_construction_overview, query);
        JSONArray list = post.getJSONArray("list");
//        基础指标数据
        Map<String, Object> basicIndicatorMap = new HashMap<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject obj = list.getJSONObject(i);
            if (Integer.parseInt((String) obj.get("year")) == currentYear) {
                basicIndicatorMap.putAll(obj);
            }
        }
        post.put("basicIndicator", basicIndicatorMap);

        // 使用 Stream 提取所有 "年" 字段的值,也就是前端图表的x轴
        List<String> xAxisData = IntStream.range(0, list.size())
                .mapToObj(list::getJSONObject)
                .map(obj -> obj.get("year") + "年")
                .collect(Collectors.toList());
        post.put("xAxisData", xAxisData);
//批复总投资
        List<String> approved_investment = IntStream.range(0, list.size())
                .mapToObj(list::getJSONObject)
                .map(obj -> obj.get("approved_investment").toString())
                .collect(Collectors.toList());
        post.put("approved_investment", approved_investment);
//地方配套资金
        List<String> local_support_funds = IntStream.range(0, list.size())
                .mapToObj(list::getJSONObject)
                .map(obj -> obj.get("local_support_funds").toString())
                .collect(Collectors.toList());
        post.put("local_support_funds", local_support_funds);
//所有工程
        List<String> all_projects = IntStream.range(0, list.size())
                .mapToObj(list::getJSONObject)
                .map(obj -> obj.get("all_projects").toString())
                .collect(Collectors.toList());
        post.put("all_projects", all_projects);
//        续建工程
        List<String> continuing_projects = IntStream.range(0, list.size())
                .mapToObj(list::getJSONObject)
                .map(obj -> obj.get("continuing_projects").toString())
                .collect(Collectors.toList());
        post.put("continuing_projects", continuing_projects);

        // 提取并去重 investment_type,也就是交通固定资产的所有类型
        List<String> uniqueInvestmentTypes = new ArrayList<>();
        Set<String> seen = new HashSet<>();
        for (int i = 0; i < list.size(); i++) {
            JSONArray investmentTypeArray = list.getJSONObject(i).getJSONArray("investment_type");

            for (int j = 0; j < investmentTypeArray.size(); j++) {
                String type = investmentTypeArray.getString(j);
                if (!seen.contains(type)) {
                    seen.add(type);
                    uniqueInvestmentTypes.add(type);
                }
            }
        }
        post.put("legendData", uniqueInvestmentTypes);

//        交通固定资产,list已经按照年排序了
        List<ChartVO> chartVOList = new ArrayList<>();
        for (String type : uniqueInvestmentTypes) {
            ChartVO chartVO = new ChartVO();
            chartVO.setName(type);
            chartVO.setType("bar");
            List<String> data = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                JSONObject obj = list.getJSONObject(i);
                JSONArray investmentType = obj.getJSONArray("investment_type");
                JSONArray investmentNum = obj.getJSONArray("investment_num");
                int indexOf = investmentType.indexOf(type);
                data.add(investmentNum.get(indexOf).toString());
            }
            chartVO.setData(data);
            chartVOList.add(chartVO);
        }
        post.put("series", chartVOList);
        return post;
    }

    /**
     * 获取工程项目信息录入
     *
     * @param query
     * @return
     */
    @Override
    public JSONObject getProjectInfo(JSONObject query) {
        StringBuilder stringBuilder = new StringBuilder();
        if (query.get("project_name") != null && !"".equals(query.get("project_name"))) {
            stringBuilder.append("project_name = '").append(query.get("project_name")).append("'");
        }
        if (query.get("approval_document") != null && !"".equals(query.get("approval_document"))) {
            if (stringBuilder.length()>0){
                stringBuilder.append(" and ");
            }
            stringBuilder.append("approval_document='").append(query.get("approval_document")).append("'");
        }
        if (StringUtils.isNotBlank(query.getString("filter"))) {
            stringBuilder.append(" ").append(query.getString("filter"));
        }
        query.put("filter", stringBuilder.toString());
        JSONObject post = template.post(RemoteUrlConstants.ads_project_progress_ranking, query);
        return post;
    }

}
