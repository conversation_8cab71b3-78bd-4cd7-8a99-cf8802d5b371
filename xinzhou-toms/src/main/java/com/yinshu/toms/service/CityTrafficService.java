package com.yinshu.toms.service;

import com.alibaba.fastjson.JSONObject;

public interface CityTrafficService {

    /**
     * 获取城市交通概览
     * @param jsonObject
     * @return
     */
    public JSONObject getOverallSummary(JSONObject jsonObject);

    /**
     * 车辆分类
     * @param jsonObject
     * @return
     */
    public JSONObject getVehicleClassification(JSONObject jsonObject);

    /**
     * 运力分类
     * @param jsonObject
     * @return
     */
    public JSONObject getCapacityClassification(JSONObject jsonObject);

    /**
     *  公交车、巡游车、网约车基础数据
     *  @param jsonObject
     * @return
     */
    public JSONObject getBasicData(JSONObject jsonObject);

    /**
     *  运营数据
     *  @param jsonObject
     * @return
     */
    public JSONObject getOperationSummary(JSONObject jsonObject);

    /**
     *  公交车燃料类型占比
     *  @param jsonObject
     * @return
     */
    public JSONObject getBusFuelType(JSONObject jsonObject);

    /**
     *  公交车线路详情
     *  @param jsonObject
     * @return
     */
    public JSONObject getBusRouteDetail(JSONObject jsonObject);

    /**
     *  公交车站点详情
     *  @param jsonObject
     * @return
     */
    public JSONObject getBusStationDetail(JSONObject jsonObject);

    /**
     *  客运地图的数据 公交车、巡游车、网约车
     *  @param jsonObject
     * @return
     */
    public JSONObject getPassengerMapData(JSONObject jsonObject);

    /**
     *  巡游车、网约车 运营概览
     * @param jsonObject
     * @return
     */
    public JSONObject getOperationOverview(JSONObject jsonObject);

    /**
     * 营业时长排名
     * @param jsonObject
     * @return
     */
    public JSONObject getBusinessHoursRanking(JSONObject jsonObject);

    /**
     * 公交车、巡游车、网约车 监测预警
     * @param jsonObject
     * @return
     */
    public JSONObject getVehicleMonitorWarning(JSONObject jsonObject);

    /**
     * 疑似黑车的数据
     * @param jsonObject
     * @return
     */
    public JSONObject getSuspectedBlackCarData(JSONObject jsonObject);

    /**
     * OD分析
     * @param jsonObject
     * @return
     */
    public JSONObject getODAnalysis(JSONObject jsonObject);

    /**
     * 导出
     * @param jsonObject
     * @return
     */
    public void getExport(JSONObject jsonObject);
}
