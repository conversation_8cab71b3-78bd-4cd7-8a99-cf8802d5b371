package com.yinshu.toms.service;

import com.alibaba.fastjson.JSONObject;

/**
 * 综合监测
 */
public interface IntegratedMonitorService {
    /**
     * 营运车辆概览、经营业户概览、从业人员概览
     * @param query
     * @return
     */
    JSONObject getBaseInfo(JSONObject query);

    /**
     * 实时对接数据
     * @param query
     * @return
     */
    JSONObject getDockingData(JSONObject query);

    /**
     * 公路概况
     * @param query
     * @return
     */
    JSONObject getRoadMileage(JSONObject query);

    /**
     * 桥梁
     * @param query
     * @return
     */
    JSONObject getRoadBridge(JSONObject query);

    /**
     * 隧道
     * @param query
     * @return
     */
    JSONObject getRoadTunnel(JSONObject query);
}
