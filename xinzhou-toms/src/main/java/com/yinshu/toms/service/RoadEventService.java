package com.yinshu.toms.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * 公路路网-事件
 *
 */
public interface RoadEventService {
    
	/**
	 * 获取公路实时事件
	 * @param query
	 * @return
	 */
	JSONArray getRoadEvent(JSONObject query);
	/**
	 * 获取公路实时事件排名
	 * @param query
	 * @return
	 */
	JSONArray getRoadEventRank(JSONObject query);

	/**
	 * 获取公路事件详情
	 * @param query
	 * @return
	 */
	JSONObject getRoadEventDetail(JSONObject query);

	/**
	 * 获取公路事件占比
	 * @param query
	 * @return
	 */
	JSONObject getRoadEventProportion(JSONObject query);



	/*
	 * <AUTHOR>
	 * @description //TODO 获取公路事件地图
	 * @date 2025/6/26 16:49
	 * @param query 查询类
	 * @return com.alibaba.fastjson.JSONObject
	 **/
	JSONObject getRoadEventMap(JSONObject query);
}
