package com.yinshu.toms.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.exception.APIException;
import com.yinshu.fast.excel.util.ExcelUtils;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.toms.common.RemoteUrlConstants;
import com.yinshu.toms.service.AviationService;
import com.yinshu.toms.vo.aviation.AviationCargoExportVo;
import com.yinshu.toms.vo.aviation.AviationPassengerExportVo;
import com.yinshu.toms.vo.aviation.TrafficAlarmExportVo;
import com.yinshu.toms.vo.aviation.TrainCargoExportVo;
import com.yinshu.toms.vo.aviation.TrainPassengerExportVo;
import com.yinshu.toms.vo.city.RouteOverviewExportVo;
import com.yinshu.utils.DateUtils;
import com.yinshu.utils.StringHelper;

/**
 * 民航专题
 * <AUTHOR>
 *
 */
@Service
public class AviationServiceImpl implements AviationService {

    @Resource
    DvisualHttpTemplate template;

    /**
	 * 基础指标
	 * @return
	 */
    public JSONObject getTotalInfo(JSONObject query) {
    	query.put("filter", "id='"+DateUtils.getCurrentYear()+"'");
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_railway_plane_total_info, query);
        return post;
    }
    
    /**
	 * 航班数
	 * @return
	 */
    public JSONObject getArriverOutList(JSONObject query) {
    	JSONObject result = new JSONObject();
    	
    	if(query.getString("dataType").equals("1")) {
    		String filter = " dt='"+DateUtils.getDate()+"' and hour = null";
    		query.put("filter", filter);
            JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_railway_plane_arriver_out_analysis, query);
            return post;
    	}
    	
    	if(query.getString("dataType").equals("2")) {
    		String filter = StringHelper.toDateRange(query.getString("dateType"), query.getJSONArray("dateRange"), null);
    		filter = query.getString("dateType").equals("1") ? filter + " and hour != null order by hour" : filter + " and hour = null order by dt";
    		List<String> xAxisList = new ArrayList<>();
    		JSONArray seriesList = new JSONArray();
    		List<String> seriesList1 = new ArrayList<>();
    		List<String> seriesList2 = new ArrayList<>();
    		query.put("filter", filter);
            JSONObject httpResult = template.post(RemoteUrlConstants.ads_xinzhou_railway_plane_arriver_out_analysis, query);
            JSONArray jsonArray = httpResult.getJSONArray("list");
            String transportType = query.getString("transportType");//运输类型：1民航  2铁路
            for(int i = 0; i < jsonArray.size(); i++){
    			JSONObject object = jsonArray.getJSONObject(i);
    			xAxisList.add(query.getString("dateType").equals("1") ? (object.getString("hour") + ":00") : StringHelper.formatDateForDD(object.getString("dt")));
    			if(transportType.equals("1")) {
    				seriesList1.add(object.getString(getColumn(query.getString("selectType"), "innum")));
        			seriesList2.add(object.getString(getColumn(query.getString("selectType"), "outnum")));
    			} else {
    				seriesList1.add(object.getString(getColumn(query.getString("selectType"), "trurailinnum")));
        			seriesList2.add(object.getString(getColumn(query.getString("selectType"), "trurailoutnum")));
    			}
    			//seriesList1.add(transportType.equals("1") ? object.getString("innum") : object.getString("trurailinnum"));
    			//seriesList2.add(transportType.equals("1") ? object.getString("outnum") : object.getString("trurailoutnum"));
            }
            seriesList.add(0, seriesList1);
    		seriesList.add(1, seriesList2);
    		result.put("xAxis", xAxisList);
    		result.put("series", seriesList);
            return result;
    	}
    	return result;
    }
    
    private String getColumn(String selectType, String column) {
    	if(selectType.equals("同比")) {
    		return column + "_pre";
    	} else if(selectType.equals("环比")) {
    		return column + "_phr";
    	} else {
    		return column;
    	}
    }
    
    /**
	 * 航班统计情况
	 * @return
	 */
    public JSONObject getAnalysisList(JSONObject query) {
    	String transportType = query.getString("transportType");
    	String type = transportType.equals("1") ? "机场" : "铁路";
    	if(query.getString("dataType").equals("1")) {
    		String filter = "dt='"+DateUtils.getDate()+"'";
    		//String filter = "dt='2025-06-17'";
    		filter = filter + " and city = null and type = '"+type+"'";
    		query.put("filter", filter);
            JSONObject httpResult = template.post(RemoteUrlConstants.ads_xinzhou_railway_plane_analysis, query);
            return httpResult;
    	}
    	if(query.getString("dataType").equals("2")) {
    		String filter = StringHelper.toDateRange(query.getString("dateType"), query.getJSONArray("dateRange"), null);
    		filter = filter + " and city != null and type = '"+type+"'";
    		query.put("filter", filter);
    		JSONObject pieData = new JSONObject();
    		
    		JSONObject httpResult = template.post(RemoteUrlConstants.ads_xinzhou_railway_plane_analysis, query);
            JSONArray jsonArray = httpResult.getJSONArray("list");
            
            BigDecimal normalCount = new BigDecimal(0);
            BigDecimal abnormalCount = new BigDecimal(0);
            Map<String, BigDecimal> inoutMap = new HashMap<>();
            List<Map<String, Object>> pieList = new ArrayList<>();
            List<Map<String, Object>> abnormalList = new ArrayList<>();
            
            for(int i = 0; i < jsonArray.size(); i++){
            	JSONObject item = jsonArray.getJSONObject(i);
            	String city = item.getString("city");/**往各地航班/列车数量占比*/
            	if(inoutMap.containsKey(city)) {
            		BigDecimal outnum = inoutMap.get(city).add(item.getBigDecimal("outnum"));
            		inoutMap.put(city, outnum);
            	} else {
            		inoutMap.put(city, item.getBigDecimal("outnum"));
            	}
            	
            	/**航班异常占比*/
            	normalCount = normalCount.add(item.getBigDecimal("outnum") == null ? new BigDecimal(0) : item.getBigDecimal("outnum"));
            	abnormalCount = abnormalCount.add(item.getBigDecimal("outmisnum") == null ? new BigDecimal(0) : item.getBigDecimal("outmisnum"));
            }
            
            for(Map.Entry<String, BigDecimal> entry : inoutMap.entrySet()) {
            	pieList.add(setMapData(entry.getKey(), entry.getValue(), null));
            }
            pieData.put("series", pieList);
            
            abnormalList.add(setMapData(transportType.equals("1") ? "正常航班" : "正常列车", normalCount, null));
            abnormalList.add(setMapData(transportType.equals("1") ? "异常航班" : "异常列车", abnormalCount, null));
            pieData.put("series1", abnormalList);
    		
            return pieData;
    	}
    	return null;
    }
    
    /**
	 * 民航客运量监测/列车客运量监测
	 */
    public JSONObject getPassengerList(JSONObject query) {
    	String transportType = query.getString("transportType");
    	String type = transportType.equals("1") ? "机场" : "铁路";
    	JSONObject result = new JSONObject();
    	if(query.getString("dataType").equals("1")) {
    		String filter = "dt='"+DateUtils.getDate()+"'";
    		//String filter = "dt='2025-06-17'";
    		filter = filter + " and hour = null and type = '"+type+"'";
    		query.put("filter", filter);
            JSONObject httpResult = template.post(RemoteUrlConstants.ads_xinzhou_railway_plane_kenum_look, query);
            return httpResult;
    	}
    	if(query.getString("dataType").equals("2")) {
    		String filter = " type = '"+type+"' and " + StringHelper.toDateRange(query.getString("dateType"), query.getJSONArray("dateRange"), null);
    		filter = query.getString("dateType").equals("1") ? filter + " and hour != null order by hour" : filter + " and hour = null order by dt";
    		List<String> xAxisList = new ArrayList<>();
    		JSONArray seriesList = new JSONArray();
    		List<String> seriesList1 = new ArrayList<>();
    		List<String> seriesList2 = new ArrayList<>();
    		
    		String classify = query.getString("classify");
    		query.put("filter", filter);
            JSONObject httpResult = template.post(RemoteUrlConstants.ads_xinzhou_railway_plane_kenum_look, query);
            JSONArray jsonArray = httpResult.getJSONArray("list");
            for(int i = 0; i < jsonArray.size(); i++){
    			JSONObject object = jsonArray.getJSONObject(i);
    			xAxisList.add(query.getString("dateType").equals("1") ? (object.getString("hour") + ":00") : StringHelper.formatDateForDD(object.getString("dt")));
    			seriesList1.add(classify.equals("1") ? object.getString("innum") : object.getString("huoinnum"));
    			seriesList2.add(classify.equals("1") ? object.getString("outnum") : object.getString("huooutnum"));
            }
            seriesList.add(0, seriesList1);
    		seriesList.add(1, seriesList2);
    		result.put("xAxis", xAxisList);
    		result.put("series", seriesList);
            return result;
    	}
    	return null;
    }
    
    /**
	 * 民航客运量监测分页列表
	 */
    public JSONObject getPassengerPageList(JSONObject query) {
    	String transportType = query.getString("transportType");
    	String type = transportType.equals("1") ? "机场" : "铁路";
    	String filter = " type='"+type+"' and " + StringHelper.toDateRange(query.getString("dateType"), query.getJSONArray("dateRange"), null);
    	filter = query.getString("dateType").equals("1") ? filter + " and hour != null order by hour" : filter + " and hour = null order by dt";
    	query.put("filter", filter);
    	JSONObject httpResult = template.post(RemoteUrlConstants.ads_xinzhou_railway_plane_kenum_look, query);
        return httpResult;
    }
    
    /**
	 * 民航货运量监测
	 * @return
	 */
    public JSONObject getCargoList(JSONObject query) {
    	//String filter = "dt='"+DateUtils.getDate()+"'";
		String filter = "dt='2025-06-16'";
    	if(query.getString("dataType").equals("1")) {
    		filter = filter + " and hour = null";
    	}
    	query.put("filter", filter);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_railway_plane_kenum_look, query);
        return post;
    }
    
    /**
	 * 机场大巴运行
	 * @return
	 */
    public JSONObject getBusList(JSONObject query) {
    	String filter = "dt='"+DateUtils.getDate()+"' and hour = null";
		//String filter = "dt='2025-06-16'  and hour = null";
    	query.put("filter", filter);
        JSONObject post = template.post(RemoteUrlConstants.ads_xinzhou_plane_runing_bus_tourist, query);
        return post;
    }
    
    /**
	 * 获取车辆信息
	 * @param query
	 * @return
	 */
    public JSONObject getVehicleList(JSONObject query) {
    	JSONObject result = new JSONObject();
    	//String filter = "stationType='"+query.getString("stationType")+"'";
    	query.put("filter", "order by station, flag desc");
        JSONObject httpResult = template.post(RemoteUrlConstants.ads_xinzhou_plane_car_map_info, query);
//        JSONArray jsonArray = httpResult.getJSONArray("list");
//        Map<Object, List<Map>> resultList = jsonArray.toJavaList(Map.class).stream().collect(Collectors.groupingBy(map -> map.get("station")));
//        result.put("list", resultList);
        return httpResult;
    }
    
    /**
	 * 执法判研分类汇总
	 */
    public JSONObject getWarningTotal(JSONObject query) {
    	query.put("filter", "locationtype='"+query.getString("locationtype")+"'");
    	//query.put("filter", "dt='"+DateUtils.getDate()+"' + locationtype='"+query.getString("locationtype")+"'");
        JSONObject httpResult = template.post(RemoteUrlConstants.ads_warn_info_self, query);
        return httpResult;
    }
    
    /**
	 * 执法判研
	 */
    public JSONObject getWarningList(JSONObject query) {
    	String filter = "locationtype='"+query.getString("locationtype")+"' ";
    	if(StringUtils.hasText(query.getString("carid"))) {
    		filter = filter + " and carid like '"+query.getString("carid")+"'";
    	}
    	JSONArray timeArray = query.getJSONArray("time");
    	if(timeArray != null) {
    		filter = filter + " and warndt >= '"+timeArray.getString(0)+"' and warndt <= '"+timeArray.getString(1)+"'";
    	}
    	query.put("filter", filter + " order by warntime desc");
        JSONObject httpResult = template.post(RemoteUrlConstants.ads_warn_info_self, query);
        return httpResult;
    }
    

	private Map<String, Object> setMapData(String name, BigDecimal value, BigDecimal sum) {
		Map<String, Object> dataMap = new HashMap<String, Object>();
		dataMap.put("name", name);
		dataMap.put("value", value);
		return dataMap;
	}
	
    /**
     *  导出
     * @param jsonObject
     */
    public void exportXls(JSONObject jsonObject) {
    	try {
    		if ("1".equals(jsonObject.getString("exportType"))) {
                JSONObject resultList = getPassengerPageList(jsonObject);
                ExcelUtils.exportExcelSheet(AviationPassengerExportVo.class, resultList);
            } else if ("2".equals(jsonObject.getString("exportType"))) {
                JSONObject resultList = getPassengerPageList(jsonObject);
                ExcelUtils.exportExcelSheet(AviationCargoExportVo.class, resultList);
            } else if ("3".equals(jsonObject.getString("exportType"))) {
                JSONObject resultList = getPassengerPageList(jsonObject);
                ExcelUtils.exportExcelSheet(TrainPassengerExportVo.class, resultList);
            } else if ("4".equals(jsonObject.getString("exportType"))) {
                JSONObject resultList = getPassengerPageList(jsonObject);
                ExcelUtils.exportExcelSheet(TrainCargoExportVo.class, resultList);
            } else if ("5".equals(jsonObject.getString("exportType"))) { //交通执法判研记录
                JSONObject resultList = getWarningList(jsonObject);
                ExcelUtils.exportExcelSheet(TrafficAlarmExportVo.class, resultList);
            }
		} catch (Exception e) {
			throw new APIException(e.getMessage());
		}
    }

}
