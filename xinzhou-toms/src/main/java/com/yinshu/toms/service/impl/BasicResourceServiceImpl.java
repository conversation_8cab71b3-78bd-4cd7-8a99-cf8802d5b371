package com.yinshu.toms.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.toms.common.RemoteUrlConstants;
import com.yinshu.toms.service.BasicResourceService;
import com.yinshu.toms.util.DataComparisonUtils;
import com.yinshu.toms.util.LngLatUtil;
import com.yinshu.toms.util.UtilService;
import com.yinshu.utils.FilterCreate;
import com.yinshu.utils.RedisCache;
import com.yinshu.utils.ReplaceUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 公共方法
 *
 */
@Service
public class BasicResourceServiceImpl implements BasicResourceService {

    protected final static Logger logger = LoggerFactory.getLogger(BasicResourceServiceImpl.class);

    @Resource
    private DvisualHttpTemplate template;

    @Autowired
    private RedisCache redisCache;

    /***
     * 公路路网地图-公路//ads_xinzhou_road_net_map_road
     */
    public static final String ADS_XINZHOU_ROAD_NET_MAP_ROAD = "/api/100505/data.json?";
    /***
     * 公路路网地图-公路//ads_xinzhou_road_net_map_road_new
     */
    public static final String ADS_XINZHOU_ROAD_NET_MAP_ROAD_NEW = "/api/100589/data.json?";
    /***
     * 公路路网地图-构造物//ads_xinzhou_road_net_map_structure
     */
    public static final String ADS_XINZHOU_ROAD_NET_MAP_STRUCTURE = "/api/100507/data.json?";
    /***
     * 信息化设备详情//ads_xinzhou_information_device_details
     */
    public static final String ADS_XINZHOU_INFORMATION_DEVICE_DETAILS = "/api/100503/data.json?";
    /***
     * 百度link实时路况//ods_baidu_link_reatime_road_condition
     */
    public static final String ODS_BAIDU_LINK_REATIME_ROAD_CONDITION = "/api/100559/data.json?";

    /**
	 * 获取地市行政区划
	 * @param query
	 * @return
	 */
    public JSONArray getCountyList(JSONObject query) {
        String filterTemplate = "order by id";
        query.put("filter", filterTemplate);
        JSONObject resultData = template.post(RemoteUrlConstants.DIM_XINZHOU_COUNTY, query);
        return DataComparisonUtils.getResultList(resultData);
    }

    /**
     * 获取路网数据
     * @param query
     * @return
     */
    public JSONArray getRoadNetwork(JSONObject query) {
        JSONArray roadNetwork = getRoadNetworkCache(query);
        String roadName = query.getString("roadName");
        String roadCode = query.getString("roadCode");
        if(StringUtils.isNotBlank(roadName) || StringUtils.isNotBlank(roadCode)){
            JSONArray roadNetworkFilter = new JSONArray();
            for(int i=0; i<roadNetwork.size(); i++){
                boolean flag = false;
                JSONObject item = roadNetwork.getJSONObject(i);
                JSONObject entity = item.getJSONObject("entity");
                String entityRoadName = entity.getString("road_name");
                String entityRoadCode = entity.getString("road_code");
                if(StringUtils.isNotBlank(entityRoadName) && StringUtils.isNotBlank(roadName)){
                    if(entityRoadName.contains(roadName)){
                        flag = true;
                    }
                }
                if(StringUtils.isNotBlank(entityRoadCode) && StringUtils.isNotBlank(roadCode)){
                    if(entityRoadCode.contains(roadCode)){
                        flag = true;
                    }
                }
                if(flag){
                    roadNetworkFilter.add(item);
                }
            }
            return roadNetworkFilter;
        }else{
            return roadNetwork;
        }
    }


    public JSONArray getRoadNetworkCache(JSONObject query) {
        String cache = query.getString("cache");
        String technicalLevel = query.getString("technicalLevel");
        String code = query.getString("code");
        JSONArray resultArray = new JSONArray();
        String filterTemplate = "technical_level = '{technicalLevel}'";
        if(StringUtils.isBlank(technicalLevel)){
            code = "all";
            filterTemplate = "";
        }
        if(StringUtils.isBlank(cache)){
            cache = UtilService.getSetting("RoadNetWorkCache");
        }
        String cacheKey = "dvisual:road_network:" + code;
        if("true".equals(cache)){
            JSONArray roadArray = redisCache.getCacheObject(cacheKey);
            if(roadArray != null && roadArray.size() > 0){
                return roadArray;
            }
        }
        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                "technicalLevel", technicalLevel));
        JSONArray jsonArray = DataComparisonUtils.getResultBatchWise(template, ADS_XINZHOU_ROAD_NET_MAP_ROAD_NEW,
                query, 10000);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject geometry = new JSONObject();
            JSONObject json = jsonArray.getJSONObject(i);
            String loc = json.getString("location");
            json.remove("location");
            if(loc.contains("[")){
                geometry = JSONObject.parseObject(loc);
            }else{
                List<List<double[]>> result = new ArrayList<>();
                String[] location = loc.split(";");
                for (String str : location) {
                    String[] parts = str.split(",");
                    if (parts.length % 2 != 0) {
                        logger.error("「" + json.getString("id") + "」经纬度数量有问题:" + parts.length + "|" + str);
                    }
                    List<double[]> lngLat = LngLatUtil.parseLngLat(str);
                    result.add(lngLat);
                }
                if(result.size() == 1){
                    geometry.put("type", "LineString");
                    geometry.put("coordinates", result.get(0));
                }else{
                    geometry.put("type", "MultiLineString");
                    geometry.put("coordinates", result);
                }
            }

            JSONObject road = new JSONObject();
            road.put("geometry", geometry);
            road.put("entity", json);
            road.put("type", "Feature");

            resultArray.add(road);
        }
        redisCache.setCacheObject(cacheKey, resultArray, 1L, TimeUnit.DAYS);
        return resultArray;
    }

    /**
     * 获取路网详细信息
     * @param query
     * @return
     */
    public JSONObject getRoadNetworkInfo(JSONObject query) {
        String roadName = query.getString("roadName");
        String roadCode = query.getString("roadCode");
        String filterTemplate = new FilterCreate()
                .and(StringUtils.isNotBlank(roadName), "road_name like '{roadName}'")
                .and(StringUtils.isNotBlank(roadCode), "road_code like '{roadCode}'")
                .toString();
        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                "roadName", roadName, "roadCode", roadCode));
        JSONObject resultData = template.post(ADS_XINZHOU_ROAD_NET_MAP_ROAD_NEW, query);
        return resultData;
    }

    /**
     * 获取构造物
     * @param query
     * @return
     */
    public JSONObject getStructureList(JSONObject query) {
        String type = query.getString("type");
        String name = query.getString("name");
        String structureCode = query.getString("structureCode");
        String filterTemplate = new FilterCreate()
                .and(StringUtils.isNotBlank(type), "type = '{type}'")
                .and(StringUtils.isNotBlank(name), "name like '{name}'")
                .and(StringUtils.isNotBlank(structureCode), "structure_code like '{structureCode}'")
                .toString();
        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                "type", type, "name", name, "structureCode", structureCode));
        JSONObject resultData = template.post(ADS_XINZHOU_ROAD_NET_MAP_STRUCTURE, query);
        return resultData;
    }

    /**
     * 获取公路设施
     * @param query
     * @return
     */
    @Override
    public JSONObject getHighwayFacilities(JSONObject query) {
        String type = query.getString("type");
        String deviceName = query.getString("device_name");
        String status = query.getString("status");
        String filterTemplate = new FilterCreate()
                .and(StringUtils.isNotBlank(type), "type = '{type}'")
                .and(StringUtils.isNotBlank(deviceName), "device_name like '{deviceName}'")
                .and(StringUtils.isNotBlank(status), "status like '{status}'")
                .toString();
        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                "type", type, "deviceName", deviceName, "status", status));
        JSONObject resultData = template.post(ADS_XINZHOU_INFORMATION_DEVICE_DETAILS, query);
        return resultData;
    }
    
    /**
     * 获取路网工程
     * @param query
     * @return
     */
    public JSONObject getRoadNetworkProjectList(JSONObject query) {
        String projectName = query.getString("project_name");
        if(StringUtils.isNotBlank(projectName)) {
        	query.put("filter", "project_name like '"+projectName+"'");
        }
        JSONObject resultData = template.post(RemoteUrlConstants.ads_xinzhou_road_net_map_road_project, query);
        return resultData;
    }

}
