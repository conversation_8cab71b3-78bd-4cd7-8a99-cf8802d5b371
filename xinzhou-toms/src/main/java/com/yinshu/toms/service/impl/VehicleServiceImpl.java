package com.yinshu.toms.service.impl;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONArray;
import com.yinshu.toms.util.DataComparisonUtils;
import com.yinshu.utils.DateUtils;
import com.yinshu.utils.ReplaceUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.toms.common.RemoteUrlConstants;
import com.yinshu.toms.service.VehicleService;

/**
 * 车辆
 * 
 * <AUTHOR>
 *
 */
@Service
public class VehicleServiceImpl implements VehicleService {

	protected final static Logger logger = LoggerFactory.getLogger(VehicleServiceImpl.class);

	@Resource
	DvisualHttpTemplate template;

	/**
	 * 获取实时定位
	 * 
	 * @return
	 */
	public JSONObject getRealLocation(JSONObject query) {
		// JSONObject result = new JSONObject();
		logger.info("------------getRealLocation-----------------");
		String plate = query.getString("plate");
		String url = "&tablename=ads_plane_fire_bus_location_info&filter=carid='" + plate + "' order by gps_timelong desc&s=0&n=1";
		JSONObject httpResult = template.post(RemoteUrlConstants.ads_plane_fire_bus_location_info + url, query);
		// JSONArray jsonArray = httpResult.getJSONArray("list");
		return httpResult;
	}

	/**
	 * 获取历史轨迹
	 *
	 * @return
	 */
	public JSONArray getHistoryLocation(JSONObject query) {
		String plate = query.getString("plate");
		String startTime = query.getString("startTime");
		String endTime = query.getString("endTime");
		long startTimeStamp = DateUtils.parse(startTime, DateUtils.YYYY_MM_DD_HH_MM).getTime();
		long endTimeStamp = DateUtils.parse(endTime, DateUtils.YYYY_MM_DD_HH_MM).getTime();
		query.put("tablename", "ads_plane_fire_bus_location_info");
		String filterTemplate = "carid = '{plate}' and gps_timelong >= {startTimeStamp} and gps_timelong < {endTimeStamp} " +
				"order by gps_timelong asc";
		query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
				"plate", plate, "startTimeStamp", startTimeStamp, "endTimeStamp", endTimeStamp));
		JSONObject resultData = template.post(RemoteUrlConstants.ads_plane_fire_bus_location_info, query);
		return DataComparisonUtils.getResultList(resultData);
	}
	
	
	/**
	 * 获取多辆车的实时定位
	 * 
	 * @return
	 */
	public JSONObject getMultiRealLocation(JSONObject query) {
		logger.info("------------getMultiRealLocation-----------------");
		JSONArray plates = query.getJSONArray("ids");
		StringBuffer carid = new StringBuffer();
		for (int i = 0; i < plates.size(); i++) {
			carid.append(" or carid='" + plates.getString(i) + "'");
		}
		String url = "&tablename=ads_plane_fire_bus_location_info&filter=" + carid.substring(4) + " order by gps_timelong desc&s=0&n="+plates.size();
		JSONObject httpResult = template.post(RemoteUrlConstants.ads_plane_fire_bus_location_info + url, query);
		return httpResult;
	}

}
