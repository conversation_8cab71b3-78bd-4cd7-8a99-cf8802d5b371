package com.yinshu.toms.common;

import com.alibaba.fastjson.JSONObject;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class RequestFilterBuilder {
    /**
     * 大数据构建查询参数
     *
     * @param jsonObject
     */
    public static void buildParam(JSONObject jsonObject) {
        StringBuilder filterSb = new StringBuilder();
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            // 跳过 s 和 n
            if ("s".equals(key) || "n".equals(key)) {
                continue;
            }
            // 忽略空值
            if (value == null || "".equals(value)) {
                continue;
            }

            if (filterSb.length() > 0) {
                filterSb.append(" and ");
            }

            if (value instanceof ArrayList) {
                List array = (List) value;
                if (!array.isEmpty()) {
                    filterSb.append("(");
                    for (int i = 0; i < array.size(); i++) {
                        if (i > 0) filterSb.append(" or ");

                        Object item = array.get(i);
                        if (item instanceof String) {
                            filterSb.append(key).append("='").append(item).append("'");
                        } else {
                            filterSb.append(key).append("=").append(item);
                        }
                    }
                    filterSb.append(")");
                }
            } else if (value instanceof String) {
                if (value.toString().trim().isEmpty()) {
                    continue;
                }
                if (key.equals("startDate")) {
                    filterSb.append("dt").append(">='").append(value).append("'");
                } else if (key.equals("endDate")) {
                    filterSb.append("dt").append("<='").append(value).append("'");
                } else {
                    filterSb.append(key).append("='").append(value).append("'");
                }

            } else {
                // 普通值处理
                if (value.toString().trim().isEmpty()) {
                    continue;
                }
                filterSb.append(key).append("=").append(value);
            }

        }

        if (filterSb.length() > 0) {
            jsonObject.put("filter", filterSb.toString());
        }

    }

    public static List<Integer> getYears(int i) {
        List<Integer> years = new ArrayList<>();
        int currentYear = LocalDate.now().getYear();
        for (int j = 0; j < i; j++) {
            years.add(currentYear - j);
        }
        return years;
    }
}
