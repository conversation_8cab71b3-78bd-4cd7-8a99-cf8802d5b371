package com.yinshu.toms.common;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RemoteUrlConstants
 * @description TODO 大数据平台api地址
 * @date 2025/6/3 18:12
 **/
public class RemoteUrlConstants {

    /***
     * 忻州市地市行政区划//dim_xinzhou_county
     */
    public static final String DIM_XINZHOU_COUNTY = "/api/100430/data.json?";

    /***
     * 预警车辆详情//ads_warn_car_daily_analysis
     */
    public static final String ads_warn_car_daily_analysis = "/api/100437/data.json?";


    // --------道路运输开始--------


    /***
     * 前缀
     */
    public static final String PREFIX = "/api/";

    /**
     * 后缀
     **/
    public static final String SUFFIX = "/data.json?";

    /**
     * 基础指标数据//ads_basic_indicator_data
     */
    public static final String ADS_BASIC_INDICATOR_DATA = PREFIX + "100449" + SUFFIX;
    /**
     * 客运站点监测//ads_passenger_transport_station
     */
    public static final String ADS_PASSENGER_TRANSPORT_STATION = PREFIX + "100459" + SUFFIX;
    /**
     * 今日概览趋势小时//ads_daily_overview_trend_hourly
     */
    public static final String ADS_DAILY_OVERVIEW_TREND_HOURLY = PREFIX + "100461" + SUFFIX;
    /**
     * 每日概览趋势//ads_daily_overview_trend
     */
    public static final String ADS_DAILY_OVERVIEW_TREND = PREFIX + "100463" + SUFFIX;

    /**
     * 危货运量周变化趋势//ads_dangerous_goods_vol_weekly_trend
     **/
    public static final String ADS_DANGEROUS_GOODS_VOL_WEEKLY_TREND = PREFIX + "100471" + SUFFIX;

    /**
     * 道路运输概览占比
     **/
    public static final String ADS_ROAD_TRANSPORT_OVERVIEW_RATIO = PREFIX + "100472" + SUFFIX;

    /**
     * 普通货运车数量详情
     **/
    public static final String ADS_GENERAL_FREIGHT_TRUCK_QUANTITY_DETAIL = PREFIX + "100473" + SUFFIX;

    /**
     * 运输地图车辆基本信息
     **/
    public static final String ADS_TRANSPORT_MAP_VEHICLE_BASE_INFO = PREFIX + "100475" + SUFFIX;

    /**
     * 运输地图车辆基本信息
     **/
    public static final String ADS_WARNING_VEHICLE_DETAIL = PREFIX + "100478" + SUFFIX;

    /**
     * 危货运数据排名//ads_dg_data_rank
     **/
    public static final String ADS_DG_DATA_RANK =  PREFIX + "100480" + SUFFIX;



    // --------道路运输结束--------

    /**
     * 忻州铁路民航汇总数据//ads_xinzhou_railway_plane_total_info
     */
    public static final String ads_xinzhou_railway_plane_total_info = "/api/100446/data.json?";

    /**
     * 忻州铁路民航汇总数据//ads_xinzhou_railway_plane_arriver_out_analysis
     */
    public static final String ads_xinzhou_railway_plane_arriver_out_analysis = "/api/100448/data.json?";

    /**
     * 忻州铁路民航统计概括//ads_xinzhou_railway_plane_analysis
     */
    public static final String ads_xinzhou_railway_plane_analysis = "/api/100450/data.json?";

    /**
     * 忻州铁路民航客运量监测//ads_xinzhou_railway_plane_kenum_look
     */
    public static final String ads_xinzhou_railway_plane_kenum_look = "/api/100452/data.json?";

    /**
     * 忻州铁路民航统计概括//ads_xinzhou_plane_runing_bus_tourist
     */
    public static final String ads_xinzhou_plane_runing_bus_tourist = "/api/100453/data.json?";
    
    /**
     * 忻州市铁路民航车辆监测地图//ads_xinzhou_plane_car_map_info
     */
    public static final String ads_xinzhou_plane_car_map_info = "/api/100479/data.json?";

    //----------在建工程-------------
    /**
     * 忻州市铁路民航车辆监测地图//ads_invest_construction_overview
     */
    public static final String ads_invest_construction_overview = "/api/100492/data.json?";

    /**
     * 工程项目信息录入//ads_invest_construction_progress_ranking
     */
    public static final String ads_project_progress_ranking = "/api/100502/data.json?";

    /**
     * 忻州市铁路民航交通执法研判//ads_xinzhou_plane_mistake_info
     */
    public static final String ads_xinzhou_plane_mistake_info = "/api/100493/data.json?";
    
    /**
     * 车辆轨迹//ads_plane_fire_bus_location_info
     */
    public static final String ads_plane_fire_bus_location_info = "/api/100508/zPSvBrMftJFPzEoG1bRi?";
    
    /***
     * 公路路网地图-路网工程//ads_xinzhou_road_net_map_road_project
     */
    public static final String ads_xinzhou_road_net_map_road_project = "/api/100509/data.json?";
    
    /***
     * 忻州车辆报警数据明细表//ads_warn_info_self
     */
    public static final String ads_warn_info_self = "/api/100588/data.json?";
    

}
