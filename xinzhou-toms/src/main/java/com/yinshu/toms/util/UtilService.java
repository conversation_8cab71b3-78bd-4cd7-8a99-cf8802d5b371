package com.yinshu.toms.util;

import com.yinshu.sys.manager.SettingManager;
import com.yinshu.utils.StringHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;

@Lazy(false)    //标记为非懒加载
@Component
public class UtilService {
    @Autowired
    private SettingManager settingManager;
    public static SettingManager staticSettingManager;

    /**
     * --------------------初始化静态Service--------------------
     */
    @PostConstruct
    public void init(){
        staticSettingManager = this.settingManager;
    }

    //配置real或test用于切换查询参数
    public static <T>  T RealOrTest(T real, T test){
        String realOrTest = staticSettingManager.getByCode("realOrTest").getParmValue();
        if("test".equals(realOrTest)){
            return test;
        }else{
            return real;
        }
    }

    public static String getSetting(String code){
        return staticSettingManager.getByCode(code).getParmValue();
    }

}
