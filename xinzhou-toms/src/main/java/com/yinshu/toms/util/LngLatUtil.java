package com.yinshu.toms.util;

import java.util.ArrayList;
import java.util.List;

public class LngLatUtil {
	
	public static List<double[]> parseLngLat(String data) {
		List<double[]> LngLatList = new ArrayList<>();
        String[] parts = data.split(",");
        if(parts.length % 2 != 0) {
        	return LngLatList;
        }
        for (int i = 0; i < parts.length; i += 2) {
        	double[] LngLat = new double[2];
        	LngLat[0] = Double.parseDouble(parts[i].trim());
        	LngLat[1] = Double.parseDouble(parts[i + 1].trim());
            LngLatList.add(LngLat);
        }

        return LngLatList;
    }

}
