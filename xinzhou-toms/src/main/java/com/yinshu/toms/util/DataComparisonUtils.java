package com.yinshu.toms.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.yinshu.exception.RestfulAPIException;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.utils.DateUtils;
import com.yinshu.utils.ReplaceUtils;
import com.yinshu.utils.StringHelper;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.function.Function;
import java.util.logging.Logger;
import java.util.stream.Collectors;

public class DataComparisonUtils {
    //一百
    public static final BigDecimal HUNDRED = new BigDecimal("100");
    //一千
    public static final BigDecimal THOUSAND = new BigDecimal("1000");
    //一万
    public static final BigDecimal TEN_THOUSAND = new BigDecimal("10000");
    //一亿
    public static final BigDecimal BILLION = new BigDecimal("100000000");
    //千分之一
    public static final BigDecimal THOUSAND_TH = new BigDecimal("0.001");
    //万分之一
    public static final BigDecimal TEN_THOUSAND_TH = new BigDecimal("0.0001");
    public static final Set<String> EXCLUDE_FIELD_SET =
            new HashSet<>(Arrays.asList("id", "dt", "quarter", "type", "date", "station", "city", "category_1_name","category_2_name","category_3_name","category_4_name"));
    private static final Logger log = Logger.getLogger(DataComparisonUtils.class.getName());

    public static JSONObject getJsonArraySum(JSONArray dataJsonArray, String... keys) {
        JSONObject resultData = new JSONObject();
        for (int i = 0; i < dataJsonArray.size(); i++) {
            JSONObject item = dataJsonArray.getJSONObject(i);
            for (String key : keys) {
                BigDecimal value = nullToZero(resultData.getBigDecimal(key));
                BigDecimal temp = item.getBigDecimal(key);
                if (temp != null) {
                    value = value.add(temp);
                }
                resultData.put(key, value);
            }
        }
        return resultData;
    }

    public static JSONObject getJsonArrayAvg(JSONArray dataJsonArray, String... keys) {
        JSONObject resultData = getJsonArraySum(dataJsonArray, keys);
        int size = dataJsonArray.size();
        if (size > 0) {
            BigDecimal arraySize = new BigDecimal(size);
            for (String key : keys) {
                BigDecimal value = nullToZero(resultData.getBigDecimal(key));
                resultData.put(key, value.divide(arraySize, 1, RoundingMode.HALF_UP));
            }
        }
        return resultData;
    }

    public static JSONObject getFactor(JSONObject jsonObject, BigDecimal factor,
                                       int scale, String... keys) {
        for (String key : keys) {
            BigDecimal value = nullToZero(jsonObject.getBigDecimal(key));
            jsonObject.put(key, value.multiply(factor).setScale(scale, RoundingMode.HALF_UP));
        }
        return jsonObject;
    }

    public static JSONObject getDayMonthYearJsonObject(DvisualHttpTemplate httpTemplate, String apiUrl,
                                                       JSONObject jsonObject, String ymd, String nameKey, String valueKey) {
        if (StringUtils.isNotBlank(ymd)) {
            String filterTemplate = "id like '%s' order by id";
            jsonObject.put("filter", String.format(filterTemplate, ymd));
        } else {
            jsonObject.put("filter", "order by id");
        }
        JSONObject dataResult = httpTemplate.post(apiUrl, jsonObject);
        JSONArray dataJsonArray = dataResult.getJSONArray("list");
        JSONObject resultData = new JSONObject();
        List<String> nameList = new ArrayList<>();
        List<BigDecimal> valueList = new ArrayList<>();
        for (int i = 0; i < dataJsonArray.size(); i++) {
            JSONObject item = dataJsonArray.getJSONObject(i);
            nameList.add(item.getString(nameKey));
            valueList.add(item.getBigDecimal(valueKey));
        }
        resultData.put("nameList", nameList);
        resultData.put("valueList", valueList);
        return resultData;
    }

    public static JSONObject getTimeJsonObject(DvisualHttpTemplate httpTemplate, String apiUrl,
                                               JSONObject jsonObject, BigDecimal factor) {
        String dayStr = DateUtils.getDate();
        if (jsonObject.containsKey("day")) {
            dayStr = jsonObject.getString("day");
        }
        Date lastDate = DateUtils.addDays(DateUtils.parse(dayStr, DateUtils.YYYY_MM_DD), -1);
        String lastDayStr = DateUtils.format(lastDate, DateUtils.YYYY_MM_DD);
        String filterTemplate = "dt = '%s' or dt = '%s' order by id";
        jsonObject.put("s", 0);
        jsonObject.put("n", 1000);
        jsonObject.put("filter", String.format(filterTemplate, dayStr, lastDayStr));
        JSONObject sameDayResult = httpTemplate.post(apiUrl, jsonObject);
        JSONArray dayDataList = sameDayResult.getJSONArray("list");
        JSONObject sameDayData = null, preDayData = null, lastDayData = null;
        //当前数据
        if (dayDataList.size() >= 1) {
            sameDayData = dayDataList.getJSONObject(dayDataList.size() - 1);
        }
        //上一时刻数据
        if (dayDataList.size() >= 2) {
            preDayData = dayDataList.getJSONObject(dayDataList.size() - 2);
        }
        //昨日同比时刻数据
        if (sameDayData != null) {
            String quarter = sameDayData.getString("quarter");
            if (StringUtils.isNotBlank(quarter)) {
                for (int i = 0; i < dayDataList.size(); i++) {
                    JSONObject item = dayDataList.getJSONObject(i);
                    if (lastDayStr.equals(item.getString("dt")) && quarter.equals(item.getString("quarter"))) {
                        lastDayData = item;
                    }
                }
            }
        }
        JSONObject resultData = new JSONObject();
        if (sameDayData != null) {
            resultData.put("preCompare", DataComparisonUtils.decimalComparison(sameDayData, preDayData, factor));
        }
        if (sameDayData != null) {
            resultData.put("lastCompare", DataComparisonUtils.decimalComparison(sameDayData, lastDayData, factor));
        }
        return resultData;
    }

    public static JSONArray getDayIntervalJsonObject(DvisualHttpTemplate httpTemplate, String apiUrl,
                                                     JSONObject jsonObject, int Interval) {
        String dayStr = DateUtils.getDate();
        if (jsonObject.containsKey("day")) {
            dayStr = jsonObject.getString("day");
        }
        Date lastDate = DateUtils.addDays(DateUtils.parse(dayStr, DateUtils.YYYY_MM_DD), Interval);
        String lastDayStr = DateUtils.format(lastDate, DateUtils.YYYY_MM_DD);
        String filterTemplate = "id > '%s' and id <= '%s' order by id";
        jsonObject.put("filter", String.format(filterTemplate, lastDayStr, dayStr));
        JSONObject dayIntervalResult = httpTemplate.post(apiUrl, jsonObject);
        JSONArray dayIntervalDataList = dayIntervalResult.getJSONArray("list");
        return dayIntervalDataList;
    }

    public static JSONArray getHourJsonObject(DvisualHttpTemplate httpTemplate, String apiUrl,
                                              JSONObject jsonObject) {
        String dayStr = DateUtils.getDate();
        if (jsonObject.containsKey("day")) {
            dayStr = jsonObject.getString("day");
        }
        String filterTemplate = "dt = '%s' order by hour";
        jsonObject.put("filter", String.format(filterTemplate, dayStr));
        JSONObject sameHourResult = httpTemplate.post(apiUrl, jsonObject);
        JSONArray hourDataList = sameHourResult.getJSONArray("list");
        return hourDataList;
    }

    public static JSONArray getHourJsonObject(DvisualHttpTemplate httpTemplate, String apiUrl,
                                              JSONObject jsonObject, int addDays) {
        String dayStr = DateUtils.getDate();
        if (jsonObject.containsKey("day")) {
            dayStr = jsonObject.getString("day");
        }
        if (addDays != 0) {
            Date lastDate = DateUtils.addDays(DateUtils.parse(dayStr, DateUtils.YYYY_MM_DD), addDays);
            dayStr = DateUtils.format(lastDate, DateUtils.YYYY_MM_DD);
        }
        String filterTemplate = "dt = '%s' order by hour";
        jsonObject.put("filter", String.format(filterTemplate, dayStr));
        JSONObject sameHourResult = httpTemplate.post(apiUrl, jsonObject);
        JSONArray hourDataList = sameHourResult.getJSONArray("list");
        return hourDataList;
    }

    public static JSONObject getDayJsonObject(DvisualHttpTemplate httpTemplate, String apiUrl, JSONObject jsonObject) {
        return getDayJsonObject(httpTemplate, apiUrl, jsonObject, BigDecimal.ONE, BigDecimal.ONE);
    }

    public static JSONObject getDayJsonObject(DvisualHttpTemplate httpTemplate, String apiUrl,
                                              JSONObject jsonObject, BigDecimal sameFactor, BigDecimal lastFactor) {
        String dayStr = DateUtils.getDate();
        if (jsonObject.containsKey("day")) {
            dayStr = jsonObject.getString("day");
        }
        Date lastDate = DateUtils.addDays(DateUtils.parse(dayStr, DateUtils.YYYY_MM_DD), -1);
        String lastDayStr = DateUtils.format(lastDate, DateUtils.YYYY_MM_DD);
        String filterTemplate = "id = '%s' or id = '%s'";
        jsonObject.put("filter", String.format(filterTemplate, dayStr, lastDayStr));
        JSONObject sameDayResult = httpTemplate.post(apiUrl, jsonObject);
        JSONArray dayDataList = sameDayResult.getJSONArray("list");
        JSONObject sameDayData = null, lastDayData = null;
        for (int i = 0; i < dayDataList.size(); i++) {
            JSONObject item = dayDataList.getJSONObject(i);
            if (dayStr.equals(item.getString("id"))) {
                sameDayData = item;
            }
            if (lastDayStr.equals(item.getString("id"))) {
                lastDayData = item;
            }
        }
        if (sameDayData != null) {
            return DataComparisonUtils.decimalComparison(sameDayData, lastDayData, sameFactor, lastFactor);
        }
        return new JSONObject();
    }

    public static JSONObject getMonthJsonObject(DvisualHttpTemplate httpTemplate, String apiUrl,
                                                JSONObject jsonObject, BigDecimal factor,
                                                String dateField, String type) {
        String monthStr = DateUtils.getYM();
        if (jsonObject.containsKey("month")) {
            monthStr = jsonObject.getString("month");
        }
        Date monthDate = DateUtils.parse(monthStr, DateUtils.YYYY_MM);
        Date lastMonthDate = DateUtils.addMonths(monthDate, -1);
        String lastMonthStr = DateUtils.format(lastMonthDate, DateUtils.YYYY_MM);
        Date onYearMonthDate = DateUtils.addMonths(monthDate, -12);
        String onYearMonthStr = DateUtils.format(onYearMonthDate, DateUtils.YYYY_MM);
        Date onYearLastMonthDate = DateUtils.addMonths(lastMonthDate, -12);
        String onYearLastMonthStr = DateUtils.format(onYearLastMonthDate, DateUtils.YYYY_MM);

        String filterTemplate = "({date} = '{monthStr}' or {date} = '{lastMonthStr}' " +
                "or {date} = '{onYearMonthStr}' or {date} = '{onYearLastMonthStr}')";
        if (StringUtils.isNotBlank(type)) {
            filterTemplate = filterTemplate + " and type = '{type}'";
        }
        Map<String, Object> filterMap = new HashMap<>();
        filterMap.put("type", type);
        filterMap.put("date", dateField);
        filterMap.put("monthStr", monthStr);
        filterMap.put("lastMonthStr", lastMonthStr);
        filterMap.put("onYearMonthStr", onYearMonthStr);
        filterMap.put("onYearLastMonthStr", onYearLastMonthStr);
        jsonObject.put("filter", ReplaceUtils.replaceWithMap(filterTemplate, filterMap));

        JSONObject sameDayResult = httpTemplate.post(apiUrl, jsonObject);
        JSONArray dayDataList = sameDayResult.getJSONArray("list");
        JSONObject sameMonthData = null, lastMonthData = null,
                onYearSameMonthData = null, onYearLastMonthData = null;
        for (int i = 0; i < dayDataList.size(); i++) {
            JSONObject item = dayDataList.getJSONObject(i);
            if (monthStr.equals(item.getString(dateField))) {
                sameMonthData = item;
            }
            if (lastMonthStr.equals(item.getString(dateField))) {
                lastMonthData = item;
            }
            if (onYearMonthStr.equals(item.getString(dateField))) {
                onYearSameMonthData = item;
            }
            if (onYearLastMonthStr.equals(item.getString(dateField))) {
                onYearLastMonthData = item;
            }
        }
        JSONObject resultData = new JSONObject();
        if (sameMonthData != null) {
            resultData.put("onYearSameMonthCompare", DataComparisonUtils.decimalComparison(sameMonthData, onYearSameMonthData, factor));
        }
        if (lastMonthData != null) {
            resultData.put("onYearLastMonthCompare", DataComparisonUtils.decimalComparison(lastMonthData, onYearLastMonthData, factor));
        }
        return resultData;
    }

    public static JSONObject getYearJsonObject(DvisualHttpTemplate httpTemplate, String apiUrl,
                                               JSONObject jsonObject, BigDecimal factor) {
        return getYearJsonObject(httpTemplate, apiUrl, jsonObject, factor, 0, 1);
    }

    public static JSONObject getYearJsonObject(DvisualHttpTemplate httpTemplate, String apiUrl,
                                               JSONObject jsonObject, BigDecimal factor,
                                               int valueScale, int perScale) {
        int year = DateUtils.getCurrentYear();
        int lastYear = year - 1;
        String filterTemplate = "id = %s or id = %s";
        jsonObject.put("filter", String.format(filterTemplate, year, lastYear));
        JSONObject sameYearResult = httpTemplate.post(apiUrl, jsonObject);
        JSONArray yearDataList = sameYearResult.getJSONArray("list");
        JSONObject sameYearData = null, lastYearData = null;
        for (int i = 0; i < yearDataList.size(); i++) {
            JSONObject item = yearDataList.getJSONObject(i);
            if (year == item.getIntValue("id")) {
                sameYearData = item;
            }
            if (lastYear == item.getIntValue("id")) {
                lastYearData = item;
            }
        }
        if (sameYearData != null) {
            JSONObject resultJsonObject = DataComparisonUtils.decimalComparison(sameYearData, lastYearData,
                    factor, valueScale, perScale);
            resultJsonObject.put("sameYear", year);
            resultJsonObject.put("lastYear", lastYear);
            return resultJsonObject;
        }
        JSONObject resultJsonObject = new JSONObject();
        resultJsonObject.put("sameYear", year);
        resultJsonObject.put("lastYear", lastYear);
        return resultJsonObject;
    }

    /**
     * 同比环比计算
     */
    public static Map<String, Object> decimalYoYMoM(JSONObject curData, String key) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        BigDecimal curValue = curData.getBigDecimal(key);
        BigDecimal yoyValue = curData.getBigDecimal(key + "_pre");
        BigDecimal momValue = curData.getBigDecimal(key + "_phr");

        /**同比环比百分比*/
        if (yoyValue != null) {
            BigDecimal percentageValue = BigDecimal.ZERO;
            percentageValue = curValue.subtract(yoyValue)
                    .divide(yoyValue, 4, RoundingMode.HALF_UP)
                    .multiply(HUNDRED)
                    .setScale(1, RoundingMode.HALF_UP);
            resultMap.put(key + "_yoyPercent", percentageValue.abs());
            resultMap.put(key + "_yoyFlag", curValue.compareTo(yoyValue));/**上涨下跌标志*/
        }
        if (momValue != null) {
            BigDecimal percentageValue = BigDecimal.ZERO;
            percentageValue = curValue.subtract(momValue)
                    .divide(momValue, 4, RoundingMode.HALF_UP)
                    .multiply(HUNDRED)
                    .setScale(1, RoundingMode.HALF_UP);
            resultMap.put(key + "_momPercent", percentageValue.abs());
            resultMap.put(key + "_momFlag", curValue.compareTo(momValue));/**上涨下跌标志*/
        }
        resultMap.put(key, curValue);
        return resultMap;
    }


    public static JSONObject decimalComparison(JSONObject sameData, JSONObject lastData) {
        return decimalComparison(sameData, lastData, null, null, 0, 1);
    }

    public static JSONObject decimalComparison(JSONObject sameData, JSONObject lastData, BigDecimal factor) {
        return decimalComparison(sameData, lastData, factor, factor, 0, 1);
    }

    public static JSONObject decimalComparison(JSONObject sameData, JSONObject lastData,
                                               BigDecimal factor, int valueScale, int perScale) {
        return decimalComparison(sameData, lastData, factor, factor, valueScale, perScale);
    }

    public static JSONObject decimalComparison(JSONObject sameData, JSONObject lastData,
                                               BigDecimal sameFactor, BigDecimal lastFactor) {
        return decimalComparison(sameData, lastData, sameFactor, lastFactor, 0, 1);
    }

    public static JSONObject decimalComparison(JSONObject sameData, JSONObject lastData,
                                               BigDecimal sameFactor, BigDecimal lastFactor,
                                               int valueScale, int perScale) {
        JSONObject result = new JSONObject();
        //如果上期对象null，替换为空对象
        if (lastData == null) {
            lastData = new JSONObject();
        }
        Set<String> keySet = sameData.keySet();
        for (String key : keySet) {
            //排除非计算字段
            if (EXCLUDE_FIELD_SET.contains(key)) {
                result.put(key, sameData.get(key));
                continue;
            }
            BigDecimal sameValue = sameData.getBigDecimal(key);
            BigDecimal lastValue = lastData.getBigDecimal(key);
            if (sameValue != null) {
                //如果上期记录为空，则用pre字段作为上期记录
                if (lastValue == null) {
                    BigDecimal sameValuePre = sameData.getBigDecimal(key + "_pre");
                    if (sameValuePre != null) {
                        lastValue = sameValuePre;
                    } else {
                        lastValue = BigDecimal.ZERO;
                    }
                }
                if (sameFactor != null) { //系数处理
                    sameValue = sameValue.multiply(sameFactor);
                }
                if (lastFactor != null) { //系数处理
                    lastValue = lastValue.multiply(lastFactor);
                }
                sameValue = sameValue.setScale(valueScale, RoundingMode.HALF_UP);
                lastValue = lastValue.setScale(valueScale, RoundingMode.HALF_UP);
                JSONObject item = new JSONObject();
                item.put("sameValue", sameValue);//当期值
                item.put("lastValue", lastValue);//上期值
                item.put("contrastValue", sameValue.subtract(lastValue).abs());//比较值
                item.put("compareFlag", sameValue.compareTo(lastValue));//比较标志
                BigDecimal percentageValue = BigDecimal.ZERO;
                if (lastValue.compareTo(BigDecimal.ZERO) > 0) {
                    percentageValue = sameValue.subtract(lastValue)
                            .divide(lastValue, 4, RoundingMode.HALF_UP)
                            .multiply(HUNDRED)
                            .setScale(perScale, RoundingMode.HALF_UP);
                } else if (lastValue.compareTo(BigDecimal.ZERO) == 0 && sameValue.compareTo(BigDecimal.ZERO) > 0) {
                    percentageValue = HUNDRED;
                }
                item.put("percentageValue", percentageValue.abs()); //比较百分比
                result.put(key, item);
            }
        }
        return result;
    }

    /**
     * 比较指定字段对应的数据
     *
     * @param sameData      现在数据
     * @param lastData      比较数据
     * @param factor        比较单位
     * @param compareFields 需要比较的字段
     * @return
     */
    public static JSONObject compareSpecifiedFieldData(JSONObject sameData, JSONObject lastData, BigDecimal factor, List<String> compareFields) {
        JSONObject filteredSameData = new JSONObject();
        JSONObject filteredLastData = new JSONObject();
        for (String field : compareFields) {
            if (sameData != null && sameData.containsKey(field)) {
                filteredSameData.put(field, sameData.get(field));
            }
            if (null != lastData && lastData.containsKey(field)) {
                filteredLastData.put(field, lastData.get(field));
            }
        }
        return decimalComparison(filteredSameData, filteredLastData, factor);
    }

    /**
     * 组装pie数据
     *
     * @param result 远程接口
     * @param name   名
     * @param value  值
     */
    public static JSONObject assemblingPieData(JSONObject result, String name, String value) {
        if (!result.isEmpty()) {
            // 获取数据列表
            List<?> list = (List<?>) result.get("list");
            //将大数据的数据 和 id 转换 value 和 name
            List<JSONObject> transformedList = list.stream()
                    .map(item -> {
                        // 确保 item 是一个 Map 类型并进行转换
                        if (item instanceof Map) {
                            Map<?, ?> map = (Map<?, ?>) item;
                            JSONObject newJson = new JSONObject();
                            newJson.put("value", map.get(value));
                            newJson.put("name", map.get(name));
                            return newJson;
                        } else {
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            result.put("chartData", transformedList);
        } else {
            result.put("chartData", new ArrayList<>());
        }
        return result;
    }

    /**
     * 组装饼图数据（支持多个字段与名称传入）
     *
     * @param result 原始数据（包含 list）
     * @param nameValuePairs 名称与字段名的映射，例如：
     *                       Map.of("公交车", "bus_vehicles",
     *                               "巡游车", "cruising_vehicles",
     *                               "网约车", "ridehailing_vehicles")
     * @return 拼装后的 Pie 图数据
     */
    public static JSONObject assemblingPieData(JSONObject result, Map<String, String> nameValuePairs) {
        if (!result.isEmpty()) {
            List<?> list = (List<?>) result.get("list");
            if (list != null && !list.isEmpty()) {
                Object firstItem = list.get(0);
                if (firstItem instanceof Map) {
                    Map<String, Object> dataMap = (Map<String, Object>) firstItem;
                    List<JSONObject> pieData = new ArrayList<>();

                    for (Map.Entry<String, String> entry : nameValuePairs.entrySet()) {
                        String name = entry.getKey();
                        String field = entry.getValue();
                        Object value = dataMap.getOrDefault(field, 0);
                        pieData.add(createPieItem(name, value));
                    }

                    result.put("chartData", pieData);
                    return result;
                }
            }
        }
        result.put("chartData", new ArrayList<>());
        return result;
    }

    /**
     * 辅助方法：创建单个饼图项
     */
    private static JSONObject createPieItem(String name, Object value) {
        JSONObject item = new JSONObject();
        item.put("name", name);
        item.put("value", value instanceof Number ? value : 0);
        return item;
    }


    /**
     * 组装line bar图表数据 适用x/y 只有一组数据的情况
     *
     * @param result
     * @param xName
     * @param seriesName
     */
    public static JSONObject assemblingBarOrLineData(JSONObject result, String xName, String seriesName) {
        return assemblingBarOrLineData(result, xName, seriesName, null);
    }

    /**
     * 组装line bar图表数据 适用x/y 只有一组数据的情况
     *
     * @param result
     * @param xName
     * @param seriesName
     */
    public static JSONObject assemblingBarOrLineData(JSONObject result, String xName, String seriesName, Integer unit) {
        if (result.containsKey("list") && result.get("list") != null) {
            List<Map<String, Object>> list = (List<Map<String, Object>>) result.get("list");
            if (!list.isEmpty()) {
                // 遍历数据 组装
                List<String> seriesData = new ArrayList<>();
                List<String> xAxisData = new ArrayList<>();
                List<String> finalXAxisData = xAxisData;
                // 排序前对列表进行日期转换和排序
                list.sort((item1, item2) -> {
                    try {
                        // 解析 xName 的日期
                        Date date1 = parseDate((String) item1.get(xName));
                        Date date2 = parseDate((String) item2.get(xName));

                        // 如果两者均解析为日期，按日期排序
                        if (date1 != null && date2 != null) {
                            return date1.compareTo(date2);
                        }
                        // 如果只有一个解析为日期，日期排在前
                        if (date1 != null) return -1;
                        if (date2 != null) return 1;

//                    // 两者均非日期，按字符串自然顺序排序
//                    String str1 = (String) item1.get(xName);
//                    String str2 = (String) item2.get(xName);
//                    return str1.compareTo(str2);
                    } catch (Exception e) {
                        // 捕获异常并默认返回 0，不改变原有顺序
                        return 0;
                    }
                    return 0;
                });
                list.forEach(item -> {
                    if (item instanceof Map) {
                        Object xVal = item.get(xName);
                        finalXAxisData.add(xVal != null ? xVal.toString() : "");

                        Object val = item.get(seriesName);
                        String strVal = (val != null) ? val.toString() : "0";
                        try {
                            if (unit != null) {
                                long num = Long.parseLong(strVal);
                                seriesData.add(String.valueOf(num / unit));
                            } else {
                                seriesData.add(strVal);
                            }
                        } catch (NumberFormatException e) {
                            seriesData.add("0");
                        }
                    }
                });
                if ("hour".equals(xName)) {
                    xAxisData = finalXAxisData.stream().map(item -> item.length() < 2 ? "0" + item + ":00" : item + ":00").collect(Collectors.toList());
                }
                result.put("seriesData", seriesData);
                result.put("xAxisData", xAxisData);
            } else {
                result.put("xAxisData", new ArrayList<>());
                result.put("seriesData", new ArrayList<>());
            }
        }
        return result;
    }

    /**
     * 组装line bar图表数据，适用x/y 有多组数据的情况
     *
     * @param result      数据源，包含一个列表
     * @param xName       x轴名称
     * @param seriesNames 数据列名称数组，可以为多个系列
     * @return JSONObject 包含 xAxisData 和 每列系列数据
     */
    public static JSONObject assemblingMoreBarOrLineData(JSONObject result, String xName, String... seriesNames) {
        // 创建返回对象，如果 result 为空则初始化一个新的 JSONObject
        JSONObject output = (result != null) ? result : new JSONObject();
        // 如果xName存在则合并数据
        // 检查 result 是否包含 "list" 键且不为空
        if (output.containsKey("list") && output.get("list") != null) {
            List<Map<String, Object>> list = (List<Map<String, Object>>) output.get("list");

            if (!list.isEmpty()) {
                // 初始化 x 轴数据和系列数据容器
                List<String> xAxisData = new ArrayList<>();
                Map<String, List<String>> seriesDataMap = new LinkedHashMap<>();

                // 初始化每列的数据列表
                for (String seriesName : seriesNames) {
                    seriesDataMap.put(seriesName, new ArrayList<>());
                }

                // 排序前对列表进行日期转换和排序
                list.sort((item1, item2) -> {
                    try {
                        // 解析 xName 的日期
                        Date date1 = parseDate((String) item1.get(xName));
                        Date date2 = parseDate((String) item2.get(xName));

                        // 如果两者均解析为日期，按日期排序
                        if (date1 != null && date2 != null) {
                            return date1.compareTo(date2);
                        }
                        // 如果只有一个解析为日期，日期排在前
                        if (date1 != null) return -1;
                        if (date2 != null) return 1;

                        // 两者均非日期，按字符串自然顺序排序
                        // String str1 = (String) item1.get(xName);
                        // String str2 = (String) item2.get(xName);
                        // return str1.compareTo(str2);
                    } catch (Exception e) {
                        // 捕获异常并默认返回 0，不改变原有顺序
                        return 0;
                    }
                    return 0;
                });

                // 遍历数据列表，提取 x 轴和各系列数据
                for (Map<String, Object> item : list) {
                    if (item != null) {
                        xAxisData.add((String) item.get(xName));
                        for (String seriesName : seriesNames) {
                            List<String> seriesData = seriesDataMap.get(seriesName);
                            if (seriesData != null && item.containsKey(seriesName)) {
                                seriesData.add(StringHelper.toString(item.get(seriesName)));
                            }
                        }
                    }
                }

                // 对特殊的 x 轴数据（如小时）格式化处理
                if ("hour".equals(xName)) {
                    xAxisData = xAxisData.stream()
                            .map(item -> item.length() < 2 ? "0" + item + ":00" : item + ":00")
                            .collect(Collectors.toList());
                }

                // 将结果存入 output
                output.put("xAxisData", xAxisData);
                for (Map.Entry<String, List<String>> entry : seriesDataMap.entrySet()) {
                    output.put(entry.getKey() + "Data", entry.getValue());
                }

            } else {
                // list 为空时返回空数据
                output.put("xAxisData", new ArrayList<>());
                for (String seriesName : seriesNames) {
                    output.put(seriesName + "Data", new ArrayList<>());
                }
            }
        } else {
            // result 不包含 "list" 或 "list" 为 null 时返回空数据
            output.put("xAxisData", new ArrayList<>());
            for (String seriesName : seriesNames) {
                output.put(seriesName + "Data", new ArrayList<>());
            }
        }
        return output;
    }

    /*
     * <AUTHOR>
     * @description //TODO 组装line bar图表数据，适用x/y 有多组数据，并且key可能重复的情况，目前只对Integer数据进行合并
     * @date 2025/5/7 10:08
     * @param result
     * @param xName
     * @param key
     * @param seriesNames
     * @return com.alibaba.fastjson.JSONObject
     **/
    public static JSONObject assemblingMoreBarOrLineDataMerge(JSONObject result, String xName, String... seriesNames) {
        // 创建返回对象，如果 result 为空则初始化一个新的 JSONObject
        JSONObject output = (result != null) ? result : new JSONObject();
        // 如果xName存在则合并数据
        // 检查 result 是否包含 "list" 键且不为空
        if (output.containsKey("list") && output.get("list") != null) {
            List<Map<String, Object>> list = (List<Map<String, Object>>) output.get("list");

            if (!list.isEmpty()) {
                // 初始化 x 轴数据和系列数据容器
                List<String> xAxisData = new ArrayList<>();
                Map<String, List<String>> seriesDataMap = new LinkedHashMap<>();

                // 初始化每列的数据列表
                for (String seriesName : seriesNames) {
                    seriesDataMap.put(seriesName, new ArrayList<>());
                }

                // 排序前对列表进行日期转换和排序
                list.sort((item1, item2) -> {
                    try {
                        // 解析 xName 的日期
                        Date date1 = parseDate((String) item1.get(xName));
                        Date date2 = parseDate((String) item2.get(xName));

                        // 如果两者均解析为日期，按日期排序
                        if (date1 != null && date2 != null) {
                            return date1.compareTo(date2);
                        }
                        // 如果只有一个解析为日期，日期排在前
                        if (date1 != null) return -1;
                        if (date2 != null) return 1;

                        // 两者均非日期，按字符串自然顺序排序
                        // String str1 = (String) item1.get(xName);
                        // String str2 = (String) item2.get(xName);
                        // return str1.compareTo(str2);
                    } catch (Exception e) {
                        // 捕获异常并默认返回 0，不改变原有顺序
                        return 0;
                    }
                    return 0;
                });

                // 遍历数据列表，提取 x 轴和各系列数据
                Map<String, Integer> distinctSeriesDataMap = new HashMap<>();
                for (Map<String, Object> item : list) {
                    if (item != null) {
                        if (!item.containsKey(xName)) {
                            throw new RestfulAPIException("数据重复key不能为空");
                        }
                        String key = (String) item.get(xName);
                        if (distinctSeriesDataMap.containsKey(key)) {
                            // 重复数据, x轴不再新增，只对series数据做累加
                            Integer dataIdx = distinctSeriesDataMap.get(key);
                            // 第二个 序列项数据
                            for (String seriesName : seriesNames) {
                                List<String> seriesData = seriesDataMap.get(seriesName);
                                if (seriesData != null && item.containsKey(seriesName)) {
                                    // 如果是数值，则合并数据
                                    Object o = item.get(seriesName);
                                    if (o instanceof Integer) {
                                        String existingValue = seriesData.get(dataIdx);
                                        Integer newValue = 0;
                                        try {
                                            newValue = Integer.parseInt(existingValue) + ((Integer) o);
                                        } catch (Exception ignore) {
                                        }
                                        seriesData.set(dataIdx, StringHelper.toString(newValue));
                                    }
                                    // todo 其他数据类型
                                }
                            }
                        } else {
                            // 第一个 x轴
                            xAxisData.add(key);
                            // 第二个 序列项数据
                            for (String seriesName : seriesNames) {
                                List<String> seriesData = seriesDataMap.get(seriesName);
                                if (seriesData != null && item.containsKey(seriesName)) {
                                    seriesData.add(StringHelper.toString(item.get(seriesName)));
                                }
                                // 记录当前x轴的下标
                                assert seriesData != null;
                                distinctSeriesDataMap.putIfAbsent(key, seriesData.size() - 1);
                            }
                        }
                    }
                }

                // 对特殊的 x 轴数据（如小时）格式化处理
                if ("hour".equals(xName)) {
                    xAxisData = xAxisData.stream()
                            .map(item -> item.length() < 2 ? "0" + item + ":00" : item + ":00")
                            .collect(Collectors.toList());
                }

                // 将结果存入 output
                output.put("xAxisData", xAxisData);
                for (Map.Entry<String, List<String>> entry : seriesDataMap.entrySet()) {
                    output.put(entry.getKey() + "Data", entry.getValue());
                }

            } else {
                // list 为空时返回空数据
                output.put("xAxisData", new ArrayList<>());
                for (String seriesName : seriesNames) {
                    output.put(seriesName + "Data", new ArrayList<>());
                }
            }
        } else {
            // result 不包含 "list" 或 "list" 为 null 时返回空数据
            output.put("xAxisData", new ArrayList<>());
            for (String seriesName : seriesNames) {
                output.put(seriesName + "Data", new ArrayList<>());
            }
        }
        return output;
    }


    /**
     * 解析日期字符串，支持 yyyy-MM-dd、yyyy-MM 和 yyyy 格式
     *
     * @param dateStr 日期字符串
     * @return Date 对象（解析失败返回 null）
     */
    private static Date parseDate(String dateStr) {
        if (dateStr == null || dateStr.isEmpty()) {
            return null;
        }
        List<DateTimeFormatter> formatters = Arrays.asList(
                DateTimeFormatter.ofPattern("yyyy-MM-dd"),
                DateTimeFormatter.ofPattern("yyyy-MM"),
                DateTimeFormatter.ofPattern("yyyy")
        );

        for (DateTimeFormatter formatter : formatters) {
            try {
                // 转换为 LocalDate 并再转为 Date
                return Date.from(LocalDate.parse(dateStr, formatter).atStartOfDay(ZoneId.systemDefault()).toInstant());
            } catch (DateTimeParseException ignored) {
                // 尝试下一个格式
            }
        }
        return null;
    }


    public static String formatMonthDayStr(String Ymd) {
        if (StringUtils.isNotBlank(Ymd) && Ymd.length() >= 10) {
            return Ymd.substring(5, 10).replace("-", "/");
        } else {
            return "";
        }
    }

    public static String formatMonthStr(String Ymd) {
        if (StringUtils.isNotBlank(Ymd) && Ymd.length() >= 7) {
            return Ymd.substring(5, 7);
        } else {
            return "";
        }
    }

    public static BigDecimal nullToZero(BigDecimal num) {
        if (num == null) {
            return BigDecimal.ZERO;
        } else {
            return num;
        }
    }

    public static JSONObject getResultFirst(JSONObject result) {
        JSONArray dataList = result.getJSONArray("list");
        if (dataList != null && dataList.size() > 0) {
            return dataList.getJSONObject(0);
        }
        return new JSONObject();
    }

    public static JSONObject getResultFirst(JSONArray dataList) {
        if (dataList != null && dataList.size() > 0) {
            return dataList.getJSONObject(0);
        }
        return new JSONObject();
    }

    public static JSONObject getResultLast(JSONObject result) {
        JSONArray dataList = result.getJSONArray("list");
        if (dataList != null && dataList.size() > 0) {
            return dataList.getJSONObject(dataList.size()-1);
        }
        return new JSONObject();
    }

    public static JSONObject getResultLast(JSONArray dataList) {
        if (dataList != null && dataList.size() > 0) {
            return dataList.getJSONObject(dataList.size()-1);
        }
        return new JSONObject();
    }

    public static JSONArray getResultList(JSONObject result) {
        JSONArray dataList = result.getJSONArray("list");
        if (dataList != null) {
            return dataList;
        }
        return new JSONArray();
    }

//    public static List<Devcon> calculateDistance(double latitude, double longitude, List<Devcon> devconList) {
//        for (Devcon devcon : devconList) {
//            double distance = OrderMapComparedUtils.distance(latitude, longitude, devcon.getLatitude(), devcon.getLongitude());
//            devcon.setDistance(Double.parseDouble(String.format("%.2f", distance)));
//        }
//        devconList.sort(Comparator.comparing(Devcon::getDistance));
//        return devconList;
//    }

    public static JSONArray getResultBatchWise(DvisualHttpTemplate httpTemplate, String url,
                                               JSONObject jsonObject, int limit) {
        int s = 0, n = limit, max = 10000 * 50;
        JSONArray dataArray = new JSONArray();
        while (true){
            jsonObject.put("s", s);
            jsonObject.put("n", n);
            JSONObject result = httpTemplate.post(url, jsonObject);
            JSONArray resultList = getResultList(result);
            if(resultList.size() > 0 && s < max){
                dataArray.addAll(resultList);
            }else{
                break;
            }
            int total = result.getIntValue("total");
            if(dataArray.size() >= total){
                break;
            }
            s = s + n;
        }
        return dataArray;
    }

    /**
     * 抽取查询最新日期
     * @param httpTemplate
     * @param url
     * @param timeField
     * @param filterTemplate
     * @param keyValueArray
     * @return
     */
    public static String getMaxTime(DvisualHttpTemplate httpTemplate, String url,
                                    String timeField, String filterTemplate,
                                    String... keyValueArray) {
        JSONObject maxTimeFilter = new JSONObject();
        maxTimeFilter.put("s", 0);
        maxTimeFilter.put("n", 10);
        maxTimeFilter.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                keyValueArray));
        JSONObject result = httpTemplate.post(url, maxTimeFilter);
        JSONObject resultFirst = getResultFirst(result);
        return resultFirst.getString(timeField);
    }

}
