package com.yinshu.toms.util;

import com.alibaba.fastjson.JSON;
import com.yinshu.toms.common.ApiResult;
import com.yinshu.toms.service.impl.VideoServiceImpl;
import com.yinshu.toms.vo.video.SignInfo;
import lombok.Data;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.HashMap;
import java.util.Map;

public class SignUtil {
    /**
     * 生成签名
     *
     * @param key    用户key
     * @param secret 秘钥
     * @return
     */
    public static String getSign(String key, String secret) throws Exception {
        SignInfo info = new SignInfo();
        info.setUserKey(key);
        info.setTimeStamp(System.currentTimeMillis());
        return encrypt(JSON.toJSONString(info), secret);
    }

    /**
     * 加密
     *
     * @param content 待加密明文
     * @param secret  秘钥
     * @return 密文
     * @throws Exception
     */
    public static String encrypt(String content, String secret) throws Exception {
        // CBC模式需要生成一个16 bytes的initialization vector:
        SecureRandom sr = SecureRandom.getInstanceStrong();
        byte[] iv = sr.generateSeed(16);
        // 初始化IV参数
        IvParameterSpec ivps = new IvParameterSpec(iv);

        // 将待加密字符串转为字节数组
        byte[] input = content.getBytes(StandardCharsets.UTF_8);
        // 将秘钥进行md5 hash处理
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] thedigest = md.digest(secret.getBytes(StandardCharsets.UTF_8));
        // aes加密，CBC加密模式，PKCS5Padding填充方式
        SecretKeySpec skc = new SecretKeySpec(thedigest, "AES");
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, skc, ivps);
        byte[] cipherText = cipher.doFinal(input);

        return parseByte2HexStr(join(iv, cipherText));
    }

    /**
     * 将iv字节数组和加密后的密文拼接好，  前面的是iv后面的是cipherText
     *
     * @param iv
     * @param cipherText
     * @return 拼接后的内容
     */
    private static byte[] join(byte[] iv, byte[] cipherText) {
        byte[] r = new byte[iv.length + cipherText.length];
        System.arraycopy(iv, 0, r, 0, iv.length);
        System.arraycopy(cipherText, 0, r, iv.length, cipherText.length);
        return r;
    }

    /**
     * 字节转16进制数组
     *
     * @param buf
     * @return
     */
    private static String parseByte2HexStr(byte[] buf) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < buf.length; i++) {
            String hex = Integer.toHexString(buf[i] & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            sb.append(hex);
        }
        return sb.toString();
    }

    public static void main(String[] args) throws Exception {
        // 生成用户签名
        String userKey = "xinzhou_operation_monitoring";
        String userSecret = "75c27d667ebd4e5f80d9dec8210caa56";
        String sign = SignUtil.getSign(userKey, userSecret);
        System.out.println("sign: " + sign);
        // AES加密密码
        String userPwd = "xinzhou_operation_monitoring";
        String encrypt = SignUtil.encrypt(userPwd, userSecret);
        System.out.println("encrypt pwd: " + encrypt);
        // 生成鉴权所需的sign
        // 构建请求参数
        Map<String, String> authRequest =new HashMap<>();
        authRequest.put("userKey", userKey);
        authRequest.put("sign", encrypt);
        // 发送请求
        HttpEntity<Map> request = new HttpEntity<>(authRequest);

        // 修复类型转换问题，使用ParameterizedTypeReference来正确处理泛型类型
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<ApiResult<ApiResult>> response = restTemplate.exchange(
                "http://111.53.42.6:20551/gateway/alpha3/api/v1/user/sign/auth", HttpMethod.POST, request, new ParameterizedTypeReference<ApiResult<ApiResult>>() {}
        );
        System.out.println("请求结果: " + response.getBody());
        // 解析响应
        ApiResult<ApiResult> result = response.getBody();
        if (result == null || !result.isSuccess() || result.getData() == null) {
           // log.error("鉴权接口返回失败：{}", result);
           // return null;
        }

    }

}
