package com.yinshu.fast.excel.demo;

import com.yinshu.fast.excel.util.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TestController
 * @description TODO
 * @date 2025/6/3 17:24
 **/
@RestController
@RequestMapping("/api")
@Slf4j
public class TestController {


    @PostMapping("/export")
    public void exportExcel(@RequestBody DemoReqeustParams object) {
        // todo 获取到请求参数，参数数据
        ExcelUtils.exportExcelSheet(DemoData.class, DemoData.data());
    }
}
