package com.yinshu.fast.excel.util;

import cn.idev.excel.converters.Converter;
import cn.idev.excel.enums.CellDataTypeEnum;
import cn.idev.excel.metadata.GlobalConfiguration;
import cn.idev.excel.metadata.data.ReadCellData;
import cn.idev.excel.metadata.data.WriteCellData;
import cn.idev.excel.metadata.property.ExcelContentProperty;

import java.util.HashMap;
import java.util.Map;

public class WarnLevelConverter implements Converter<String> {

    private static final Map<String, String> codeToText = new HashMap<>();
    private static final Map<String, String> textToCode = new HashMap<>();

    static {
        codeToText.put("0", "无");
        codeToText.put("1", "一级");
        codeToText.put("2", "二级");
        codeToText.put("3", "三级");
        codeToText.put("4", "四级");
        codeToText.put("5", "五级");

        // 反向映射
        textToCode.put("无", "0");
        textToCode.put("一级", "1");
        textToCode.put("二级", "2");
        textToCode.put("三级", "3");
        textToCode.put("四级", "4");
        textToCode.put("五级", "5");
    }

    @Override
    public Class<?> supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    // Excel -> Java
    @Override
    public String convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        String code = String.valueOf(cellData.getData());
        return textToCode.getOrDefault(code, code);
    }

    // Java -> Excel
    @Override
    public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        String code = codeToText.getOrDefault(value, value);
        return new WriteCellData<>(code);
    }
}
