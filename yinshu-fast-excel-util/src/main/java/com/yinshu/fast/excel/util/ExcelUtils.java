package com.yinshu.fast.excel.util;

import cn.idev.excel.FastExcel;
import cn.idev.excel.write.metadata.style.WriteCellStyle;
import cn.idev.excel.write.metadata.style.WriteFont;
import cn.idev.excel.write.style.HorizontalCellStyleStrategy;
import cn.idev.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ExcelUtils
 * @description TODO ExcelUtils
 * @date 2025/6/3 17:51
 **/
@Slf4j
public class ExcelUtils {

    public static final String DEFAULT_SHEET_NAME = "导出数据";

    /*
     * <AUTHOR>
     * @description //TODO 获取response
     * @date 2025/6/3 17:51
     * @return javax.servlet.http.HttpServletResponse
     **/
    public static HttpServletResponse getResponse() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes != null ? attributes.getResponse() : null;
    }

    /*
     * <AUTHOR>
     * @description //TODO 导出Excel
     * @date 2025/6/3 17:57
     * @param clazz 映射实体类
     * @param data 数据
     **/
    public static <T> void exportExcelSheet(Class<T> clazz, List<T> data) {
        exportExcelSheet(DEFAULT_SHEET_NAME, UUID.randomUUID().toString(), clazz, data);
    }


    /*
     * <AUTHOR>
     * @description //TODO 导出Excel
     * @date 2025/6/3 17:57
     * @param clazz 映射实体类
     * @param data 数据
     **/
    public static <T> void exportExcelSheet(Class<T> clazz, JSONArray data) {
        List<T> dataList = data.toJavaList(clazz);
        exportExcelSheet(DEFAULT_SHEET_NAME, UUID.randomUUID().toString(), clazz, dataList);
    }

    /*
     * <AUTHOR>
     * @description //TODO 导出Excel
     * @date 2025/6/3 17:57
     * @param clazz 映射实体类
     * @param data 数据
     **/
    public static <T> void exportExcelSheet(Class<T> clazz, JSONObject data) {
        JSONArray list = data.getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject jsonObject = list.getJSONObject(i);
            jsonObject.put("no", i + 1);
        }
        List<T> dataList = list.toJavaList(clazz);
        exportExcelSheet(DEFAULT_SHEET_NAME, UUID.randomUUID().toString(), clazz, dataList);
    }


    /*
     * <AUTHOR>
     * @description //TODO 导出Excel
     * @date 2025/6/3 17:57
     * @param fileName 文件名
     * @param clazz 映射实体类
     * @param data 数据
     **/
    public static <T> void exportExcelSheet(String fileName, Class<T> clazz, List<T> data) {
        exportExcelSheet(DEFAULT_SHEET_NAME, fileName, clazz, data);
    }


    /*
     * <AUTHOR>
     * @description //TODO 导出Excel
     * @date 2025/6/3 17:51
     * @param sheetName 表单名称
     * @param clazz 映射实体类
     * @param data 数据
     **/
    public static <T> void exportExcelSheet(String sheetName, String fileName, Class<T> clazz, List<T> data) {
        try {
            HttpServletResponse response = getResponse();
            assert response != null;
            // 设置响应头：告诉浏览器是个下载文件
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");

            // 设置文件名（防止中文乱码）
            String name = URLEncoder.encode(fileName + ".xlsx", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + name);

            // 标题样式
            WriteCellStyle headStyle = new WriteCellStyle();
            WriteFont headFont = new WriteFont();
            // 设置字体
            headFont.setFontName("微软雅黑");
            // 设置字体大小
            headFont.setFontHeightInPoints((short) 12);
            headStyle.setWriteFont(headFont);
            // 居中
            headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

            // 内容样式
            WriteCellStyle contentStyle = new WriteCellStyle();
            WriteFont contentFont = new WriteFont();
            contentFont.setFontName("微软雅黑");
            contentFont.setFontHeightInPoints((short) 12);
            contentStyle.setWriteFont(contentFont);
            contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            // 样式策略
            HorizontalCellStyleStrategy styleStrategy = new HorizontalCellStyleStrategy(headStyle, contentStyle);

            // 写入响应流，而不是写入文件系统
            FastExcel.write(response.getOutputStream(), clazz)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(styleStrategy)
                    .sheet(sheetName)
                    .doWrite(data);
        } catch (Exception e) {
            // 也可以使用日志记录
            log.error(e.getMessage());
        }
    }


}
