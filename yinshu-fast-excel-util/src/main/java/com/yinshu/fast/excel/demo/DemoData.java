package com.yinshu.fast.excel.demo;

import cn.idev.excel.FastExcel;
import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DemoData
 * @description TODO https://readmex.com/fast-excel/fastexcel/page-151366ada-2ea3-4451-aa3d-98ac90ce4f6e
 * @date 2025/6/3 17:10
 **/
// 示例数据类
@Data
public class DemoData {

    @ExcelProperty(value = "字符串标题", order = 1)
    private String string;

    @ExcelProperty(value = "日期标题", order = 2)
    @ColumnWidth(25)
    private Date date;

    @ExcelProperty(value = "数字标题", order = 3)
    private Double doubleData;

    @ExcelIgnore
    private String ignore;


    // 填充要写入的数据
    public static List<DemoData> data() {
        List<DemoData> list = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            DemoData data = new DemoData();
            data.setString("字符串" + i);
            data.setDate(new Date());
            data.setDoubleData(0.56);
            list.add(data);
        }
        return list;
    }


    public static void main(String[] args) {
        String fileName = "demo.xlsx";
        // 创建一个名为“模板”的 sheet 页，并写入数据
        FastExcel.write(fileName, DemoData.class).sheet("模板").doWrite(data());
    }
}
