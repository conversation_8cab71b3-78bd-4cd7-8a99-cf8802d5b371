<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.tcps.dao.RecordSupervisionDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yinshu.tcps.entity.RecordSupervision">
        <id column="id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="record_time" property="recordTime" />
        <result column="work_mode" property="workMode" />
        <result column="site_record_file" property="siteRecordFile" />
        <result column="supervisor_name" property="supervisorName" />
        <result column="site_leader" property="siteLeader" />
        <result column="inspection_content" property="inspectionContent" />
        <result column="created_at" property="createdAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_at" property="updatedAt" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_id, record_time, work_mode, supervisor_name, site_leader, inspection_content, created_at, created_by, updated_at, updated_by,site_record_file
    </sql>


	<select id="queryList" resultMap="BaseResultMap" parameterType="com.yinshu.tcps.entity.RecordSupervision">
        select
        <include refid="Base_Column_List"></include>
        from tcps_record_supervision
        <where>
            <if test="entity.id != null and entity.id != '' ">
                and id = #{entity.id}
            </if>
            <if test="entity.projectId != null and entity.projectId != '' ">
                and project_id = #{entity.projectId}
            </if>
            <if test="entity.recordTime != null and entity.recordTime != '' ">
                and record_time = #{entity.recordTime}
            </if>
            <if test="entity.workMode != null and entity.workMode != '' ">
                and work_mode = #{entity.workMode}
            </if>
            <if test="entity.supervisorName != null and entity.supervisorName != '' ">
                and supervisor_name like concat('%', #{entity.supervisorName}, '%')
            </if>
            <if test="entity.siteLeader != null and entity.siteLeader != '' ">
                and site_leader = #{entity.siteLeader}
            </if>
            <if test="entity.inspectionContent != null and entity.inspectionContent != '' ">
                and inspection_content = #{entity.inspectionContent}
            </if>
            <if test="entity.createdAt != null and entity.createdAt != '' ">
                and created_at = #{entity.createdAt}
            </if>
            <if test="entity.createdBy != null and entity.createdBy != '' ">
                and created_by = #{entity.createdBy}
            </if>
            <if test="entity.updatedAt != null and entity.updatedAt != '' ">
                and updated_at = #{entity.updatedAt}
            </if>
            <if test="entity.updatedBy != null and entity.updatedBy != '' ">
                and updated_by = #{entity.updatedBy}
            </if>
        </where>
        order by updated_by desc,created_at desc
    </select>

	<!-- 分页查询  -->
	<select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.tcps.entity.RecordSupervision">
        select
        <include refid="Base_Column_List"></include>
        from tcps_record_supervision
        <where>
            <if test="entity.keyword != null and entity.keyword != '' ">
                (
                <trim prefix="" prefixOverrides="or">
                    or work_mode like concat(concat('%', #{entity.keyword}), '%')
                    or supervisor_name like concat(concat('%', #{entity.keyword}), '%')
                    or site_leader like concat(concat('%', #{entity.keyword}), '%')
                    or inspection_content like concat(concat('%', #{entity.keyword}), '%')
                    or created_by like concat(concat('%', #{entity.keyword}), '%')
                    or updated_by like concat(concat('%', #{entity.keyword}), '%')
                </trim>
                )
            </if>
            <if test="entity.startTime != null and entity.stopTime != null">
                and record_time between #{entity.startTime} and #{entity.stopTime}
            </if>
            <if test="entity.id != null and entity.id != '' ">
                and id = #{entity.id}
            </if>
            <if test="entity.projectId != null and entity.projectId != '' ">
                and project_id = #{entity.projectId}
            </if>
            <if test="entity.recordTime != null and entity.recordTime != '' ">
                and record_time = #{entity.recordTime}
            </if>
            <if test="entity.workMode != null and entity.workMode != '' ">
                and work_mode = #{entity.workMode}
            </if>
            <if test="entity.supervisorName != null and entity.supervisorName != '' ">
                and supervisor_name = #{entity.supervisorName}
            </if>
            <if test="entity.siteLeader != null and entity.siteLeader != '' ">
                and site_leader = #{entity.siteLeader}
            </if>
            <if test="entity.inspectionContent != null and entity.inspectionContent != '' ">
                and inspection_content = #{entity.inspectionContent}
            </if>
            <if test="entity.createdAt != null and entity.createdAt != '' ">
                and created_at = #{entity.createdAt}
            </if>
            <if test="entity.createdBy != null and entity.createdBy != '' ">
                and created_by = #{entity.createdBy}
            </if>
            <if test="entity.updatedAt != null and entity.updatedAt != '' ">
                and updated_at = #{entity.updatedAt}
            </if>
            <if test="entity.updatedBy != null and entity.updatedBy != '' ">
                and updated_by = #{entity.updatedBy}
            </if>
        </where>
        order by updated_by desc,created_at desc
    </select>



</mapper>