<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.tcps.dao.ProjectDocumentDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yinshu.tcps.entity.ProjectDocument">
        <id column="id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="file_name" property="fileName" />
        <result column="file_path" property="filePath" />
        <result column="document_type" property="documentType" />
        <result column="document_source" property="documentSource" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_id, file_name, file_path, document_type, document_source, create_time, create_user, update_time, update_user
    </sql>


	<select id="queryList" resultMap="BaseResultMap" parameterType="com.yinshu.tcps.entity.ProjectDocument">
		select <include refid="Base_Column_List"></include>
		from tcps_project_document
		<where>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.projectId != null and entity.projectId != '' ">
					and project_id = #{entity.projectId}
				</if>
				<if test="entity.fileName != null and entity.fileName != '' ">
					and file_name = #{entity.fileName}
				</if>
				<if test="entity.filePath != null and entity.filePath != '' ">
					and file_path = #{entity.filePath}
				</if>
				<if test="entity.documentType != null and entity.documentType != '' ">
					and document_type = #{entity.documentType}
				</if>
				<if test="entity.documentSource != null and entity.documentSource != '' ">
					and document_source = #{entity.documentSource}
				</if>
				<if test="entity.createTime != null and entity.createTime != '' ">
					and create_time = #{entity.createTime}
				</if>
				<if test="entity.createUser != null and entity.createUser != '' ">
					and create_user = #{entity.createUser}
				</if>
				<if test="entity.updateTime != null and entity.updateTime != '' ">
					and update_time = #{entity.updateTime}
				</if>
				<if test="entity.updateUser != null and entity.updateUser != '' ">
					and update_user = #{entity.updateUser}
				</if>
		</where>
	</select>

	<!-- 分页查询  -->
	<select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.tcps.entity.ProjectDocument">
		select <include refid="Base_Column_List"></include>
		from tcps_project_document
		<where>
			<if test="entity.keyword != null and entity.keyword != '' ">
				(
				<trim prefix="" prefixOverrides="or">
						or file_name like concat(concat('%', #{entity.keyword}), '%')
						or file_path like concat(concat('%', #{entity.keyword}), '%')
						or document_type like concat(concat('%', #{entity.keyword}), '%')
						or document_source like concat(concat('%', #{entity.keyword}), '%')
						or create_user like concat(concat('%', #{entity.keyword}), '%')
						or update_user like concat(concat('%', #{entity.keyword}), '%')
				</trim>
				)
			</if>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.projectId != null and entity.projectId != '' ">
					and project_id = #{entity.projectId}
				</if>
				<if test="entity.fileName != null and entity.fileName != '' ">
					and file_name = #{entity.fileName}
				</if>
				<if test="entity.filePath != null and entity.filePath != '' ">
					and file_path = #{entity.filePath}
				</if>
				<if test="entity.documentType != null and entity.documentType != '' ">
					and document_type = #{entity.documentType}
				</if>
				<if test="entity.documentSource != null and entity.documentSource != '' ">
					and document_source = #{entity.documentSource}
				</if>
				<if test="entity.createTime != null and entity.createTime != '' ">
					and create_time = #{entity.createTime}
				</if>
				<if test="entity.createUser != null and entity.createUser != '' ">
					and create_user = #{entity.createUser}
				</if>
				<if test="entity.updateTime != null and entity.updateTime != '' ">
					and update_time = #{entity.updateTime}
				</if>
				<if test="entity.updateUser != null and entity.updateUser != '' ">
					and update_user = #{entity.updateUser}
				</if>
		</where>
		order by create_time desc
	</select>



</mapper>