<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.tcps.dao.ProjectPersonnelDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yinshu.tcps.entity.ProjectPersonnel">
        <id column="id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="name" property="name" />
        <result column="gender" property="gender" />
        <result column="job" property="job" />
        <result column="position" property="position" />
        <result column="id_number" property="idNumber" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_id, name, gender, job, position, id_number, status, create_time, create_user, update_time, update_user
    </sql>


	<select id="queryList" resultMap="BaseResultMap" parameterType="com.yinshu.tcps.entity.ProjectPersonnel">
		select <include refid="Base_Column_List"></include>
		from tcps_project_personnel
		<where>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.projectId != null and entity.projectId != '' ">
					and project_id = #{entity.projectId}
				</if>
				<if test="entity.name != null and entity.name != '' ">
					and name = #{entity.name}
				</if>
				<if test="entity.gender != null and entity.gender != '' ">
					and gender = #{entity.gender}
				</if>
				<if test="entity.job != null and entity.job != '' ">
					and job = #{entity.job}
				</if>
				<if test="entity.position != null and entity.position != '' ">
					and position = #{entity.position}
				</if>
				<if test="entity.idNumber != null and entity.idNumber != '' ">
					and id_number = #{entity.idNumber}
				</if>
				<if test="entity.status != null and entity.status != '' ">
					and status = #{entity.status}
				</if>
				<if test="entity.createTime != null and entity.createTime != '' ">
					and create_time = #{entity.createTime}
				</if>
				<if test="entity.createUser != null and entity.createUser != '' ">
					and create_user = #{entity.createUser}
				</if>
				<if test="entity.updateTime != null and entity.updateTime != '' ">
					and update_time = #{entity.updateTime}
				</if>
				<if test="entity.updateUser != null and entity.updateUser != '' ">
					and update_user = #{entity.updateUser}
				</if>
		</where>
	</select>

	<!-- 分页查询  -->
	<select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.tcps.entity.ProjectPersonnel">
		select <include refid="Base_Column_List"></include>
		from tcps_project_personnel
		<where>
			<if test="entity.keyword != null and entity.keyword != '' ">
				(
				<trim prefix="" prefixOverrides="or">
						or name like concat(concat('%', #{entity.keyword}), '%')
						or gender like concat(concat('%', #{entity.keyword}), '%')
						or job like concat(concat('%', #{entity.keyword}), '%')
						or position like concat(concat('%', #{entity.keyword}), '%')
						or id_number like concat(concat('%', #{entity.keyword}), '%')
						or status like concat(concat('%', #{entity.keyword}), '%')
						or create_user like concat(concat('%', #{entity.keyword}), '%')
						or update_user like concat(concat('%', #{entity.keyword}), '%')
				</trim>
				)
			</if>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.projectId != null and entity.projectId != '' ">
					and project_id = #{entity.projectId}
				</if>
				<if test="entity.name != null and entity.name != '' ">
					and name like concat(concat('%', #{entity.name}), '%')
				</if>
				<if test="entity.gender != null and entity.gender != '' ">
					and gender = #{entity.gender}
				</if>
				<if test="entity.job != null and entity.job != '' ">
					and job like concat(concat('%', #{entity.job}), '%')
				</if>
				<if test="entity.position != null and entity.position != '' ">
					and position like concat(concat('%', #{entity.position}), '%')
				</if>
				<if test="entity.idNumber != null and entity.idNumber != '' ">
					and id_number like concat(concat('%', #{entity.idNumber}), '%')
				</if>
				<if test="entity.status != null and entity.status != '' ">
					and status = #{entity.status}
				</if>
				<if test="entity.createTime != null and entity.createTime != '' ">
					and create_time = #{entity.createTime}
				</if>
				<if test="entity.createUser != null and entity.createUser != '' ">
					and create_user = #{entity.createUser}
				</if>
				<if test="entity.updateTime != null and entity.updateTime != '' ">
					and update_time = #{entity.updateTime}
				</if>
				<if test="entity.updateUser != null and entity.updateUser != '' ">
					and update_user = #{entity.updateUser}
				</if>
		</where>
		order by create_time desc
	</select>


	<!-- 分类统计数量 -->
	<select id="countPersonnelType" resultType="java.util.HashMap">
		select job as type, count(id_number) as num from
		(select DISTINCT name, job, id_number from tcps_project_personnel) t1
		GROUP BY job
	</select>

</mapper>