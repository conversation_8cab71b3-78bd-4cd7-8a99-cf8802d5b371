<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.tcps.dao.TcpsRecordTestDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yinshu.tcps.entity.TcpsRecordTest">
        <id column="id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="record_time" property="recordTime" />
        <result column="inspection_type" property="inspectionType" />
        <result column="inspector" property="inspector" />
        <result column="site_leader" property="siteLeader" />
        <result column="inspection_content" property="inspectionContent" />
        <result column="created_at" property="createdAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_at" property="updatedAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="file_info" property="fileInfo" />
        <result column="video_info" property="videoInfo" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_id, record_time, inspection_type, inspector, site_leader, inspection_content, created_at, created_by, updated_at, updated_by, file_info, video_info
    </sql>

	<select id="queryList" resultMap="BaseResultMap" parameterType="com.yinshu.tcps.entity.TcpsRecordTest">
		select <include refid="Base_Column_List"></include>
		from tcps_record_test
		<where>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.projectId != null and entity.projectId != '' ">
					and project_id = #{entity.projectId}
				</if>
				<if test="entity.recordTime != null">
					and record_time = #{entity.recordTime}
				</if>
				<if test="entity.inspectionType != null and entity.inspectionType != '' ">
					and inspection_type = #{entity.inspectionType}
				</if>
				<if test="entity.inspector != null and entity.inspector != '' ">
					and inspector = #{entity.inspector}
				</if>
				<if test="entity.siteLeader != null and entity.siteLeader != '' ">
					and site_leader = #{entity.siteLeader}
				</if>
				<if test="entity.inspectionContent != null and entity.inspectionContent != '' ">
					and inspection_content like concat(concat('%', #{entity.inspectionContent}), '%')
				</if>
				<if test="entity.createdAt != null">
					and created_at = #{entity.createdAt}
				</if>
				<if test="entity.createdBy != null and entity.createdBy != '' ">
					and created_by = #{entity.createdBy}
				</if>
				<if test="entity.updatedAt != null">
					and updated_at = #{entity.updatedAt}
				</if>
				<if test="entity.updatedBy != null and entity.updatedBy != '' ">
					and updated_by = #{entity.updatedBy}
				</if>
		</where>
		order by created_at desc
	</select>

	<!-- 分页查询  -->
	<select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.tcps.entity.TcpsRecordTest">
		select <include refid="Base_Column_List"></include>
		from tcps_record_test
		<where>
			<if test="entity.queryKeyword != null and entity.queryKeyword != '' ">
				(
				<trim prefix="" prefixOverrides="or">
						or inspection_type like concat(concat('%', #{entity.queryKeyword}), '%')
						or inspector like concat(concat('%', #{entity.queryKeyword}), '%')
						or site_leader like concat(concat('%', #{entity.queryKeyword}), '%')
						or inspection_content like concat(concat('%', #{entity.queryKeyword}), '%')
						or created_by like concat(concat('%', #{entity.queryKeyword}), '%')
						or updated_by like concat(concat('%', #{entity.queryKeyword}), '%')
				</trim>
				)
			</if>
			<if test="entity.projectId != null and entity.projectId != '' ">
				and project_id = #{entity.projectId}
			</if>
			<if test="entity.inspectionType != null and entity.inspectionType != '' ">
				and inspection_type = #{entity.inspectionType}
			</if>
			<if test="entity.inspector != null and entity.inspector != '' ">
				and inspector = #{entity.inspector}
			</if>
			<if test="entity.recordTime != null">
				and record_time = #{entity.recordTime}
			</if>
		</where>
		order by created_at desc
	</select>

</mapper>
