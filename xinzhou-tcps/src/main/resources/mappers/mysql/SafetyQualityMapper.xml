<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.tcps.dao.SafetyQualityDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yinshu.tcps.entity.SafetyQuality">
    	<id column="id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="project_name" property="projectName" />
        <result column="bid_section" property="bidSection" />
        <result column="check_type" property="checkType" />
        <result column="supervisor_unit" property="supervisorUnit" />
        <result column="supervisor_name" property="supervisorName" />
        <result column="supervisor_phone" property="supervisorPhone" />
        <result column="construct_unit" property="constructUnit" />
        <result column="construct_name" property="constructName" />
        <result column="construct_phone" property="constructPhone" />
        <result column="safety_name" property="safetyName" />
        <result column="safety_phone" property="safetyPhone" />
        <result column="quality" property="quality" />
        <result column="check_object" property="checkObject" />
        <result column="hazard_level" property="hazardLevel" />
        <result column="hazard_type" property="hazardType" />
        <result column="pictures" property="pictures" />
        <result column="videos" property="videos" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_name, check_type, supervisor, safety, quality, check_object, remark, create_time, create_user, update_time
    </sql>


	<select id="queryList" resultMap="BaseResultMap" parameterType="com.yinshu.tcps.entity.SafetyQuality">
		select a.*, b.project_name from tcps_safety_quality a left join tcps_project b on a.project_id = b.id
		<where>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.projectName != null and entity.projectName != '' ">
					and project_name = #{entity.projectName}
				</if>
				<if test="entity.bidSection != null and entity.bidSection != '' ">
					and bid_section = #{entity.bidSection}
				</if>
				<if test="entity.checkType != null and entity.checkType != '' ">
					and check_type = #{entity.checkType}
				</if>
				<if test="entity.supervisorUnit != null and entity.supervisorUnit != '' ">
					and supervisor_unit = #{entity.supervisorUnit}
				</if>
				<if test="entity.supervisorName != null and entity.supervisorName != '' ">
					and supervisor_name = #{entity.supervisorName}
				</if>
				<if test="entity.supervisorPhone != null and entity.supervisorPhone != '' ">
					and supervisor_phone = #{entity.supervisorPhone}
				</if>
				<if test="entity.constructUnit != null and entity.constructUnit != '' ">
					and construct_unit = #{entity.constructUnit}
				</if>
				<if test="entity.constructName != null and entity.constructName != '' ">
					and construct_name = #{entity.constructName}
				</if>
				<if test="entity.constructPhone != null and entity.constructPhone != '' ">
					and construct_phone = #{entity.constructPhone}
				</if>
				<if test="entity.safetyName != null and entity.safetyName != '' ">
					and safety_name = #{entity.safetyName}
				</if>
				<if test="entity.createTime != null ">
					and DATE_FORMAT(create_time, '%Y-%m-%d') = DATE_FORMAT(#{entity.createTime}, '%Y-%m-%d')
				</if>
		</where>
		order by create_time desc
	</select>

	<!-- 分页查询  -->
	<select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.tcps.entity.SafetyQuality">
		select a.*, b.project_name from tcps_safety_quality a left join tcps_project b on a.project_id = b.id
		<where>
			<if test="entity.id != null and entity.id != '' ">
				and id = #{entity.id}
			</if>
			<if test="entity.projectId != null and entity.projectId != '' ">
				and project_id = #{entity.projectId}
			</if>
			<if test="entity.projectName != null and entity.projectName != '' ">
				and project_name = #{entity.projectName}
			</if>
			<if test="entity.bidSection != null and entity.bidSection != '' ">
				and bid_section = #{entity.bidSection}
			</if>
			<if test="entity.checkType != null and entity.checkType != '' ">
				and check_type = #{entity.checkType}
			</if>
			<if test="entity.supervisorUnit != null and entity.supervisorUnit != '' ">
				and supervisor_unit = #{entity.supervisorUnit}
			</if>
			<if test="entity.supervisorName != null and entity.supervisorName != '' ">
				and supervisor_name = #{entity.supervisorName}
			</if>
			<if test="entity.supervisorPhone != null and entity.supervisorPhone != '' ">
				and supervisor_phone = #{entity.supervisorPhone}
			</if>
			<if test="entity.constructUnit != null and entity.constructUnit != '' ">
				and construct_unit = #{entity.constructUnit}
			</if>
			<if test="entity.constructName != null and entity.constructName != '' ">
				and construct_name = #{entity.constructName}
			</if>
			<if test="entity.constructPhone != null and entity.constructPhone != '' ">
				and construct_phone = #{entity.constructPhone}
			</if>
			<if test="entity.safetyName != null and entity.safetyName != '' ">
				and safety_name = #{entity.safetyName}
			</if>
			<if test="entity.checkObject != null and entity.checkObject != '' ">
				and check_object = #{entity.checkObject}
			</if>
			<if test="entity.createTime != null ">
                and DATE_FORMAT(create_time, '%Y-%m-%d') = DATE_FORMAT(#{entity.createTime}, '%Y-%m-%d')
            </if>
		</where>
		order by create_time desc
	</select>

	<!-- 行业监管看板地图标点 -->
	<select id="queryMapList" resultType="java.util.HashMap">
		select t3.dic_name as type, t1.hazard_type, t2.lng,t2.lat from
		tcps_safety_quality t1 LEFT JOIN tcps_project t2
		on t1.project_id = t2.id
		LEFT JOIN (select * from s_dictionary where parent_code = 'TCPS_Hidden_Danger' ) t3
		on t1.hazard_type = t3.dic_code
	</select>

</mapper>