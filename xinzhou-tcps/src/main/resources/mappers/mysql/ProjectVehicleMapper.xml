<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.tcps.dao.ProjectVehicleDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yinshu.tcps.entity.ProjectVehicle">
        <id column="id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="plate_number" property="plateNumber" />
        <result column="vehicle_color" property="vehicleColor" />
        <result column="brand" property="brand" />
        <result column="model" property="model" />
        <result column="vin" property="vin" />
        <result column="sim_number" property="simNumber" />
        <result column="status" property="status" />
        <result column="created_at" property="createdAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_at" property="updatedAt" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_id, plate_number, vehicle_color, brand, model, vin, sim_number, status, created_at, created_by, updated_at, updated_by
    </sql>


	<select id="queryList" resultMap="BaseResultMap" parameterType="com.yinshu.tcps.entity.ProjectVehicle">
		select <include refid="Base_Column_List"></include>
		from tcps_project_vehicle
		<where>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.projectId != null and entity.projectId != '' ">
					and project_id = #{entity.projectId}
				</if>
				<if test="entity.plateNumber != null and entity.plateNumber != '' ">
					and plate_number = #{entity.plateNumber}
				</if>
				<if test="entity.vehicleColor != null and entity.vehicleColor != '' ">
					and vehicle_color = #{entity.vehicleColor}
				</if>
				<if test="entity.brand != null and entity.brand != '' ">
					and brand = #{entity.brand}
				</if>
				<if test="entity.model != null and entity.model != '' ">
					and model = #{entity.model}
				</if>
				<if test="entity.vin != null and entity.vin != '' ">
					and vin = #{entity.vin}
				</if>
				<if test="entity.simNumber != null and entity.simNumber != '' ">
					and sim_number = #{entity.simNumber}
				</if>
				<if test="entity.status != null and entity.status != '' ">
					and status = #{entity.status}
				</if>
				<if test="entity.createdAt != null and entity.createdAt != '' ">
					and created_at = #{entity.createdAt}
				</if>
				<if test="entity.createdBy != null and entity.createdBy != '' ">
					and created_by = #{entity.createdBy}
				</if>
				<if test="entity.updatedAt != null and entity.updatedAt != '' ">
					and updated_at = #{entity.updatedAt}
				</if>
				<if test="entity.updatedBy != null and entity.updatedBy != '' ">
					and updated_by = #{entity.updatedBy}
				</if>
		</where>
	</select>

	<!-- 分页查询  -->
	<select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.tcps.entity.ProjectVehicle">
		select <include refid="Base_Column_List"></include>
		from tcps_project_vehicle
		<where>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.projectId != null and entity.projectId != '' ">
					and project_id = #{entity.projectId}
				</if>
				<if test="entity.plateNumber != null and entity.plateNumber != '' ">
					and plate_number like concat(concat('%', #{entity.plateNumber}), '%')
				</if>
				<if test="entity.vehicleColor != null and entity.vehicleColor != '' ">
					and vehicle_color like concat(concat('%', #{entity.vehicleColor}), '%')
				</if>
				<if test="entity.brand != null and entity.brand != '' ">
					and brand like concat(concat('%', #{entity.brand}), '%')
				</if>
				<if test="entity.model != null and entity.model != '' ">
					and model like concat(concat('%', #{entity.model}), '%')
				</if>
				<if test="entity.vin != null and entity.vin != '' ">
					and vin like concat(concat('%', #{entity.vin}), '%')
				</if>
				<if test="entity.simNumber != null and entity.simNumber != '' ">
					and sim_number like concat(concat('%', #{entity.simNumber}), '%')
				</if>
				<if test="entity.status != null and entity.status != '' ">
					and status = #{entity.status}
				</if>
		</where>
		<where>
			<if test="entity.queryKeyword != null and entity.queryKeyword != '' ">
				(
				<trim prefix="" prefixOverrides="or">
						or plate_number like concat(concat('%', #{entity.queryKeyword}), '%')
						or vehicle_color like concat(concat('%', #{entity.queryKeyword}), '%')
						or brand like concat(concat('%', #{entity.queryKeyword}), '%')
						or model like concat(concat('%', #{entity.queryKeyword}), '%')
						or vin like concat(concat('%', #{entity.queryKeyword}), '%')
						or sim_number like concat(concat('%', #{entity.queryKeyword}), '%')
						or status like concat(concat('%', #{entity.queryKeyword}), '%')
						or created_by like concat(concat('%', #{entity.queryKeyword}), '%')
						or updated_by like concat(concat('%', #{entity.queryKeyword}), '%')
				</trim>
				)
			</if>
		</where>
	</select>



</mapper>