<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.tcps.dao.ProjectInvestProgressDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yinshu.tcps.entity.ProjectInvestProgress">
        <id column="id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="total_amount" property="totalAmount" />
        <result column="arrived_funds" property="arrivedFunds" />
        <result column="record_date" property="recordDate" />
        <result column="progress_desc" property="progressDesc" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_id, total_amount, arrived_funds, record_date, progress_desc, create_time, create_user, update_time, update_user
    </sql>


	<select id="queryList" resultMap="BaseResultMap" parameterType="com.yinshu.tcps.entity.ProjectInvestProgress">
		select <include refid="Base_Column_List"></include>
		from tcps_project_invest_progress
		<where>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.projectId != null and entity.projectId != '' ">
					and project_id = #{entity.projectId}
				</if>
				<if test="entity.totalAmount != null and entity.totalAmount != '' ">
					and total_amount = #{entity.totalAmount}
				</if>
				<if test="entity.arrivedFunds != null and entity.arrivedFunds != '' ">
					and arrived_funds = #{entity.arrivedFunds}
				</if>
				<if test="entity.recordDate != null and entity.recordDate != '' ">
					and record_date = #{entity.recordDate}
				</if>
				<if test="entity.progressDesc != null and entity.progressDesc != '' ">
					and progress_desc = #{entity.progressDesc}
				</if>
				<if test="entity.createTime != null and entity.createTime != '' ">
					and create_time = #{entity.createTime}
				</if>
				<if test="entity.createUser != null and entity.createUser != '' ">
					and create_user = #{entity.createUser}
				</if>
				<if test="entity.updateTime != null and entity.updateTime != '' ">
					and update_time = #{entity.updateTime}
				</if>
				<if test="entity.updateUser != null and entity.updateUser != '' ">
					and update_user = #{entity.updateUser}
				</if>
		</where>
	</select>

	<!-- 分页查询  -->
	<select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.tcps.entity.ProjectInvestProgress">
		select <include refid="Base_Column_List"></include>
		from tcps_project_invest_progress
		<where>
			<if test="entity.keyword != null and entity.keyword != '' ">
				(
				<trim prefix="" prefixOverrides="or">
						or progress_desc like concat(concat('%', #{entity.keyword}), '%')
						or create_user like concat(concat('%', #{entity.keyword}), '%')
						or update_user like concat(concat('%', #{entity.keyword}), '%')
				</trim>
				)
			</if>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.projectId != null and entity.projectId != '' ">
					and project_id = #{entity.projectId}
				</if>
				<if test="entity.totalAmount != null and entity.totalAmount != '' ">
					and total_amount = #{entity.totalAmount}
				</if>
				<if test="entity.arrivedFunds != null and entity.arrivedFunds != '' ">
					and arrived_funds = #{entity.arrivedFunds}
				</if>
				<if test="entity.recordDate != null and entity.recordDate != '' ">
					and record_date = #{entity.recordDate}
				</if>
				<if test="entity.progressDesc != null and entity.progressDesc != '' ">
					and progress_desc = #{entity.progressDesc}
				</if>
				<if test="entity.createTime != null and entity.createTime != '' ">
					and create_time = #{entity.createTime}
				</if>
				<if test="entity.createUser != null and entity.createUser != '' ">
					and create_user = #{entity.createUser}
				</if>
				<if test="entity.updateTime != null and entity.updateTime != '' ">
					and update_time = #{entity.updateTime}
				</if>
				<if test="entity.updateUser != null and entity.updateUser != '' ">
					and update_user = #{entity.updateUser}
				</if>
		</where>
		order by create_time desc
	</select>



</mapper>