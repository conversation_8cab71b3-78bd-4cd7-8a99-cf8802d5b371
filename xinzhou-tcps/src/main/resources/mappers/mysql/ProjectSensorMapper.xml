<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.tcps.dao.ProjectSensorDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yinshu.tcps.entity.ProjectSensor">
        <id column="id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="sensor_name" property="sensorName" />
        <result column="sensor_code" property="sensorCode" />
        <result column="monitor_location" property="monitorLocation" />
		<result column="lng" property="lng" />
        <result column="lat" property="lat" />
        <result column="model" property="model" />
        <result column="install_date" property="installDate" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_id, sensor_name, sensor_code, monitor_location, lng, lat, model, install_date, status, create_time, create_user, update_time, update_user
    </sql>


	<select id="queryList" resultMap="BaseResultMap" parameterType="com.yinshu.tcps.entity.ProjectSensor">
		select <include refid="Base_Column_List"></include>
		from tcps_project_sensor
		<where>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.projectId != null and entity.projectId != '' ">
					and project_id = #{entity.projectId}
				</if>
				<if test="entity.sensorName != null and entity.sensorName != '' ">
					and sensor_name = #{entity.sensorName}
				</if>
				<if test="entity.sensorCode != null and entity.sensorCode != '' ">
					and sensor_code = #{entity.sensorCode}
				</if>
				<if test="entity.monitorLocation != null and entity.monitorLocation != '' ">
					and monitor_location = #{entity.monitorLocation}
				</if>
				<if test="entity.model != null and entity.model != '' ">
					and model = #{entity.model}
				</if>
				<if test="entity.installDate != null and entity.installDate != '' ">
					and install_date = #{entity.installDate}
				</if>
				<if test="entity.status != null and entity.status != '' ">
					and status = #{entity.status}
				</if>
				<if test="entity.createTime != null and entity.createTime != '' ">
					and create_time = #{entity.createTime}
				</if>
				<if test="entity.createUser != null and entity.createUser != '' ">
					and create_user = #{entity.createUser}
				</if>
				<if test="entity.updateTime != null and entity.updateTime != '' ">
					and update_time = #{entity.updateTime}
				</if>
				<if test="entity.updateUser != null and entity.updateUser != '' ">
					and update_user = #{entity.updateUser}
				</if>
		</where>
	</select>

	<!-- 分页查询  -->
	<select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.tcps.entity.ProjectSensor">
		select <include refid="Base_Column_List"></include>
		from tcps_project_sensor
		<where>
			<if test="entity.keyword != null and entity.keyword != '' ">
				(
				<trim prefix="" prefixOverrides="or">
						or sensor_name like concat(concat('%', #{entity.keyword}), '%')
						or sensor_code like concat(concat('%', #{entity.keyword}), '%')
						or monitor_location like concat(concat('%', #{entity.keyword}), '%')
						or model like concat(concat('%', #{entity.keyword}), '%')
						or status like concat(concat('%', #{entity.keyword}), '%')
						or create_user like concat(concat('%', #{entity.keyword}), '%')
						or update_user like concat(concat('%', #{entity.keyword}), '%')
				</trim>
				)
			</if>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.projectId != null and entity.projectId != '' ">
					and project_id = #{entity.projectId}
				</if>
				<if test="entity.sensorName != null and entity.sensorName != '' ">
					and sensor_name like concat(concat('%', #{entity.sensorName}), '%')
				</if>
				<if test="entity.sensorCode != null and entity.sensorCode != '' ">
					and sensor_code like concat(concat('%', #{entity.sensorCode}), '%')
				</if>
				<if test="entity.monitorLocation != null and entity.monitorLocation != '' ">
					and monitor_location like concat(concat('%', #{entity.monitorLocation}), '%')
				</if>
				<if test="entity.model != null and entity.model != '' ">
					and model like concat(concat('%', #{entity.model}), '%')
				</if>
				<if test="entity.installDate != null and entity.installDate != '' ">
					and install_date = #{entity.installDate}
				</if>
				<if test="entity.status != null and entity.status != '' ">
					and status = #{entity.status}
				</if>
				<if test="entity.createTime != null and entity.createTime != '' ">
					and create_time = #{entity.createTime}
				</if>
				<if test="entity.createUser != null and entity.createUser != '' ">
					and create_user = #{entity.createUser}
				</if>
				<if test="entity.updateTime != null and entity.updateTime != '' ">
					and update_time = #{entity.updateTime}
				</if>
				<if test="entity.updateUser != null and entity.updateUser != '' ">
					and update_user = #{entity.updateUser}
				</if>
		</where>
		order by create_time desc
	</select>



</mapper>