<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.tcps.dao.ProjectDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yinshu.tcps.entity.Project">
        <id column="id" property="id" />
        <result column="region" property="region" />
        <result column="contract_date" property="contractDate" />
        <result column="project_name" property="projectName" />
        <result column="project_status" property="projectStatus" />
        <result column="project_type" property="projectType" />
        <result column="approval_doc" property="approvalDoc" />
        <result column="approval_standard_doc_no" property="approvalStandardDocNo" />
        <result column="project_level" property="projectLevel" />
        <result column="project_belong" property="projectBelong" />
        <result column="project_scale_km" property="projectScaleKm" />
        <result column="legal_unit" property="legalUnit" />
        <result column="project_location" property="projectLocation" />
		<result column="lng" property="lng" />
        <result column="lat" property="lat" />
        <result column="total_investment" property="totalInvestment" />
        <result column="local_funds" property="localFunds" />
        <result column="audit_amount" property="auditAmount" />
        <result column="settlement_amount" property="settlementAmount" />
        <result column="remark" property="remark" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, region, contract_date, project_name, project_status, project_type, approval_doc, approval_standard_doc_no, project_level, project_belong, project_scale_km, legal_unit, project_location, lng, lat, total_investment, local_funds, audit_amount, settlement_amount, remark, created_at, updated_at, created_by, updated_by
    </sql>


	<select id="queryList" resultMap="BaseResultMap" parameterType="com.yinshu.tcps.entity.Project">
		select <include refid="Base_Column_List"></include>
		from tcps_project
		<where>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.region != null and entity.region != '' ">
					and region = #{entity.region}
				</if>
				<if test="entity.contractDate != null ">
					and contract_date = #{entity.contractDate}
				</if>
				<if test="entity.projectName != null and entity.projectName != '' ">
					and project_name like concat(concat('%', #{entity.projectName}), '%')
				</if>
				<if test="entity.projectStatus != null and entity.projectStatus != '' ">
					and project_status = #{entity.projectStatus}
				</if>
				<if test="entity.projectType != null and entity.projectType != '' ">
					and project_type = #{entity.projectType}
				</if>
				<if test="entity.approvalDoc != null and entity.approvalDoc != '' ">
					and approval_doc like concat(concat('%', #{entity.approvalDoc}), '%')
				</if>
				<if test="entity.approvalStandardDocNo != null and entity.approvalStandardDocNo != '' ">
					and approval_standard_doc_no = like concat(concat('%', #{entity.approvalStandardDocNo}), '%')
				</if>
				<if test="entity.projectLevel != null and entity.projectLevel != '' ">
					and project_level = #{entity.projectLevel}
				</if>
				<if test="entity.projectBelong != null and entity.projectBelong != '' ">
					and project_belong = #{entity.projectBelong}
				</if>
				<if test="entity.legalUnit != null and entity.legalUnit != '' ">
					and legal_unit = like concat(concat('%', #{entity.legalUnit}), '%')
				</if>
				<if test="entity.projectLocation != null and entity.projectLocation != '' ">
					and project_location = like concat(concat('%', #{entity.projectLocation}), '%')
				</if>
		</where>
	</select>

	<!-- 分页查询  -->
	<select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.tcps.entity.Project">
		select <include refid="Base_Column_List"></include>
		from tcps_project
		<where>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.region != null and entity.region != '' ">
					and region = #{entity.region}
				</if>
				<if test="entity.contractDate != null">
					and contract_date = #{entity.contractDate}
				</if>
				<if test="entity.projectName != null and entity.projectName != '' ">
					and project_name like concat(concat('%', #{entity.projectName}), '%')
				</if>
				<if test="entity.projectStatus != null and entity.projectStatus != '' ">
					and project_status = #{entity.projectStatus}
				</if>
				<if test="entity.projectType != null and entity.projectType != '' ">
					and project_type = #{entity.projectType}
				</if>
				<if test="entity.approvalDoc != null and entity.approvalDoc != '' ">
					and approval_doc like concat(concat('%', #{entity.approvalDoc}), '%')
				</if>
				<if test="entity.approvalStandardDocNo != null and entity.approvalStandardDocNo != '' ">
					and approval_standard_doc_no = like concat(concat('%', #{entity.approvalStandardDocNo}), '%')
				</if>
				<if test="entity.projectLevel != null and entity.projectLevel != '' ">
					and project_level = #{entity.projectLevel}
				</if>
				<if test="entity.projectBelong != null and entity.projectBelong != '' ">
					and project_belong = #{entity.projectBelong}
				</if>
				<if test="entity.legalUnit != null and entity.legalUnit != '' ">
					and legal_unit = like concat(concat('%', #{entity.legalUnit}), '%')
				</if>
				<if test="entity.projectLocation != null and entity.projectLocation != '' ">
					and project_location = like concat(concat('%', #{entity.projectLocation}), '%')
				</if>
		</where>
	</select>
	
	<!-- 获取所有附件内容 -->
	<select id="getArchiveList" resultType="java.util.HashMap" parameterType="com.yinshu.tcps.entity.Project">
		select c.id, a.region, a.project_name,c.file_name, c.file_path, '质量检测' category from tcps_project a, tcps_safety_quality b, s_file_upload c 
		where a.id = b.project_id and b.id = c.f_id
		union all
		select c.id, a.region, a.project_name,c.file_name, c.file_path, '试验检测' category from tcps_project a, tcps_record_test b, s_file_upload c 
		where a.id = b.project_id and b.id = c.f_id
		union all
		select c.id, a.region, a.project_name,c.file_name, c.file_path, '项目监理' category from tcps_project a, tcps_record_supervision b, s_file_upload c 
		where a.id = b.project_id and b.id = c.f_id
		union all
		select b.id, a.region, a.project_name,b.file_name, b.file_path, '综合台账' category from tcps_project a, tcps_project_ledger b
		where a.id = b.project_id 
		union all
		select b.id, a.region, a.project_name,b.file_name, b.file_path, '综合台账' category from tcps_project a, tcps_project_document b
		where a.id = b.project_id 
		union all
		select b.id, a.region, a.project_name,b.file_name, b.file_path, '综合台账' category from tcps_project a, tcps_project_permit b
		where a.id = b.project_id 
	</select>

	<!-- 分类统计数量 -->
	<select id="countProjectType" resultType="java.util.HashMap">
		select t2.dic_name as type,
		(CASE WHEN t1.num IS NULL THEN 0 ELSE t1.num END) as num
		from
		(select project_type, count(id) num from tcps_project GROUP BY project_type) t1
		RIGHT JOIN
		(select * from s_dictionary where parent_code = 'TCPS_PRO_TYPE') t2
		on t1.project_type = t2.dic_code
	</select>



</mapper>