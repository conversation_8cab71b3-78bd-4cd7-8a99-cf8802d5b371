<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinshu.tcps.dao.TcpsFileDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yinshu.tcps.entity.TcpsFile">
        <id column="id" property="id" />
        <result column="category" property="category" />
        <result column="category_record_id" property="categoryRecordId" />
        <result column="file_name" property="fileName" />
        <result column="file_path" property="filePath" />
        <result column="file_type" property="fileType" />
        <result column="ext_attr" property="extAttr" />
        <result column="is_deleted" property="isDeleted" />
        <result column="created_at" property="createdAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_at" property="updatedAt" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, category, category_record_id, file_name, file_path, file_type, ext_attr, is_deleted, created_at, created_by, updated_at, updated_by
    </sql>


	<select id="queryList" resultMap="BaseResultMap" parameterType="com.yinshu.tcps.entity.TcpsFile">
		select <include refid="Base_Column_List"></include>
		from tcps_file
		<where>
				<if test="entity.id != null and entity.id != '' ">
					and id = #{entity.id}
				</if>
				<if test="entity.category != null and entity.category != '' ">
					and category = #{entity.category}
				</if>
				<if test="entity.categoryRecordId != null and entity.categoryRecordId != '' ">
					and category_record_id = #{entity.categoryRecordId}
				</if>
				<if test="entity.fileName != null and entity.fileName != '' ">
					and file_name = #{entity.fileName}
				</if>
				<if test="entity.filePath != null and entity.filePath != '' ">
					and file_path = #{entity.filePath}
				</if>
				<if test="entity.fileType != null and entity.fileType != '' ">
					and file_type = #{entity.fileType}
				</if>
				<if test="entity.fId != null and entity.fId != '' ">
					and f_id = #{entity.fId}
				</if>
		</where>
	</select>

	<!-- 分页查询  -->
	<select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.tcps.entity.TcpsFile">
		select <include refid="Base_Column_List"></include>
		from tcps_file
		<where>
			<if test="entity.queryKeyword != null and entity.queryKeyword != '' ">
				(
				<trim prefix="" prefixOverrides="or">
						or category like concat(concat('%', #{entity.queryKeyword}), '%')
						or file_name like concat(concat('%', #{entity.queryKeyword}), '%')
				</trim>
				)
			</if>
		</where>
	</select>
	
	<select id="queryPageList" resultMap="BaseResultMap" parameterType="com.yinshu.tcps.entity.TcpsFile">
		select <include refid="Base_Column_List"></include>
		from tcps_file
		<where>
			<if test="entity.queryKeyword != null and entity.queryKeyword != '' ">
				(
				<trim prefix="" prefixOverrides="or">
						or category like concat(concat('%', #{entity.queryKeyword}), '%')
						or file_name like concat(concat('%', #{entity.queryKeyword}), '%')
				</trim>
				)
			</if>
		</where>
	</select>

	<!-- 根据业务ID删除对应的附件 -->
	<delete id="removeByFids" parameterType="java.lang.String">
        delete from tcps_file where f_id in 
        <foreach collection="array" open="(" close=")" separator="," item="id">
		 	#{id}
		 </foreach>
    </delete>
    
    <!-- 根据业务ID删除对应的附件 -->
	<delete id="removeByFids" parameterType="java.lang.String">
        delete from tcps_file where f_id in 
        <foreach collection="array" open="(" close=")" separator="," item="id">
		 	#{id}
		 </foreach>
    </delete>

</mapper>