package com.yinshu.tcps.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.ProjectCamera;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 
 * 项目视频监控表
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
public interface ProjectCameraDao extends BaseMapper<ProjectCamera> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<ProjectCamera> queryList(@Param("entity") ProjectCamera entity);

	/**
	* 条件分页查询
	* @param page
	* @param entity
	* @return
	*/
	IPage<ProjectCamera> queryPageList(IPage<ProjectCamera> page, @Param("entity") ProjectCamera entity);

}
