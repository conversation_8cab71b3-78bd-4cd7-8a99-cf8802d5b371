package com.yinshu.tcps.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.TcpsRecordTest;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 试验检测管理表
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface TcpsRecordTestDao extends BaseMapper<TcpsRecordTest> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<TcpsRecordTest> queryList(@Param("entity") TcpsRecordTest entity);

	/**
	* 条件分页查询
	* @param page
	* @param entity
	* @return
	*/
	IPage<TcpsRecordTest> queryPageList(IPage<TcpsRecordTest> page, @Param("entity") TcpsRecordTest entity);

}
