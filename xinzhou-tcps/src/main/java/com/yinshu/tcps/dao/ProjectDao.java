package com.yinshu.tcps.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.Project;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 
 * 工程项目主表
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface ProjectDao extends BaseMapper<Project> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<Project> queryList(@Param("entity") Project entity);

	/**
	* 条件分页查询
	* @param page
	* @param entity
	* @return
	*/
	IPage<Project> queryPageList(IPage<Project> page, @Param("entity") Project entity);
	
	/**
	 * 获取所有附件内容
	 * @param entity
	 * @return
	 */
	List<Map<String, String>> getArchiveList();

	/**
	 * 分类统计数量
	 * @return
	 */
	List<Map<String, Object>> countProjectType();
	

}
