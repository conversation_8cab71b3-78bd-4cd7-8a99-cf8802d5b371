package com.yinshu.tcps.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.ProjectLedger;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 
 * 项目台账资料表
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
public interface ProjectLedgerDao extends BaseMapper<ProjectLedger> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<ProjectLedger> queryList(@Param("entity") ProjectLedger entity);

	/**
	* 条件分页查询
	* @param page
	* @param entity
	* @return
	*/
	IPage<ProjectLedger> queryPageList(IPage<ProjectLedger> page, @Param("entity") ProjectLedger entity);

}
