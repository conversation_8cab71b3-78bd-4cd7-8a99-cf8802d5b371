package com.yinshu.tcps.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.ProjectPermit;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 
 * 项目许可及证书
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
public interface ProjectPermitDao extends BaseMapper<ProjectPermit> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<ProjectPermit> queryList(@Param("entity") ProjectPermit entity);

	/**
	* 条件分页查询
	* @param page
	* @param entity
	* @return
	*/
	IPage<ProjectPermit> queryPageList(IPage<ProjectPermit> page, @Param("entity") ProjectPermit entity);

}
