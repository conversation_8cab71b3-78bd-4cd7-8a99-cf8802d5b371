package com.yinshu.tcps.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.RecordSupervision;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 
 * 项目监理管理表
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface RecordSupervisionDao extends BaseMapper<RecordSupervision> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<RecordSupervision> queryList(@Param("entity") RecordSupervision entity);

	/**
	* 条件分页查询
	* @param page
	* @param entity
	* @return
	*/
	IPage<RecordSupervision> queryPageList(IPage<RecordSupervision> page, @Param("entity") RecordSupervision entity);

}
