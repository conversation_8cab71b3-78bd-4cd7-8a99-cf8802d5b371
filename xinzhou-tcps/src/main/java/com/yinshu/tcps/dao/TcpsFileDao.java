package com.yinshu.tcps.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.TcpsFile;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 
 * 管理现场记录文件表
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
public interface TcpsFileDao extends BaseMapper<TcpsFile> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<TcpsFile> queryList(@Param("entity") TcpsFile entity);

	/**
	* 条件分页查询
	* @param page
	* @param entity
	* @return
	*/
	IPage<TcpsFile> queryPageList(IPage<TcpsFile> page, @Param("entity") TcpsFile entity);
	
	/**
	 * 根据业务ID删除对应的附件 
	 * @param roleId
	 */
	void removeByFids(String[] fids);

}
