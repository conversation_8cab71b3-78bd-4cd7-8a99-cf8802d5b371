package com.yinshu.tcps.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.ProjectSensor;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 
 * 项目传感器表
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
public interface ProjectSensorDao extends BaseMapper<ProjectSensor> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<ProjectSensor> queryList(@Param("entity") ProjectSensor entity);

	/**
	* 条件分页查询
	* @param page
	* @param entity
	* @return
	*/
	IPage<ProjectSensor> queryPageList(IPage<ProjectSensor> page, @Param("entity") ProjectSensor entity);

}
