package com.yinshu.tcps.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.ProjectPersonnel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 
 * 项目人员表
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
public interface ProjectPersonnelDao extends BaseMapper<ProjectPersonnel> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<ProjectPersonnel> queryList(@Param("entity") ProjectPersonnel entity);

	/**
	* 条件分页查询
	* @param page
	* @param entity
	* @return
	*/
	IPage<ProjectPersonnel> queryPageList(IPage<ProjectPersonnel> page, @Param("entity") ProjectPersonnel entity);

	/**
	 * 分类统计数量
	 * @return
	 */
	List<Map<String, Object>> countPersonnelType();
}
