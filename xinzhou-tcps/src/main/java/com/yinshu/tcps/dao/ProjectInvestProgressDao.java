package com.yinshu.tcps.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.ProjectInvestProgress;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 
 * 资金投入与建设进度
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface ProjectInvestProgressDao extends BaseMapper<ProjectInvestProgress> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<ProjectInvestProgress> queryList(@Param("entity") ProjectInvestProgress entity);

	/**
	* 条件分页查询
	* @param page
	* @param entity
	* @return
	*/
	IPage<ProjectInvestProgress> queryPageList(IPage<ProjectInvestProgress> page, @Param("entity") ProjectInvestProgress entity);

}
