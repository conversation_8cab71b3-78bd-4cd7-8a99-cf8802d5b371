package com.yinshu.tcps.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.ProjectDocument;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 
 * 项目资料表
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
public interface ProjectDocumentDao extends BaseMapper<ProjectDocument> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<ProjectDocument> queryList(@Param("entity") ProjectDocument entity);

	/**
	* 条件分页查询
	* @param page
	* @param entity
	* @return
	*/
	IPage<ProjectDocument> queryPageList(IPage<ProjectDocument> page, @Param("entity") ProjectDocument entity);

}
