package com.yinshu.tcps.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.ProjectVehicle;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 
 * 项目车辆表
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface ProjectVehicleDao extends BaseMapper<ProjectVehicle> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<ProjectVehicle> queryList(@Param("entity") ProjectVehicle entity);

	/**
	* 条件分页查询
	* @param page
	* @param entity
	* @return
	*/
	IPage<ProjectVehicle> queryPageList(IPage<ProjectVehicle> page, @Param("entity") ProjectVehicle entity);

}
