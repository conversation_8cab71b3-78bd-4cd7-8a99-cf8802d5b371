package com.yinshu.tcps.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.SafetyQuality;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 
 * 安全质量检查
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface SafetyQualityDao extends BaseMapper<SafetyQuality> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<SafetyQuality> queryList(@Param("entity") SafetyQuality entity);

	/**
	* 条件分页查询
	* @param page
	* @param entity
	* @return
	*/
	IPage<SafetyQuality> queryPageList(IPage<SafetyQuality> page, @Param("entity") SafetyQuality entity);

	/**
	 * 行业监管看板地图标点
	 * @return
	 */
	List<Map<String, Object>> queryMapList();

}
