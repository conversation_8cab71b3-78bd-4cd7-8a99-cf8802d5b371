package com.yinshu.tcps.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.ProjectPermit;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 项目许可及证书 
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
public interface ProjectPermitService extends IService<ProjectPermit> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<ProjectPermit> queryList(ProjectPermit entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<ProjectPermit> queryPageList(ProjectPermit entity);

}
