package com.yinshu.tcps.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.SafetyQuality;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 安全质量检查 
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface SafetyQualityService extends IService<SafetyQuality> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<SafetyQuality> queryList(SafetyQuality entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<SafetyQuality> queryPageList(SafetyQuality entity);

	/**
	 * 行业监管看板地图标点
	 * @return
	 */
	List<Map<String, Object>> queryMapList();

}
