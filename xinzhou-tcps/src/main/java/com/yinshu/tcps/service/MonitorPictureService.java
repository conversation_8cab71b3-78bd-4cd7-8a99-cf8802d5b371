package com.yinshu.tcps.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.tcps.entity.Project;
import com.yinshu.tcps.vo.AreaProjectTreeVO;

import java.util.List;

/**
 * 行车监控
 *
 */
public interface MonitorPictureService {


    /**
     * 项目树结构
     */
    List<AreaProjectTreeVO> getProjectTree(Project entity);

    /**
     * 门禁树结构
     */
    List<AreaProjectTreeVO> getAccessControlTree(Project entity);

    /**
     * 门禁记录
     *
     * @return
     */
    JSONObject getAccessControlData(JSONObject query);

}
