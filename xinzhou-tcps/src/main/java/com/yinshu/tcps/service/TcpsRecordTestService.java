package com.yinshu.tcps.service;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.TcpsRecordTest;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 试验检测管理表
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface TcpsRecordTestService extends IService<TcpsRecordTest> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<TcpsRecordTest> queryList(TcpsRecordTest entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<TcpsRecordTest> queryPageList(TcpsRecordTest entity);

}
