package com.yinshu.tcps.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.tcps.entity.Project;
import com.yinshu.tcps.service.IndustryRegulationService;
import com.yinshu.tcps.service.ProjectPersonnelService;
import com.yinshu.tcps.service.ProjectService;
import com.yinshu.tcps.service.SafetyQualityService;
import com.yinshu.tcps.util.DataComparisonUtils;
import com.yinshu.utils.ReplaceUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 行业监管看板
 */
@Service
public class IndustryRegulationServiceImpl implements IndustryRegulationService {

    /***
     * 运输行业统计汇总表//ads_transport_statistics_summary
     */
    public static final String ADS_TRANSPORT_STATISTICS_SUMMARY = "/api/100561/data.json?";
    /***
     * 公路隐患分类占比//ads_xinzhou_road_event_ratio
     */
    public static final String ADS_XINZHOU_ROAD_EVENT_RATIO = "/api/100017/data.json?";
    /***
     * 公路隐患详情按月份//ads_xinzhou_road_event_ratio_month
     */
    public static final String ADS_XINZHOU_ROAD_EVENT_RATIO_MONTH = "/api/100015/data.json?";
    /***
     * 公路隐患详情按年//ads_xinzhou_road_hidden_danger_ratio_year
     */
    public static final String ADS_XINZHOU_ROAD_HIDDEN_DANGER_RATIO_YEAR = "/api/100019/data.json?";
    /***
     * 在建工程//ads_xinzhou_highway_network_map
     */
    public static final String ADS_XINZHOU_HIGHWAY_NETWORK_MAP = "/api/100066/data.json?";
    /***
     * 安全隐患//ads_xinzhou_road_safety_hazard
     */
    public static final String ADS_XINZHOU_ROAD_SAFETY_HAZARD = "/api/100106/data.json?";

    @Resource
    private DvisualHttpTemplate template;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectPersonnelService projectPersonnelService;

    @Autowired
    private SafetyQualityService safetyQualityService;

    /**
     * 获取运输行业统计
     *
     * @param query
     * @return
     */
//    public JSONObject getTransportStatistics(JSONObject query) {
//        String type = query.getString("type");
//        String filterTemplate = "type = '{type}' and types != null and types != ''";
//        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
//                "type", type));
//        JSONObject resultData = template.post(ADS_TRANSPORT_STATISTICS_SUMMARY, query);
//        return DataComparisonUtils.assemblingPieData(resultData, "types", "num");
//    }

    public JSONObject getTransportStatistics(JSONObject query) {
        String type = query.getString("type");
        JSONObject resultData = new JSONObject();
        JSONArray list = new JSONArray();
        resultData.put("list", list);
        if("项目".equals(type)){
            list.addAll(projectService.countProjectType());
        }else if("人员".equals(type)){
            list.addAll(projectPersonnelService.countPersonnelType());
        }else if("企业".equals(type)){

        }
        return DataComparisonUtils.assemblingPieData(resultData, "type", "num");
    }

    /**
     * 获取安全隐患统计
     *
     * @param query
     * @return
     */
    public JSONObject getHazardStatistics(JSONObject query) {
        String dateType = query.getString("dateType");
        String date = query.getString("date");
        String filterTemplate = "";
        JSONObject resultData = new JSONObject();
        if ("M".equals(dateType)) {
            filterTemplate = "month = '{date}' ";
            query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                    "date", date));
            resultData = template.post(ADS_XINZHOU_ROAD_EVENT_RATIO_MONTH, query);
            return DataComparisonUtils.getResultFirst(resultData);
        } else if ("Y".equals(dateType)) {
            filterTemplate = "year = '{date}' ";
            query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                    "date", date));
            resultData = template.post(ADS_XINZHOU_ROAD_HIDDEN_DANGER_RATIO_YEAR, query);
            return DataComparisonUtils.getResultFirst(resultData);
        }
        return resultData;
    }

    /**
     * 获取安全隐患列表
     *
     * @param query
     * @return
     */
    public JSONArray getHazardList(JSONObject query) {
        String dateType = query.getString("dateType");
        String date = query.getString("date");
        String filterTemplate = "";
        if ("M".equals(dateType)) {
            filterTemplate = "day like '{date}' order by day";
            query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                    "date", date));
            JSONObject resultData = template.post(ADS_XINZHOU_ROAD_EVENT_RATIO, query);
            return DataComparisonUtils.getResultList(resultData);
        } else if ("Y".equals(dateType)) {
            filterTemplate = "month like '{date}' order by month";
            query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                    "date", date));
            JSONObject resultData = template.post(ADS_XINZHOU_ROAD_EVENT_RATIO_MONTH, query);
            return DataComparisonUtils.getResultList(resultData);
        }
        return new JSONArray();
    }

    /**
     * 获取地图在建工程
     *
     * @param query
     * @return
     */
//    public JSONArray getProgressConstruction(JSONObject query) {
//        String filterTemplate = "construction_progress = '是'";
//        query.put("filter", filterTemplate);
//        JSONObject resultData = template.post(ADS_XINZHOU_HIGHWAY_NETWORK_MAP, query);
//        return DataComparisonUtils.getResultList(resultData);
//    }

    public JSONArray getProgressConstruction(JSONObject query) {
        JSONArray list = new JSONArray();
        list.addAll(projectService.list());
        return list;
    }

    /**
     * 获取地图安全隐患
     *
     * @param query
     * @return
     */
//    public JSONArray getHiddenDanger(JSONObject query) {
//        JSONObject resultData = template.post(ADS_XINZHOU_ROAD_SAFETY_HAZARD, query);
//        return DataComparisonUtils.getResultList(resultData);
//    }

    public JSONArray getHiddenDanger(JSONObject query) {
        JSONArray list = new JSONArray();
        list.addAll(safetyQualityService.queryMapList());
        return list;
    }



}
