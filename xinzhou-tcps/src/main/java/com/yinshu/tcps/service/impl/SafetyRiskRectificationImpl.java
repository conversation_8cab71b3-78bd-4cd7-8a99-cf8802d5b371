package com.yinshu.tcps.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.fast.excel.util.ExcelUtils;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.tcps.entity.vo.BusinessSafetyExportVO;
import com.yinshu.tcps.service.SafetyRiskRectificationService;
import com.yinshu.tcps.util.DataComparisonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 安全隐患整改
 */
@Service
public class SafetyRiskRectificationImpl implements SafetyRiskRectificationService {

    @Autowired
    private DvisualHttpTemplate httpTemplate;

    /**
     * 道路安全监管监督安全隐患整改/ads_road_safe_hidden_danger_rectification
     */
    public static final String ADS_ROAD_SAFE_HIDDEN_DANGER_RECTIFICATION = "/api/100569/data.json?";

    /**
     * 道路安全监管监督隐患详情 ads_road_safe_hidden_danger_details
     */
    public static final String ADS_ROAD_SAFE_HIDDEN_DANGER_DETAILS = "/api/100570/data.json?";

    /**
     * 道路安全监管监督企业安全评估 ads_road_safe_company_security_assessment
     *
     */
    public static final String ADS_ROAD_SAFE_COMPANY_SECURITY_ASSESSMENT = "/api/100575/data.json?";

    /**
     * 企业综合评分走势日 ads_road_safe_company_safety_scores_day
     */
    public static final String ADS_ROAD_SAFE_COMPANY_SAFETY_SCORES_DAY = "/api/100576/data.json?";

    /**
     * 企业报警构成情况月 ads_road_safety_company_alarm_composition_month
     */
    public static final String ADS_ROAD_SAFETY_COMPANY_ALARM_COMPOSITION_MONTH = "/api/100577/data.json?";

    /**
     * 企业车辆风险排行月 ads_road_safety_car_risk_rank_month
     */
    public static final String ADS_ROAD_SAFETY_CAR_RISK_RANK_MONTH = "/api/100578/data.json?";

    /**
     * 车辆安全评估 ads_road_safe_car_safety_evaluation
     */
    public static final String ADS_ROAD_SAFE_CAR_SAFETY_EVALUATION = "/api/100579/data.json?";

    /**
     * 车辆出车日历 ads_road_safe_car_departure_calendar
      */
    public static final String ADS_ROAD_SAFE_CAR_DEPARTURE_CALENDAR = "/api/100580/data.json?";

    /**
     * 车辆运营信息 ads_road_safe_car_operation_info
     */
    public static final String ADS_ROAD_SAFE_CAR_OPERATION_INFO = "/api/100581/data.json?";

    /**
     * 百公里报警趋势 ads_road_safe_car_per_100km_alarm_sum
     */
    public static final String ADS_ROAD_SAFE_CAR_PER_100KM_ALARM_SUM = "/api/100582/data.json?";

    /**
     * 车辆报警构成情况 ads_road_safe_car_alarm_composition
      */
    public static final String ADS_ROAD_SAFE_CAR_ALARM_COMPOSITION = "/api/100583/data.json?";
    /**
     *   道路安全监督监管报警督办 ads_road_safe_alarm_supervision
     */
    public static final String ADS_ROAD_SAFE_ALARM_SUPERVISION = "/api/100568/data.json?";

    /**
     * 安全隐患整改
     * @param query
     * @return
     */
    @Override
    public JSONObject getRectificationListData(JSONObject query) {
        String county = query.getString("county");
        StringBuilder filter = new StringBuilder();
        if (county != null && !county.isEmpty()) {
            filter.append(" county = '").append(county).append("' ");
        }
        String projectname = query.getString("projectname");
        if (projectname != null && !projectname.isEmpty()) {
            if (filter.length() > 0) {
                filter.append(" and ");
            }
            filter.append(" projectname = '").append(projectname).append("' ");
        }
        query.put("filter", filter.toString());
        return httpTemplate.post(ADS_ROAD_SAFE_HIDDEN_DANGER_RECTIFICATION, query);
    }

    /**
     * 安全隐患整改详情
     * @param query
     * @return
     */
    @Override
    public JSONObject getRectificationDetailData(JSONObject query) {
        String foundTime = query.getString("found_time");
        StringBuilder filter = new StringBuilder();
        if (foundTime != null && !foundTime.isEmpty()) {
            filter.append(" found_time = '").append(foundTime).append("' ");
        }
        String projectId = query.getString("project_id");
        if (projectId!= null &&!projectId.isEmpty()) {
            if (filter.length() > 0) {
                filter.append(" and ");
            }
           filter.append(" project_id = '").append(projectId).append("' ");
        }
        query.put("filter", filter.toString());
        return httpTemplate.post(ADS_ROAD_SAFE_HIDDEN_DANGER_DETAILS, query);
    }

    /**
     * 企业安全评估
     * @param query
     * @return
     */
    @Override
    public JSONObject getBusinessListData(JSONObject query) {
        String county = query.getString("county");
        StringBuilder filter = new StringBuilder();
        if (county != null && !county.isEmpty()) {
            filter.append(" county = '").append(county).append("' ");
        }
        String company = query.getString("company");
        if (company != null && !company.isEmpty()) {
            if (filter.length() > 0) {
                filter.append(" and ");
            }
            filter.append(" company = '").append(company).append("' ");
        }
        query.put("filter", filter.toString());
        return httpTemplate.post(ADS_ROAD_SAFE_COMPANY_SECURITY_ASSESSMENT, query);
    }

    /**
     * 企业综合评分走势日
     * @param query
     * @return
     */
    @Override
    public JSONObject getComprehensiveScoreTrend(JSONObject query) {
        String month = query.getString("month");
        StringBuilder filter = new StringBuilder();
        if (null != month && !month.isEmpty()) {
            filter.append(" month = '").append(month).append("' ");
        }
        String company = query.getString("company");
        if (null != company && !company.isEmpty()) {
            if (filter.length() > 0) {
                filter.append(" and ");
            }
            filter.append(" company = '").append(company).append("' ");
        }
        query.put("filter", filter.toString());
        JSONObject post = httpTemplate.post(ADS_ROAD_SAFE_COMPANY_SAFETY_SCORES_DAY, query);
        return DataComparisonUtils.assemblingBarOrLineData(post, "dt", "company_score");
    }

    /**
     * 报警构成
     * @param query
     * @return
     */
    @Override
    public JSONObject getAlarmComposition(JSONObject query) {
        String month = query.getString("month");
        StringBuilder filter = new StringBuilder();
        if (null != month && !month.isEmpty()) {
            filter.append(" month = '").append(month).append("' ");
        }
        String company = query.getString("company");
        if (null != company && !company.isEmpty()) {
            if (filter.length() > 0) {
                filter.append(" and ");
            }
            filter.append(" company = '").append(company).append("' ");
        }
        query.put("filter", filter.toString());
        return httpTemplate.post(ADS_ROAD_SAFETY_COMPANY_ALARM_COMPOSITION_MONTH, query);
    }

    /**
     * 车辆风险排行
     * @param query
     * @return
     */
    @Override
    public JSONObject getVehicleRiskRanking(JSONObject query) {
        String month = query.getString("month");
        StringBuilder filter = new StringBuilder();
        if (null != month && !month.isEmpty()) {
            filter.append(" month = '").append(month).append("' ");
        }
        String company = query.getString("company");
        if (null != company && !company.isEmpty()) {
            if (filter.length() > 0) {
                filter.append(" and ");
            }
            filter.append(" company = '").append(company).append("' ");
        }
        query.put("filter", filter + " order by rank");
        return httpTemplate.post(ADS_ROAD_SAFETY_CAR_RISK_RANK_MONTH, query);
    }

    @Override
    public JSONObject getVehicleList(JSONObject query) {
        StringBuilder filter = new StringBuilder();
        String county = query.getString("county");
        if (null != county && !county.isEmpty()) {
            filter.append(" county = '").append(county).append("' ");
        }
        String company = query.getString("company");
        if (null!= company &&!company.isEmpty()) {
            if (filter.length() > 0) {
                filter.append(" and ");
            }
            filter.append(" company = '").append(company).append("' ");
        }
        String carid = query.getString("carid");
        if (null!= carid &&!carid.isEmpty()) {
            if (filter.length() > 0) {
                filter.append(" and ");
            }
            filter.append(" carid = '").append(carid).append("' ");
        }
        String driver = query.getString("driver");
        if (null!= driver &&!driver.isEmpty()) {
            if (filter.length() > 0) {
                filter.append(" and ");
            }
            filter.append(" driver = '").append(driver).append("' ");
        }
        query.put("filter", filter.toString());
        return httpTemplate.post(ADS_ROAD_SAFE_CAR_SAFETY_EVALUATION, query);
    }

    /**
     * 车辆出车日历
     * @param query
     * @return
     */
    @Override
    public JSONObject getVehicleCalendar(JSONObject query) {
        String carid = query.getString("carid");
        query.put("filter", "carid = " + carid);
        String month = query.getString("month");
        if (null != month && !month.isEmpty()) {
            query.put("filter", "carid = '" + carid + "' and month = '" + month + "'");
        }
        return httpTemplate.post(ADS_ROAD_SAFE_CAR_DEPARTURE_CALENDAR, query);
    }

    /**
     * 车辆运营信息
     * @param query
     * @return
     */
    @Override
    public JSONObject getCarOperationInfo(JSONObject query) {
        String carid = query.getString("carid");
        query.put("filter", "carid = " + carid);
        String month = query.getString("month");
        if (null != month && !month.isEmpty()) {
            query.put("filter", "carid = '" + carid + "' and month = '" + month + "'");
        }
        return httpTemplate.post(ADS_ROAD_SAFE_CAR_OPERATION_INFO, query);
    }

    /**
     *
     * @param query
     * @return
     */
    @Override
    public JSONObject get100KmAlarmTrend(JSONObject query) {
        String carid = query.getString("carid");
        query.put("filter", "carid = " + carid);
        String month = query.getString("month");
        if (null != month && !month.isEmpty()) {
            query.put("filter", "carid = '" + carid + "' and month = '" + month + "'");
        }
        JSONObject post = httpTemplate.post(ADS_ROAD_SAFE_CAR_PER_100KM_ALARM_SUM, query);
        return DataComparisonUtils.assemblingBarOrLineData(post, "dt", "alarm_num_per_100km");
    }

    /**
     * 车辆报警构成情况
     * @param query
     * @return
     */
    @Override
    public JSONObject getCarAlarmComposition(JSONObject query) {
        String carid = query.getString("carid");
        query.put("filter", "carid = " + carid);
        String month = query.getString("month");
        if (null != month && !month.isEmpty()) {
            query.put("filter", "carid = '" + carid + "' and month = '" + month + "'");
        }
        return httpTemplate.post(ADS_ROAD_SAFE_CAR_ALARM_COMPOSITION, query);
    }

    /**
     * 高峰运行时段
     * @param query
     * @return
     */
    @Override
    public JSONObject getPeakRunningPeriod(JSONObject query){
        String carid = query.getString("carid");
        query.put("filter", "carid = '" + carid + "'");
        String month = query.getString("month");
        if (null != month && !month.isEmpty()) {
            LocalDate firstDay = LocalDate.parse(month + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            LocalDate lastDay = firstDay.withDayOfMonth(firstDay.lengthOfMonth());

            String start = firstDay + " 00:00:00";
            String end = lastDay + " 23:59:59";
            query.put("filter", "carid = '" + carid + "' and alarmtime >= '" + start + "' and alarmtime <= '" + end + "'");
        }
        return httpTemplate.post(ADS_ROAD_SAFE_ALARM_SUPERVISION, query);
    }

    /**
     * 导出
     * @param query
     */
    @Override
    public void getExport(JSONObject query) {
        String exportType = query.getString("exportType");
        if ("1".equals(exportType)) {
            JSONObject businessListData = getBusinessListData(query);
            ExcelUtils.exportExcelSheet(BusinessSafetyExportVO.class, businessListData);
        }
    }

}
