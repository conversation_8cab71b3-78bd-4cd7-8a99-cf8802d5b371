package com.yinshu.tcps.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.RecordSupervision;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 项目监理管理表 
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface RecordSupervisionService extends IService<RecordSupervision> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<RecordSupervision> queryList(RecordSupervision entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<RecordSupervision> queryPageList(RecordSupervision entity);

}
