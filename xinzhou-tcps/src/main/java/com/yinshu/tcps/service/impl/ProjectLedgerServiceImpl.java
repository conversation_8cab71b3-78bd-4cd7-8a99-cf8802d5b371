package com.yinshu.tcps.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinshu.tcps.entity.ProjectLedger;
import com.yinshu.tcps.dao.ProjectLedgerDao;
import com.yinshu.tcps.service.ProjectLedgerService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 项目台账资料表 
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Service
public class ProjectLedgerServiceImpl extends ServiceImpl<ProjectLedgerDao, ProjectLedger> implements ProjectLedgerService {

	
	@Autowired
	private ProjectLedgerDao projectLedgerDao;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<ProjectLedger> queryList(ProjectLedger entity) {
		return projectLedgerDao.queryList(entity);
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<ProjectLedger> queryPageList(ProjectLedger entity) {
		return projectLedgerDao.queryPageList(entity.toPage(), entity);
	}
}
