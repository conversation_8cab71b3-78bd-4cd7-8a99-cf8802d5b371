package com.yinshu.tcps.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.ProjectCamera;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 项目视频监控表 
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
public interface ProjectCameraService extends IService<ProjectCamera> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<ProjectCamera> queryList(ProjectCamera entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<ProjectCamera> queryPageList(ProjectCamera entity);

}
