package com.yinshu.tcps.service;

import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.core.util.Json;

public interface SafetyRiskRectificationService {
    /**
     * 安全隐患整改
     * @param query
     * @return
     */
    public JSONObject getRectificationListData(JSONObject query);

    /**
     * 安全隐患整改详情
     * @param query
     * @return
     */
    public JSONObject getRectificationDetailData(JSONObject query);

    /**
     * 企业安全评估
     * @param query
     * @return
     */
    public JSONObject getBusinessListData(JSONObject query);

    /**
     * 企业综合评分走势日
     * @param query
     * @return
     */
    public JSONObject getComprehensiveScoreTrend(JSONObject query);

    /**
     * 报警构成
     * @param query
     * @return
     */
    public JSONObject getAlarmComposition(JSONObject query);

    /**
     * 报警构成
     * @param query
     * @return
     */
    public JSONObject getVehicleRiskRanking(JSONObject query);

    /**
     * 车辆安全评估
     * @param query
     * @return
     */
    public JSONObject getVehicleList(JSONObject query);

    /**
     * 车辆日历
     * @param query
     * @return
     */
    public JSONObject getVehicleCalendar(JSONObject query);

    /**
     * 车辆运营信息
     * @param query
     * @return
     */
    public JSONObject getCarOperationInfo(JSONObject query);

    /**
     * 百公里报警趋势
     * @param query
     * @return
     */
    public JSONObject get100KmAlarmTrend(JSONObject query);

    /**
     * 车辆报警构成
     * @param query
     * @return
     */
    public JSONObject getCarAlarmComposition(JSONObject query);

    /**
     * 高峰运行时段
     * @param query
     * @return
     */
    public JSONObject getPeakRunningPeriod(JSONObject query);

    /**
     * 导出
     * @param query
     */
    public void getExport(JSONObject query);
}
