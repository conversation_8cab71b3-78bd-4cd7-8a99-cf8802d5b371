package com.yinshu.tcps.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * 行车监控
 *
 */
public interface DrivingMonitorService {


    /**
     * 获取车辆信息
     */
    JSONObject getVehicleList(JSONObject query);

    /**
     * 获取多辆车的实时定位
     *
     * @return
     */
    JSONObject getMultiRealLocation(JSONObject query);

    /**
     * 根据车牌号码获取实时定位
     */
    JSONObject getRealLocation(JSONObject query);

    /**
     * 获取历史轨迹
     *
     * @return
     */
    JSONArray getHistoryLocation(JSONObject query);

}
