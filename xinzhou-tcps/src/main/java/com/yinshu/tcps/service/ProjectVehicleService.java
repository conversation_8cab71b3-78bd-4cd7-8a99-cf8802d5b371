package com.yinshu.tcps.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.ProjectVehicle;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 项目车辆表 
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface ProjectVehicleService extends IService<ProjectVehicle> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<ProjectVehicle> queryList(ProjectVehicle entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<ProjectVehicle> queryPageList(ProjectVehicle entity);

}
