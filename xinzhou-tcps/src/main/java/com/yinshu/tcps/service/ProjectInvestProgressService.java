package com.yinshu.tcps.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.ProjectInvestProgress;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 资金投入与建设进度 
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface ProjectInvestProgressService extends IService<ProjectInvestProgress> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<ProjectInvestProgress> queryList(ProjectInvestProgress entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<ProjectInvestProgress> queryPageList(ProjectInvestProgress entity);

}
