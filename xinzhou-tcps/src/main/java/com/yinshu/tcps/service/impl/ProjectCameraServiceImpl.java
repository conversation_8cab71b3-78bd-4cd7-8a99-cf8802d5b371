package com.yinshu.tcps.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinshu.tcps.entity.ProjectCamera;
import com.yinshu.tcps.dao.ProjectCameraDao;
import com.yinshu.tcps.service.ProjectCameraService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 项目视频监控表 
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Service
public class ProjectCameraServiceImpl extends ServiceImpl<ProjectCameraDao, ProjectCamera> implements ProjectCameraService {

	
	@Autowired
	private ProjectCameraDao projectCameraDao;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<ProjectCamera> queryList(ProjectCamera entity) {
		return projectCameraDao.queryList(entity);
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<ProjectCamera> queryPageList(ProjectCamera entity) {
		return projectCameraDao.queryPageList(entity.toPage(), entity);
	}
}
