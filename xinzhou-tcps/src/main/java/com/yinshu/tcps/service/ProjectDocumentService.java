package com.yinshu.tcps.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.ProjectDocument;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 项目资料表 
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
public interface ProjectDocumentService extends IService<ProjectDocument> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<ProjectDocument> queryList(ProjectDocument entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<ProjectDocument> queryPageList(ProjectDocument entity);

}
