package com.yinshu.tcps.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.RecordSupervision;
import com.yinshu.tcps.dao.RecordSupervisionDao;
import com.yinshu.tcps.service.RecordSupervisionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 项目监理管理表 
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Service
public class RecordSupervisionServiceImpl extends ServiceImpl<RecordSupervisionDao, RecordSupervision> implements RecordSupervisionService {

	
	@Autowired
	private RecordSupervisionDao recordSupervisionDao;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<RecordSupervision> queryList(RecordSupervision entity) {
		return recordSupervisionDao.queryList(entity);
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<RecordSupervision> queryPageList(RecordSupervision entity) {
		return recordSupervisionDao.queryPageList(entity.toPage(), entity);
	}
}
