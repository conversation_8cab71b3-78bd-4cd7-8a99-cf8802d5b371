package com.yinshu.tcps.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yinshu.tcps.entity.TcpsFile;

/**
 * 管理现场记录文件表 
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
public interface TcpsFileService extends IService<TcpsFile> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<TcpsFile> queryList(TcpsFile entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<TcpsFile> queryPageList(TcpsFile entity);

}
