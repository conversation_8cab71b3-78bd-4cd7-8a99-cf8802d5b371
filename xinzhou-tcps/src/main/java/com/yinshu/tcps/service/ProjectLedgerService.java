package com.yinshu.tcps.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.ProjectLedger;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 项目台账资料表 
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
public interface ProjectLedgerService extends IService<ProjectLedger> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<ProjectLedger> queryList(ProjectLedger entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<ProjectLedger> queryPageList(ProjectLedger entity);

}
