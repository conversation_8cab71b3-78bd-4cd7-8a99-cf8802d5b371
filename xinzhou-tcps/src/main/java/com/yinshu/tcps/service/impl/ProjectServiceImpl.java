package com.yinshu.tcps.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.tcps.common.RemoteUrlConstants;
import com.yinshu.tcps.dao.ProjectDao;
import com.yinshu.tcps.entity.Project;
import com.yinshu.tcps.service.ProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 工程项目主表 
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Service
public class ProjectServiceImpl extends ServiceImpl<ProjectDao, Project> implements ProjectService {

	@Autowired
    private DvisualHttpTemplate httpTemplate;
	
	@Autowired
	private ProjectDao projectDao;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<Project> queryList(Project entity) {
		return projectDao.queryList(entity);
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<Project> queryPageList(Project entity) {
		return projectDao.queryPageList(entity.toPage(), entity);
	}

	/**
	 * 通过经纬度取得位置（调用大数据接口）
	 * @param lng
	 * @param lat
	 * @return
	 */
	public JSONObject getLocationByLngLat(Double lng, Double lat){
		JSONObject query = new JSONObject();
		query.put("lng", lng);
		query.put("lat", lat);
		return httpTemplate.post(RemoteUrlConstants.map_getLocationByLngLat, query);
	}
	
	/**
	 * 获取档案附件数据（调用大数据接口）
	 * @param lng
	 * @param lat
	 * @return
	 */
	public List<Map<String, String>> getArchiveList(){
		return projectDao.getArchiveList();
	}

	/**
	 * 分类统计数量
	 * @return
	 */
	public List<Map<String, Object>> countProjectType(){
		return projectDao.countProjectType();
	}
}
