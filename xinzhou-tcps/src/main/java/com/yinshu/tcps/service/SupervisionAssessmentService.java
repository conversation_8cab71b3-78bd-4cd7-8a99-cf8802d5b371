package com.yinshu.tcps.service;

import com.alibaba.fastjson.JSONObject;

/**
 * 监督考核管理
 */
public interface SupervisionAssessmentService {

    /**
     * 报警处理率统计-按月
     * @param query
     * @return
     */
    JSONObject rateStatisticsMth(JSONObject query);
    /**
     * 报警处理率统计-按年
     * @param query
     * @return
     */
    JSONObject rateStatisticsYear(JSONObject query);
    /**
     * 报警处理率统计-按季
     * @param query
     * @return
     */
    JSONObject rateStatisticsQuarter(JSONObject query);
    /**
     * 报警督办率统计-按月
     * @param query
     * @return
     */
    JSONObject warnSuperviseRateStatisticsMth(JSONObject query);
    /**
     * 报警督办率统计-按年
     * @param query
     * @return
     */
    JSONObject warnSuperviseRateStatisticsYear(JSONObject query);
    /**
     * 报警督办率统计-按季
     * @param query
     * @return
     */
    JSONObject warnSuperviseRateStatisticsQuarter(JSONObject query);
    /**
     * 超速驾驶统计-按月
     * @param query
     * @return
     */
    JSONObject speedstatisticsmth(JSONObject query);
    /**
     * 超速驾驶统计-按年
     * @param query
     * @return
     */
    JSONObject speedstatisticsyear(JSONObject query);
    /**
     * 超速驾驶统计-按季
     * @param query
     * @return
     */
    JSONObject speedStatisticsQuarter(JSONObject query);
    /**
     * 疲劳驾驶统计-按月
     * @param query
     * @return
     */
    JSONObject drivingStatisticsMth(JSONObject query);
    /**
     * 疲劳驾驶统计-按年
     * @param query
     * @return
     */
    JSONObject drivingStatisticsYear(JSONObject query);
    /**
     * 疲劳驾驶统计-按季
     * @param query
     * @return
     */
    JSONObject drivingStatisticsQuarter(JSONObject query);
    /**
     * 平台联通率统计-按月
     * @param query
     * @return
     */
    JSONObject platformUnicomRateStatisticsMth(JSONObject query);
    /**
     * 平台联通率统计-按年
     * @param query
     * @return
     */
    JSONObject platformUnicomRateStatisticsYear(JSONObject query);
    /**
     * 平台联通率统计-按季
     * @param query
     * @return
     */
    JSONObject platformUnicomRateStatisticsQuarter(JSONObject query);
    /**
     * 卫星定位漂移率统计-按月
     * @param query
     * @return
     */
    JSONObject gpsDriftRateStatisticsMth(JSONObject query);
    /**
     * 卫星定位漂移率统计-按年
     * @param query
     * @return
     */
    JSONObject gpsDriftRateStatisticsYear(JSONObject query);
    /**
     * 卫星定位漂移率统计-按季
     * @param query
     * @return
     */
    JSONObject gpsDriftRateStatisticsQuarter(JSONObject query);
    /**
     * 数据合格率统计-按月
     * @param query
     * @return
     */
    JSONObject qualifiedRateStatisticsMth(JSONObject query);
    /**
     * 数据合格率统计-按年
     * @param query
     * @return
     */
    JSONObject qualifiedRateStatisticsYear(JSONObject query);
    /**
     * 数据合格率统计-按季
     * @param query
     * @return
     */
    JSONObject qualifiedRateStatisticsQuarter(JSONObject query);
    /**
     * 轨迹完整率统计-按月
     * @param query
     * @return
     */
    JSONObject completenessRateStatisticsMth(JSONObject query);
    /**
     * 轨迹完整率统计-按年
     * @param query
     * @return
     */
    JSONObject completenessRateStatisticsYear(JSONObject query);
    /**
     * 轨迹完整率统计-按季
     * @param query
     * @return
     */
    JSONObject completenessRateStatisticsQuarter(JSONObject query);
    /**
     * 监控考核-按月
     * @param query
     * @return
     */
    JSONObject monitorExamineMth(JSONObject query);
    /**
     * 监控考核-按年
     * @param query
     * @return
     */
    JSONObject monitorExamineYear(JSONObject query);
    /**
     * 监控考核-按季
     * @param query
     * @return
     */
    JSONObject monitorExamineQuarter(JSONObject query);
    /**
     * 监控统计详情-按年
     * @param query
     * @return
     */
    JSONObject monitorStatisticsDetailsYear(JSONObject query);
    /**
     * 监控统计详情-按季
     * @param query
     * @return
     */
    JSONObject monitorStatisticsDetailsQuarter(JSONObject query);
    /**
     * 监控统计详情-按月
     * @param query
     * @return
     */
    JSONObject monitorStatisticsDetailsMth(JSONObject query);
    /**
     * 离线车辆统计
     * @param query
     * @return
     */
    JSONObject offlineStatistics(JSONObject query);
}
