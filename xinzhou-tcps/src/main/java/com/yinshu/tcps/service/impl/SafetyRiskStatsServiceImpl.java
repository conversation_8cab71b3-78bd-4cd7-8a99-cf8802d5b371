package com.yinshu.tcps.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.tcps.common.RemoteUrlConstants;
import com.yinshu.tcps.service.OrganStatsService;
import com.yinshu.tcps.service.SafetyRiskStatsService;
import com.yinshu.utils.DateUtils;
import com.yinshu.utils.StringHelper;

/**
 * 安全隐患统计分析
 * <AUTHOR>
 *
 */
@Service
public class SafetyRiskStatsServiceImpl implements SafetyRiskStatsService {

	@Autowired
    DvisualHttpTemplate template;
	
	/**
	 * 当前各区域隐患数量分析
	 */
	public JSONObject getCountyChart(JSONObject query) {
		
		JSONObject result = new JSONObject();
	    List<String> xAxisList = new ArrayList<>();
	    List<Object> seriesList1 = new ArrayList<>();
	    
	    JSONObject httpResult = template.post(RemoteUrlConstants.ads_road_safe_county_hidden_danger_sum, query);
        JSONArray jsonArray = httpResult.getJSONArray("list");
        for(int i = 0; i < jsonArray.size(); i++){
        	JSONObject item = jsonArray.getJSONObject(i);
        	xAxisList.add(item.getString("county"));
        	seriesList1.add(item.getString("hidden_danger_num"));//报警总数
        }
        
        JSONArray seriesList = new JSONArray();
        seriesList.add(0, seriesList1);
		result.put("xAxis", xAxisList);
		result.put("series", seriesList);
        
        return result;
        
	}
	
	/**
	 * 各类别平均治理时间
	 */
	public JSONObject getCategoryChart(JSONObject query) {
		JSONObject result = new JSONObject();
		List<Map<String, Object>> pieList = new ArrayList<>();
	    JSONObject httpResult = template.post(RemoteUrlConstants.ads_road_safe_hidden_danger_type_avg_governance_time, query);
        JSONArray jsonArray = httpResult.getJSONArray("list");
        for(int i = 0; i < jsonArray.size(); i++){
        	JSONObject item = jsonArray.getJSONObject(i);
        	pieList.add(StringHelper.setMapData(item.getString("hidden_danger_type"), item.getBigDecimal("avg_governance_time"), null));
        }
        result.put("series", pieList);
        return result;
	}
	
	
	/**
	 * 各等级平均治理时间
	 */
	public JSONObject getGradeChart(JSONObject query) {
		JSONObject result = new JSONObject();
		List<Map<String, Object>> pieList = new ArrayList<>();
	    JSONObject httpResult = template.post(RemoteUrlConstants.ads_road_safe_hidden_danger_level_avg_governance_time, query);
        JSONArray jsonArray = httpResult.getJSONArray("list");
        for(int i = 0; i < jsonArray.size(); i++){
        	JSONObject item = jsonArray.getJSONObject(i);
        	pieList.add(StringHelper.setMapData(item.getString("hidden_danger_level"), item.getBigDecimal("avg_governance_time"), null));
        }
        result.put("series", pieList);
        return result;
	}
	
	/**
	 * 当前隐患治理进度
	 */
	public JSONObject getProcessList(JSONObject query) {
		JSONObject httpResult = template.post(RemoteUrlConstants.ads_road_safe_hazard_governance, query);
    	return httpResult;
	}
	
	
	
	private String[] covertFilter(JSONObject query, String type) {
		String[] strs = new String[2];
		String dateType = query.getString("dateType");
		String dateValue = query.getString("dateRange");
		
		if(dateType.equals("month")) {
			strs[0] = " mth='" + (StringUtils.hasText(dateValue) ? dateValue : DateUtils.getYM()) + "'";
			strs[1] = type.equals("1") ? RemoteUrlConstants.ads_month_operation_stats : RemoteUrlConstants.ads_month_safety_stats;
		} else if(dateType.equals("quarter")) {
			strs[0] = " quarter='" + dateValue + "'";
			strs[1] = type.equals("1") ? RemoteUrlConstants.ads_quarter_operation_stats : RemoteUrlConstants.ads_quarter_safety_stats;
		} else if(dateType.equals("year")) {
			strs[0] = " year='" + dateValue + "'";
			strs[1] = type.equals("1") ? RemoteUrlConstants.ads_year_operation_stats : RemoteUrlConstants.ads_year_safety_stats;
		} else {
			strs[0] = " dt='" + dateValue + "'";
			strs[1] = type.equals("1") ? RemoteUrlConstants.ads_daily_operation_stats : RemoteUrlConstants.ads_daily_safety_stats;
		}
		
		return strs;
	}
	
}
