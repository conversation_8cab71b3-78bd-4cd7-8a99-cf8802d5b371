package com.yinshu.tcps.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.tcps.service.DrivingMonitorService;
import com.yinshu.tcps.util.DataComparisonUtils;
import com.yinshu.utils.DateUtils;
import com.yinshu.utils.ReplaceUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 行业监管看板
 */
@Service
public class DrivingMonitorServiceImpl implements DrivingMonitorService {

    /***
     * 道路运输监管行车监控车辆基础信息及最新定位//ads_road_control_car_info_and_newlocation
     */
    public static final String ADS_ROAD_CONTROL_CAR_INFO_AND_NEWLOCATION = "/api/100545/data.json?";

    /**
     * 车辆轨迹//ads_plane_fire_bus_location_info
     */
    public static final String ADS_PLANE_FIRE_BUS_LOCATION_INFO = "/api/100508/zPSvBrMftJFPzEoG1bRi?";

    @Resource
    private DvisualHttpTemplate template;

    /**
     * 获取车辆信息
     * @param query
     * @return
     */
    public JSONObject getVehicleList(JSONObject query) {
        query.put("filter", "order by company, statu");
        JSONObject httpResult = template.post(ADS_ROAD_CONTROL_CAR_INFO_AND_NEWLOCATION, query);
        return httpResult;
    }

    /**
     * 获取多辆车的实时定位
     *
     * @return
     */
    public JSONObject getMultiRealLocation(JSONObject query) {
        JSONArray plates = query.getJSONArray("ids");
        StringBuffer carid = new StringBuffer();
        for (int i = 0; i < plates.size(); i++) {
            carid.append(" or carid='" + plates.getString(i) + "'");
        }
        String url = "&tablename=ads_plane_fire_bus_location_info&filter=" + carid.substring(4) + " order by gps_timelong desc&s=0&n="+plates.size();
        JSONObject httpResult = template.post(ADS_PLANE_FIRE_BUS_LOCATION_INFO + url, query);
        return httpResult;
    }


    /**
     * 获取实时定位
     *
     * @return
     */
    public JSONObject getRealLocation(JSONObject query) {
        String plate = query.getString("plate");
        String url = "&tablename=ads_plane_fire_bus_location_info&filter=carid='" + plate + "' order by gps_timelong desc&s=0&n=1";
        JSONObject httpResult = template.post(ADS_PLANE_FIRE_BUS_LOCATION_INFO + url, query);
        return httpResult;
    }

    /**
     * 获取历史轨迹
     *
     * @return
     */
    public JSONArray getHistoryLocation(JSONObject query) {
        String plate = query.getString("plate");
        String startTime = query.getString("startTime");
        String endTime = query.getString("endTime");
        long startTimeStamp = DateUtils.parse(startTime, DateUtils.YYYY_MM_DD_HH_MM).getTime();
        long endTimeStamp = DateUtils.parse(endTime, DateUtils.YYYY_MM_DD_HH_MM).getTime();
        query.put("tablename", "ads_plane_fire_bus_location_info");
        String filterTemplate = "carid = '{plate}' and gps_timelong >= {startTimeStamp} and gps_timelong < {endTimeStamp} " +
                "order by gps_timelong asc";
        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                "plate", plate, "startTimeStamp", startTimeStamp, "endTimeStamp", endTimeStamp));
        JSONObject resultData = template.post(ADS_PLANE_FIRE_BUS_LOCATION_INFO, query);
        return DataComparisonUtils.getResultList(resultData);
    }

}
