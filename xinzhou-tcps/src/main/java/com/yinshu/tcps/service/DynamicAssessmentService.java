package com.yinshu.tcps.service;

import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DynamicAssessment
 * @description TODO 动态监管服务
 * @date 2025/7/17 15:54
 **/
public interface DynamicAssessmentService {
    /**
     * <AUTHOR>
     * @description //TODO 获取公司基础信息表
     * @date 2025/7/17 15:58
     * @param query 查询参数
     * @return com.alibaba.fastjson.JSONObject
     **/
    JSONObject adsCompanyInfoBaseTable(JSONObject query);

    
    /*
     * <AUTHOR>
     * @description //TODO 运营商明细基础表
     * @date 2025/7/17 15:58 
     * @param query 查询参数
     * @return com.alibaba.fastjson.JSONObject
     **/
    JSONObject adsOperatorInfoBaseTable(JSONObject query);
}
