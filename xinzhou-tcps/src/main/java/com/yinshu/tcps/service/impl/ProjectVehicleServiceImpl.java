package com.yinshu.tcps.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinshu.tcps.entity.ProjectVehicle;
import com.yinshu.tcps.dao.ProjectVehicleDao;
import com.yinshu.tcps.service.ProjectVehicleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 项目车辆表 
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Service
public class ProjectVehicleServiceImpl extends ServiceImpl<ProjectVehicleDao, ProjectVehicle> implements ProjectVehicleService {

	
	@Autowired
	private ProjectVehicleDao projectVehicleDao;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<ProjectVehicle> queryList(ProjectVehicle entity) {
		return projectVehicleDao.queryList(entity);
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<ProjectVehicle> queryPageList(ProjectVehicle entity) {
		return projectVehicleDao.queryPageList(entity.toPage(), entity);
	}
}
