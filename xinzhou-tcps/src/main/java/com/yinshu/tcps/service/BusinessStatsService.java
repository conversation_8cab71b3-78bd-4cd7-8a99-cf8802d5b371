package com.yinshu.tcps.service;

import com.alibaba.fastjson.JSONObject;

/**
 * 机构综合统计
 * <AUTHOR>
 *
 */
public interface BusinessStatsService {
	
	/**
	 * 获取运营数据汇总
	 */
	JSONObject getOperationTotal(JSONObject query);
	
	/**
	 * 获取运营数据
	 */
    JSONObject getOperationList(JSONObject query);
    
    /**
	 * 获取运营数据图表
	 */
	JSONObject getOperationChart(JSONObject query);
	
	/**
	 * 获取安全数据汇总
	 */
	JSONObject getSafetyTotal(JSONObject query);
	
	/**
	 * 获取安全数据列表
	 */
	JSONObject getSafetyList(JSONObject query);
	
	/**
	 * 获取安全数据饼图
	 */
	JSONObject getSafetyChart(JSONObject query);
	
	/**
	 * 综合统计分析报警事件详情
	 */
	JSONObject getDetailList(JSONObject query);
    
}
