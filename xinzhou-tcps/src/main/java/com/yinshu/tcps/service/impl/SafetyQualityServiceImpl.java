package com.yinshu.tcps.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinshu.tcps.entity.SafetyQuality;
import com.yinshu.tcps.dao.SafetyQualityDao;
import com.yinshu.tcps.service.SafetyQualityService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 安全质量检查 
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Service
public class SafetyQualityServiceImpl extends ServiceImpl<SafetyQualityDao, SafetyQuality> implements SafetyQualityService {

	
	@Autowired
	private SafetyQualityDao safetyQualityDao;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<SafetyQuality> queryList(SafetyQuality entity) {
		return safetyQualityDao.queryList(entity);
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<SafetyQuality> queryPageList(SafetyQuality entity) {
		return safetyQualityDao.queryPageList(entity.toPage(), entity);
	}

	/**
	 * 行业监管看板地图标点
	 * @return
	 */
	public List<Map<String, Object>> queryMapList(){
		return safetyQualityDao.queryMapList();
	}
}
