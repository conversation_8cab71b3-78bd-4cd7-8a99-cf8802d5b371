package com.yinshu.tcps.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.TcpsRecordTest;
import com.yinshu.tcps.dao.TcpsRecordTestDao;
import com.yinshu.tcps.service.TcpsRecordTestService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 试验检测管理表
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Service
public class TcpsRecordTestServiceImpl extends ServiceImpl<TcpsRecordTestDao, TcpsRecordTest> implements TcpsRecordTestService {

	@Autowired
	private TcpsRecordTestDao tcpsRecordTestDao;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<TcpsRecordTest> queryList(TcpsRecordTest entity) {
		return tcpsRecordTestDao.queryList(entity);
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<TcpsRecordTest> queryPageList(TcpsRecordTest entity) {
		return tcpsRecordTestDao.queryPageList(entity.toPage(), entity);
	}
}
