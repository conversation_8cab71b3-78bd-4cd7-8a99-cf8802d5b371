package com.yinshu.tcps.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinshu.tcps.dao.TcpsFileDao;
import com.yinshu.tcps.entity.TcpsFile;
import com.yinshu.tcps.service.TcpsFileService;

/**
 * 管理现场记录文件表 
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Service
public class TcpsFileServiceImpl extends ServiceImpl<TcpsFileDao, TcpsFile> implements TcpsFileService {
	
	@Autowired
	private TcpsFileDao tcpsFileDao;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<TcpsFile> queryList(TcpsFile entity) {
		return tcpsFileDao.queryList(entity);
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<TcpsFile> queryPageList(TcpsFile entity) {
		return tcpsFileDao.queryPageList(entity.toPage(), entity);
	}
	
	
}
