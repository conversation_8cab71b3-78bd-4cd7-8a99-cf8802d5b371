package com.yinshu.tcps.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.tcps.common.RemoteUrlConstants;
import com.yinshu.tcps.service.RoadSafety;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RoadSafetyImpl
 * @description TODO 道路安全服务类
 * @date 2025/7/15 10:28
 **/
@Service
public class RoadSafetyImpl implements RoadSafety {

    @Resource
    DvisualHttpTemplate template;

    @Override
    public JSONObject adsRoadSafeDynamicInspection(JSONObject query) {
        return template.post(RemoteUrlConstants.ADS_ROAD_SAFE_DYNAMIC_INSPECTION, query);
    }

    @Override
    public JSONObject adsRoadSafeAlarmSupervision(JSONObject query) {
        return template.post(RemoteUrlConstants.ADS_ROAD_SAFE_ALARM_SUPERVISION, query);
    }

    @Override
    public JSONObject odsCompanyInfo(JSONObject query) {
        return template.post(RemoteUrlConstants.ODS_COMPANY_INFO, query);
    }

    @Override
    public JSONObject dimXinzhouCounty(JSONObject query) {
        return template.post(RemoteUrlConstants.DIM_XINZHOU_COUNTY, query);
    }
}
