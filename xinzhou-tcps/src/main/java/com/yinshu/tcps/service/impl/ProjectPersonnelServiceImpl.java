package com.yinshu.tcps.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinshu.tcps.entity.ProjectPersonnel;
import com.yinshu.tcps.dao.ProjectPersonnelDao;
import com.yinshu.tcps.service.ProjectPersonnelService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 项目人员表 
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Service
public class ProjectPersonnelServiceImpl extends ServiceImpl<ProjectPersonnelDao, ProjectPersonnel> implements ProjectPersonnelService {

	
	@Autowired
	private ProjectPersonnelDao projectPersonnelDao;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<ProjectPersonnel> queryList(ProjectPersonnel entity) {
		return projectPersonnelDao.queryList(entity);
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<ProjectPersonnel> queryPageList(ProjectPersonnel entity) {
		return projectPersonnelDao.queryPageList(entity.toPage(), entity);
	}

	/**
	 * 分类统计数量
	 * @return
	 */
	public List<Map<String, Object>> countPersonnelType(){
		return projectPersonnelDao.countPersonnelType();
	}
}
