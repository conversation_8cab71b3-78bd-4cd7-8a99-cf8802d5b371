package com.yinshu.tcps.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.ProjectSensor;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 项目传感器表 
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
public interface ProjectSensorService extends IService<ProjectSensor> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<ProjectSensor> queryList(ProjectSensor entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<ProjectSensor> queryPageList(ProjectSensor entity);

}
