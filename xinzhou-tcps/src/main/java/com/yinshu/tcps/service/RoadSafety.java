package com.yinshu.tcps.service;


import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RoadSafety
 * @description TODO 道路安全服务
 * @date 2025/7/15 10:28
 **/
public interface RoadSafety {
    /*
     * <AUTHOR>
     * @description //TODO 道路安全动态巡察
     * @date 2025/7/15 10:31
     * @param query 查询参数
     * @return cn.hutool.json.JSONObject
     **/
    JSONObject adsRoadSafeDynamicInspection(JSONObject query);

    /*
     * <AUTHOR>
     * @description //TODO 道路安全报警督办
     * @date 2025/7/15 10:35
     * @param query 查询参数
     * @return com.alibaba.fastjson.JSONObject
     **/
    JSONObject adsRoadSafeAlarmSupervision(JSONObject query);

    /*
     * <AUTHOR>
     * @description //TODO 企业信息表
     * @date 2025/7/15 14:53
     * @param query 查询参数
     * @return com.alibaba.fastjson.JSONObject
     **/
    JSONObject odsCompanyInfo(JSONObject query);

    /*
     * <AUTHOR>
     * @description //TODO 行政区划表
     * @date 2025/7/15 15:04
     * @param query 查询参数
     * @return com.alibaba.fastjson.JSONObject
     **/
    JSONObject dimXinzhouCounty(JSONObject query);
}
