package com.yinshu.tcps.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinshu.tcps.entity.ProjectInvestProgress;
import com.yinshu.tcps.dao.ProjectInvestProgressDao;
import com.yinshu.tcps.service.ProjectInvestProgressService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 资金投入与建设进度 
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
public class ProjectInvestProgressServiceImpl extends ServiceImpl<ProjectInvestProgressDao, ProjectInvestProgress> implements ProjectInvestProgressService {

	
	@Autowired
	private ProjectInvestProgressDao projectInvestProgressDao;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<ProjectInvestProgress> queryList(ProjectInvestProgress entity) {
		return projectInvestProgressDao.queryList(entity);
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<ProjectInvestProgress> queryPageList(ProjectInvestProgress entity) {
		return projectInvestProgressDao.queryPageList(entity.toPage(), entity);
	}
}
