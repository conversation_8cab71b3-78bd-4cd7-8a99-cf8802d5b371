package com.yinshu.tcps.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yinshu.tcps.entity.Project;

import java.util.List;
import java.util.Map;

/**
 * 工程项目主表 
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface ProjectService extends IService<Project> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<Project> queryList(Project entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<Project> queryPageList(Project entity);

	/**
	 * 通过经纬度取得位置（调用大数据接口）
	 * @param lng
	 * @param lat
	 * @return
	 */
	JSONObject getLocationByLngLat(Double lng, Double lat);
	
	/**
	 * 获取所有附件内容
	 * @param entity
	 * @return
	 */
	List<Map<String, String>> getArchiveList();

	/**
	 * 分类统计数量
	 * @return
	 */
	List<Map<String, Object>> countProjectType();

}
