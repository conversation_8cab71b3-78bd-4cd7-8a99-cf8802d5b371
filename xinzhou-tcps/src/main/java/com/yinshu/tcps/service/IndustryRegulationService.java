package com.yinshu.tcps.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * 行业监管看板
 *
 */
public interface IndustryRegulationService {
    
	/**
	 * 获取运输行业统计
	 * @param query
	 * @return
	 */
	JSONObject getTransportStatistics(JSONObject query);
	/**
	 * 获取安全隐患统计
	 * @param query
	 * @return
	 */
	JSONObject getHazardStatistics(JSONObject query);
	/**
	 * 获取安全隐患列表
	 * @param query
	 * @return
	 */
	JSONArray getHazardList(JSONObject query);
	/**
	 * 获取地图在建工程
	 * @param query
	 * @return
	 */
	JSONArray getProgressConstruction(JSONObject query);
	/**
	 * 获取地图安全隐患
	 * @param query
	 * @return
	 */
	JSONArray getHiddenDanger(JSONObject query);



}
