package com.yinshu.tcps.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.tcps.common.RemoteUrlConstants;
import com.yinshu.tcps.service.OrganStatsService;
import com.yinshu.utils.DateUtils;
import com.yinshu.utils.StringHelper;

/**
 * 机构综合统计
 * <AUTHOR>
 *
 */
@Service
public class OrganStatsServiceImpl implements OrganStatsService {

	@Autowired
    DvisualHttpTemplate template;
	
	/**
	 * 获取运营数据汇总
	 */
	public JSONObject getOperationTotal(JSONObject query) {
		JSONObject httpResult = null;
		String[] strs = covertFilter(query, "1", "total");
		String filter = "company = null and car_number = null";
		filter = filter +  " and " + strs[0];
		query.put("filter", filter);
		httpResult = template.post(strs[1], query);
        return httpResult;
	}
	
	/**
	 * 获取运营数据列表
	 */
	public JSONObject getOperationList(JSONObject query) {
		JSONObject httpResult = null;
		String[] strs = covertFilter(query, "1", "list");
		String filter = "company = null and car_number = null";
		filter = filter +  " and " + strs[0];
		query.put("filter", filter);
		httpResult = template.post(strs[1], query);
        return httpResult;
	}
	
	
	/**
	 * 获取运营数据图表
	 */
	public JSONObject getOperationChart(JSONObject query) {
		
		JSONObject result = new JSONObject();
		
        List<String> xAxisList = new ArrayList<>();
		JSONArray seriesList = new JSONArray();
		List<Integer> seriesList1 = new ArrayList<>();
		List<Integer> seriesList2 = new ArrayList<>();
		List<Integer> seriesList3 = new ArrayList<>();
		List<Integer> seriesList4 = new ArrayList<>();
		
		List<String> listKeys = Arrays.asList("online_vehicles", "operating_vehicles", "driving_distance", "operating_distance");
		String[] strs = getChartList(query, "1");
		String filter = " company = null and car_number = null ";
		filter = filter +  " and " + strs[0] + " order by " + strs[2] + " desc ";
		query.put("filter", filter);
		
		JSONObject httpResult = template.post(strs[1], query);
        JSONArray jsonArray = httpResult.getJSONArray("list");
        
        Map<String, Map<String, Integer>> dataMap = new TreeMap<>();
        
        for(int i = 0; i < jsonArray.size(); i++){
			JSONObject object = jsonArray.getJSONObject(i);
			
			String date = object.getString(strs[2]);
			if(dataMap.containsKey(date)) {
				Map<String, Integer> itemMap = dataMap.get(date);
				for(String column : listKeys) {
					itemMap.put(column, itemMap.get(column) + convertInteger(object.getInteger(column)));
				}
			} else {
				Map<String, Integer> itemMap = new HashMap<>();
				for(String column : listKeys) {
					itemMap.put(column, convertInteger(object.getInteger(column)));
				}
				dataMap.put(date, itemMap);
			}
        }
        
        for(Map.Entry<String, Map<String, Integer>> entry : dataMap.entrySet()) {
        	xAxisList.add(entry.getKey());
        	seriesList1.add(entry.getValue().get("online_vehicles"));
        	seriesList2.add(entry.getValue().get("operating_vehicles"));
        	seriesList3.add(entry.getValue().get("driving_distance"));
        	seriesList4.add(entry.getValue().get("operating_distance"));
        }
        
        
        seriesList.add(0, seriesList1);
		seriesList.add(1, seriesList2);
		seriesList.add(2, seriesList3); 
		seriesList.add(3, seriesList4);
		result.put("xAxis", xAxisList);
		result.put("series", seriesList);
        return result;
	}
	
	/**
	 * 获取安全数据汇总
	 */
	public JSONObject getSafetyTotal(JSONObject query) {
		JSONObject httpResult = null;
		String[] strs = covertFilter(query, "2", "total");
		String filter = "company = null and car_number = null";
		filter = filter +  " and " + strs[0];
		query.put("filter", filter);
		httpResult = template.post(strs[1], query);
        return httpResult;
	}
	
	/**
	 * 获取安全数据列表
	 */
	public JSONObject getSafetyList(JSONObject query) {
		JSONObject httpResult = null;
		String[] strs = covertFilter(query, "2", "list");
		String filter = "company = null and car_number = null";
		filter = filter +  " and " + strs[0];
		query.put("filter", filter);
		httpResult = template.post(strs[1], query);
		JSONArray postList = httpResult.getJSONArray("list");
		for (int i = 0; i < postList.size(); i++) {
		    JSONObject jsonObject = postList.getJSONObject(i);
		    jsonObject.put("alarm_rate",calculateData2(jsonObject.getString("alarm_total_num"),jsonObject.getString("alarm_handled_num")));
		    jsonObject.put("supervise_rate",calculateData2(jsonObject.getString("alarm_total_num"),jsonObject.getString("supervision")));
		    jsonObject.put("supervise_finish_rate",calculateData2(jsonObject.getString("alarm_total_num"),jsonObject.getString("supervision_done")));
		}
        return httpResult;
	}
	
	public String calculateData2(String num1, String num2) {
	    if (StringUtils.hasText(num1) && StringUtils.hasText(num2)) {
	        double handledVal = (Double.valueOf(num2) / Double.valueOf(num1)) * 100;
	        handledVal = Math.round(handledVal * 10) / 10.0;
	        return handledVal + "%";
	    } else {
	        return "0.0%";
	    }
	}
	
	/**
	 * 获取安全数据饼图
	 */
	public JSONObject getSafetyChart(JSONObject query) {
		
		JSONObject result = new JSONObject();
		List<Map<String, Object>> pieList = new ArrayList<>();
		
		int non_op_hours_warn = 0; //非营运时段行驶预警
	    int offline_long_warn = 0; //长期不在线预警
	    int over_speed_warn = 0; //超速预警
	    int fatigue_drive_warn = 0; //疲劳驾驶预警
	    int abnormal_gather_warn = 0; //异常聚集预警
	    int online_overtime_warn = 0; //在线时长过长预警
	    
	    List<String> xAxisList = new ArrayList<>();
	    List<Object> seriesList1 = new ArrayList<>();
	    List<Object> seriesList2 = new ArrayList<>();
	    List<Object> seriesList3 = new ArrayList<>();
	    List<Object> seriesList4 = new ArrayList<>();
	    
		String[] strs = getChartList(query, "2");
		String filter = " company = null and car_number = null ";
		filter = filter +  " and " + strs[0] + " order by " + strs[2] + " desc ";
		query.put("filter", filter);
		
		JSONObject httpResult = template.post(strs[1], query);
	    JSONArray jsonArray = httpResult.getJSONArray("list");
        
        for(int i = 0; i < jsonArray.size(); i++){
        	JSONObject item = jsonArray.getJSONObject(i);
        	
        	xAxisList.add(item.getString(strs[2]));
        	seriesList1.add(item.getString("alarm_total_num"));//报警总数
        	seriesList2.add(item.getString("alarm_handled_num"));//报警处理数
        	seriesList3.add(item.getString("supervision"));//督办数
        	seriesList4.add(item.getString("supervision_done"));//督办完成数
        	
        	non_op_hours_warn = non_op_hours_warn + item.getIntValue("non_op_hours_warn");
        	offline_long_warn = offline_long_warn + item.getIntValue("offline_long_warn");
        	over_speed_warn = offline_long_warn + item.getIntValue("over_speed_warn");
        	over_speed_warn = offline_long_warn + item.getIntValue("fatigue_drive_warn");
        	over_speed_warn = offline_long_warn + item.getIntValue("abnormal_gather_warn");
        	over_speed_warn = offline_long_warn + item.getIntValue("online_overtime_warn");
        }
        
        //饼图
        pieList.add(StringHelper.setMapData("非营运时段行驶预警", new BigDecimal(non_op_hours_warn), null));
        pieList.add(StringHelper.setMapData("长期不在线预警", new BigDecimal(offline_long_warn), null));
        pieList.add(StringHelper.setMapData("超速预警", new BigDecimal(over_speed_warn), null));
        pieList.add(StringHelper.setMapData("疲劳驾驶预警", new BigDecimal(fatigue_drive_warn), null));
        pieList.add(StringHelper.setMapData("异常聚集预警", new BigDecimal(abnormal_gather_warn), null));
        pieList.add(StringHelper.setMapData("在线时长过长预警", new BigDecimal(online_overtime_warn), null));
        result.put("series", pieList);
        
        //柱状图
        JSONArray seriesList = new JSONArray();
        seriesList.add(0, seriesList1);
		seriesList.add(1, seriesList2);
		seriesList.add(2, seriesList3);
		seriesList.add(3, seriesList4);
		result.put("barXAxis", xAxisList);
		result.put("barSeries", seriesList);
        
        
        return result;
        
	}
	
	/**
	 * 综合统计分析报警事件详情
	 */
	public JSONObject getDetailList(JSONObject query) {
		String fitler = "country='" + query.getString("country") + "' and ";
		fitler = fitler + covertFilter(query, "1", null)[0];
		if(StringUtils.hasText(query.getString("car_number"))) {
			fitler = fitler + " and car_number='"+query.getString("car_number")+"'";
		}
		if(StringUtils.hasText(query.getString("company"))) {
			fitler = fitler + " and company='"+query.getString("company")+"'";
		}
		if(StringUtils.hasText(query.getString("driver_name"))) {
			fitler = fitler + " and driver_name='"+query.getString("driver_name")+"'";
		}
		if(StringUtils.hasText(query.getString("alarm_level"))) {
			fitler = fitler + " and alarm_level='"+query.getString("alarm_level")+"'";
		}
		query.put("filter", fitler);
		JSONObject httpResult = template.post(RemoteUrlConstants.ads_comprehensive_alarm_analysis, query);
    	return httpResult;
	}
	
	
	/**
	 * 
	 * @param query
	 * @param type
	 * @param totalType 
	 * @return
	 */
	private String[] covertFilter(JSONObject query, String type, String totalType) {
		String[] strs = new String[2];
		String dateType = query.getString("dateType");
		String dateValue = query.getString("dateRange");
		String county = query.getString("county");
		
		if(dateType.equals("month")) {
			strs[0] = " mth='" + (StringUtils.hasText(dateValue) ? dateValue : DateUtils.getYM()) + "'";
			strs[1] = type.equals("1") ? RemoteUrlConstants.ads_month_operation_stats : RemoteUrlConstants.ads_month_safety_stats;
		} else if(dateType.equals("quarter")) {
			strs[0] = " quarter='" + dateValue + "'";
			strs[1] = type.equals("1") ? RemoteUrlConstants.ads_quarter_operation_stats : RemoteUrlConstants.ads_quarter_safety_stats;
		} else if(dateType.equals("year")) {
			strs[0] = " year='" + dateValue + "'";
			strs[1] = type.equals("1") ? RemoteUrlConstants.ads_year_operation_stats : RemoteUrlConstants.ads_year_safety_stats;
		} else {
			String[] arrs = dateValue.split(",");
			strs[0] = " mth>='" + arrs[0] + "' and mth<='" + arrs[1] + "' ";
			strs[1] = type.equals("1") ? RemoteUrlConstants.ads_month_operation_stats : RemoteUrlConstants.ads_month_safety_stats;
		}
		
		if(totalType == null) {
			return strs;
		}
		
		if(StringUtils.hasText(county)) {
			strs[0] = strs[0] + " and county='"+query.getString("county")+"'";
		} else {
			strs[0] = strs[0] + (totalType.equals("total") ? " and county=null" : " and county!=null");
		}
		
		return strs;
	}
	
	/**
	 * 
	 * @param query
	 * @param type 1:运行数据   2:安全数据
	 * @param totalType 
	 * @return
	 */
	private String[] getChartList(JSONObject query, String type) {
		String[] strs = new String[3];
		String dateType = query.getString("dateType");
		String dateValue = query.getString("dateRange");
		String county = query.getString("county");
		String orderBy = "";
		if(dateType.equals("month")) {
			dateValue = (StringUtils.hasText(dateValue) ? dateValue : DateUtils.getYM()) ;
			strs[0] = " dt>='" + dateValue + "-01' and " + " dt<='" + dateValue + "-31' ";
			strs[1] = type.equals("1") ? RemoteUrlConstants.ads_daily_operation_stats : RemoteUrlConstants.ads_daily_safety_stats;
			orderBy = "dt";
		} else if(dateType.equals("quarter")) {
			String[] arr = dateValue.split("-");
			String[] quarters = StringHelper.getQuarterList(arr[1]);
			strs[0] = " mth>='" + arr[0] + "-" + quarters[0] + "' and mth<='" + arr[0] + "-" + quarters[1] + "' ";
			strs[1] = type.equals("1") ? RemoteUrlConstants.ads_month_operation_stats : RemoteUrlConstants.ads_month_safety_stats;
			orderBy = "mth";
		} else if(dateType.equals("year")) {
			strs[0] = " mth>='" + dateValue + "-01' and " + " mth<='" + dateValue + "-12' ";
			strs[1] = type.equals("1") ? RemoteUrlConstants.ads_month_operation_stats : RemoteUrlConstants.ads_month_safety_stats;
			orderBy = "mth";
		} else {
			String[] arrs = dateValue.split(",");
			strs[0] = " mth>='" + arrs[0] + "' and mth<='" + arrs[1] + "' ";
			strs[1] = type.equals("1") ? RemoteUrlConstants.ads_month_operation_stats : RemoteUrlConstants.ads_month_safety_stats;
			orderBy = "mth";
		}
		
		
		if(StringUtils.hasText(county)) {
			strs[0] = strs[0] + " and county='"+query.getString("county")+"'";
		} else {
			strs[0] = strs[0] + " and county=null";
		}
		
		strs[2] = orderBy;
        return strs;
	}
	
	private int convertInteger(Integer obj) {
		return obj == 0 ? 0 : obj.intValue();
	}
	
}
