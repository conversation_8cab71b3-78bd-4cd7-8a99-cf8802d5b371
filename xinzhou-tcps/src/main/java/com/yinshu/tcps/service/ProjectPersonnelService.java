package com.yinshu.tcps.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.ProjectPersonnel;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 项目人员表 
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
public interface ProjectPersonnelService extends IService<ProjectPersonnel> {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<ProjectPersonnel> queryList(ProjectPersonnel entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<ProjectPersonnel> queryPageList(ProjectPersonnel entity);

	/**
	 * 分类统计数量
	 * @return
	 */
	List<Map<String, Object>> countPersonnelType();
}
