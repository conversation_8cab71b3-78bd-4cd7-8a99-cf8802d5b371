package com.yinshu.tcps.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.tcps.entity.*;
import com.yinshu.tcps.manager.*;
import com.yinshu.tcps.service.MonitorPictureService;
import com.yinshu.tcps.util.DataComparisonUtils;
import com.yinshu.tcps.vo.AreaProjectTreeVO;
import com.yinshu.utils.DateUtils;
import com.yinshu.utils.FilterCreate;
import com.yinshu.utils.ReplaceUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 行业监管看板
 */
@Service
public class MonitorPictureServiceImpl implements MonitorPictureService {


    @Resource
    private DvisualHttpTemplate template;

    @Autowired
    private ProjectManager projectManager;

    @Autowired
    private ProjectPersonnelManager projectPersonnelManager;

    @Autowired
    private ProjectVehicleManager projectVehicleManager;

    @Autowired
    private ProjectCameraManager projectCameraManager;

    @Autowired
    private ProjectSensorManager projectSensorManager;

    /***
     * 门禁具体明细信息基础表//ads_door_infos_base_table
     */
    public static final String ADS_DOOR_INFOS_BASE_TABLE = "/api/100109/data.json?";
    /***
     * 监测一张图门禁记录//ads_door_info_analysis
     */
    public static final String ADS_DOOR_INFO_ANALYSIS = "/api/100110/data.json?";

    /**
     * 项目树结构
     * @param entity
     * @return
     */
    public List<AreaProjectTreeVO> getProjectTree(Project entity) {
        List<AreaProjectTreeVO> tree = projectManager.tree(entity);
        tree.forEach(nodeLv1 ->{
            List<AreaProjectTreeVO> childrenLv1 = nodeLv1.getChildren();
            childrenLv1.forEach(nodeLv2 -> {
                handleProjectNode(nodeLv2);
            });
        });
        return tree;
    }

    private void handleProjectNode(AreaProjectTreeVO node) {
        List<AreaProjectTreeVO> children = new ArrayList<>();
        children.add(getPersonnelNode(node));
        children.add(getVehicleNode(node));
        children.add(getCameraNode(node));
        children.add(getSensorNode(node));
        node.setChildren(children);
    }

    private AreaProjectTreeVO getPersonnelNode(AreaProjectTreeVO node) {
        String prefix = "personnel";
        AreaProjectTreeVO personnelNode = new AreaProjectTreeVO();
        personnelNode.setId(prefix + "-parent-" + node.getId());
        personnelNode.setLabel("人员");
        personnelNode.setType(prefix + "-parent");
        ProjectPersonnel entity = new ProjectPersonnel();
        entity.setProjectId(node.getId());
        List<ProjectPersonnel> personnelList = projectPersonnelManager.queryList(entity);
        List<AreaProjectTreeVO> children = personnelList.stream().map(item -> {
            AreaProjectTreeVO childrenNode = new AreaProjectTreeVO();
            childrenNode.setId(prefix + "-" + item.getId());
            childrenNode.setLabel(item.getName());
            childrenNode.setType(prefix);
            childrenNode.setAttribute(item);
            return childrenNode;
        }).collect(Collectors.toList());
        personnelNode.setChildren(children);
        return personnelNode;
    }

    private AreaProjectTreeVO getVehicleNode(AreaProjectTreeVO node) {
        String prefix = "vehicle";
        AreaProjectTreeVO vehicleNode = new AreaProjectTreeVO();
        vehicleNode.setId(prefix + "-parent-" + node.getId());
        vehicleNode.setLabel("车辆");
        vehicleNode.setType(prefix + "-parent");
        ProjectVehicle entity = new ProjectVehicle();
        entity.setProjectId(node.getId());
        List<ProjectVehicle> personnelList = projectVehicleManager.queryList(entity);
        List<AreaProjectTreeVO> children = personnelList.stream().map(item -> {
            AreaProjectTreeVO childrenNode = new AreaProjectTreeVO();
            childrenNode.setId(prefix + "-" + item.getId());
            childrenNode.setLabel(item.getPlateNumber());
            childrenNode.setType(prefix);
            childrenNode.setAttribute(item);
            return childrenNode;
        }).collect(Collectors.toList());
        vehicleNode.setChildren(children);
        return vehicleNode;
    }

    private AreaProjectTreeVO getCameraNode(AreaProjectTreeVO node) {
        String prefix = "camera";
        AreaProjectTreeVO cameraNode = new AreaProjectTreeVO();
        cameraNode.setId(prefix + "-parent-" + node.getId());
        cameraNode.setLabel("视频监控");
        cameraNode.setType(prefix + "-parent");
        ProjectCamera entity = new ProjectCamera();
        entity.setProjectId(node.getId());
        List<ProjectCamera> personnelList = projectCameraManager.queryList(entity);
        List<AreaProjectTreeVO> children = personnelList.stream().map(item -> {
            AreaProjectTreeVO childrenNode = new AreaProjectTreeVO();
            childrenNode.setId(prefix + "-" + item.getId());
            childrenNode.setLabel(item.getInstallLocation());
            childrenNode.setType(prefix);
            childrenNode.setAttribute(item);
            return childrenNode;
        }).collect(Collectors.toList());
        cameraNode.setChildren(children);
        return cameraNode;
    }

    private AreaProjectTreeVO getSensorNode(AreaProjectTreeVO node) {
        String prefix = "sensor";
        AreaProjectTreeVO sensorNode = new AreaProjectTreeVO();
        sensorNode.setId(prefix + "-parent-" + node.getId());
        sensorNode.setLabel("传感器");
        sensorNode.setType(prefix + "-parent");
        ProjectSensor entity = new ProjectSensor();
        entity.setProjectId(node.getId());
        List<ProjectSensor> personnelList = projectSensorManager.queryList(entity);
        List<AreaProjectTreeVO> children = personnelList.stream().map(item -> {
            AreaProjectTreeVO childrenNode = new AreaProjectTreeVO();
            childrenNode.setId(prefix + "-" + item.getId());
            childrenNode.setLabel(item.getSensorName());
            childrenNode.setType(prefix);
            childrenNode.setAttribute(item);
            return childrenNode;
        }).collect(Collectors.toList());
        sensorNode.setChildren(children);
        return sensorNode;
    }

    /**
     * 门禁树结构
     * @param entity
     * @return
     */
    public List<AreaProjectTreeVO> getAccessControlTree(Project entity) {
        List<AreaProjectTreeVO> tree = projectManager.tree(entity);
        JSONObject query = new JSONObject();
        query.put("s", 0);
        query.put("n", DataComparisonUtils.TEN_THOUSAND);
        JSONObject resultData = template.post(ADS_DOOR_INFOS_BASE_TABLE, query);
        JSONArray resultList = DataComparisonUtils.getResultList(resultData);
        tree.forEach(nodeLv1 ->{
            List<AreaProjectTreeVO> childrenLv1 = nodeLv1.getChildren();
            childrenLv1.forEach(nodeLv2 -> {
                handleAccessControlNode(nodeLv2, resultList);
            });
        });
        return tree;
    }

    private void handleAccessControlNode(AreaProjectTreeVO node, JSONArray resultList) {
        List<AreaProjectTreeVO> children = new ArrayList<>();
        for(int i=0; i<resultList.size(); i++){
            JSONObject jsonObject = resultList.getJSONObject(i);
            String projectName = jsonObject.getString("projectname");
            if(node.getLabel().equals(projectName)){
                AreaProjectTreeVO accessControlNode = new AreaProjectTreeVO();
                accessControlNode.setId("accessControl-" + jsonObject.getString("id"));
                accessControlNode.setLabel(jsonObject.getString("location"));
                accessControlNode.setType("accessControl");
                accessControlNode.setAttribute(node.getAttribute());
                children.add(accessControlNode);
            }
        }
        node.setChildren(children);
    }

    /**
     * 门禁记录
     *
     * @return
     */
    public JSONObject getAccessControlData(JSONObject query) {
        String area = query.getString("area");
        String projectName = query.getString("projectName");
        String location = query.getString("location");
        String filterTemplate = new FilterCreate()
                .and("county = '{area}'")
                .and(StringUtils.isNotBlank(projectName), "projectname = '{projectName}'")
                .and(StringUtils.isNotBlank(location), "location = '{location}'")
                .concat(" order by dttime desc")
                .toString();
        query.put("filter", ReplaceUtils.replaceWithArray(filterTemplate,
                "area", area, "projectName", projectName, "location", location));
        JSONObject resultData = template.post(ADS_DOOR_INFO_ANALYSIS, query);
        return resultData;
    }

}
