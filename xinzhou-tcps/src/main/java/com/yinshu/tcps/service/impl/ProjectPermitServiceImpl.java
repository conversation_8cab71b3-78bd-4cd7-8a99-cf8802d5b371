package com.yinshu.tcps.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinshu.tcps.entity.ProjectPermit;
import com.yinshu.tcps.dao.ProjectPermitDao;
import com.yinshu.tcps.service.ProjectPermitService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 项目许可及证书 
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Service
public class ProjectPermitServiceImpl extends ServiceImpl<ProjectPermitDao, ProjectPermit> implements ProjectPermitService {

	
	@Autowired
	private ProjectPermitDao projectPermitDao;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<ProjectPermit> queryList(ProjectPermit entity) {
		return projectPermitDao.queryList(entity);
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<ProjectPermit> queryPageList(ProjectPermit entity) {
		return projectPermitDao.queryPageList(entity.toPage(), entity);
	}
}
