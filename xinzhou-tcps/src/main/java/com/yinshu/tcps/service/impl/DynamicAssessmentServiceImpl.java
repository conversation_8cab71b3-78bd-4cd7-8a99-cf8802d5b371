package com.yinshu.tcps.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.sys.http.DvisualHttpTemplate;
import com.yinshu.tcps.common.RemoteUrlConstants;
import com.yinshu.tcps.service.DynamicAssessmentService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DynamicAssessmentServiceImpl
 * @description TODO 动态考核服务实现类
 * @date 2025/7/17 15:55
 **/
@Service
public class DynamicAssessmentServiceImpl implements DynamicAssessmentService {

    @Resource
    DvisualHttpTemplate template;

    /**
     * @param query 查询参数
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @description //TODO 获取公司基础信息表
     * @date 2025/7/17 15:58
     **/
    @Override
    public JSONObject adsCompanyInfoBaseTable(JSONObject query) {
        return template.post(RemoteUrlConstants.ADS_COMPANY_INFO_BASE_TABLE, query);
    }

    /**
     * @param query 查询参数
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @description //TODO 获取运营商基础信息表
     * @date 2025/7/17 16:04
     **/
    @Override
    public JSONObject adsOperatorInfoBaseTable(JSONObject query) {
        return template.post(RemoteUrlConstants.ADS_OPERATOR_INFO_BASE_TABLE, query);
    }
}
