package com.yinshu.tcps.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinshu.tcps.entity.ProjectDocument;
import com.yinshu.tcps.dao.ProjectDocumentDao;
import com.yinshu.tcps.service.ProjectDocumentService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 项目资料表 
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Service
public class ProjectDocumentServiceImpl extends ServiceImpl<ProjectDocumentDao, ProjectDocument> implements ProjectDocumentService {

	
	@Autowired
	private ProjectDocumentDao projectDocumentDao;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<ProjectDocument> queryList(ProjectDocument entity) {
		return projectDocumentDao.queryList(entity);
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<ProjectDocument> queryPageList(ProjectDocument entity) {
		return projectDocumentDao.queryPageList(entity.toPage(), entity);
	}
}
