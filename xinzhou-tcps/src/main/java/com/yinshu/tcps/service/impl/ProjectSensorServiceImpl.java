package com.yinshu.tcps.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinshu.tcps.entity.ProjectSensor;
import com.yinshu.tcps.dao.ProjectSensorDao;
import com.yinshu.tcps.service.ProjectSensorService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 项目传感器表 
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Service
public class ProjectSensorServiceImpl extends ServiceImpl<ProjectSensorDao, ProjectSensor> implements ProjectSensorService {

	
	@Autowired
	private ProjectSensorDao projectSensorDao;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<ProjectSensor> queryList(ProjectSensor entity) {
		return projectSensorDao.queryList(entity);
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<ProjectSensor> queryPageList(ProjectSensor entity) {
		return projectSensorDao.queryPageList(entity.toPage(), entity);
	}
}
