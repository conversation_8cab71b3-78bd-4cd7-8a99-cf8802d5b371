package com.yinshu.tcps.config;

import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriUtils;

import com.yinshu.utils.DateUtils;
import com.yinshu.utils.SnowflakeIdGenerator;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;

/**
 * 本地存储配置
 *
 * <AUTHOR> @date 2025/07/02
 */
@Component
public class TcpsLocalStorageConfig implements InitializingBean {

    private static final String BUCKET_NAME = "tcps";

    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;
    /**
     * 文件存储路径
     */
    @Value("${attachment.default-path}")
    private String rootPath;

    protected final static Logger logger = LoggerFactory.getLogger(TcpsLocalStorageConfig.class);

    @Override
    public void afterPropertiesSet() throws Exception {
        // 初始化存储根目录
        Path root = Paths.get(rootPath);
        if (!Files.exists(root)) {
            Files.createDirectories(root);
        }
    }

    /**
     * 上传对象
     *
     * @param multipartFile Multipart 文件
     * @return {@link String }
     */
    public String putObject(MultipartFile multipartFile) {
        return putObject(multipartFile, BUCKET_NAME);
    }

    /**
     * 上传对象
     *
     * @param multipartFile Multipart 文件
     * @param bucketName    存储桶名称
     * @return {@link String }
     */
    public String putObject(MultipartFile multipartFile, String bucketName) {
        try (InputStream inputStream = multipartFile.getInputStream()) {
            return saveFile(inputStream, multipartFile.getOriginalFilename(), bucketName);
        } catch (Exception e) {
            throw new RuntimeException("文件上传失败", e);
        }
    }

    /**
     * 上传对象
     *
     * @param inputStream 输入流
     * @param filename    文件名
     * @return {@link String }
     */
    public String putObject(InputStream inputStream, String filename) {
        return putObject(inputStream, filename, BUCKET_NAME);
    }

    /**
     * 上传对象
     *
     * @param inputStream 输入流
     * @param filename    文件名
     * @param bucketName  存储桶名称
     * @return {@link String }
     */
    public String putObject(InputStream inputStream, String filename, String bucketName) {
        return saveFile(inputStream, filename, bucketName);
    }

    public String saveFile(InputStream inputStream, String filename, String bucketName) {
        try {
            String date = DateUtils.getDateTime("yyyyMMdd");
            String filePath = bucketName + File.separator + date; 
            createMkdir(rootPath + filePath);  
            String saveName = filePath  + File.separator + snowflakeIdGenerator.nextIdStr() + "_" + filename;
            Files.copy(inputStream, Paths.get(rootPath + saveName), StandardCopyOption.REPLACE_EXISTING);
            return saveName;
        } catch (Exception e) {
            throw new RuntimeException("文件保存失败", e);
        }
    }



    public void downloadFile(String filePath, HttpServletResponse response) {
        try {
            String decodedFileName = UriUtils.decode(filePath, StandardCharsets.UTF_8);
            String[] parts = decodedFileName.split("_");

            if (!Files.exists(Paths.get(filePath))) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
                return;
            }
            response.setContentType(Files.probeContentType(Paths.get(filePath)));
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION,
                    "attachment;filename=\"" + UriUtils.encode(getDisplayFileName(parts), StandardCharsets.UTF_8) + "\"");

            try (InputStream is = Files.newInputStream(Paths.get(filePath));
                 OutputStream os = response.getOutputStream()) {
                IOUtils.copy(is, os);
            }
        } catch (Exception e) {
            logger.error("文件下载失败", e);
        }
    }

    public boolean removeObject(String filePath) {
        try {
            return Files.deleteIfExists(Paths.get(filePath));
        } catch (Exception e) {
            throw new RuntimeException("文件删除失败", e);
        }
    }

	private void createMkdir(String path) {
		File file = new File(path);
		if (!file.exists() && !file.isDirectory()) {
			file.mkdirs();
		}
	}

    private String getDisplayFileName(String[] parts) {
        return parts[parts.length-1];
    }

}