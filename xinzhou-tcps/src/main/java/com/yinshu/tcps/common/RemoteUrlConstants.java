package com.yinshu.tcps.common;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO 大数据平台api地址
 * @date 2025/6/3 18:12
 **/
public interface RemoteUrlConstants {

    /**
     * 前缀
     **/
    String PREFIX = "/api/";
    /**
     * 后缀
     **/
    String SUFFIX = "/data.json?";
    /**
     * 道路安全监督监管动态查岗 ads_road_safe_dynamic_inspection
     **/
    String ADS_ROAD_SAFE_DYNAMIC_INSPECTION = PREFIX + "100567" + SUFFIX;
    /**
     * 道路安全监督监管报警督办 ads_road_safe_alarm_supervision
     **/
    String ADS_ROAD_SAFE_ALARM_SUPERVISION = PREFIX + "100568" + SUFFIX;
    /**
     * 企业信息表 ods_company_info
     **/
    String ODS_COMPANY_INFO = PREFIX + "100000" + SUFFIX;
    /**
     * 忻州市地市行政区划//dim_xinzhou_county
     **/
    String DIM_XINZHOU_COUNTY = PREFIX + "100430" + SUFFIX;


    /**
     * 监督考核管理
     */
    // 报警处理率统计-按月//ads_xinzhou_warn_handle_rate_statistics_mth
    public static final String ads_xinzhou_warn_handle_rate_statistics_mth  =  "/api/100539/data.json?";

    // 报警处理率统计-按年//ads_xinzhou_warn_handle_rate_statistics_year
    public static final String ads_xinzhou_warn_handle_rate_statistics_year  =  "/api/100541/data.json?";

    // 报警处理率统计-按季//ads_xinzhou_warn_handle_rate_statistics_quarter
    public static final String ads_xinzhou_warn_handle_rate_statistics_quarter  =  "/api/100540/data.json?";

    // 报警督办率统计-按月//ads_xinzhou_warn_supervise_rate_statistics_mth
    public static final String ads_xinzhou_warn_supervise_rate_statistics_mth  =  "/api/100542/data.json?";


    // 报警督办率统计-按年//ads_xinzhou_warn_supervise_rate_statistics_year
    public static final String ads_xinzhou_warn_supervise_rate_statistics_year  =  "/api/100544/data.json?";


    // 报警督办率统计-按季//ads_xinzhou_warn_supervise_rate_statistics_quarter
    public static final String ads_xinzhou_warn_supervise_rate_statistics_quarter  =  "/api/100543/data.json?";

    // 超速驾驶统计-按月//ads_xinzhou_over_speed_statistics_mth
    public static final String ads_xinzhou_over_speed_statistics_mth  =  "/api/100530/data.json?";


    // 超速驾驶统计-按年//ads_xinzhou_over_speed_statistics_year
    public static final String ads_xinzhou_over_speed_statistics_year  =  "/api/100532/data.json?";


    // 超速驾驶统计-按季//ads_xinzhou_over_speed_statistics_quarter
    public static final String ads_xinzhou_over_speed_statistics_quarter  =  "/api/100531/data.json?";


    // 疲劳驾驶统计-按月//ads_xinzhou_fatigue_driving_statistics_mth
    public static final String ads_xinzhou_fatigue_driving_statistics_mth  =  "/api/100517/data.json?";


    // 疲劳驾驶统计-按年//ads_xinzhou_fatigue_driving_statistics_year
    public static final String ads_xinzhou_fatigue_driving_statistics_year  =  "/api/100519/data.json?";


    // 疲劳驾驶统计-按季//ads_xinzhou_fatigue_driving_statistics_quarter
    public static final String ads_xinzhou_fatigue_driving_statistics_quarter  =  "/api/100518/data.json?";


    // 平台联通率统计-按月//ads_xinzhou_platform_unicom_rate_statistics_mth
    public static final String ads_xinzhou_platform_unicom_rate_statistics_mth  =  "/api/100533/data.json?";


    // 平台联通率统计-按年//ads_xinzhou_platform_unicom_rate_statistics_year
    public static final String ads_xinzhou_platform_unicom_rate_statistics_year  =  "/api/100535/data.json?";


    // 平台联通率统计-按季//ads_xinzhou_platform_unicom_rate_statistics_quarter
    public static final String ads_xinzhou_platform_unicom_rate_statistics_quarter  =  "/api/100534/data.json?";


    // 卫星定位漂移率统计-按月//ads_xinzhou_gps_drift_rate_statistics_mth
    public static final String ads_xinzhou_gps_drift_rate_statistics_mth  =  "/api/100520/data.json?";


    // 卫星定位漂移率统计-按年//ads_xinzhou_gps_drift_rate_statistics_year
    public static final String ads_xinzhou_gps_drift_rate_statistics_year  =  "/api/100522/data.json?";


    // 卫星定位漂移率统计-按季//ads_xinzhou_gps_drift_rate_statistics_quarter
    public static final String ads_xinzhou_gps_drift_rate_statistics_quarter  =  "/api/100521/data.json?";


    // 数据合格率统计-按月//ads_xinzhou_data_qualified_rate_statistics_mth
    public static final String ads_xinzhou_data_qualified_rate_statistics_mth  =  "/api/100514/data.json?";


    // 数据合格率统计-按年//ads_xinzhou_data_qualified_rate_statistics_year
    public static final String ads_xinzhou_data_qualified_rate_statistics_year  =  "/api/100516/data.json?";


    // 数据合格率统计-按季//ads_xinzhou_data_qualified_rate_statistics_quarter
    public static final String ads_xinzhou_data_qualified_rate_statistics_quarter  =  "/api/100515/data.json?";


    // 轨迹完整率统计-按月//ads_xinzhou_trajectory_completeness_rate_statistics_mth
    public static final String ads_xinzhou_trajectory_completeness_rate_statistics_mth  =  "/api/100536/data.json?";


    // 轨迹完整率统计-按年//ads_xinzhou_trajectory_completeness_rate_statistics_year
    public static final String ads_xinzhou_trajectory_completeness_rate_statistics_year  =  "/api/100538/data.json?";


    // 轨迹完整率统计-按季//ads_xinzhou_trajectory_completeness_rate_statistics_quarter
    public static final String ads_xinzhou_trajectory_completeness_rate_statistics_quarter  =  "/api/100537/data.json?";


    // 监控考核-按月//ads_xinzhou_monitor_examine_mth
    public static final String ads_xinzhou_monitor_examine_mth  =  "/api/100523/data.json?";


    // 监控考核-按年//ads_xinzhou_monitor_examine_year
    public static final String ads_xinzhou_monitor_examine_year  =  "/api/100525/data.json?";


    // 监控考核-按季//ads_xinzhou_monitor_examine_quarter
    public static final String ads_xinzhou_monitor_examine_quarter  =  "/api/100524/data.json?";


    // 监控统计详情-按年//ads_xinzhou_monitor_statistics_details_year
    public static final String ads_xinzhou_monitor_statistics_details_year  =  "/api/100528/data.json?";


    // 监控统计详情-按季//ads_xinzhou_monitor_statistics_details_quarter
    public static final String ads_xinzhou_monitor_statistics_details_quarter  =  "/api/100527/data.json?";


    // 监控统计详情-按月//ads_xinzhou_monitor_statistics_details_mth
    public static final String ads_xinzhou_monitor_statistics_details_mth  =  "/api/100526/data.json?";

    // 离线车辆统计//ads_xinzhou_offline_statistics
    public static final String ads_xinzhou_offline_statistics  =  "/api/100529/data.json?";

    /***
     * 综合统计分析机构统计-营运数据按日//ads_daily_operation_stats
     */
    public static final String ads_daily_operation_stats = "/api/100551/data.json?";

    /***
     * 综合统计分析机构统计-营运数据按月//ads_month_operation_stats
     */
    public static final String ads_month_operation_stats = "/api/100553/data.json?";

    /***
     * 综合统计分析机构统计-营运数据按季//ads_quarter_operation_stats
     */
    public static final String ads_quarter_operation_stats = "/api/100555/data.json?";

    /***
     * 综合统计分析机构统计-营运数据按年//ads_year_operation_stats
     */
    public static final String ads_year_operation_stats = "/api/100557/data.json?";

    /***
     * 综合统计分析-安全数据按日//ads_daily_safety_stats
     */
    public static final String ads_daily_safety_stats = "/api/100552/data.json?";

    /***
     * 综合统计分析-安全数据按月//ads_month_safety_stats
     */
    public static final String ads_month_safety_stats = "/api/100554/data.json?";

    /***
     * 综合统计分析-安全数据按季//ads_quarter_safety_stats
     */
    public static final String ads_quarter_safety_stats = "/api/100556/data.json?";

    /***
     * 综合统计分析-安全数据按年//ads_year_safety_stats
     */
    public static final String ads_year_safety_stats = "/api/100558/data.json?";

    /***
     * 综合统计分析报警事件详情//ads_comprehensive_alarm_analysis
     */
    public static final String ads_comprehensive_alarm_analysis = "/api/100550/data.json?";

    /***
     * 道路安全监管监督当前隐患治理进度//ads_road_safe_hazard_governance
     */
    public static final String ads_road_safe_hazard_governance = "/api/100574/data.json?";

    /***
     * 道路安全监管监督各区域隐患数量分析	ads_road_safe_county_hidden_danger_sum
     */
    public static final String ads_road_safe_county_hidden_danger_sum = "/api/100571/data.json?";

    /***
     * 道路安全监督监管各类型平均治理时间	ads_road_safe_hidden_danger_type_avg_governance_time
     */
    public static final String ads_road_safe_hidden_danger_type_avg_governance_time = "/api/100572/data.json?";

    /***
     * 道路安全监督监管各等级平均治理时间	ads_road_safe_hidden_danger_level_avg_governance_time
     */
    public static final String ads_road_safe_hidden_danger_level_avg_governance_time = "/api/100573/data.json?";

    /**
     * 企业明细基础表//ads_company_info_base_table
     **/
    String ADS_COMPANY_INFO_BASE_TABLE = PREFIX + "100107" + SUFFIX;

    /**
     * 运营商明细基础表//ads_operator_info_base_table
     **/
    String ADS_OPERATOR_INFO_BASE_TABLE = PREFIX + "100108" + SUFFIX;

    /**
     * 通过经纬度取得位置
     **/
    public static final String map_getLocationByLngLat = PREFIX + "lnglat_to_address_info/vsynEfBy1WTgUqku4D1q";
}
