package com.yinshu.tcps.entity;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yinshu.sys.entity.FileUpload;
import com.yinshu.utils.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 项目监理管理表
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@EqualsAndHashCode(callSuper = true)
@TableName("tcps_record_supervision")
@Data
public class RecordSupervision extends PageParam<RecordSupervision> implements Serializable {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    @ExcelIgnore
    private String id;

    /**
     * 关联项目ID
     */
    @ExcelIgnore
    private String projectId;

    /**
     * 记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "记录时间", order = 6)
    @ColumnWidth(70)
    private Date recordTime;

    /**
     * 工作方式
     */
    @ExcelIgnore
    private String workMode;

    /**
     * 现场记录文件
     */
    @ExcelProperty(value = "现场记录文件", order = 5)
    private String siteRecordFile;

    /**
     * 监理姓名
     */
    @ExcelProperty(value = "监理姓名", order = 3)
    private String supervisorName;

    /**
     * 施工现场负责人
     */
    @ExcelProperty(value = "施工现场负责人", order = 5)
    private String siteLeader;

    /**
     * 检查内容
     */
    @ExcelProperty(value = "检查内容", order = 4)
    @ColumnWidth(70)
    private String inspectionContent;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelIgnore
    private Date createdAt;

    /**
     * 创建人
     */
    @ExcelIgnore
    private String createdBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelIgnore
    private Date updatedAt;

    /**
     * 更新人
     */
    @ExcelIgnore
    private String updatedBy;


    // 查询

    /**
     * 开始时间
     */
    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelIgnore
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelIgnore
    private Date stopTime;

    // 返回vo

    /**
     * 项目名称
     */
    @TableField(exist = false)
    @ExcelProperty(value = "项目名称", order = 1)
    private String projectName;


    /**
     * 工作方式名称
     */
    @TableField(exist = false)
    @ExcelProperty(value = "工作方式", order = 2)
    private String workModeName;


    @TableField(exist = false)
    private List<FileUpload> fileList;

    /**
     * 视频上传列表
     */
    @TableField(exist = false)
    private List<FileUpload> videoList;

}