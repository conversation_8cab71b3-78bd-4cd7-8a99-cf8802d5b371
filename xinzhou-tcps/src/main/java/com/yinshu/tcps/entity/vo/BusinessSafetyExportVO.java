package com.yinshu.tcps.entity.vo;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import cn.idev.excel.annotation.write.style.ContentStyle;
import cn.idev.excel.annotation.write.style.HeadStyle;
import cn.idev.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

@Data
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class BusinessSafetyExportVO {

    private String no;

    @ExcelProperty(value = "区县")
    @ColumnWidth(10)
    private String county;

    @ExcelProperty(value = "企业名称")
    @ColumnWidth(15)
    private String company;

    @ExcelProperty(value = "企业地址")
    @ColumnWidth(15)
    private String address;

    @ExcelProperty(value = "法人")
    @ColumnWidth(15)
    private String legal;

    @ExcelProperty(value = "联系电话")
    @ColumnWidth(15)
    private String tel;

    @ExcelProperty(value = "企业评分")
    @ColumnWidth(15)
    private Double companyScore;

    @ExcelProperty(value = "全市排行")
    @ColumnWidth(15)
    private String rank;

    @ExcelProperty(value = "车辆总数")
    @ColumnWidth(15)
    private String carsum;

    @ExcelProperty(value = "入网数")
    @ColumnWidth(15)
    private String connectedCarSum;

    @ExcelProperty(value = "总行驶里程")
    @ColumnWidth(15)
    private String totalMileage;
}
