package com.yinshu.tcps.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yinshu.sys.entity.FileUpload;
import com.yinshu.utils.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

/**
 * 试验检测管理表
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@TableName("tcps_record_test")
public class TcpsRecordTest extends PageParam<TcpsRecordTest> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private String id;

    /**
     * 关联项目ID
     */
    private String projectId;

    /**
     * 记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recordTime;

    /**
     * 检测类别
     */
    private String inspectionType;

    /**
     * 检测员
     */
    private String inspector;

    /**
     * 施工现场负责人
     */
    private String siteLeader;

    /**
     * 检测内容
     */
    private String inspectionContent;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 更新人
     */
    private String updatedBy;

    private String fileInfo;

    private String videoInfo;

    @TableField(exist = false)
    private List<FileUpload> fileList;

    /**
     * 视频上传列表
     */
    @TableField(exist = false)
    private List<FileUpload> videoList;

    public List<FileUpload> getVideoList() {
        return videoList;
    }

    public void setVideoList(List<FileUpload> videoList) {
        this.videoList = videoList;
    }

    public List<FileUpload> getFileList() {
        return fileList;
    }

    public void setFileList(List<FileUpload> fileList) {
        this.fileList = fileList;
    }

    public String getVideoInfo() {
        return videoInfo;
    }

    public void setVideoInfo(String videoInfo) {
        this.videoInfo = videoInfo;
    }

    public String getFileInfo() {
		return fileInfo;
	}

	public void setFileInfo(String fileInfo) {
		this.fileInfo = fileInfo;
	}

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public Date getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(Date recordTime) {
        this.recordTime = recordTime;
    }

    public String getInspectionType() {
        return inspectionType;
    }

    public void setInspectionType(String inspectionType) {
        this.inspectionType = inspectionType;
    }

    public String getInspector() {
        return inspector;
    }

    public void setInspector(String inspector) {
        this.inspector = inspector;
    }

    public String getSiteLeader() {
        return siteLeader;
    }

    public void setSiteLeader(String siteLeader) {
        this.siteLeader = siteLeader;
    }

    public String getInspectionContent() {
        return inspectionContent;
    }

    public void setInspectionContent(String inspectionContent) {
        this.inspectionContent = inspectionContent;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Override
    public String toString() {
        return "TcpsRecordTest{" +
            "id=" + id +
            ", projectId=" + projectId +
            ", recordTime=" + recordTime +
            ", inspectionType=" + inspectionType +
            ", inspector=" + inspector +
            ", siteLeader=" + siteLeader +
            ", inspectionContent=" + inspectionContent +
            ", createdAt=" + createdAt +
            ", createdBy=" + createdBy +
            ", updatedAt=" + updatedAt +
            ", updatedBy=" + updatedBy +
        "}";
    }
}
