package com.yinshu.tcps.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yinshu.sys.entity.FileUpload;
import com.yinshu.utils.PageParam;

/**
 * 
 * 安全质量检查
 *
 * <AUTHOR>
 * @since 2025-07-19
 */
@TableName("tcps_safety_quality")
public class SafetyQuality extends PageParam<SafetyQuality> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.NONE)
    @ExcelIgnore
    private String id;

    /**
     * 项目ID
     */
    @ExcelIgnore
    private String projectId;

    /**
     * 所属标段
     */
    @ExcelIgnore
    private String bidSection;

    /**
     * 检查类别
     */
    @ExcelIgnore
    private String checkType;

    /**
     * 监理单位
     */
    @ExcelIgnore
    private String supervisorUnit;

    /**
     * 监理姓名
     */
    @ExcelProperty(value = "监理", order = 3)
    private String supervisorName;

    /**
     * 监理电话
     */
    @ExcelIgnore
    private String supervisorPhone;

    /**
     * 施工单位
     */
    @ExcelIgnore
    private String constructUnit;

    /**
     * 施工负责人
     */
    @ExcelIgnore
    private String constructName;

    /**
     * 施工电话
     */
    @ExcelIgnore
    private String constructPhone;

    /**
     * 安全员
     */
    @ExcelProperty(value = "安全员", order = 4)
    private String safetyName;

    /**
     * 安全员电话
     */
    @ExcelIgnore
    private String safetyPhone;

    /**
     * 质检员
     */
    @ExcelProperty(value = "质检员", order = 5)
    private String quality;

    /**
     * 监测对象
     */
    @ExcelProperty(value = "安全检查对象", order = 6)
    private String checkObject;

    /**
     * 隐患等级
     */
    @ExcelIgnore
    private String hazardLevel;

    /**
     * 隐患类型
     */
    @ExcelIgnore
    private String hazardType;

    /**
     * 现场照片
     */
    @ExcelIgnore
    private String pictures;

    /**
     * 现场视频
     */
    @ExcelIgnore
    private String videos;

    /**
     * 问题详情
     */
    @ExcelIgnore
    private String remark;

    /**
     * 创建时间
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "记录时间", order = 2)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建人
     */
    @ExcelIgnore
    private String createUser;

    /**
     * 更新时间
     */
    @ExcelIgnore
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;
    
    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称", order = 1)
    @TableField(exist = false)
    private String projectName;
    
    /**
     * 文件上传列表
     */
    @ExcelIgnore
    @TableField(exist = false)
    private List<FileUpload> fileList;
    
    /**
     * 视频上传列表
     */
    @ExcelIgnore
    @TableField(exist = false)
    private List<FileUpload> videoList;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }
    public String getBidSection() {
        return bidSection;
    }

    public void setBidSection(String bidSection) {
        this.bidSection = bidSection;
    }
    public String getCheckType() {
        return checkType;
    }

    public void setCheckType(String checkType) {
        this.checkType = checkType;
    }
    public String getSupervisorUnit() {
        return supervisorUnit;
    }

    public void setSupervisorUnit(String supervisorUnit) {
        this.supervisorUnit = supervisorUnit;
    }
    public String getSupervisorName() {
        return supervisorName;
    }

    public void setSupervisorName(String supervisorName) {
        this.supervisorName = supervisorName;
    }
    public String getSupervisorPhone() {
        return supervisorPhone;
    }

    public void setSupervisorPhone(String supervisorPhone) {
        this.supervisorPhone = supervisorPhone;
    }
    public String getConstructUnit() {
        return constructUnit;
    }

    public void setConstructUnit(String constructUnit) {
        this.constructUnit = constructUnit;
    }
    public String getConstructName() {
        return constructName;
    }

    public void setConstructName(String constructName) {
        this.constructName = constructName;
    }
    public String getConstructPhone() {
        return constructPhone;
    }

    public void setConstructPhone(String constructPhone) {
        this.constructPhone = constructPhone;
    }
    public String getSafetyName() {
        return safetyName;
    }

    public void setSafetyName(String safetyName) {
        this.safetyName = safetyName;
    }
    public String getSafetyPhone() {
        return safetyPhone;
    }

    public void setSafetyPhone(String safetyPhone) {
        this.safetyPhone = safetyPhone;
    }
    public String getQuality() {
        return quality;
    }

    public void setQuality(String quality) {
        this.quality = quality;
    }
    public String getCheckObject() {
        return checkObject;
    }

    public void setCheckObject(String checkObject) {
        this.checkObject = checkObject;
    }
    public String getHazardLevel() {
        return hazardLevel;
    }

    public void setHazardLevel(String hazardLevel) {
        this.hazardLevel = hazardLevel;
    }
    public String getHazardType() {
        return hazardType;
    }

    public void setHazardType(String hazardType) {
        this.hazardType = hazardType;
    }
    public String getPictures() {
        return pictures;
    }

    public void setPictures(String pictures) {
        this.pictures = pictures;
    }
    public String getVideos() {
        return videos;
    }

    public void setVideos(String videos) {
        this.videos = videos;
    }
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    
    public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}
	
	public List<FileUpload> getFileList() {
		return fileList;
	}

	public void setFileList(List<FileUpload> fileList) {
		this.fileList = fileList;
	}
	
	public List<FileUpload> getVideoList() {
		return videoList;
	}

	public void setVideoList(List<FileUpload> videoList) {
		this.videoList = videoList;
	}

	@Override
    public String toString() {
        return "SafetyQuality{" +
            "id=" + id +
            ", projectId=" + projectId +
            ", bidSection=" + bidSection +
            ", checkType=" + checkType +
            ", supervisorUnit=" + supervisorUnit +
            ", supervisorName=" + supervisorName +
            ", supervisorPhone=" + supervisorPhone +
            ", constructUnit=" + constructUnit +
            ", constructName=" + constructName +
            ", constructPhone=" + constructPhone +
            ", safetyName=" + safetyName +
            ", safetyPhone=" + safetyPhone +
            ", quality=" + quality +
            ", checkObject=" + checkObject +
            ", hazardLevel=" + hazardLevel +
            ", hazardType=" + hazardType +
            ", pictures=" + pictures +
            ", videos=" + videos +
            ", remark=" + remark +
            ", createTime=" + createTime +
            ", createUser=" + createUser +
            ", updateTime=" + updateTime +
        "}";
    }
}