package com.yinshu.tcps.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;

import java.util.Date;

import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.annotation.TableId;
import com.yinshu.utils.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

/**
 * 
 * 管理现场记录文件表
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@TableName("tcps_file")
public class TcpsFile extends PageParam<TcpsFile> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private String id;

    /**
     * 类型：项目监理、试验检测、安全质量……
     */
    private String category;

    /**
     * 项目监理、试验检测、安全质量……的记录ID
     */
    private String categoryRecordId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件路径
     */
    private String filePath;
    
    /**
     * 文件web路径
     */
    private String fileUrl;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 扩展属性
     */
    private String extAttr;

    /**
     * 是否已删除 0-否 1-是
     */
    private Boolean isDeleted;
    
    /**
     * 业务外键
     */
    private String fId;

    @TableField(exist = false)
    private MultipartFile file;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createdAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 更新人
     */
    private String updatedBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }
    public String getCategoryRecordId() {
        return categoryRecordId;
    }

    public void setCategoryRecordId(String categoryRecordId) {
        this.categoryRecordId = categoryRecordId;
    }
    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }
    public String getExtAttr() {
        return extAttr;
    }

    public void setExtAttr(String extAttr) {
        this.extAttr = extAttr;
    }
    public Boolean getDeleted() {
        return isDeleted;
    }
    public MultipartFile getFile() {
        return file;
    }
    public void setFile(MultipartFile file) {
        this.file = file;
    }
    public void setDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }
    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
    
    public Boolean getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}

	public String getfId() {
		return fId;
	}

	public void setfId(String fId) {
		this.fId = fId;
	}
	
	public String getFileUrl() {
		return fileUrl;
	}

	public void setFileUrl(String fileUrl) {
		this.fileUrl = fileUrl;
	}

	@Override
    public String toString() {
        return "File{" +
            "id=" + id +
            ", category=" + category +
            ", categoryRecordId=" + categoryRecordId +
            ", fileName=" + fileName +
            ", filePath=" + filePath +
            ", fileType=" + fileType +
            ", extAttr=" + extAttr +
            ", isDeleted=" + isDeleted +
            ", createdAt=" + createdAt +
            ", createdBy=" + createdBy +
            ", updatedAt=" + updatedAt +
            ", updatedBy=" + updatedBy +
        "}";
    }
}