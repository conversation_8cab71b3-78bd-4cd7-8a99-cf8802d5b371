package com.yinshu.tcps.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.TableId;
import com.yinshu.utils.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

/**
 * 
 * 工程项目主表
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@TableName("tcps_project")
public class Project extends PageParam<Project> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private String id;

    /**
     * 行政区域
     */
    private String region;

    /**
     * 合同日期
     */
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date contractDate;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目状态
     */
    private String projectStatus;

    /**
     * 项目类型
     */
    private String projectType;

    /**
     * 立项批文
     */
    private String approvalDoc;

    /**
     * 核准文号
     */
    private String approvalStandardDocNo;

    /**
     * 项目等级
     */
    private String projectLevel;

    /**
     * 项目归属
     */
    private String projectBelong;

    /**
     * 项目规模（米）
     */
    private Long projectScaleKm;

    /**
     * 项目法人单位
     */
    private String legalUnit;

    /**
     * 项目位置
     */
    private String projectLocation;
    /**
     * 经度
     */
    private Double lng;

    /**
     * 纬度
     */
    private Double lat;
    /**
     * 批复总投资（分）
     */
    private Long totalInvestment;

    /**
     * 地方配套资金（分）
     */
    private Long localFunds;

    /**
     * 审计金额（分）
     */
    private Long auditAmount;

    /**
     * 决算金额（分）
     */
    private Long settlementAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }
    public Date getContractDate() {
        return contractDate;
    }

    public void setContractDate(Date contractDate) {
        this.contractDate = contractDate;
    }
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }
    public String getProjectStatus() {
        return projectStatus;
    }

    public void setProjectStatus(String projectStatus) {
        this.projectStatus = projectStatus;
    }
    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType;
    }
    public String getApprovalDoc() {
        return approvalDoc;
    }

    public void setApprovalDoc(String approvalDoc) {
        this.approvalDoc = approvalDoc;
    }
    
    public String getApprovalStandardDocNo() {
        return approvalStandardDocNo;
    }

    public void setApprovalStandardDocNo(String approvalStandardDocNo) {
        this.approvalStandardDocNo = approvalStandardDocNo;
    }
    public String getProjectLevel() {
        return projectLevel;
    }

    public void setProjectLevel(String projectLevel) {
        this.projectLevel = projectLevel;
    }
    public String getProjectBelong() {
        return projectBelong;
    }

    public void setProjectBelong(String projectBelong) {
        this.projectBelong = projectBelong;
    }
    public Long getProjectScaleKm() {
        return projectScaleKm;
    }

    public void setProjectScaleKm(Long projectScaleKm) {
        this.projectScaleKm = projectScaleKm;
    }
    public String getLegalUnit() {
        return legalUnit;
    }

    public void setLegalUnit(String legalUnit) {
        this.legalUnit = legalUnit;
    }
    public String getProjectLocation() {
        return projectLocation;
    }

    public void setProjectLocation(String projectLocation) {
        this.projectLocation = projectLocation;
    }
    public Double getLng() {
        return lng;
    }

    public void setLng(Double lng) {
        this.lng = lng;
    }
    public Double getLat() {
        return lat;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }
    public Long getTotalInvestment() {
        return totalInvestment;
    }

    public void setTotalInvestment(Long totalInvestment) {
        this.totalInvestment = totalInvestment;
    }
    public Long getLocalFunds() {
        return localFunds;
    }

    public void setLocalFunds(Long localFunds) {
        this.localFunds = localFunds;
    }
    public Long getAuditAmount() {
        return auditAmount;
    }

    public void setAuditAmount(Long auditAmount) {
        this.auditAmount = auditAmount;
    }
    public Long getSettlementAmount() {
        return settlementAmount;
    }

    public void setSettlementAmount(Long settlementAmount) {
        this.settlementAmount = settlementAmount;
    }
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }
    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Override
    public String toString() {
        return "Project{" +
            "id=" + id +
            ", region=" + region +
            ", contractDate=" + contractDate +
            ", projectName=" + projectName +
            ", projectStatus=" + projectStatus +
            ", projectType=" + projectType +
            ", approvalDoc=" + approvalDoc +
            ", approvalStandardDocNo=" + approvalStandardDocNo +
            ", projectLevel=" + projectLevel +
            ", projectBelong=" + projectBelong +
            ", projectScaleKm=" + projectScaleKm +
            ", legalUnit=" + legalUnit +
            ", projectLocation=" + projectLocation +
            ", lng=" + lng +
            ", lat=" + lat +
            ", totalInvestment=" + totalInvestment +
            ", localFunds=" + localFunds +
            ", auditAmount=" + auditAmount +
            ", settlementAmount=" + settlementAmount +
            ", remark=" + remark +
            ", createdAt=" + createdAt +
            ", updatedAt=" + updatedAt +
            ", createdBy=" + createdBy +
            ", updatedBy=" + updatedBy +
        "}";
    }
}