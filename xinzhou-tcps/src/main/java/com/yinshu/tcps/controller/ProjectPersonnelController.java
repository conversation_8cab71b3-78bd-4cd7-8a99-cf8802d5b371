package com.yinshu.tcps.controller;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.*;
import com.yinshu.tcps.entity.ProjectPersonnel;
import com.yinshu.tcps.manager.ProjectPersonnelManager;
import com.yinshu.common.ApiConstant;

import org.springframework.web.bind.annotation.RestController;

/**
 *
 * 项目人员表 
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@RestController
@RequestMapping(ApiConstant.API_TCPS_PREFIX +"/sqc/project/personnel")
public class ProjectPersonnelController {

	@Autowired
	private ProjectPersonnelManager projectPersonnelManager;

	
	@GetMapping("/pageList")
    public ResultVO<?> pageList(ProjectPersonnel entity) {
		IPage<ProjectPersonnel> resultList = projectPersonnelManager.queryPageList(entity);
		return new ResultVO<>(resultList);
    }
    
    @GetMapping("/queryList")
    public ResultVO<?> queryList(ProjectPersonnel entity) {
		List<ProjectPersonnel> resultList = projectPersonnelManager.queryList(entity);
		return new ResultVO<>(resultList);
    }

	@GetMapping("/getById/{id}")
	public ResultVO<?> getById(@PathVariable("id") String id){
	return new ResultVO<>(projectPersonnelManager.getById(id));
	}
    
	@PostMapping("/create")
    public ResultVO<?> create(@RequestBody ProjectPersonnel entity){
		projectPersonnelManager.save(entity);
		return new ResultVO<>(entity);
	}
	
	@PutMapping("/update")
    public ResultVO<?> update(@RequestBody ProjectPersonnel entity){
		projectPersonnelManager.update(entity);
		return new ResultVO<>(entity);
	}

	@DeleteMapping("/remove/{ids}")
	public ResultVO<?> remove(@PathVariable List<String> ids){
		projectPersonnelManager.remove(ids);
		return new ResultVO<>(ids);
	}


}
