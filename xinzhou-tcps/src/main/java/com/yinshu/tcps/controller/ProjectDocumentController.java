package com.yinshu.tcps.controller;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.RequestMapping;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.*;
import com.yinshu.tcps.entity.ProjectDocument;
import com.yinshu.tcps.manager.ProjectDocumentManager;
import com.yinshu.common.ApiConstant;

import org.springframework.web.bind.annotation.RestController;

/**
 *
 * 项目资料表 
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@RestController
@RequestMapping(ApiConstant.API_TCPS_PREFIX +"/sqc/project/document")
public class ProjectDocumentController {

	@Autowired
	private ProjectDocumentManager projectDocumentManager;

	@Autowired
	private Environment environment;
	
	@GetMapping("/pageList")
    public ResultVO<?> pageList(ProjectDocument entity) {
		IPage<ProjectDocument> resultList = projectDocumentManager.queryPageList(entity);
		return new ResultVO<>(resultList);
    }
    
    @GetMapping("/queryList")
    public ResultVO<?> queryList(ProjectDocument entity) {
		List<ProjectDocument> resultList = projectDocumentManager.queryList(entity);
		for(ProjectDocument file : resultList) {
        	file.setFileUrl(file.getFilePath() == null ? null : environment.getProperty("system.oss-path") + file.getFilePath().replaceAll("\\\\", "/"));
        }
		return new ResultVO<>(resultList);
    }

	@GetMapping("/getById/{id}")
	public ResultVO<?> getById(@PathVariable("id") String id){
	return new ResultVO<>(projectDocumentManager.getById(id));
	}
    
	@PostMapping("/create")
    public ResultVO<?> create(@ModelAttribute ProjectDocument entity){
		projectDocumentManager.save(entity);
		entity.setFile(null);
		return new ResultVO<>(entity);
	}
	
	@PutMapping("/update")
    public ResultVO<?> update(@ModelAttribute ProjectDocument entity){
		projectDocumentManager.update(entity);
		entity.setFile(null);
		return new ResultVO<>(entity);
	}

	@DeleteMapping("/remove/{ids}")
	public ResultVO<?> remove(@PathVariable List<String> ids){
		projectDocumentManager.remove(ids);
		return new ResultVO<>(ids);
	}


}
