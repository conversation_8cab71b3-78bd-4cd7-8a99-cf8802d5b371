package com.yinshu.tcps.controller;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.tcps.entity.Project;
import com.yinshu.tcps.service.DrivingMonitorService;
import com.yinshu.tcps.service.MonitorPictureService;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 行业监管看板
 *
 */
@RestController
@RequestMapping(ApiConstant.API_TCPS_PREFIX + "/monitor-picture")
public class MonitorPictureController {

    @Autowired
    private MonitorPictureService monitorPictureService;

    /**
     * 项目树结构
     */
    @PostMapping("/getProjectTree")
    public ResultVO<?> getProjectTree(@RequestBody Project entity) {
        return ResultVO.suc(monitorPictureService.getProjectTree(entity));
    }

    /**
     * 门禁树结构
     */
    @PostMapping("/getAccessControlTree")
    public ResultVO<?> getAccessControlTree(@RequestBody Project entity) {
        return ResultVO.suc(monitorPictureService.getAccessControlTree(entity));
    }

    /**
     * 门禁记录
     * @param query
     * @return
     */
    @PostMapping("/getAccessControlData")
    public ResultVO<?> getAccessControlData(@RequestBody JSONObject query) {
        return ResultVO.suc(monitorPictureService.getAccessControlData(query));
    }

}
