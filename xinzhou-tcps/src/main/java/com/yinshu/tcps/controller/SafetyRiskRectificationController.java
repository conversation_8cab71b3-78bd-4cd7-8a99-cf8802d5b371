package com.yinshu.tcps.controller;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.fast.excel.util.ExcelUtils;
import com.yinshu.tcps.service.SafetyRiskRectificationService;
import com.yinshu.utils.ResultVO;
import org.springframework.security.core.parameters.P;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping(ApiConstant.API_TCPS_PREFIX + "/safety-risk/rectification")
public class SafetyRiskRectificationController {

    @Resource
    private SafetyRiskRectificationService safetyRiskRectificationService;

    /**
     * 安全隐患整改
     * @param query
     * @return
     */
    @PostMapping("/getRectificationListData")
    public ResultVO<?> getRectificationListData(@RequestBody JSONObject query) {
        return ResultVO.suc(safetyRiskRectificationService.getRectificationListData(query));
    }

    /**
     * 安全隐患整改详情
     * @param query
     * @return
     */
    @PostMapping("/getRectificationDetailData")
    public ResultVO<?> getRectificationDetailData(@RequestBody JSONObject query) {
        return ResultVO.suc(safetyRiskRectificationService.getRectificationDetailData(query));
    }

    /**
     * 企业安全评估
     * @param query
     * @return
     */
    @PostMapping("/getBusinessListData")
    public ResultVO<?> getBusinessListData(@RequestBody JSONObject query) {
        return ResultVO.suc(safetyRiskRectificationService.getBusinessListData(query));
    }

    /**
     * 企业综合评分走势日
     * @param query
     * @return
     */
    @PostMapping("/getComprehensiveScoreTrend")
    public ResultVO<?> getComprehensiveScoreTrend(@RequestBody JSONObject query) {
        return ResultVO.suc(safetyRiskRectificationService.getComprehensiveScoreTrend(query));
    }

    /**
     * 报警构成
     * @param query
     * @return
     */
    @PostMapping("/getAlarmComposition")
    public ResultVO<?> getAlarmComposition(@RequestBody JSONObject query) {
        return ResultVO.suc(safetyRiskRectificationService.getAlarmComposition(query));
    }

    /**
     * 车辆风险排行
     * @param query
     * @return
     */
    @PostMapping("/getVehicleRiskRanking")
    public ResultVO<?> getVehicleRiskRanking(@RequestBody JSONObject query) {
        return ResultVO.suc(safetyRiskRectificationService.getVehicleRiskRanking(query));
    }

    /**
     * 车辆安全评估
     * @param query
     * @return
     */
    @PostMapping("/getVehicleList")
    public ResultVO<?> getVehicleList(@RequestBody JSONObject query) {
        return ResultVO.suc(safetyRiskRectificationService.getVehicleList(query));
    }

    /**
     * 车辆出车日历
     * @param query
     * @return
     */
    @PostMapping("/getVehicleCalendar")
    public ResultVO<?> getVehicleCalendar(@RequestBody JSONObject query) {
        return ResultVO.suc(safetyRiskRectificationService.getVehicleCalendar(query));
    }

    /**
     * 车辆运营信息
     * @param query
     * @return
     */
    @PostMapping("/getCarOperationInfo")
    public ResultVO<?> getCarOperationInfo(@RequestBody JSONObject query) {
        return ResultVO.suc(safetyRiskRectificationService.getCarOperationInfo(query));
    }

    /**
     * 百公里报警趋势
     * @param query
     * @return
     */
    @PostMapping("/get100KmAlarmTrend")
    public ResultVO<?> get100KmAlarmTrend(@RequestBody JSONObject query) {
        return ResultVO.suc(safetyRiskRectificationService.get100KmAlarmTrend(query));
    }

    /**
     * 车辆报警构成
     * @param query
     * @return
     */
    @PostMapping("/getCarAlarmComposition")
    public ResultVO<?> getCarAlarmComposition(@RequestBody JSONObject query) {
        return ResultVO.suc(safetyRiskRectificationService.getCarAlarmComposition(query));
    }

    /**
     * 高峰运行时段
     */
    @PostMapping("/getPeakRunningPeriod")
    public ResultVO<?> getPeakRunningPeriod(@RequestBody JSONObject query) {
        return ResultVO.suc(safetyRiskRectificationService.getPeakRunningPeriod(query));
    }

    /**
     * 导出
     */
    @PostMapping("getExport")
    public void getExport(@RequestBody JSONObject query) {
       safetyRiskRectificationService.getExport(query);
    }

}
