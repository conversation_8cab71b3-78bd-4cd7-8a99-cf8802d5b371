package com.yinshu.tcps.controller;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.*;
import com.yinshu.tcps.entity.ProjectCamera;
import com.yinshu.tcps.manager.ProjectCameraManager;
import com.yinshu.common.ApiConstant;

import org.springframework.web.bind.annotation.RestController;

/**
 *
 * 项目视频监控表 
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@RestController
@RequestMapping(ApiConstant.API_TCPS_PREFIX +"/sqc/project/camera")
public class ProjectCameraController {

	@Autowired
	private ProjectCameraManager projectCameraManager;

	
	@GetMapping("/pageList")
    public ResultVO<?> pageList(ProjectCamera entity) {
		IPage<ProjectCamera> resultList = projectCameraManager.queryPageList(entity);
		return new ResultVO<>(resultList);
    }
    
    @GetMapping("/queryList")
    public ResultVO<?> queryList(ProjectCamera entity) {
		List<ProjectCamera> resultList = projectCameraManager.queryList(entity);
		return new ResultVO<>(resultList);
    }

	@GetMapping("/getById/{id}")
	public ResultVO<?> getById(@PathVariable("id") String id){
	return new ResultVO<>(projectCameraManager.getById(id));
	}
    
	@PostMapping("/create")
    public ResultVO<?> create(@RequestBody ProjectCamera entity){
		projectCameraManager.save(entity);
		return new ResultVO<>(entity);
	}
	
	@PutMapping("/update")
    public ResultVO<?> update(@RequestBody ProjectCamera entity){
		projectCameraManager.update(entity);
		return new ResultVO<>(entity);
	}

	@DeleteMapping("/remove/{ids}")
	public ResultVO<?> remove(@PathVariable List<String> ids){
		projectCameraManager.remove(ids);
		return new ResultVO<>(ids);
	}


}
