package com.yinshu.tcps.controller;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.RequestMapping;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.*;
import com.yinshu.tcps.entity.ProjectPermit;
import com.yinshu.tcps.manager.ProjectPermitManager;
import com.yinshu.common.ApiConstant;

import org.springframework.web.bind.annotation.RestController;

/**
 *
 * 项目许可及证书 
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@RestController
@RequestMapping(ApiConstant.API_TCPS_PREFIX +"/sqc/project/permit")
public class ProjectPermitController {

	@Autowired
	private ProjectPermitManager projectPermitManager;

	@Autowired
	private Environment environment;
	
	@GetMapping("/pageList")
    public ResultVO<?> pageList(ProjectPermit entity) {
		IPage<ProjectPermit> resultList = projectPermitManager.queryPageList(entity);
		return new ResultVO<>(resultList);
    }
    
    @GetMapping("/queryList")
    public ResultVO<?> queryList(ProjectPermit entity) {
		List<ProjectPermit> resultList = projectPermitManager.queryList(entity);
		for(ProjectPermit file : resultList) {
        	file.setFileUrl(file.getFilePath() == null ? null : environment.getProperty("system.oss-path") + file.getFilePath().replaceAll("\\\\", "/"));
        }
		return new ResultVO<>(resultList);
    }

	@GetMapping("/getById/{id}")
	public ResultVO<?> getById(@PathVariable("id") String id){
	return new ResultVO<>(projectPermitManager.getById(id));
	}
    
	@PostMapping("/create")
    public ResultVO<?> create(@ModelAttribute ProjectPermit entity){
		projectPermitManager.save(entity);
		entity.setFile(null);
		return new ResultVO<>(entity);
	}
	
	@PutMapping("/update")
    public ResultVO<?> update(@ModelAttribute ProjectPermit entity){
		projectPermitManager.update(entity);
		entity.setFile(null);
		return new ResultVO<>(entity);
	}

	@DeleteMapping("/remove/{ids}")
	public ResultVO<?> remove(@PathVariable List<String> ids){
		projectPermitManager.remove(ids);
		return new ResultVO<>(ids);
	}


}
