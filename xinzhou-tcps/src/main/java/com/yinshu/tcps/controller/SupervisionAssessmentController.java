package com.yinshu.tcps.controller;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.fast.excel.util.ExcelUtils;
import com.yinshu.tcps.service.SupervisionAssessmentService;
import com.yinshu.tcps.vo.SupervisionAssessmentVO.CompletenessRateStatistics.CompletenessRateStatisticsEnterpriseVO;
import com.yinshu.tcps.vo.SupervisionAssessmentVO.CompletenessRateStatistics.CompletenessRateStatisticsOperatorVO;
import com.yinshu.tcps.vo.SupervisionAssessmentVO.CompletenessRateStatistics.CompletenessRateStatisticsOrgVO;
import com.yinshu.tcps.vo.SupervisionAssessmentVO.DrivingStatistics.DrivingStatisticsEnterpriseVO;
import com.yinshu.tcps.vo.SupervisionAssessmentVO.DrivingStatistics.DrivingStatisticsOrgVO;
import com.yinshu.tcps.vo.SupervisionAssessmentVO.GpsDriftRateStatistics.GpsDriftRateStatisticsEnterpriseVO;
import com.yinshu.tcps.vo.SupervisionAssessmentVO.GpsDriftRateStatistics.GpsDriftRateStatisticsOperatorVO;
import com.yinshu.tcps.vo.SupervisionAssessmentVO.GpsDriftRateStatistics.GpsDriftRateStatisticsOrgVO;
import com.yinshu.tcps.vo.SupervisionAssessmentVO.MonitorExamine.MonitorExamineEnterpriseVO;
import com.yinshu.tcps.vo.SupervisionAssessmentVO.MonitorExamine.MonitorExamineOperatorVO;
import com.yinshu.tcps.vo.SupervisionAssessmentVO.MonitorExamine.MonitorExamineOrgVO;
import com.yinshu.tcps.vo.SupervisionAssessmentVO.MonitorStatisticsDetails.enterprise.*;
import com.yinshu.tcps.vo.SupervisionAssessmentVO.MonitorStatisticsDetails.operator.*;
import com.yinshu.tcps.vo.SupervisionAssessmentVO.OffLineStatistics.OffLineStatisticsVO;
import com.yinshu.tcps.vo.SupervisionAssessmentVO.PlatformUnicomRateStatistics.PlatformUnicomRateStatisticsOperatorVO;
import com.yinshu.tcps.vo.SupervisionAssessmentVO.QualifiedRateStatistics.QualifiedRateStatisticsEnterpriseVO;
import com.yinshu.tcps.vo.SupervisionAssessmentVO.QualifiedRateStatistics.QualifiedRateStatisticsOperatorVO;
import com.yinshu.tcps.vo.SupervisionAssessmentVO.QualifiedRateStatistics.QualifiedRateStatisticsOrgVO;
import com.yinshu.tcps.vo.SupervisionAssessmentVO.RateStatistics.RateStatisticsEnterpriseVO;
import com.yinshu.tcps.vo.SupervisionAssessmentVO.RateStatistics.RateStatisticsOrgVO;
import com.yinshu.tcps.vo.SupervisionAssessmentVO.Speedstatistics.SpeedstatisticsEnterpriseVO;
import com.yinshu.tcps.vo.SupervisionAssessmentVO.Speedstatistics.SpeedstatisticsOrgVO;
import com.yinshu.tcps.vo.SupervisionAssessmentVO.WarnSuperviseRateStatistics.WarnSuperviseRateStatisticsEnterpriseVO;
import com.yinshu.tcps.vo.SupervisionAssessmentVO.WarnSuperviseRateStatistics.WarnSuperviseRateStatisticsOrgVO;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 监督考核管理
 */
@RestController("tcpsSupervisionAssessmentController")
@RequestMapping(ApiConstant.API_TCPS_PREFIX + "/supervisionAssessment")
public class SupervisionAssessmentController {
    @Autowired
    private SupervisionAssessmentService supervisionAssessmentService;
    // ----------报警处理率统计------------
    /**
     * 报警处理率统计-按月
     * @param query
     * @return
     */
    @PostMapping("/statics/rateStatisticsMth")
    public ResultVO<?> rateStatisticsMth(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.rateStatisticsMth(query);
        return ResultVO.suc(object);
    }

    /**
     * 报警处理率统计-导出
     * @param query
     * @return
     */
    @PostMapping("/statics/exportRateStatisticsMth")
    public void exportRateStatisticsMth(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.rateStatisticsMth(query);
        if (query.getString("navType").equals("org")){
            ExcelUtils.exportExcelSheet(RateStatisticsOrgVO.class, object);
        }else {
            ExcelUtils.exportExcelSheet(RateStatisticsEnterpriseVO.class, object);
        }
    }

    /**
     * 报警处理率统计-按年
     * @param query
     * @return
     */
    @PostMapping("/statics/rateStatisticsYear")
    public ResultVO<?> rateStatisticsYear(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.rateStatisticsYear(query);
        return ResultVO.suc(object);
    }

    /**
     * 报警处理率统计-导出
     * @param query
     * @return
     */
    @PostMapping("/statics/exportRateStatisticsYear")
    public void exportRateStatisticsYear(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.rateStatisticsYear(query);
        if (query.getString("navType").equals("org")){
            ExcelUtils.exportExcelSheet(RateStatisticsOrgVO.class, object);
        }else {
            ExcelUtils.exportExcelSheet(RateStatisticsEnterpriseVO.class, object);
        }
    }

    /**
     * 报警处理率统计-按季
     * @param query
     * @return
     */
    @PostMapping("/statics/rateStatisticsQuarter")
    public ResultVO<?> rateStatisticsQuarter(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.rateStatisticsQuarter(query);
        return ResultVO.suc(object);
    }

    /**
     * 报警处理率统计-导出
     * @param query
     * @return
     */
    @PostMapping("/statics/exportRateStatisticsQuarter")
    public void exportRateStatisticsQuarter(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.rateStatisticsQuarter(query);
        if (query.getString("navType").equals("org")){
            ExcelUtils.exportExcelSheet(RateStatisticsOrgVO.class, object);
        }else {
            ExcelUtils.exportExcelSheet(RateStatisticsEnterpriseVO.class, object);
        }
    }


    // ----------报警督办率统计---------
    /**
     * 报警督办率统计-按月
     * @param query
     * @return
     */
    @PostMapping("/statics/warnSuperviseRateStatisticsMth")
    public ResultVO<?> warnSuperviseRateStatisticsMth(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.warnSuperviseRateStatisticsMth(query);
        return ResultVO.suc(object);
    }

    /**
     * 报警督办率统计-导出
     * @param query
     * @return
     */
    @PostMapping("/statics/exportWarnSuperviseRateStatisticsMth")
    public void exportWarnSuperviseRateStatisticsMth(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.warnSuperviseRateStatisticsMth(query);
        if (query.getString("navType").equals("org")){
            ExcelUtils.exportExcelSheet(WarnSuperviseRateStatisticsOrgVO.class, object);
        }else {
            ExcelUtils.exportExcelSheet(WarnSuperviseRateStatisticsEnterpriseVO.class, object);
        }

    }

    /**
     * 报警督办率统计-按年
     * @param query
     * @return
     */
    @PostMapping("/statics/warnSuperviseRateStatisticsYear")
    public ResultVO<?> warnSuperviseRateStatisticsYear(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.warnSuperviseRateStatisticsYear(query);
        return ResultVO.suc(object);
    }

    /**
     * 报警督办率统计-导出
     * @param query
     * @return
     */
    @PostMapping("/statics/exportWarnSuperviseRateStatisticsYear")
    public void exportWarnSuperviseRateStatisticsYear(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.warnSuperviseRateStatisticsYear(query);
        if (query.getString("navType").equals("org")){
            ExcelUtils.exportExcelSheet(WarnSuperviseRateStatisticsOrgVO.class, object);
        }else {
            ExcelUtils.exportExcelSheet(WarnSuperviseRateStatisticsEnterpriseVO.class, object);
        }

    }

    /**
     * 报警督办率统计-按季
     * @param query
     * @return
     */
    @PostMapping("/statics/warnSuperviseRateStatisticsQuarter")
    public ResultVO<?> warnSuperviseRateStatisticsQuarter(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.warnSuperviseRateStatisticsQuarter(query);
        return ResultVO.suc(object);
    }

    /**
     * 报警督办率统计-导出
     * @param query
     * @return
     */
    @PostMapping("/statics/exportWarnSuperviseRateStatisticsQuarter")
    public void exportWarnSuperviseRateStatisticsQuarter(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.warnSuperviseRateStatisticsQuarter(query);
        if (query.getString("navType").equals("org")){
            ExcelUtils.exportExcelSheet(WarnSuperviseRateStatisticsOrgVO.class, object);
        }else {
            ExcelUtils.exportExcelSheet(WarnSuperviseRateStatisticsEnterpriseVO.class, object);
        }

    }
    // ------------超速驾驶统计--------------
    /**
     * 超速驾驶统计-按月
     * @param query
     * @return
     */
    @PostMapping("/statics/speedstatisticsmth")
    public ResultVO<?> speedstatisticsmth(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.speedstatisticsmth(query);
        return ResultVO.suc(object);
    }

    /**
     * 超速驾驶统计-导出
     * @param query
     * @return
     */
    @PostMapping("/statics/exportSpeedstatisticsmth")
    public void exportSpeedstatisticsmth(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.speedstatisticsmth(query);
        if (query.getString("navType").equals("org")){
            ExcelUtils.exportExcelSheet(SpeedstatisticsOrgVO.class, object);
        }else {
            ExcelUtils.exportExcelSheet(SpeedstatisticsEnterpriseVO.class, object);
        }
    }

    /**
     * 超速驾驶统计-按年
     * @param query
     * @return
     */
    @PostMapping("/statics/speedstatisticsyear")
    public ResultVO<?> speedstatisticsyear(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.speedstatisticsyear(query);
        return ResultVO.suc(object);
    }

    /**
     * 超速驾驶统计-导出
     * @param query
     * @return
     */
    @PostMapping("/statics/exportSpeedstatisticsyear")
    public void exportSpeedstatisticsyear(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.speedstatisticsyear(query);
        if (query.getString("navType").equals("org")){
            ExcelUtils.exportExcelSheet(SpeedstatisticsOrgVO.class, object);
        }else {
            ExcelUtils.exportExcelSheet(SpeedstatisticsEnterpriseVO.class, object);
        }
    }

    /**
     * 超速驾驶统计-按季
     * @param query
     * @return
     */
    @PostMapping("/statics/speedStatisticsQuarter")
    public ResultVO<?> speedStatisticsQuarter(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.speedStatisticsQuarter(query);
        return ResultVO.suc(object);
    }

    /**
     * 超速驾驶统计-导出
     * @param query
     * @return
     */
    @PostMapping("/statics/exportSpeedstatisticsQuarter")
    public void exportSpeedstatisticsQuarter(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.speedStatisticsQuarter(query);
        if (query.getString("navType").equals("org")){
            ExcelUtils.exportExcelSheet(SpeedstatisticsOrgVO.class, object);
        }else {
            ExcelUtils.exportExcelSheet(SpeedstatisticsEnterpriseVO.class, object);
        }
    }
    // -------------疲劳驾驶统计 ----------------
    /**
     * 疲劳驾驶统计-按月
     * @param query
     * @return
     */
    @PostMapping("/statics/drivingStatisticsMth")
    public ResultVO<?> drivingStatisticsMth(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.drivingStatisticsMth(query);
        return ResultVO.suc(object);
    }

    /**
     * 疲劳驾驶统计-导出
     * @param query
     * @return
     */
    @PostMapping("/statics/exportDrivingStatisticsMth")
    public void exportDrivingStatisticsMth(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.drivingStatisticsMth(query);
        if (query.getString("navType").equals("org")){
            ExcelUtils.exportExcelSheet(DrivingStatisticsOrgVO.class, object);
        }else {
            ExcelUtils.exportExcelSheet(DrivingStatisticsEnterpriseVO.class, object);
        }
    }

    /**
     * 疲劳驾驶统计-按年
     * @param query
     * @return
     */
    @PostMapping("/statics/drivingStatisticsYear")
    public ResultVO<?> drivingStatisticsYear(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.drivingStatisticsYear(query);
        return ResultVO.suc(object);
    }

    /**
     * 疲劳驾驶统计-导出
     * @param query
     * @return
     */
    @PostMapping("/statics/exportDrivingStatisticsYear")
    public void exportDrivingStatisticsYear(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.drivingStatisticsYear(query);
        if (query.getString("navType").equals("org")){
            ExcelUtils.exportExcelSheet(DrivingStatisticsOrgVO.class, object);
        }else {
            ExcelUtils.exportExcelSheet(DrivingStatisticsEnterpriseVO.class, object);
        }
    }

    /**
     *  疲劳驾驶统计-按季
     * @param query
     * @return
     */
    @PostMapping("/statics/drivingStatisticsQuarter")
    public ResultVO<?> drivingStatisticsQuarter(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.drivingStatisticsQuarter(query);
        return ResultVO.suc(object);
    }

    /**
     * 疲劳驾驶统计-导出
     * @param query
     * @return
     */
    @PostMapping("/statics/exportDrivingStatisticsQuarter")
    public void exportDrivingStatisticsQuarter(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.drivingStatisticsQuarter(query);
        if (query.getString("navType").equals("org")){
            ExcelUtils.exportExcelSheet(DrivingStatisticsOrgVO.class, object);
        }else {
            ExcelUtils.exportExcelSheet(DrivingStatisticsEnterpriseVO.class, object);
        }
    }
    // -------------平台联通率统计--------------
    /**
     * 平台联通率统计-按月
     * @param query
     * @return
     */
    @PostMapping("/statics/platformUnicomRateStatisticsMth")
    public ResultVO<?> platformUnicomRateStatisticsMth(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.platformUnicomRateStatisticsMth(query);
        return ResultVO.suc(object);
    }

    /**
     * 平台联通率统计-导出
     * @param query
     * @return
     */
    @PostMapping("/statics/exportPlatformUnicomRateStatisticsMth")
    public void exportPlatformUnicomRateStatisticsMth(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.platformUnicomRateStatisticsMth(query);
        ExcelUtils.exportExcelSheet(PlatformUnicomRateStatisticsOperatorVO.class, object);
    }

    /**
     * 平台联通率统计-按年
     * @param query
     * @return
     */
    @PostMapping("/statics/platformUnicomRateStatisticsYear")
    public ResultVO<?> platformUnicomRateStatisticsYear(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.platformUnicomRateStatisticsYear(query);
        return ResultVO.suc(object);
    }

    /**
     * 平台联通率统计-导出
     * @param query
     * @return
     */
    @PostMapping("/statics/exportPlatformUnicomRateStatisticsYear")
    public void exportPlatformUnicomRateStatisticsYear(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.platformUnicomRateStatisticsYear(query);
        ExcelUtils.exportExcelSheet(PlatformUnicomRateStatisticsOperatorVO.class, object);
    }

    /**
     * 平台联通率统计-按季
     * @param query
     * @return
     */
    @PostMapping("/statics/platformUnicomRateStatisticsQuarter")
    public ResultVO<?> platformUnicomRateStatisticsQuarter(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.platformUnicomRateStatisticsQuarter(query);
        return ResultVO.suc(object);
    }

    /**
     * 平台联通率统计-导出
     * @param query
     * @return
     */
    @PostMapping("/statics/exportPlatformUnicomRateStatisticsQuarter")
    public void exportPlatformUnicomRateStatisticsQuarter(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.platformUnicomRateStatisticsQuarter(query);
        ExcelUtils.exportExcelSheet(PlatformUnicomRateStatisticsOperatorVO.class, object);
    }
    // ------------卫星定位漂移率统计----------------
    /**
     * 卫星定位漂移率统计-按月
     * @param query
     * @return
     */
    @PostMapping("/statics/gpsDriftRateStatisticsMth")
    public ResultVO<?> gpsDriftRateStatisticsMth(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.gpsDriftRateStatisticsMth(query);
        return ResultVO.suc(object);
    }

    /**
     * 卫星定位漂移率统计-导出
     * @param query
     * @return
     */
    @PostMapping("/statics/exportQpsDriftRateStatisticsMth")
    public void exportQpsDriftRateStatisticsMth(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.gpsDriftRateStatisticsMth(query);
        if (query.getString("navType").equals("org")){
            ExcelUtils.exportExcelSheet(GpsDriftRateStatisticsOrgVO.class, object);
        } else if (query.getString("navType").equals("operator")) {
            ExcelUtils.exportExcelSheet(GpsDriftRateStatisticsOperatorVO.class, object);
        }else {
            ExcelUtils.exportExcelSheet(GpsDriftRateStatisticsEnterpriseVO.class, object);
        }
    }

    /**
     * 卫星定位漂移率统计-按年
     * @param query
     * @return
     */
    @PostMapping("/statics/gpsDriftRateStatisticsYear")
    public ResultVO<?> gpsDriftRateStatisticsYear(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.gpsDriftRateStatisticsYear(query);
        return ResultVO.suc(object);
    }

    /**
     * 卫星定位漂移率统计-导出
     * @param query
     * @return
     */
    @PostMapping("/statics/exportQpsDriftRateStatisticsYear")
    public void exportQpsDriftRateStatisticsYear(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.gpsDriftRateStatisticsYear(query);
        if (query.getString("navType").equals("org")){
            ExcelUtils.exportExcelSheet(GpsDriftRateStatisticsOrgVO.class, object);
        } else if (query.getString("navType").equals("operator")) {
            ExcelUtils.exportExcelSheet(GpsDriftRateStatisticsOperatorVO.class, object);
        }else {
            ExcelUtils.exportExcelSheet(GpsDriftRateStatisticsEnterpriseVO.class, object);
        }
    }

    /**
     * 卫星定位漂移率统计-按季
     * @param query
     * @return
     */
    @PostMapping("/statics/gpsDriftRateStatisticsQuarter")
    public ResultVO<?> gpsDriftRateStatisticsQuarter(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.gpsDriftRateStatisticsQuarter(query);
        return ResultVO.suc(object);
    }

    /**
     * 卫星定位漂移率统计-导出
     * @param query
     * @return
     */
    @PostMapping("/statics/exportQpsDriftRateStatisticsQuarter")
    public void exportQpsDriftRateStatisticsQuarter(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.gpsDriftRateStatisticsQuarter(query);
        if (query.getString("navType").equals("org")){
            ExcelUtils.exportExcelSheet(GpsDriftRateStatisticsOrgVO.class, object);
        } else if (query.getString("navType").equals("operator")) {
            ExcelUtils.exportExcelSheet(GpsDriftRateStatisticsOperatorVO.class, object);
        }else {
            ExcelUtils.exportExcelSheet(GpsDriftRateStatisticsEnterpriseVO.class, object);
        }
    }

    // ----------数据合格率统计 ---------------
    /**
     * 数据合格率统计-按月
     * @param query
     * @return
     */
    @PostMapping("/statics/qualifiedRateStatisticsMth")
    public ResultVO<?> qualifiedRateStatisticsMth(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.qualifiedRateStatisticsMth(query);
        return ResultVO.suc(object);
    }

    /**
     * 数据合格率统计-按月
     * @param query
     * @return
     */
    @PostMapping("/statics/exportQualifiedRateStatisticsMth")
    public void exportQualifiedRateStatisticsMth(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.qualifiedRateStatisticsMth(query);
        if (query.getString("navType").equals("org")){
            ExcelUtils.exportExcelSheet(QualifiedRateStatisticsOrgVO.class, object);
        } else if (query.getString("navType").equals("operator")) {
            ExcelUtils.exportExcelSheet(QualifiedRateStatisticsOperatorVO.class, object);
        }else {
            ExcelUtils.exportExcelSheet(QualifiedRateStatisticsEnterpriseVO.class, object);
        }
    }

    /**
     * 数据合格率统计-按年
     * @param query
     * @return
     */
    @PostMapping("/statics/qualifiedRateStatisticsYear")
    public ResultVO<?> qualifiedRateStatisticsYear(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.qualifiedRateStatisticsYear(query);
        return ResultVO.suc(object);
    }

    /**
     * 数据合格率统计-按月
     * @param query
     * @return
     */
    @PostMapping("/statics/exportQualifiedRateStatisticsYear")
    public void exportQualifiedRateStatisticsYear(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.qualifiedRateStatisticsYear(query);
        if (query.getString("navType").equals("org")){
            ExcelUtils.exportExcelSheet(QualifiedRateStatisticsOrgVO.class, object);
        } else if (query.getString("navType").equals("operator")) {
            ExcelUtils.exportExcelSheet(QualifiedRateStatisticsOperatorVO.class, object);
        }else {
            ExcelUtils.exportExcelSheet(QualifiedRateStatisticsEnterpriseVO.class, object);
        }
    }

    /**
     * 数据合格率统计-按季
     * @param query
     * @return
     */
    @PostMapping("/statics/qualifiedRateStatisticsQuarter")
    public ResultVO<?> qualifiedRateStatisticsQuarter(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.qualifiedRateStatisticsQuarter(query);
        return ResultVO.suc(object);
    }

    /**
     * 数据合格率统计-按季
     * @param query
     * @return
     */
    @PostMapping("/statics/exportQualifiedRateStatisticsQuarter")
    public void exportQualifiedRateStatisticsQuarter(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.qualifiedRateStatisticsQuarter(query);
        if (query.getString("navType").equals("org")){
            ExcelUtils.exportExcelSheet(QualifiedRateStatisticsOrgVO.class, object);
        } else if (query.getString("navType").equals("operator")) {
            ExcelUtils.exportExcelSheet(QualifiedRateStatisticsOperatorVO.class, object);
        }else {
            ExcelUtils.exportExcelSheet(QualifiedRateStatisticsEnterpriseVO.class, object);
        }
    }
    // -----------轨迹完整率统计--------------------
    /**
     * 轨迹完整率统计-按月
     * @param query
     * @return
     */
    @PostMapping("/statics/completenessRateStatisticsMth")
    public ResultVO<?> completenessRateStatisticsMth(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.completenessRateStatisticsMth(query);
        return ResultVO.suc(object);
    }

    /**
     * 轨迹完整率统计-按月 导出
     * @param query
     * @return
     */
    @PostMapping("/statics/exportCompletenessRateStatisticsMth")
    public void exportCompletenessRateStatisticsMth(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.completenessRateStatisticsMth(query);
        if (query.getString("navType").equals("org")){
            ExcelUtils.exportExcelSheet(CompletenessRateStatisticsOrgVO.class, object);
        } else if (query.getString("navType").equals("operator")) {
            ExcelUtils.exportExcelSheet(CompletenessRateStatisticsOperatorVO.class, object);
        }else {
            ExcelUtils.exportExcelSheet(CompletenessRateStatisticsEnterpriseVO.class, object);
        }
    }

    /**
     * 轨迹完整率统计-按年
     * @param query
     * @return
     */
    @PostMapping("/statics/completenessRateStatisticsYear")
    public ResultVO<?> completenessRateStatisticsYear(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.completenessRateStatisticsYear(query);
        return ResultVO.suc(object);
    }

    /**
     * 轨迹完整率统计-按年 导出
     * @param query
     * @return
     */
    @PostMapping("/statics/exportCompletenessRateStatisticsYear")
    public void exportCompletenessRateStatisticsYear(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.completenessRateStatisticsYear(query);
        if (query.getString("navType").equals("org")){
            ExcelUtils.exportExcelSheet(CompletenessRateStatisticsOrgVO.class, object);
        } else if (query.getString("navType").equals("operator")) {
            ExcelUtils.exportExcelSheet(CompletenessRateStatisticsOperatorVO.class, object);
        }else {
            ExcelUtils.exportExcelSheet(CompletenessRateStatisticsEnterpriseVO.class, object);
        }
    }

    /**
     * 轨迹完整率统计-按季
     * @param query
     * @return
     */
    @PostMapping("/statics/completenessRateStatisticsQuarter")
    public ResultVO<?> completenessRateStatisticsQuarter(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.completenessRateStatisticsQuarter(query);
        return ResultVO.suc(object);
    }

    /**
     * 轨迹完整率统计-按季 导出
     * @param query
     * @return
     */
    @PostMapping("/statics/exportCompletenessRateStatisticsQuarter")
    public void exportCompletenessRateStatisticsQuarter(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.completenessRateStatisticsQuarter(query);
        if (query.getString("navType").equals("org")){
            ExcelUtils.exportExcelSheet(CompletenessRateStatisticsOrgVO.class, object);
        } else if (query.getString("navType").equals("operator")) {
            ExcelUtils.exportExcelSheet(CompletenessRateStatisticsOperatorVO.class, object);
        }else {
            ExcelUtils.exportExcelSheet(CompletenessRateStatisticsEnterpriseVO.class, object);
        }
    }

    // -----------轨迹完整率统计--------------------


    // ------------------监控考核 - -------------------------------
    /**
     * 监控考核-按月
     * @param query
     * @return
     */
    @PostMapping("/assessment/monitorExamineMth")
    public ResultVO<?> monitorExamineMth(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.monitorExamineMth(query);
        return ResultVO.suc(object);
    }

    /**
     * 监控考核-按月 导出
     * @param query
     * @return
     */
    @PostMapping("/assessment/exportMonitorExamineMth")
    public void exportEnterpriseSecurity(@RequestBody JSONObject query) {
        JSONObject object = supervisionAssessmentService.monitorExamineMth(query);
        if (query.getString("navType").equals("org")){
            ExcelUtils.exportExcelSheet(MonitorExamineOrgVO.class, object);
        } else if (query.getString("navType").equals("operator")) {
            ExcelUtils.exportExcelSheet(MonitorExamineOperatorVO.class, object);
        }else {
            ExcelUtils.exportExcelSheet(MonitorExamineEnterpriseVO.class, object);
        }
    }

    /**
     * 监控考核-按年
     * @param query
     * @return
     */
    @PostMapping("/assessment/monitorExamineYear")
    public ResultVO<?> monitorExamineYear(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.monitorExamineYear(query);
        return ResultVO.suc(object);
    }

    /**
     * 监控考核-按年  导出
     * @param query
     * @return
     */
    @PostMapping("/assessment/exportMonitorExamineYear")
    public void exportMonitorExamineYear(@RequestBody JSONObject query) {
        JSONObject object = supervisionAssessmentService.monitorExamineYear(query);
        if (query.getString("navType").equals("org")){
            ExcelUtils.exportExcelSheet(MonitorExamineOrgVO.class, object);
        } else if (query.getString("navType").equals("operator")) {
            ExcelUtils.exportExcelSheet(MonitorExamineOperatorVO.class, object);
        }else {
            ExcelUtils.exportExcelSheet(MonitorExamineEnterpriseVO.class, object);
        }
    }

    /**
     * 监控考核-按季
     * @param query
     * @return
     */
    @PostMapping("/assessment/monitorExamineQuarter")
    public ResultVO<?> monitorExamineQuarter(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.monitorExamineQuarter(query);
        return ResultVO.suc(object);
    }

    /**
     * 监控考核-按季  导出
     * @param query
     * @return
     */
    @PostMapping("/assessment/exportMonitorExamineQuarter")
    public void exportMonitorExamineQuarter(@RequestBody JSONObject query) {
        JSONObject object = supervisionAssessmentService.monitorExamineQuarter(query);
        if (query.getString("navType").equals("org")){
            ExcelUtils.exportExcelSheet(MonitorExamineOrgVO.class, object);
        } else if (query.getString("navType").equals("operator")) {
            ExcelUtils.exportExcelSheet(MonitorExamineOperatorVO.class, object);
        }else {
            ExcelUtils.exportExcelSheet(MonitorExamineEnterpriseVO.class, object);
        }

    }
    // ------------------监控考核 ------------------------------

    // ------------------监控统计详情----------------
    /**
     * 监控统计详情-按年
     * @param query
     * @return
     */
    @PostMapping("/statics/monitorStatisticsDetailsYear")
    public ResultVO<?> monitorStatisticsDetailsYear(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.monitorStatisticsDetailsYear(query);
        return ResultVO.suc(object);
    }

    /**
     * 监控统计详情-导出
     * @param query
     * @return
     */
    @PostMapping("/statics/exportMonitorStatisticsDetailsYear")
    public void exportMonitorStatisticsDetailsYear(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.monitorStatisticsDetailsYear(query);
        exportExcel(object,query);
    }



    /**
     * 监控统计详情-按季
     * @param query
     * @return
     */
    @PostMapping("/statics/monitorStatisticsDetailsQuarter")
    public ResultVO<?> monitorStatisticsDetailsQuarter(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.monitorStatisticsDetailsQuarter(query);
        return ResultVO.suc(object);
    }

    /**
     * 监控统计详情-导出
     * @param query
     * @return
     */
    @PostMapping("/statics/exportMonitorStatisticsDetailsQuarter")
    public void exportMonitorStatisticsDetailsQuarter(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.monitorStatisticsDetailsYear(query);
        exportExcel(object,query);
    }



    /**
     * 监控统计详情-按月
     * @param query
     * @return
     */
    @PostMapping("/statics/monitorStatisticsDetailsMth")
    public ResultVO<?> monitorStatisticsDetailsMth(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.monitorStatisticsDetailsMth(query);
        return ResultVO.suc(object);
    }

    /**
     * 监控统计详情-导出
     * @param query
     * @return
     */
    @PostMapping("/statics/exportMonitorStatisticsDetailsMth")
    public void exportMonitorStatisticsDetailsMth(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.monitorStatisticsDetailsMth(query);
        exportExcel(object,query);
    }

    private static void exportExcel(JSONObject object, JSONObject query) {
        String navType = query.getString("navType");
        String tabName = query.getString("tabName");

        if (navType == null || tabName == null) {
            throw new IllegalArgumentException("导出失败");
        }

        // 定义映射关系
        Map<String, Class<?>> enterpriseMapping = new HashMap<>();
        enterpriseMapping.put("tab2", EnterpriseTab2VO.class);
        enterpriseMapping.put("tab3", EnterpriseTab3VO.class);
        enterpriseMapping.put("tab4", EnterpriseTab4VO.class);
        enterpriseMapping.put("tab6", EnterpriseTab6VO.class);
        enterpriseMapping.put("tab7", EnterpriseTab7VO.class);
        enterpriseMapping.put("tab8", EnterpriseTab8VO.class);
        enterpriseMapping.put("tab9", EnterpriseTab9VO.class); // 默认情况

        Map<String, Class<?>> operatorMapping = new HashMap<>();
        operatorMapping.put("tab2", OperatorTab2VO.class);
        operatorMapping.put("tab3", OperatorTab3VO.class);
        operatorMapping.put("tab4", OperatorTab4VO.class);
        operatorMapping.put("tab6", OperatorTab6VO.class);
        operatorMapping.put("tab7", OperatorTab7VO.class);
        operatorMapping.put("tab8", OperatorTab8VO.class);
        operatorMapping.put("tab9", OperatorTab9VO.class); // 默认情况

        // 根据navType选择对应的映射
        Map<String, Class<?>> mapping = "enterprise".equals(navType) ? enterpriseMapping : operatorMapping;

        // 获取对应的VO类，如果没有匹配则使用默认的tab9
        Class<?> voClass = mapping.getOrDefault(tabName, mapping.get("tab9"));

        // 导出Excel
        ExcelUtils.exportExcelSheet(voClass, object);
    }

    // -----------离线车辆统计-----------------
    /**
     * 离线车辆统计
     * @param query
     * @return
     */
    @PostMapping("/statics/offlineStatistics")
    public ResultVO<?> offlineStatistics(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.offlineStatistics(query);
        return ResultVO.suc(object);
    }

    /**
     * 离线车辆统计 导出
     * @param query
     * @return
     */
    @PostMapping("/statics/exportOfflineStatistics")
    public void exportOfflineStatistics(@RequestBody JSONObject query){
        JSONObject object = supervisionAssessmentService.offlineStatistics(query);
        ExcelUtils.exportExcelSheet(OffLineStatisticsVO.class, object);
    }
}
