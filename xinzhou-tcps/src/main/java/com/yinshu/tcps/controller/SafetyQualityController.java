package com.yinshu.tcps.controller;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.fast.excel.util.ExcelUtils;
import com.yinshu.tcps.entity.RecordSupervision;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.*;

import com.yinshu.common.ApiConstant;
import com.yinshu.tcps.entity.SafetyQuality;
import com.yinshu.tcps.manager.SafetyQualityManager;

import org.springframework.web.bind.annotation.RestController;

/**
 *
 * 安全质量检查 
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@RestController
@RequestMapping(ApiConstant.API_TCPS_PREFIX + "/engineering-safety/safetyQuality")
public class SafetyQualityController {

	@Autowired
	private SafetyQualityManager safetyQualityManager;

	
	@GetMapping("/pageList")
    public ResultVO<?> pageList(SafetyQuality entity) {
		IPage<SafetyQuality> resultList = safetyQualityManager.queryPageList(entity);
		return new ResultVO<>(resultList);
    }
    
    @GetMapping("/queryList")
    public ResultVO<?> queryList(SafetyQuality entity) {
		List<SafetyQuality> resultList = safetyQualityManager.queryList(entity);
		return new ResultVO<>(resultList);
    }

	@GetMapping("/getById/{id}")
	public ResultVO<?> getById(@PathVariable("id") String id){
	return new ResultVO<>(safetyQualityManager.getById(id));
	}
    
	@PostMapping("/create")
    public ResultVO<?> create(@RequestBody SafetyQuality entity){
		
		safetyQualityManager.save(entity);
		return new ResultVO<>(entity);
	}
	
	@PutMapping("/update")
    public ResultVO<?> update(@RequestBody SafetyQuality entity){
		safetyQualityManager.update(entity);
		return new ResultVO<>(entity);
	}

	@DeleteMapping("/remove/{ids}")
	public ResultVO<?> remove(@PathVariable List<String> ids){
		safetyQualityManager.remove(ids);
		return new ResultVO<>(ids);
	}

	@GetMapping("/export")
	public void export(SafetyQuality entity) {
		List<SafetyQuality> resultList = safetyQualityManager.queryList(entity);
		ExcelUtils.exportExcelSheet(SafetyQuality.class, resultList);
	}


}
