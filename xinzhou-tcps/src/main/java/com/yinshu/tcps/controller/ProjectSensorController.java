package com.yinshu.tcps.controller;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.*;
import com.yinshu.tcps.entity.ProjectSensor;
import com.yinshu.tcps.manager.ProjectSensorManager;
import com.yinshu.common.ApiConstant;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * 项目传感器表 
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@RestController
@RequestMapping(ApiConstant.API_TCPS_PREFIX +"/sqc/project/sensor")
public class ProjectSensorController {

	@Autowired
	private ProjectSensorManager projectSensorManager;

	
	@GetMapping("/pageList")
    public ResultVO<?> pageList(ProjectSensor entity) {
		IPage<ProjectSensor> resultList = projectSensorManager.queryPageList(entity);
		return new ResultVO<>(resultList);
    }
    
    @GetMapping("/queryList")
    public ResultVO<?> queryList(ProjectSensor entity) {
		List<ProjectSensor> resultList = projectSensorManager.queryList(entity);
		return new ResultVO<>(resultList);
    }

	@GetMapping("/getById/{id}")
	public ResultVO<?> getById(@PathVariable("id") String id){
	return new ResultVO<>(projectSensorManager.getById(id));
	}
    
	@PostMapping("/create")
    public ResultVO<?> create(@RequestBody ProjectSensor entity){
		projectSensorManager.save(entity);
		return new ResultVO<>(entity);
	}
	
	@PutMapping("/update")
    public ResultVO<?> update(@RequestBody ProjectSensor entity){
		projectSensorManager.update(entity);
		return new ResultVO<>(entity);
	}

	@DeleteMapping("/remove/{ids}")
	public ResultVO<?> remove(@PathVariable List<String> ids){
		projectSensorManager.remove(ids);
		return new ResultVO<>(ids);
	}


}
