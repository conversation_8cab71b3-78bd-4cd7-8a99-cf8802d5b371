package com.yinshu.tcps.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.tcps.service.OrganStatsService;
import com.yinshu.tcps.service.SafetyRiskStatsService;
import com.yinshu.utils.ResultVO;

/**
 * 机构综合统计
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping(ApiConstant.API_TCPS_PREFIX + "/safety-risk/risk")
public class SafetyRiskStatsController {

	@Autowired
    private SafetyRiskStatsService safetyRiskStatsService;
    
    /**
	 * 当前各区域隐患数量分析
	 */
    @PostMapping("/getCountyChart")
    public ResultVO<?> getCountyChart(@RequestBody JSONObject query) {
        JSONObject object = safetyRiskStatsService.getCountyChart(query);
        return ResultVO.suc(object);
    }
    
    /**
     * 各类别平均治理时间
     */
    @PostMapping("/getCategoryChart")
    public ResultVO<?> getCategoryChart(@RequestBody JSONObject query) {
        JSONObject object = safetyRiskStatsService.getCategoryChart(query);
        return ResultVO.suc(object);
    }
    
    /**
     * 各等级平均治理时间
     */
    @PostMapping("/getGradeChart")
    public ResultVO<?> getGradeChart(@RequestBody JSONObject query) {
        JSONObject object = safetyRiskStatsService.getGradeChart(query);
        return ResultVO.suc(object);
    }
    
    /**
     * 综合统计分析报警事件详情
     */
    @PostMapping("/getProcessList")
    public ResultVO<?> getProcessList(@RequestBody JSONObject query) {
        JSONObject object = safetyRiskStatsService.getProcessList(query);
        return ResultVO.suc(object);
    }
}
