package com.yinshu.tcps.controller;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.tcps.service.DrivingMonitorService;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 行业监管看板
 *
 */
@RestController
@RequestMapping(ApiConstant.API_TCPS_PREFIX + "/driving-monitor")
public class DrivingMonitorController {

    @Autowired
    private DrivingMonitorService drivingMonitorService;

    /**
     * 获取车辆信息
     */
    @PostMapping("/getVehicleList")
    public ResultVO<?> getVehicleList(@RequestBody JSONObject query) {
        JSONObject object = drivingMonitorService.getVehicleList(query);
        return ResultVO.suc(object);
    }

    /**
     * 获取多辆车的实时定位
     *
     * @return
     */
    @PostMapping("/getMultiRealLocation")
    public ResultVO<?> getMultiRealLocation(@RequestBody JSONObject query) {
        JSONObject object = drivingMonitorService.getMultiRealLocation(query);
        return ResultVO.suc(object);
    }

    /**
     * 根据车牌号码获取实时定位
     */
    @PostMapping("/getRealLocation")
    public ResultVO<?> getRealLocation(@RequestBody JSONObject query) {
        JSONObject object = drivingMonitorService.getRealLocation(query);
        return ResultVO.suc(object);
    }

    /**
     * 获取历史轨迹
     */
    @PostMapping("/getHistoryLocation")
    public ResultVO<?> getHistoryLocation(@RequestBody JSONObject query) {
        return ResultVO.suc(drivingMonitorService.getHistoryLocation(query));
    }

}
