package com.yinshu.tcps.controller;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.tcps.service.DynamicAssessmentService;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DynamicAssessmentController
 * @description TODO 动态监督考核控制器
 * @date 2025/7/17 15:52
 **/
@RestController
@RequestMapping(ApiConstant.API_TCPS_PREFIX + "/dynamicAssessment")
public class DynamicAssessmentController {

    @Resource
    DynamicAssessmentService service;

    @PostMapping("/ads_company_info_base_table")
    public ResultVO<?> adsCompanyInfoBaseTable(@RequestBody JSONObject query) {
        JSONObject data = service.adsCompanyInfoBaseTable(query);
        return new ResultVO<>(data);
    }

    @PostMapping("/ads_operator_info_base_table")
    public ResultVO<?> adsOperatorInfoBaseTable(@RequestBody JSONObject query) {
        JSONObject data = service.adsOperatorInfoBaseTable(query);
        return new ResultVO<>(data);
    }

}
