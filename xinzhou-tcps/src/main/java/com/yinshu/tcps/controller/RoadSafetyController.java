package com.yinshu.tcps.controller;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.tcps.service.RoadSafety;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RoadSafetyController
 * @description TODO 道路安全控制器
 * @date 2025/7/15 10:22
 **/
@RestController
@RequestMapping(ApiConstant.API_TCPS_PREFIX + "/roadSafety")
public class RoadSafetyController {

    @Resource
    RoadSafety roadSafety;


    /**
     * @param query 查询参数
     * @return com.yinshu.utils.ResultVO<cn.hutool.json.JSONObject>
     * <AUTHOR>
     * @description //TODO 道路安全监督监管动态查岗
     * @date 2025/7/15 10:31
     **/
    @PostMapping("/ads_road_safe_dynamic_inspection")
    public ResultVO<JSONObject> adsRoadSafeDynamicInspection(@RequestBody JSONObject query) {
        return ResultVO.suc(roadSafety.adsRoadSafeDynamicInspection(query));
    }


    /**
     * @param query 查询参数
     * @return com.yinshu.utils.ResultVO<cn.hutool.json.JSONObject>
     * <AUTHOR>
     * @description //TODO 道路安全监督监管报警督办
     * @date 2025/7/15 10:31
     **/
    @PostMapping("/ads_road_safe_alarm_supervision")
    public ResultVO<JSONObject> adsRoadSafeAlarmSupervision(@RequestBody JSONObject query) {
        return ResultVO.suc(roadSafety.adsRoadSafeAlarmSupervision(query));
    }

    /**
     * @param query 查询参数
     * @return com.yinshu.utils.ResultVO<cn.hutool.json.JSONObject>
     * <AUTHOR>
     * @description //TODO 企业信息表
     * @date 2025/7/15 10:31
     **/
    @PostMapping("/ods_company_info")
    public ResultVO<JSONObject> odsCompanyInfo(@RequestBody JSONObject query) {
        return ResultVO.suc(roadSafety.odsCompanyInfo(query));
    }

    /**
     * @param query 查询参数
     * @return com.yinshu.utils.ResultVO<cn.hutool.json.JSONObject>
     * <AUTHOR>
     * @description //TODO 忻州市地市行政区划
     * @date 2025/7/15 10:31
     **/
    @PostMapping("/dim_xinzhou_county")
    public ResultVO<JSONObject> dimXinzhouCounty(@RequestBody JSONObject query) {
        return ResultVO.suc(roadSafety.dimXinzhouCounty(query));
    }
}
