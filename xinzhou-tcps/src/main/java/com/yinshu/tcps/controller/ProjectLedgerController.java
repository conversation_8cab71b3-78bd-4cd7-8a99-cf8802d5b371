package com.yinshu.tcps.controller;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import com.yinshu.utils.ResultVO;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.*;
import com.yinshu.tcps.entity.ProjectLedger;
import com.yinshu.tcps.manager.ProjectLedgerManager;
import com.yinshu.common.ApiConstant;

import org.springframework.web.bind.annotation.RestController;

/**
 *
 * 项目台账资料表 
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@RestController
@RequestMapping(ApiConstant.API_TCPS_PREFIX +"/sqc/project/ledger")
public class ProjectLedgerController {

	@Autowired
	private ProjectLedgerManager projectLedgerManager;
	
	@Autowired
	private Environment environment;
	
	@GetMapping("/pageList")
    public ResultVO<?> pageList(ProjectLedger entity) {
		IPage<ProjectLedger> resultList = projectLedgerManager.queryPageList(entity);
		return new ResultVO<>(resultList);
    }
    
    @GetMapping("/queryList")
    public ResultVO<?> queryList(ProjectLedger entity) {
		List<ProjectLedger> resultList = projectLedgerManager.queryList(entity);
		for(ProjectLedger file : resultList) {
        	file.setFileUrl(file.getFilePath() == null ? null : environment.getProperty("system.oss-path") + file.getFilePath().replaceAll("\\\\", "/"));
        }
		return new ResultVO<>(resultList);
    }

	@GetMapping("/getById/{id}")
	public ResultVO<?> getById(@PathVariable("id") String id){
	return new ResultVO<>(projectLedgerManager.getById(id));
	}
    
	@PostMapping("/create")
    public ResultVO<?> create(@ModelAttribute ProjectLedger entity){
		projectLedgerManager.save(entity);
		entity.setFile(null);
		return new ResultVO<>(entity);
	}
	
	@PutMapping("/update")
    public ResultVO<?> update(@ModelAttribute ProjectLedger entity){
		projectLedgerManager.update(entity);
		entity.setFile(null);
		return new ResultVO<>(entity);
	}

	@DeleteMapping("/remove/{ids}")
	public ResultVO<?> remove(@PathVariable List<String> ids){
		projectLedgerManager.remove(ids);
		return new ResultVO<>(ids);
	}


}
