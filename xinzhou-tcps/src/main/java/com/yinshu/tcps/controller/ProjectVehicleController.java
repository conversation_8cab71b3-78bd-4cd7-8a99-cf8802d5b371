package com.yinshu.tcps.controller;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.*;
import com.yinshu.tcps.entity.ProjectVehicle;
import com.yinshu.tcps.manager.ProjectVehicleManager;
import com.yinshu.common.ApiConstant;

import org.springframework.web.bind.annotation.RestController;

/**
 *
 * 项目车辆表 
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@RestController
@RequestMapping(ApiConstant.API_TCPS_PREFIX +"/sqc/project/vehicle")
public class ProjectVehicleController {

	@Autowired
	private ProjectVehicleManager projectVehicleManager;

	
	@GetMapping("/pageList")
    public ResultVO<?> pageList(ProjectVehicle entity) {
		IPage<ProjectVehicle> resultList = projectVehicleManager.queryPageList(entity);
		return new ResultVO<>(resultList);
    }
    
    @GetMapping("/queryList")
    public ResultVO<?> queryList(ProjectVehicle entity) {
		List<ProjectVehicle> resultList = projectVehicleManager.queryList(entity);
		return new ResultVO<>(resultList);
    }

	@GetMapping("/getById/{id}")
	public ResultVO<?> getById(@PathVariable("id") String id){
	return new ResultVO<>(projectVehicleManager.getById(id));
	}
    
	@PostMapping("/create")
    public ResultVO<?> create(@RequestBody ProjectVehicle entity){
		projectVehicleManager.save(entity);
		return new ResultVO<>(entity);
	}
	
	@PutMapping("/update")
    public ResultVO<?> update(@RequestBody ProjectVehicle entity){
		projectVehicleManager.update(entity);
		return new ResultVO<>(entity);
	}

	@DeleteMapping("/remove/{ids}")
	public ResultVO<?> remove(@PathVariable List<String> ids){
		projectVehicleManager.remove(ids);
		return new ResultVO<>(ids);
	}


}
