package com.yinshu.tcps.controller;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.*;
import com.yinshu.tcps.entity.ProjectInvestProgress;
import com.yinshu.tcps.manager.ProjectInvestProgressManager;
import com.yinshu.common.ApiConstant;

import org.springframework.web.bind.annotation.RestController;

/**
 *
 * 资金投入与建设进度 
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@RestController
@RequestMapping(ApiConstant.API_TCPS_PREFIX + "/sqc/project/investProgress")
public class ProjectInvestProgressController {

	@Autowired
	private ProjectInvestProgressManager projectInvestProgressManager;

	
	@GetMapping("/pageList")
    public ResultVO<?> pageList(ProjectInvestProgress entity) {
		IPage<ProjectInvestProgress> resultList = projectInvestProgressManager.queryPageList(entity);
		return new ResultVO<>(resultList);
    }
    
    @GetMapping("/queryList")
    public ResultVO<?> queryList(ProjectInvestProgress entity) {
		List<ProjectInvestProgress> resultList = projectInvestProgressManager.queryList(entity);
		return new ResultVO<>(resultList);
    }

	@GetMapping("/getById/{id}")
	public ResultVO<?> getById(@PathVariable("id") String id){
	return new ResultVO<>(projectInvestProgressManager.getById(id));
	}
    
	@PostMapping("/create")
    public ResultVO<?> create(@RequestBody ProjectInvestProgress entity){
		projectInvestProgressManager.save(entity);
		return new ResultVO<>(entity);
	}
	
	@PutMapping("/update")
    public ResultVO<?> update(@RequestBody ProjectInvestProgress entity){
		projectInvestProgressManager.update(entity);
		return new ResultVO<>(entity);
	}

	@DeleteMapping("/remove/{ids}")
	public ResultVO<?> remove(@PathVariable List<String> ids){
		projectInvestProgressManager.remove(ids);
		return new ResultVO<>(ids);
	}


}
