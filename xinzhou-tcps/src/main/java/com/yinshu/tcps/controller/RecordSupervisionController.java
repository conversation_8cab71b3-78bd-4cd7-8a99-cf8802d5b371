package com.yinshu.tcps.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.common.ApiConstant;
import com.yinshu.fast.excel.util.ExcelUtils;
import com.yinshu.tcps.entity.RecordSupervision;
import com.yinshu.tcps.manager.RecordSupervisionManager;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目监理管理表
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@RestController
@RequestMapping(ApiConstant.API_TCPS_PREFIX + "/recordSupervision")
public class RecordSupervisionController {

    @Autowired
    private RecordSupervisionManager recordSupervisionManager;


    @GetMapping("/pageList")
    public ResultVO<?> pageList(RecordSupervision entity) {
        IPage<RecordSupervision> resultList = recordSupervisionManager.queryPageList(entity);
        return new ResultVO<>(resultList);
    }


    @GetMapping("/export")
    public void export(RecordSupervision entity) {
        List<RecordSupervision> resultList = recordSupervisionManager.queryList(entity);
        ExcelUtils.exportExcelSheet(RecordSupervision.class, resultList);
    }

    @GetMapping("/queryList")
    public ResultVO<?> queryList(RecordSupervision entity) {
        List<RecordSupervision> resultList = recordSupervisionManager.queryList(entity);
        return new ResultVO<>(resultList);
    }

    @GetMapping("/getById/{id}")
    public ResultVO<?> getById(@PathVariable("id") String id) {
        return new ResultVO<>(recordSupervisionManager.getById(id));
    }

    @PostMapping("/create")
    public ResultVO<?> create(@RequestBody RecordSupervision entity) {
        recordSupervisionManager.save(entity);
        return new ResultVO<>(entity);
    }

    @PutMapping("/update")
    public ResultVO<?> update(@RequestBody RecordSupervision entity) {
        recordSupervisionManager.update(entity);
        return new ResultVO<>(entity);
    }

    @DeleteMapping("/remove/{ids}")
    public ResultVO<?> remove(@PathVariable List<String> ids) {
        recordSupervisionManager.remove(ids);
        return new ResultVO<>(ids);
    }


}
