package com.yinshu.tcps.controller;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.annotation.OperLog;
import com.yinshu.common.ApiConstant;
import com.yinshu.tcps.entity.TcpsFile;
import com.yinshu.tcps.manager.TcpsFileManager;
import com.yinshu.utils.CommonUtils;
import com.yinshu.utils.ResultVO;
import com.yinshu.utils.SnowflakeIdGenerator;

/**
 * 管理现场记录文件表
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@RestController
@RequestMapping(ApiConstant.API_TCPS_PREFIX + "/file")
public class TcpsFileController {
	
	@Autowired
	private Environment environment; 

    @Resource
    SnowflakeIdGenerator snowflakeIdGenerator;
    
    @Autowired
    private TcpsFileManager fileManager;

    @GetMapping("/pageList")
    public ResultVO<?> pageList(TcpsFile entity) {
        IPage<TcpsFile> resultList = fileManager.queryPageList(entity);
        return new ResultVO<>(resultList);
    }

    @GetMapping("/queryList")
    public ResultVO<?> queryList(TcpsFile entity) {
        List<TcpsFile> resultList = fileManager.queryList(entity);
        return new ResultVO<>(resultList);
    }
    
    @GetMapping("/getById/{id}")
    public ResultVO<?> getById(@PathVariable("id") String id) {
        return new ResultVO<>(fileManager.getById(id));
    }


    @PostMapping("/create")
    public ResultVO<?> create(TcpsFile entity) {
        fileManager.save(entity);
        entity.setFile(null);
        return new ResultVO<>(entity);
    }

    @PutMapping("/update")
    public ResultVO<?> update(TcpsFile entity) {
        fileManager.update(entity);
        entity.setFile(null);
        return new ResultVO<>(entity);
    }

    @DeleteMapping("/remove/{ids}")
    public ResultVO<?> remove(@PathVariable List<String> ids) {
        fileManager.remove(ids);
        return new ResultVO<>(ids);
    }

    /**
     * 文件上传
     */
    @PostMapping("/upload")
    public ResultVO<?> upload(@RequestPart("file") MultipartFile file,
                              @RequestPart(value = "info", required = false) String info) {
        TcpsFile tcpsFile = null;
        try {
            if (info != null) {
                tcpsFile = JSONObject.parseObject(info, TcpsFile.class);
            } else {
                tcpsFile = new TcpsFile();
            }
        } catch (Exception e) {
            tcpsFile = new TcpsFile();
        }
        if (StringUtils.isBlank(tcpsFile.getCategory())) {
            tcpsFile.setCategory("DEFAULT");
        }
        if (StringUtils.isBlank(tcpsFile.getCategoryRecordId())) {
            tcpsFile.setCategoryRecordId(snowflakeIdGenerator.nextIdStr());
        }
        TcpsFile data = fileManager.upload(file, tcpsFile);
        data.setFile(null);
        return new ResultVO<>(data);
    }

    /**
     * 文件下载
     */
    @GetMapping("/download/{filePath}")
    @OperLog(operModul = "", operType = "TCPS文件下载")
    public void fileDownload(@PathVariable(name = "filePath") String filePath, HttpServletResponse response) {
        fileManager.downloadFile(filePath, response);
    }
    
}
