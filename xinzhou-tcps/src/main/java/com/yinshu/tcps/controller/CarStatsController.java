package com.yinshu.tcps.controller;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.fast.excel.util.ExcelUtils;
import com.yinshu.tcps.service.CarStatsService;
import com.yinshu.tcps.vo.export.CarOperationListExportVO;
import com.yinshu.tcps.vo.export.CarSafetyListExportVO;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 机构综合统计
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(ApiConstant.API_TCPS_PREFIX + "/integrated-stats/car")
public class CarStatsController {

    @Autowired
    private CarStatsService carStatService;

    /**
     * 获取运营数据汇总
     */
    @PostMapping("/getOperationTotal")
    public ResultVO<?> getOperationTotal(@RequestBody JSONObject query) {
        JSONObject object = carStatService.getOperationTotal(query);
        return ResultVO.suc(object);
    }


    /**
     * 获取运营数据
     */
    @PostMapping("/getOperationList")
    public ResultVO<?> getWarningList(@RequestBody JSONObject query) {
        JSONObject object = carStatService.getOperationList(query);
        return ResultVO.suc(object);
    }

    @PostMapping("/getOperationListExport")
    public void getOperationListExport(@RequestBody JSONObject query) {
        JSONObject object = carStatService.getOperationList(query);
        ExcelUtils.exportExcelSheet(CarOperationListExportVO.class, object);
    }


    /**
     * 获取运营数据图表
     */
    @PostMapping("/getOperationChart")
    public ResultVO<?> getOperationChart(@RequestBody JSONObject query) {
        JSONObject object = carStatService.getOperationChart(query);
        return ResultVO.suc(object);
    }

    /**
     * 获取安全数据汇总
     */
    @PostMapping("/getSafetyTotal")
    public ResultVO<?> getSafetyTotal(@RequestBody JSONObject query) {
        JSONObject object = carStatService.getSafetyTotal(query);
        return ResultVO.suc(object);
    }

    /**
     * 获取安全数据列表
     */
    @PostMapping("/getSafetyList")
    public ResultVO<?> getSafetyList(@RequestBody JSONObject query) {
        JSONObject object = carStatService.getSafetyList(query);
        return ResultVO.suc(object);
    }

    @PostMapping("/getSafetyListExport")
    public void getSafetyListExport(@RequestBody JSONObject query) {
        JSONObject object = carStatService.getSafetyList(query);
        ExcelUtils.exportExcelSheet(CarSafetyListExportVO.class, object);
    }

    /**
     * 获取安全数据饼图
     */
    @PostMapping("/getSafetyChart")
    public ResultVO<?> getSafetyChart(@RequestBody JSONObject query) {
        JSONObject object = carStatService.getSafetyChart(query);
        return ResultVO.suc(object);
    }

    /**
     * 综合统计分析报警事件详情
     */
    @PostMapping("/getDetailList")
    public ResultVO<?> getDetailList(@RequestBody JSONObject query) {
        JSONObject object = carStatService.getDetailList(query);
        return ResultVO.suc(object);
    }
}