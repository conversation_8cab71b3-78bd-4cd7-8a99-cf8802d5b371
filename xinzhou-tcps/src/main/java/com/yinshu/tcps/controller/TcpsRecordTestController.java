package com.yinshu.tcps.controller;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import com.yinshu.utils.ResultVO;
import org.springframework.web.bind.annotation.*;
import com.yinshu.common.ApiConstant;
import com.yinshu.tcps.entity.TcpsRecordTest;
import com.yinshu.tcps.manager.TcpsRecordTestManager;
import org.springframework.web.bind.annotation.RestController;

/**
 * 试验检测管理表
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@RestController
@RequestMapping(ApiConstant.API_TCPS_PREFIX + "/recordTest")
public class TcpsRecordTestController {

	@Autowired
	private TcpsRecordTestManager tcpsRecordTestManager;

	@GetMapping("/pageList")
    public ResultVO<?> pageList(TcpsRecordTest entity) {
		IPage<TcpsRecordTest> resultList = tcpsRecordTestManager.queryPageList(entity);
		return new ResultVO<>(resultList);
    }
    
    @GetMapping("/queryList")
    public ResultVO<?> queryList(TcpsRecordTest entity) {
		List<TcpsRecordTest> resultList = tcpsRecordTestManager.queryList(entity);
		return new ResultVO<>(resultList);
    }

	@GetMapping("/getById/{id}")
	public ResultVO<?> getById(@PathVariable("id") String id){
		return new ResultVO<>(tcpsRecordTestManager.getById(id));
	}

	@PostMapping("/create")
	public ResultVO<?> create(@RequestBody TcpsRecordTest entity) {
		tcpsRecordTestManager.save(entity); 
		return new ResultVO<>(entity);
	}
	
	@PutMapping("/update")
    public ResultVO<?> update(@RequestBody TcpsRecordTest entity){
		tcpsRecordTestManager.update(entity);
		return new ResultVO<>(entity);
	}

	@DeleteMapping("/remove/{ids}")
	public ResultVO<?> remove(@PathVariable List<String> ids){
		tcpsRecordTestManager.remove(ids);
		return new ResultVO<>(ids);
	}
}
