package com.yinshu.tcps.controller;

import com.alibaba.fastjson.JSONObject;
import com.yinshu.common.ApiConstant;
import com.yinshu.tcps.service.IndustryRegulationService;
import com.yinshu.utils.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 行业监管看板
 *
 */
@RestController
@RequestMapping(ApiConstant.API_TCPS_PREFIX + "/industry-regulation")
public class IndustryRegulationController {

    @Autowired
    private IndustryRegulationService industryRegulationService;

    /**
     * 运输行业统计
     * @param query
     * @return
     */
    @PostMapping("/getTransportStatistics")
    public ResultVO<?> getTransportStatistics(@RequestBody JSONObject query) {
        return ResultVO.suc(industryRegulationService.getTransportStatistics(query));
    }

    /**
     * 获取安全隐患统计
     * @param query
     * @return
     */
    @PostMapping("/getHazardStatistics")
    public ResultVO<?> getHazardStatistics(@RequestBody JSONObject query) {
        return ResultVO.suc(industryRegulationService.getHazardStatistics(query));
    }

    /**
     * 获取安全隐患列表
     * @param query
     * @return
     */
    @PostMapping("/getHazardList")
    public ResultVO<?> getHazardList(@RequestBody JSONObject query) {
        return ResultVO.suc(industryRegulationService.getHazardList(query));
    }

    /**
     * 获取安全隐患列表
     * @param query
     * @return
     */
    @PostMapping("/getProgressConstruction")
    public ResultVO<?> getProgressConstruction(@RequestBody JSONObject query) {
        return ResultVO.suc(industryRegulationService.getProgressConstruction(query));
    }

    /**
     * 获取安全隐患列表
     * @param query
     * @return
     */
    @PostMapping("/getHiddenDanger")
    public ResultVO<?> getHiddenDanger(@RequestBody JSONObject query) {
        return ResultVO.suc(industryRegulationService.getHiddenDanger(query));
    }


}
