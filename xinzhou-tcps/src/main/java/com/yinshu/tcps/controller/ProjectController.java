package com.yinshu.tcps.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.common.ApiConstant;
import com.yinshu.tcps.entity.Project;
import com.yinshu.tcps.manager.ProjectManager;
import com.yinshu.utils.ResultVO;

/**
 *
 * 工程项目主表 
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@RestController
@RequestMapping(ApiConstant.API_TCPS_PREFIX +"/sqc/project")
public class ProjectController {

	@Autowired
	private Environment environment;
	
	@Autowired
	private ProjectManager projectManager;

	
	@GetMapping("/pageList")
    public ResultVO<?> pageList(Project entity) {
		IPage<Project> resultList = projectManager.queryPageList(entity);
		return new ResultVO<>(resultList);
    }
    
    @GetMapping("/queryList")
    public ResultVO<?> queryList(Project entity) {
		List<Project> resultList = projectManager.queryList(entity);
		return new ResultVO<>(resultList);
    }

	@GetMapping("/getById/{id}")
	public ResultVO<?> getById(@PathVariable("id") String id){
	return new ResultVO<>(projectManager.getById(id));
	}
    
	@PostMapping("/create")
    public ResultVO<?> create(@RequestBody Project entity){
		projectManager.save(entity);
		return new ResultVO<>(entity);
	}
	
	@PutMapping("/update")
    public ResultVO<?> update(@RequestBody Project entity){
		projectManager.update(entity);
		return new ResultVO<>(entity);
	}

	@DeleteMapping("/remove/{ids}")
	public ResultVO<?> remove(@PathVariable List<String> ids){
		projectManager.remove(ids);
		return new ResultVO<>(ids);
	}


	@PostMapping("/tree")
	public ResultVO<?> tree(@RequestBody Project entity){
		return new ResultVO<>(projectManager.tree(entity));
	}

	/**
	 * 通过经纬度取得位置（调用大数据接口）
	 * @param lng
	 * @param lat
	 * @return
	 */
	@GetMapping("/location/{lng}/{lat}")
	public ResultVO<?> getLocationByLngLat(@PathVariable("lng") Double lng, @PathVariable("lat") Double lat){
		return new ResultVO<>(projectManager.getLocationByLngLat(lng, lat));
	}
	
	/**
	 * 获取所有附件内容
	 * @param entity
	 * @return
	 */
    @GetMapping("/getArchiveList")
    public ResultVO<?> getArchiveList(Project entity) {
		List<Map<String, String>> resultList = projectManager.getArchiveList();
		for(Map<String, String> file : resultList) {
			String url = file.get("file_path") == null ? null : environment.getProperty("system.oss-path") + file.get("file_path").replaceAll("\\\\", "/");
			file.put("url", url);
        }
		return new ResultVO<>(resultList);
    }
}
