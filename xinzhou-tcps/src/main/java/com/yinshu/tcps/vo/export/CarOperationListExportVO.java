package com.yinshu.tcps.vo.export;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CarOperationTotalExportVO
 * @description TODO 车辆数据分析导出
 * @date 2025/7/21 20:43
 **/
@Data
public class CarOperationListExportVO {

    @ExcelProperty(value = "序号")
    private Integer no;

    @ExcelProperty(value = "企业名称")
    private String company;

    @ExcelProperty(value = "车牌号")
    @ColumnWidth(40)
    private String carNumber;

    @ExcelProperty(value = "行驶里程")
    private Double drivingDistance;

    @ExcelProperty(value = "营运里程")
    private Double operatingDistance;

}
