package com.yinshu.tcps.vo.export;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CarSafetyListExportVO
 * @description TODO 安全数据列表导出
 * @date 2025/7/21 20:47
 **/
@Data
public class CarSafetyListExportVO {

    @ExcelProperty(value = "序号")
    private Integer no;

    @ExcelProperty(value = "企业名称")
    private String company;

    @ExcelProperty(value = "车牌号")
    @ColumnWidth(40)
    private String carNumber;

    @ExcelProperty(value = "报警总数")
    private Integer alarmTotalNum;

    @ExcelProperty(value = "百公里报警数")
    private Integer alarmPer100km;

    @ExcelProperty(value = "报警处理率")
    private String alarmRate;

    @ExcelProperty(value = "督办率")
    private String superviseRate;

    @ExcelProperty(value = "督办完成率")
    private String superviseFinishRate;

    @ExcelProperty(value = "超速预警")
    private Integer overSpeedWarn;

    @ExcelProperty(value = "疲劳驾驶预警")
    private Integer fatigueDriveWarn;

    @ExcelProperty(value = "异常聚集预警")
    private Integer abnormalGatherWarn;

    @ExcelProperty(value = "在线时长过长预警")
    private Integer onlineOvertimeWarn;

    @ExcelProperty(value = "长期不在线预警")
    private Integer offlineLongWarn;

    @ExcelProperty(value = "非营运时段行驶预警")
    private Integer nonOpHoursWarn;

}
