package com.yinshu.tcps.vo.SupervisionAssessmentVO.GpsDriftRateStatistics;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
/**
 * 卫星定位漂移率统计
 */
@Data
public class GpsDriftRateStatisticsOrgVO {
    @ExcelProperty(value = "区县")
    @ColumnWidth(15)
    private String district;

    @ExcelProperty(value = "漂移车辆数")
    @ColumnWidth(15)
    private String drift_car_num;

    @ExcelProperty(value = "漂移次数")
    @ColumnWidth(15)
    private String drift_num;

    @ExcelProperty(value = "营运车辆数")
    @ColumnWidth(15)
    private String operations_num;

    @ExcelProperty(value = "卫星定位漂移率")
    @ColumnWidth(15)
    private String gps_drift_rate;
}
