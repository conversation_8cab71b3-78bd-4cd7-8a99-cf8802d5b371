package com.yinshu.tcps.vo.SupervisionAssessmentVO.WarnSuperviseRateStatistics;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * 报警督办效率
 */
@Data
public class WarnSuperviseRateStatisticsOrgVO {
    @ExcelProperty(value = "区县")
    @ColumnWidth(15)
    private String district;

    @ExcelProperty(value = "报警总数")
    @ColumnWidth(15)
    private String warn_num;

    @ExcelProperty(value = "督办数")
    @ColumnWidth(15)
    private String supervise_num;

    @ExcelProperty(value = "督办完成数")
    @ColumnWidth(15)
    private String supervise_over_num;

    @ExcelProperty(value = "报警督办率")
    @ColumnWidth(15)
    private String warn_supervise_rate;

    @ExcelProperty(value = "督办完成率")
    @ColumnWidth(15)
    private String supervise_over_rate;
}
