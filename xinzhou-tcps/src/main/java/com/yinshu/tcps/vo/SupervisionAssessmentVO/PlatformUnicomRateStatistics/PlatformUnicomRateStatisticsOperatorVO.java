package com.yinshu.tcps.vo.SupervisionAssessmentVO.PlatformUnicomRateStatistics;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * 平台联通率统计
 */
@Data
public class PlatformUnicomRateStatisticsOperatorVO {
    @ExcelProperty(value = "区县")
    @ColumnWidth(15)
    private String district;

    @ExcelProperty(value = "运营商")
    @ColumnWidth(15)
    private String operator;

    @ExcelProperty(value = "上下级平台实际联通时长")
    @ColumnWidth(15)
    private String real_unicom_time;

    @ExcelProperty(value = "上下级平台应连通时长")
    @ColumnWidth(15)
    private String should_unicom_time;

    @ExcelProperty(value = "平台联通率")
    @ColumnWidth(15)
    private String unicom_rate;
}
