package com.yinshu.tcps.vo.export;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class OrgOperationListExportVO {

    @ExcelProperty(value = "序号")
    private Integer no;

    @ExcelProperty(value = "区县")
    private String county;

    @ExcelProperty(value = "车辆总数")
    private Double totalVehicles;

    @ExcelProperty(value = "上线车辆数")
    private Double onlineVehicles;

    @ExcelProperty(value = "营运车辆数")
    private Double operatingVehicles;

    @ExcelProperty(value = "行驶里程")
    private String drivingDistance;

    @ExcelProperty(value = "营运里程")
    private String operatingDistance;
}
