package com.yinshu.tcps.vo.export;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class OrgSafetyListExportVO {
    // 序号
    @ExcelProperty(value = "序号")
    private Integer no;
    // 区县
    private String county;
    // 报警总数
    private Double alarmTotalNum;
    // 百公里报警数
    private Double alarmPer100km;
    // 报警处理率
    private String alarmRate;
    // 督办率
    private String superviseRate;
    // 督办完成率
    private String superviseFinishRate;
    // 超速预警
    private Double overSpeedWarn;
    // 疲劳驾驶预警
    private Double fatigueDriveWarn;
    // 异常聚集预警
    private Double abnormalGatherWarn;
    // 在线过长预警
    private Double onlineOvertimeWarn;
    // 长期不在线预警
    private Double offlineLongWarn;
    // 非营运时段行驶报警
    private Double nonOpHoursWarn;
}
