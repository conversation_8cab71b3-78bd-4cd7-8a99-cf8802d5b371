package com.yinshu.tcps.vo.SupervisionAssessmentVO.Speedstatistics;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * 超速驾驶统计
 */
@Data
public class SpeedstatisticsOrgVO {
    @ExcelProperty(value = "区县")
    @ColumnWidth(15)
    private String district;

    @ExcelProperty(value = "超速总次数")
    @ColumnWidth(15)
    private String over_speed_num;

    @ExcelProperty(value = "营运车辆数")
    @ColumnWidth(15)
    private String operations_num;

    @ExcelProperty(value = "平均超速次数")
    @ColumnWidth(15)
    private String over_speed_avg;
}
