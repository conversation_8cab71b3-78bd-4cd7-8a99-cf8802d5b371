package com.yinshu.tcps.vo.SupervisionAssessmentVO.MonitorStatisticsDetails.operator;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
public class OperatorTab3VO {
    @ExcelProperty(value = "区县")
    @ColumnWidth(15)
    private String district;

    @ExcelProperty(value = "运营商")
    @ColumnWidth(15)
    private String operator;

    @ExcelProperty(value = "车牌号码")
    @ColumnWidth(15)
    private String vehicle_num;

    @ExcelProperty(value = "驾驶员")
    @ColumnWidth(15)
    private String driver;

    @ExcelProperty(value = "车牌颜色")
    @ColumnWidth(15)
    private String vehicle_color;

    @ExcelProperty(value = "数据合格率")
    @ColumnWidth(15)
    private String qualified_rate;
}
