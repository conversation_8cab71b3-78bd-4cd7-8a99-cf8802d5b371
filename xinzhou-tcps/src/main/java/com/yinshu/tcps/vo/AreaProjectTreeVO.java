package com.yinshu.tcps.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AreaProjectTreeVO
 * @description TODO 区域项目树
 * @date 2025/7/8 16:14
 **/
@Data
public class AreaProjectTreeVO {
    /**
     * id
     **/
    private String id;
    /**
     * 标签
     **/
    private String label;
    /**
     * 类型 1、area 2、project
     **/
    private String type;
    /**
     * 额外属性
     **/
    private Object attribute;
    /**
     * 子集
     **/
    private List<AreaProjectTreeVO> children;
}
