package com.yinshu.tcps.vo.SupervisionAssessmentVO.QualifiedRateStatistics;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * 数据合格率 - 导出
 */
@Data
public class QualifiedRateStatisticsOperatorVO {

    @ExcelProperty(value = "区县")
    @ColumnWidth(15)
    private String district;

    @ExcelProperty(value = "运营商")
    @ColumnWidth(15)
    private String operator;

    @ExcelProperty(value = "合格记录数")
    @ColumnWidth(15)
    private String qualified_num;

    @ExcelProperty(value = "总记录数")
    @ColumnWidth(15)
    private String record_num;

    @ExcelProperty(value = "数据合格率")
    @ColumnWidth(15)
    private String qualified_rate;
}
