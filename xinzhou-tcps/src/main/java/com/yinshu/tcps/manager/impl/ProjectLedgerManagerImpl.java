package com.yinshu.tcps.manager.impl;

import java.util.Map;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.security.SecurityUtils;
import com.yinshu.tcps.config.TcpsLocalStorageConfig;
import com.yinshu.tcps.entity.ProjectLedger;
import com.yinshu.tcps.service.ProjectLedgerService;
import com.yinshu.tcps.manager.ProjectLedgerManager;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.yinshu.utils.SnowflakeIdGenerator;
import com.yinshu.utils.DateUtils;

/**
 * 项目台账资料表 
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Service
public class ProjectLedgerManagerImpl implements ProjectLedgerManager {

	@Autowired
	private ProjectLedgerService projectLedgerService;

	@Autowired
	private SnowflakeIdGenerator snowflakeIdGenerator;
	
	@Autowired
	private TcpsLocalStorageConfig localStorageConfig;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<ProjectLedger> queryList(ProjectLedger entity) {
		List<ProjectLedger> resultList = projectLedgerService.queryList(entity);
		return resultList;
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<ProjectLedger> queryPageList(ProjectLedger entity) {
		IPage<ProjectLedger> resultList = projectLedgerService.queryPageList(entity);
		return resultList;
	}
	
	/**
	 * 保存
	 * @param entity
	 */
	public void save(ProjectLedger entity) {
		entity.setId(snowflakeIdGenerator.nextIdStr());
		entity.setCreateTime(DateUtils.getNow());
        entity.setCreateUser(SecurityUtils.getUsername());
		MultipartFile file = entity.getFile();
		if (file != null && !file.isEmpty()) {
			String originalFilename = file.getOriginalFilename();
			entity.setFileName(originalFilename);
			String filepath = localStorageConfig.putObject(file);
			entity.setFilePath(filepath);
		}
		projectLedgerService.save(entity);
	}
	
	/**
	 * 删除
	 * @param id
	 */
	
	public void remove(String id) {
		projectLedgerService.removeById(id);	
	}
	
	/**
	 * 批量删除
	 * @param idList
	 */
	public void remove(List<String> idList) {
		projectLedgerService.removeByIds(idList);	
	}
	
	/**
	 * 更新
	 * @param entity
	 */
	public void update(ProjectLedger entity) {
		entity.setUpdateTime(DateUtils.getNow());
        entity.setUpdateUser(SecurityUtils.getUsername());
		MultipartFile file = entity.getFile();
		if (file != null && !file.isEmpty()) {
			String originalFilename = file.getOriginalFilename();
			entity.setFileName(originalFilename);
			String filepath = localStorageConfig.putObject(file);
			entity.setFilePath(filepath);
		}
		projectLedgerService.updateById(entity);
	}
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	public ProjectLedger getById(String id) {
		return projectLedgerService.getById(id);
	}
	
}
