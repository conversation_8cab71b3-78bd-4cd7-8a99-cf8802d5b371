package com.yinshu.tcps.manager.impl;

import java.util.Map;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.security.SecurityUtils;
import com.yinshu.tcps.entity.ProjectVehicle;
import com.yinshu.tcps.service.ProjectVehicleService;
import com.yinshu.tcps.manager.ProjectVehicleManager;
import org.springframework.stereotype.Service;
import com.yinshu.utils.SnowflakeIdGenerator;

/**
 * 项目车辆表 
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Service
public class ProjectVehicleManagerImpl implements ProjectVehicleManager {

	@Autowired
	private ProjectVehicleService projectVehicleService;

	@Autowired
	private SnowflakeIdGenerator snowflakeIdGenerator;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<ProjectVehicle> queryList(ProjectVehicle entity) {
		List<ProjectVehicle> resultList = projectVehicleService.queryList(entity);
		return resultList;
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<ProjectVehicle> queryPageList(ProjectVehicle entity) {
		IPage<ProjectVehicle> resultList = projectVehicleService.queryPageList(entity);
		return resultList;
	}
	
	/**
	 * 保存
	 * @param entity
	 */
	public void save(ProjectVehicle entity) {
		entity.setId(snowflakeIdGenerator.nextIdStr());
		entity.setCreatedBy(SecurityUtils.getUsername());
		projectVehicleService.save(entity);
	}
	
	/**
	 * 删除
	 * @param id
	 */
	
	public void remove(String id) {
		projectVehicleService.removeById(id);	
	}
	
	/**
	 * 批量删除
	 * @param idList
	 */
	public void remove(List<String> idList) {
		projectVehicleService.removeByIds(idList);	
	}
	
	/**
	 * 更新
	 * @param entity
	 */
	public void update(ProjectVehicle entity) {
		entity.setUpdatedBy(SecurityUtils.getUsername());
		projectVehicleService.updateById(entity);
	}
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	public ProjectVehicle getById(String id) {
		return projectVehicleService.getById(id);
	}
	
}
