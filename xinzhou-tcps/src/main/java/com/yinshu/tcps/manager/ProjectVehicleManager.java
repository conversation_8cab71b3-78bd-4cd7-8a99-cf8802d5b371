package com.yinshu.tcps.manager;

import java.util.Map;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.ProjectVehicle;

/**
 * 项目车辆表 
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface ProjectVehicleManager {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<ProjectVehicle> queryList(ProjectVehicle entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<ProjectVehicle> queryPageList(ProjectVehicle entity);
	
	/**
	 * 保存
	 * @param entity
	 */
	void save(ProjectVehicle entity);
	
	/**
	 * 删除
	 * @param id
	 */
	
	void remove(String id);
	
	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);
	
	/**
	 * 更新
	 * @param entity
	 */
	void update(ProjectVehicle entity);
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	ProjectVehicle getById(String id);

}

