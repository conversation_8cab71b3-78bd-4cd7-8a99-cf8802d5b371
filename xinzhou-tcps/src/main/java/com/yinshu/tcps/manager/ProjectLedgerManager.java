package com.yinshu.tcps.manager;

import java.util.Map;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.ProjectLedger;

/**
 * 项目台账资料表 
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
public interface ProjectLedgerManager {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<ProjectLedger> queryList(ProjectLedger entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<ProjectLedger> queryPageList(ProjectLedger entity);
	
	/**
	 * 保存
	 * @param entity
	 */
	void save(ProjectLedger entity);
	
	/**
	 * 删除
	 * @param id
	 */
	
	void remove(String id);
	
	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);
	
	/**
	 * 更新
	 * @param entity
	 */
	void update(ProjectLedger entity);
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	ProjectLedger getById(String id);

}

