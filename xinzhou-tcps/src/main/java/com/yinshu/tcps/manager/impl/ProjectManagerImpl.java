package com.yinshu.tcps.manager.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.entity.DictTreeNode;
import com.yinshu.sys.manager.DictionaryManager;
import com.yinshu.sys.security.SecurityUtils;
import com.yinshu.tcps.entity.Project;
import com.yinshu.tcps.manager.ProjectManager;
import com.yinshu.tcps.service.ProjectService;
import com.yinshu.tcps.service.TcpsFileService;
import com.yinshu.tcps.vo.AreaProjectTreeVO;
import com.yinshu.utils.SnowflakeIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工程项目主表
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Service
public class ProjectManagerImpl implements ProjectManager {

    @Resource
    private DictionaryManager dictionaryManager;
    
    @Autowired
    private ProjectService projectService;
    
    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;

    /**
     * @param result      数据
     * @param node        区域信息
     * @param regionGroup 区域项目分组信息
     * <AUTHOR>
     * @description //TODO 获取项目树
     * @date 2025/7/8 16:24
     **/
    private static void buildTree(List<AreaProjectTreeVO> result, DictTreeNode node, Map<String, List<Project>> regionGroup) {
        List<DictTreeNode> children = node.getChildren();
        for (DictTreeNode child : children) {
            AreaProjectTreeVO vo = new AreaProjectTreeVO();
            vo.setId(child.getId());
            vo.setLabel(child.getLabel());
            vo.setType("area");
            vo.setChildren(new ArrayList<>());
            if (CollectionUtils.isEmpty(child.getChildren())) {
                List<Project> projects = regionGroup.get(child.getValue());
                if (!CollectionUtils.isEmpty(projects)) {
                    vo.setChildren(projects.stream().map(project -> {
                        AreaProjectTreeVO childVO = new AreaProjectTreeVO();
                        childVO.setId(project.getId());
                        childVO.setLabel(project.getProjectName());
                        childVO.setType("project");
                        childVO.setChildren(new ArrayList<>());
                        childVO.setAttribute(project);
                        return childVO;
                    }).collect(Collectors.toList()));
                }
            } else {
                buildTree(vo.getChildren(), child, regionGroup);
            }
            result.add(vo);
        }
    }

    /**
     * 条件查询
     *
     * @param entity
     * @return
     */
    public List<Project> queryList(Project entity) {
        List<Project> resultList = projectService.queryList(entity);
        return resultList;
    }

    /**
     * 条件分页查询
     *
     * @param entity
     * @return
     */
    public IPage<Project> queryPageList(Project entity) {
        IPage<Project> resultList = projectService.queryPageList(entity);
        return resultList;
    }

    /**
     * 保存
     *
     * @param entity
     */
    public void save(Project entity) {
        entity.setId(snowflakeIdGenerator.nextIdStr());
        entity.setCreatedBy(SecurityUtils.getUsername());
        projectService.save(entity);
    }

    /**
     * 删除
     *
     * @param id
     */

    public void remove(String id) {
        projectService.removeById(id);
    }

    /**
     * 批量删除
     *
     * @param idList
     */
    public void remove(List<String> idList) {
        projectService.removeByIds(idList);
    }

    /**
     * 更新
     *
     * @param entity
     */
    public void update(Project entity) {
        entity.setUpdatedBy(SecurityUtils.getUsername());
        projectService.updateById(entity);
    }

    /**
     * 根据ID获取对象
     *
     * @param id
     * @return
     */
    public Project getById(String id) {
        return projectService.getById(id);
    }

    /**
     * @param entity 请求参数
     * @return com.yinshu.tcps.entity.Project
     * <AUTHOR>
     * @description //TODO 获取项目树
     * @date 2025/7/8 16:07
     **/
    @Override
    public List<AreaProjectTreeVO> tree(Project entity) {
        // 获取区域信息
        DictTreeNode node = dictionaryManager.treeByDicCode("TCPS_REGION");
        if (node == null) {
            return new ArrayList<>();
        }
        // 获取项目信息
        List<Project> list = queryList(entity);
        Map<String, List<Project>> regionGroup = list.stream().collect(Collectors.groupingBy(Project::getRegion));
        // 构造数据
        List<AreaProjectTreeVO> data = new ArrayList<>();
        buildTree(data, node, regionGroup);
        return data;
    }

    /**
     * @param projectIds 项目id
     * @return java.util.Map<java.lang.String, com.yinshu.tcps.entity.Project>
     * <AUTHOR>
     * @description //TODO 批量获取项目信息
     * @date 2025/7/9 15:58
     **/
    @Override
    public Map<String, Project> getMapByIds(List<String> projectIds) {
        if (!CollectionUtils.isEmpty(projectIds)) {
            List<Project> list = projectService.listByIds(projectIds);
            return list.stream().collect(Collectors.toMap(Project::getId, project -> project, (project1, project2) -> project1));
        }
        return Collections.emptyMap();
    }

    /**
	 * 通过经纬度取得位置（调用大数据接口）
	 * @param lng
	 * @param lat
	 * @return
	 */
    @Override
    public JSONObject getLocationByLngLat(Double lng, Double lat){
        return projectService.getLocationByLngLat(lng, lat);
    }
    
    /**
     * 获取所有附件内容
     */
    public List<Map<String, String>> getArchiveList() {
    	List<Map<String, String>> resultList = projectService.getArchiveList();
    	return resultList;
    }
}
