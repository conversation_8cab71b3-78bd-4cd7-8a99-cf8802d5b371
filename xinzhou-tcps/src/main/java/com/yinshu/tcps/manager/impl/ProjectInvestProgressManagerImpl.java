package com.yinshu.tcps.manager.impl;

import java.util.Map;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.security.SecurityUtils;
import com.yinshu.tcps.entity.ProjectInvestProgress;
import com.yinshu.tcps.service.ProjectInvestProgressService;
import com.yinshu.tcps.manager.ProjectInvestProgressManager;
import org.springframework.stereotype.Service;
import com.yinshu.utils.SnowflakeIdGenerator;
import com.yinshu.utils.DateUtils;

/**
 * 资金投入与建设进度 
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
public class ProjectInvestProgressManagerImpl implements ProjectInvestProgressManager {

	@Autowired
	private ProjectInvestProgressService projectInvestProgressService;

	@Autowired
	private SnowflakeIdGenerator snowflakeIdGenerator;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<ProjectInvestProgress> queryList(ProjectInvestProgress entity) {
		List<ProjectInvestProgress> resultList = projectInvestProgressService.queryList(entity);
		return resultList;
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<ProjectInvestProgress> queryPageList(ProjectInvestProgress entity) {
		IPage<ProjectInvestProgress> resultList = projectInvestProgressService.queryPageList(entity);
		return resultList;
	}
	
	/**
	 * 保存
	 * @param entity
	 */
	public void save(ProjectInvestProgress entity) {
		entity.setId(snowflakeIdGenerator.nextIdStr());
		entity.setCreateUser(SecurityUtils.getUsername());
		entity.setCreateTime(DateUtils.getNow());
		projectInvestProgressService.save(entity);
	}
	
	/**
	 * 删除
	 * @param id
	 */
	
	public void remove(String id) {
		projectInvestProgressService.removeById(id);	
	}
	
	/**
	 * 批量删除
	 * @param idList
	 */
	public void remove(List<String> idList) {
		projectInvestProgressService.removeByIds(idList);	
	}
	
	/**
	 * 更新
	 * @param entity
	 */
	public void update(ProjectInvestProgress entity) {
		entity.setUpdateTime(DateUtils.getNow());
        entity.setUpdateUser(SecurityUtils.getUsername());
		projectInvestProgressService.updateById(entity);
	}
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	public ProjectInvestProgress getById(String id) {
		return projectInvestProgressService.getById(id);
	}
	
}
