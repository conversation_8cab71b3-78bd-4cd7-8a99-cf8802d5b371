package com.yinshu.tcps.manager;

import java.util.Map;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.ProjectInvestProgress;

/**
 * 资金投入与建设进度 
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface ProjectInvestProgressManager {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<ProjectInvestProgress> queryList(ProjectInvestProgress entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<ProjectInvestProgress> queryPageList(ProjectInvestProgress entity);
	
	/**
	 * 保存
	 * @param entity
	 */
	void save(ProjectInvestProgress entity);
	
	/**
	 * 删除
	 * @param id
	 */
	
	void remove(String id);
	
	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);
	
	/**
	 * 更新
	 * @param entity
	 */
	void update(ProjectInvestProgress entity);
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	ProjectInvestProgress getById(String id);

}

