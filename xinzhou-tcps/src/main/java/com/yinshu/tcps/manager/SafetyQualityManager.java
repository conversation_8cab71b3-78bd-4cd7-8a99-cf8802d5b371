package com.yinshu.tcps.manager;

import java.util.Map;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.SafetyQuality;

/**
 * 安全质量检查 
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface SafetyQualityManager {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<SafetyQuality> queryList(SafetyQuality entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<SafetyQuality> queryPageList(SafetyQuality entity);
	
	/**
	 * 保存
	 * @param entity
	 */
	void save(SafetyQuality entity);
	
	/**
	 * 删除
	 * @param id
	 */
	
	void remove(String id);
	
	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);
	
	/**
	 * 更新
	 * @param entity
	 */
	void update(SafetyQuality entity);
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	SafetyQuality getById(String id);

}

