package com.yinshu.tcps.manager.impl;

import java.util.Map;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.security.SecurityUtils;
import com.yinshu.tcps.entity.ProjectCamera;
import com.yinshu.tcps.service.ProjectCameraService;
import com.yinshu.tcps.manager.********************;
import org.springframework.stereotype.Service;
import com.yinshu.utils.SnowflakeIdGenerator;
import com.yinshu.utils.DateUtils;

/**
 * 项目视频监控表 
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Service
public class ********************Impl implements ******************** {

	@Autowired
	private ProjectCameraService ********************;

	@Autowired
	private SnowflakeIdGenerator snowflakeIdGenerator;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<ProjectCamera> queryList(ProjectCamera entity) {
		List<ProjectCamera> resultList = ********************.queryList(entity);
		return resultList;
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<ProjectCamera> queryPageList(ProjectCamera entity) {
		IPage<ProjectCamera> resultList = ********************.queryPageList(entity);
		return resultList;
	}
	
	/**
	 * 保存
	 * @param entity
	 */
	public void save(ProjectCamera entity) {
		entity.setId(snowflakeIdGenerator.nextIdStr());
		entity.setCreateUser(SecurityUtils.getUsername());
		entity.setCreateTime(DateUtils.getNow());
		********************.save(entity);
	}
	
	/**
	 * 删除
	 * @param id
	 */
	
	public void remove(String id) {
		********************.removeById(id);	
	}
	
	/**
	 * 批量删除
	 * @param idList
	 */
	public void remove(List<String> idList) {
		********************.removeByIds(idList);	
	}
	
	/**
	 * 更新
	 * @param entity
	 */
	public void update(ProjectCamera entity) {
		entity.setUpdateUser(SecurityUtils.getUsername());
		entity.setUpdateTime(DateUtils.getNow());
		********************.updateById(entity);
	}
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	public ProjectCamera getById(String id) {
		return ********************.getById(id);
	}
	
}
