package com.yinshu.tcps.manager;

import java.util.Map;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.ProjectCamera;

/**
 * 项目视频监控表 
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
public interface ProjectCameraManager {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<ProjectCamera> queryList(ProjectCamera entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<ProjectCamera> queryPageList(ProjectCamera entity);
	
	/**
	 * 保存
	 * @param entity
	 */
	void save(ProjectCamera entity);
	
	/**
	 * 删除
	 * @param id
	 */
	
	void remove(String id);
	
	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);
	
	/**
	 * 更新
	 * @param entity
	 */
	void update(ProjectCamera entity);
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	ProjectCamera getById(String id);

}

