package com.yinshu.tcps.manager.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.service.FileUploadService;
import com.yinshu.tcps.entity.SafetyQuality;
import com.yinshu.tcps.manager.SafetyQualityManager;
import com.yinshu.tcps.service.SafetyQualityService;
import com.yinshu.utils.DateUtils;
import com.yinshu.utils.SnowflakeIdGenerator;

/**
 * 安全质量检查 
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Service
public class SafetyQualityManagerImpl implements SafetyQualityManager {
	
	@Autowired
	private FileUploadService fileUploadService;

	@Autowired
	private SafetyQualityService safetyQualityService;

	@Autowired
	private SnowflakeIdGenerator snowflakeIdGenerator;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<SafetyQuality> queryList(SafetyQuality entity) {
		List<SafetyQuality> resultList = safetyQualityService.queryList(entity);
		return resultList;
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<SafetyQuality> queryPageList(SafetyQuality entity) {
		IPage<SafetyQuality> resultList = safetyQualityService.queryPageList(entity);
		return resultList;
	}
	
	/**
	 * 保存
	 * @param entity
	 */
	public void save(SafetyQuality entity) {
		entity.setId(snowflakeIdGenerator.nextIdStr());
		entity.setCreateTime(DateUtils.getNow());
		safetyQualityService.save(entity);
		fileUploadService.saveBatchAll(entity.getId(), entity.getFileList());
		fileUploadService.saveBatchAll(entity.getId(), entity.getVideoList());
	}
	
	/**
	 * 删除
	 * @param id
	 */
	
	public void remove(String id) {
		safetyQualityService.removeById(id);	
	}
	
	/**
	 * 批量删除
	 * @param idList
	 */
	public void remove(List<String> idList) {
		safetyQualityService.removeByIds(idList);	
		fileUploadService.removeByFids(idList.toArray(new String[0]));
	}
	
	/**
	 * 更新
	 * @param entity
	 */
	public void update(SafetyQuality entity) {
		entity.setUpdateTime(DateUtils.getNow());
		safetyQualityService.updateById(entity);
		fileUploadService.updateBatchAll(entity.getId(), "1", entity.getFileList());
		fileUploadService.updateBatchAll(entity.getId(), "2", entity.getVideoList());
	}
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	public SafetyQuality getById(String id) {
		return safetyQualityService.getById(id);
	}
	
}
