package com.yinshu.tcps.manager.impl;

import java.util.Map;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.security.SecurityUtils;
import com.yinshu.tcps.config.TcpsLocalStorageConfig;
import com.yinshu.tcps.entity.ProjectPermit;
import com.yinshu.tcps.service.ProjectPermitService;
import com.yinshu.tcps.manager.ProjectPermitManager;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.yinshu.utils.SnowflakeIdGenerator;
import com.yinshu.utils.DateUtils;

/**
 * 项目许可及证书 
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Service
public class ProjectPermitManagerImpl implements ProjectPermitManager {

	@Autowired
	private ProjectPermitService projectPermitService;

	@Autowired
	private SnowflakeIdGenerator snowflakeIdGenerator;
	
	@Autowired
	private TcpsLocalStorageConfig localStorageConfig;
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<ProjectPermit> queryList(ProjectPermit entity) {
		List<ProjectPermit> resultList = projectPermitService.queryList(entity);
		return resultList;
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<ProjectPermit> queryPageList(ProjectPermit entity) {
		IPage<ProjectPermit> resultList = projectPermitService.queryPageList(entity);
		return resultList;
	}
	
	/**
	 * 保存
	 * @param entity
	 */
	public void save(ProjectPermit entity) {
		entity.setId(snowflakeIdGenerator.nextIdStr());
		entity.setCreateUser(SecurityUtils.getUsername());
		entity.setCreateTime(DateUtils.getNow());
		MultipartFile file = entity.getFile();
		if (file != null && !file.isEmpty()) {
			String originalFilename = file.getOriginalFilename();
			entity.setFileName(originalFilename);
			String filepath = localStorageConfig.putObject(file);
			entity.setFilePath(filepath);
		}
		projectPermitService.save(entity);
	}
	
	/**
	 * 删除
	 * @param id
	 */
	
	public void remove(String id) {
		projectPermitService.removeById(id);	
	}
	
	/**
	 * 批量删除
	 * @param idList
	 */
	public void remove(List<String> idList) {
		projectPermitService.removeByIds(idList);	
	}
	
	/**
	 * 更新
	 * @param entity
	 */
	public void update(ProjectPermit entity) {
		entity.setUpdateUser(SecurityUtils.getUsername());
		entity.setUpdateTime(DateUtils.getNow());
		MultipartFile file = entity.getFile();
		if (file != null && !file.isEmpty()) {
			String originalFilename = file.getOriginalFilename();
			entity.setFileName(originalFilename);
			String filepath = localStorageConfig.putObject(file);
			entity.setFilePath(filepath);
		}
		projectPermitService.updateById(entity);
	}
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	public ProjectPermit getById(String id) {
		return projectPermitService.getById(id);
	}
	
}
