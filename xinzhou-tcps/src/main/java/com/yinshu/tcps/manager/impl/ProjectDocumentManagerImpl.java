package com.yinshu.tcps.manager.impl;

import java.util.Map;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.ProjectDocument;
import com.yinshu.tcps.service.ProjectDocumentService;
import com.yinshu.tcps.manager.ProjectDocumentManager;
import org.springframework.stereotype.Service;
import com.yinshu.utils.SnowflakeIdGenerator;
import com.yinshu.utils.DateUtils;
import com.yinshu.sys.security.SecurityUtils;
import com.yinshu.tcps.config.TcpsLocalStorageConfig;
import org.springframework.web.multipart.MultipartFile;

/**
 * 项目资料表 
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Service
public class ProjectDocumentManagerImpl implements ProjectDocumentManager {

	@Autowired
	private ProjectDocumentService projectDocumentService;

	@Autowired
	private SnowflakeIdGenerator snowflakeIdGenerator;
	
	@Autowired
	private TcpsLocalStorageConfig localStorageConfig;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<ProjectDocument> queryList(ProjectDocument entity) {
		List<ProjectDocument> resultList = projectDocumentService.queryList(entity);
		return resultList;
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<ProjectDocument> queryPageList(ProjectDocument entity) {
		IPage<ProjectDocument> resultList = projectDocumentService.queryPageList(entity);
		return resultList;
	}
	
	/**
	 * 保存
	 * @param entity
	 */
	public void save(ProjectDocument entity) {
		entity.setId(snowflakeIdGenerator.nextIdStr());
		entity.setCreateUser(SecurityUtils.getUsername());
		entity.setCreateTime(DateUtils.getNow());
		MultipartFile file = entity.getFile();
		if (file != null && !file.isEmpty()) {
			String originalFilename = file.getOriginalFilename();
			entity.setFileName(originalFilename);
			String filepath = localStorageConfig.putObject(file);
			entity.setFilePath(filepath);
		}
		projectDocumentService.save(entity);
	}
	
	/**
	 * 删除
	 * @param id
	 */
	
	public void remove(String id) {
		projectDocumentService.removeById(id);	
	}
	
	/**
	 * 批量删除
	 * @param idList
	 */
	public void remove(List<String> idList) {
		projectDocumentService.removeByIds(idList);	
	}
	
	/**
	 * 更新
	 * @param entity
	 */
	public void update(ProjectDocument entity) {
		entity.setUpdateUser(SecurityUtils.getUsername());
		entity.setUpdateTime(DateUtils.getNow());
		MultipartFile file = entity.getFile();
		if (file != null && !file.isEmpty()) {
			String originalFilename = file.getOriginalFilename();
			entity.setFileName(originalFilename);
			String filepath = localStorageConfig.putObject(file);
			entity.setFilePath(filepath);
		}
		projectDocumentService.updateById(entity);
	}
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	public ProjectDocument getById(String id) {
		return projectDocumentService.getById(id);
	}
	
}
