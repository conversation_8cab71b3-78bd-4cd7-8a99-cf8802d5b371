package com.yinshu.tcps.manager;

import java.util.Map;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.ProjectPersonnel;

/**
 * 项目人员表 
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
public interface ProjectPersonnelManager {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<ProjectPersonnel> queryList(ProjectPersonnel entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<ProjectPersonnel> queryPageList(ProjectPersonnel entity);
	
	/**
	 * 保存
	 * @param entity
	 */
	void save(ProjectPersonnel entity);
	
	/**
	 * 删除
	 * @param id
	 */
	
	void remove(String id);
	
	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);
	
	/**
	 * 更新
	 * @param entity
	 */
	void update(ProjectPersonnel entity);
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	ProjectPersonnel getById(String id);

}

