package com.yinshu.tcps.manager;

import java.util.Map;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.RecordSupervision;

/**
 * 项目监理管理表 
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface RecordSupervisionManager {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<RecordSupervision> queryList(RecordSupervision entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<RecordSupervision> queryPageList(RecordSupervision entity);
	
	/**
	 * 保存
	 * @param entity
	 */
	void save(RecordSupervision entity);
	
	/**
	 * 删除
	 * @param id
	 */
	
	void remove(String id);
	
	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);
	
	/**
	 * 更新
	 * @param entity
	 */
	void update(RecordSupervision entity);
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	RecordSupervision getById(String id);

}

