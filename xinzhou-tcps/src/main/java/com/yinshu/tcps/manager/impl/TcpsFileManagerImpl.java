package com.yinshu.tcps.manager.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.config.TcpsLocalStorageConfig;
import com.yinshu.tcps.entity.TcpsFile;
import com.yinshu.tcps.manager.TcpsFileManager;
import com.yinshu.tcps.service.TcpsFileService;
import com.yinshu.utils.SnowflakeIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 管理现场记录文件表
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Service
public class TcpsFileManagerImpl implements TcpsFileManager {

    @Autowired
    private TcpsFileService fileService;

    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;

    @Autowired
    private TcpsLocalStorageConfig localStorageConfig;

    /**
     * 条件查询
     *
     * @param entity
     * @return
     */
    public List<TcpsFile> queryList(TcpsFile entity) {
        List<TcpsFile> resultList = fileService.queryList(entity);
        return resultList;
    }

    /**
     * 条件分页查询
     *
     * @param entity
     * @return
     */
    public IPage<TcpsFile> queryPageList(TcpsFile entity) {
        IPage<TcpsFile> resultList = fileService.queryPageList(entity);
        return resultList;
    }

    /**
     * 保存
     *
     * @param entity
     */
    public void save(TcpsFile entity) {
        entity.setId(snowflakeIdGenerator.nextIdStr());
        // 文件保存逻辑
        if (entity.getFile() != null && !entity.getFile().isEmpty()) {
            String originalFilename = entity.getFile().getOriginalFilename();
            entity.setFileName(originalFilename);
            String fileType = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                fileType = originalFilename.substring(originalFilename.lastIndexOf('.') + 1).toUpperCase();
            }
            entity.setFileType(fileType);
            String filepath = localStorageConfig.putObject(entity.getFile());
            entity.setFilePath(filepath);
        }
        fileService.save(entity);
    }

    /**
     * 删除
     *
     * @param id
     */

    public void remove(String id) {
        fileService.removeById(id);
    }

    /**
     * 批量删除
     *
     * @param idList
     */
    public void remove(List<String> idList) {
        fileService.removeByIds(idList);
    }

    /**
     * 更新
     *
     * @param entity
     */
    public void update(TcpsFile entity) {
        // 文件保存逻辑
        if (entity.getFile() != null && !entity.getFile().isEmpty()) {
            String originalFilename = entity.getFile().getOriginalFilename();
            entity.setFileName(originalFilename);
            String fileType = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                fileType = originalFilename.substring(originalFilename.lastIndexOf('.') + 1).toUpperCase();
            }
            entity.setFileType(fileType);
            String filepath = localStorageConfig.putObject(entity.getFile());
            entity.setFilePath(filepath);
        }
        fileService.updateById(entity);
    }

    /**
     * 根据ID获取对象
     *
     * @param id
     * @return
     */
    public TcpsFile getById(String id) {
        return fileService.getById(id);
    }

    /**
     * 下载文件
     *
     * @param fileName
     * @param response
     */
    public void downloadFile(String fileName, HttpServletResponse response) {
        localStorageConfig.downloadFile(fileName, response);
    }

    /**
     * @param file 文件
     * @param info 业务信息
     * @return com.yinshu.tcps.entity.TcpsFile
     * <AUTHOR>
     * @description //TODO 文件上传
     * @date 2025/7/9 17:36
     **/
    @Override
    public TcpsFile upload(MultipartFile file, TcpsFile info) {
        info.setFile(file);
        save(info);
        return info;
    }
}
