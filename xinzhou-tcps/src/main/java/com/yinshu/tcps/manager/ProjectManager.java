package com.yinshu.tcps.manager;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.Project;
import com.yinshu.tcps.vo.AreaProjectTreeVO;

import java.util.List;
import java.util.Map;
import com.alibaba.fastjson.JSONObject;
/**
 * 工程项目主表
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface ProjectManager {

    /**
     * 条件查询
     *
     * @param entity
     * @return
     */
    List<Project> queryList(Project entity);

    /**
     * 条件分页查询
     *
     * @param entity
     * @return
     */
    IPage<Project> queryPageList(Project entity);

    /**
     * 保存
     *
     * @param entity
     */
    void save(Project entity);

    /**
     * 删除
     *
     * @param id
     */

    void remove(String id);

    /**
     * 批量删除
     *
     * @param idList
     */
    void remove(List<String> idList);

    /**
     * 更新
     *
     * @param entity
     */
    void update(Project entity);

    /**
     * 根据ID获取对象
     *
     * @param id
     * @return
     */
    Project getById(String id);

    /**
     * @param entity 请求参数
     * @return com.yinshu.tcps.entity.Project
     * <AUTHOR>
     * @description //TODO 获取项目树
     * @date 2025/7/8 16:07
     **/
    List<AreaProjectTreeVO> tree(Project entity);

    /**
     * <AUTHOR>
     * @description //TODO 批量获取项目信息
     * @date 2025/7/9 15:58
     * @param projectIds 项目id
     * @return java.util.Map<java.lang.String,com.yinshu.tcps.entity.Project>
     **/
    Map<String, Project> getMapByIds(List<String> projectIds);

   /**
	 * 通过经纬度取得位置（调用大数据接口）
	 * @param lng
	 * @param lat
	 * @return
	 */
	JSONObject getLocationByLngLat(Double lng, Double lat);
	
    /**
     * 获取所有附件内容
     */
	 List<Map<String, String>> getArchiveList();
}

