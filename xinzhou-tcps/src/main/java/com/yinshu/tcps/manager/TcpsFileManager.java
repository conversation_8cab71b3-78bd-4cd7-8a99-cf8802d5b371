package com.yinshu.tcps.manager;

import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.TcpsFile;
import org.springframework.web.multipart.MultipartFile;

/**
 * 管理现场记录文件表 
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
public interface TcpsFileManager {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<TcpsFile> queryList(TcpsFile entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<TcpsFile> queryPageList(TcpsFile entity);
	
	/**
	 * 保存
	 * @param entity
	 */
	void save(TcpsFile entity);
	
	/**
	 * 删除
	 * @param id
	 */
	
	void remove(String id);
	
	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);
	
	/**
	 * 更新
	 * @param entity
	 */
	void update(TcpsFile entity);
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	TcpsFile getById(String id);

	/**
	 * 下载文件 
	 * @param fileName
	 * @param response
	 */
	void downloadFile(String fileName, HttpServletResponse response);

	/**
	 * <AUTHOR>
	 * @description //TODO 文件上传
	 * @date 2025/7/9 17:36
	 * @param file 文件
	 * @param info 业务信息
	 * @return com.yinshu.tcps.entity.TcpsFile
	 **/
    TcpsFile upload(MultipartFile file, TcpsFile info);
}

