package com.yinshu.tcps.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.exception.RestfulAPIException;
import com.yinshu.sys.entity.Dictionary;
import com.yinshu.sys.manager.DictionaryManager;
import com.yinshu.sys.service.FileUploadService;
import com.yinshu.tcps.entity.Project;
import com.yinshu.tcps.entity.RecordSupervision;
import com.yinshu.tcps.manager.ProjectManager;
import com.yinshu.tcps.manager.RecordSupervisionManager;
import com.yinshu.tcps.service.RecordSupervisionService;
import com.yinshu.utils.SnowflakeIdGenerator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 项目监理管理表
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Service
public class RecordSupervisionManagerImpl implements RecordSupervisionManager {

    @Autowired
    private RecordSupervisionService recordSupervisionService;

    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;

    @Resource
    private ProjectManager projectManager;

    @Resource
    private DictionaryManager dictionaryManager;

    @Resource
    private FileUploadService fileUploadService;

    /**
     * 条件查询
     *
     * @param entity
     * @return
     */
    public List<RecordSupervision> queryList(RecordSupervision entity) {
        List<RecordSupervision> resultList = recordSupervisionService.queryList(entity);
        batchSetName(resultList);
        return resultList;
    }

    public void checkData(RecordSupervision entity) {
        if (StringUtils.isBlank(entity.getProjectId())) {
            throw new RestfulAPIException("项目名称不能为空");
        }
        if (StringUtils.isBlank(entity.getWorkMode())) {
            throw new RestfulAPIException("工作方式不能为空");
        }
        if (StringUtils.isBlank(entity.getInspectionContent())) {
            throw new RestfulAPIException("检查内容不能为空");
        }
        if (recordSupervisionService.count(new LambdaQueryWrapper<RecordSupervision>()
                .ne(StringUtils.isNotBlank(entity.getId()), RecordSupervision::getId, entity.getId())
                .eq(RecordSupervision::getSupervisorName, entity.getSupervisorName())
                .eq(RecordSupervision::getProjectId, entity.getProjectId())
        ) > 0) {
            throw new RestfulAPIException("项目名称不能重复");
        }
    }

    /**
     * 条件分页查询
     *
     * @param entity
     * @return
     */
    public IPage<RecordSupervision> queryPageList(RecordSupervision entity) {
        IPage<RecordSupervision> resultList = recordSupervisionService.queryPageList(entity);
        List<RecordSupervision> records = resultList.getRecords();
        batchSetName(records);
        return resultList;
    }

    /**
     * @param records 项目监理信息列表
     * <AUTHOR>
     * @description //TODO 批量设置名称
     * @date 2025/7/9 16:03
     **/
    private void batchSetName(List<RecordSupervision> records) {
        Map<String, Project> projectMap = projectManager.getMapByIds(records.stream().map(RecordSupervision::getProjectId).collect(Collectors.toList()));
        Map<String, Dictionary> dictionaryMap = dictionaryManager.getMapByIds(records.stream().map(RecordSupervision::getWorkMode).collect(Collectors.toList()));
        records.forEach(item -> {
            if (projectMap.containsKey(item.getProjectId())) {
                item.setProjectName(projectMap.get(item.getProjectId()).getProjectName());
            }
            if (dictionaryMap.containsKey(item.getWorkMode())) {
                item.setWorkModeName(dictionaryMap.get(item.getWorkMode()).getDicName());
            }
        });
    }

    /**
     * 保存
     *
     * @param entity
     */
    public void save(RecordSupervision entity) {
        entity.setRecordTime(new Date());
        checkData(entity);
        entity.setId(snowflakeIdGenerator.nextIdStr());
        fileUploadService.saveBatchAll(entity.getId(), entity.getFileList());
        fileUploadService.saveBatchAll(entity.getId(), entity.getVideoList());
        recordSupervisionService.save(entity);
    }

    /**
     * 删除
     *
     * @param id
     */

    public void remove(String id) {
        recordSupervisionService.removeById(id);
    }

    /**
     * 批量删除
     *
     * @param idList
     */
    public void remove(List<String> idList) {
        fileUploadService.removeByFids(idList.toArray(new String[0]));
        recordSupervisionService.removeByIds(idList);
    }

    /**
     * 更新
     *
     * @param entity
     */
    public void update(RecordSupervision entity) {
        checkData(entity);
        recordSupervisionService.updateById(entity);
        fileUploadService.updateBatchAll(entity.getId(), "1", entity.getFileList());
        fileUploadService.updateBatchAll(entity.getId(), "2", entity.getVideoList());
    }

    /**
     * 根据ID获取对象
     *
     * @param id
     * @return
     */
    public RecordSupervision getById(String id) {
        return recordSupervisionService.getById(id);
    }

}
