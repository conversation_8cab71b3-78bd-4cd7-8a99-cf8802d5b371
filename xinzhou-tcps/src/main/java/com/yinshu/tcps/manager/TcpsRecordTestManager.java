package com.yinshu.tcps.manager;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.TcpsRecordTest;

/**
 * 试验检测管理表
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface TcpsRecordTestManager {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<TcpsRecordTest> queryList(TcpsRecordTest entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<TcpsRecordTest> queryPageList(TcpsRecordTest entity);
	
	/**
	 * 保存
	 * @param entity
	 */
	void save(TcpsRecordTest entity);
	
	/**
	 * 删除
	 * @param id
	 */
	void remove(String id);
	
	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);
	
	/**
	 * 更新
	 * @param entity
	 */
	void update(TcpsRecordTest entity);
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	TcpsRecordTest getById(String id);

}
