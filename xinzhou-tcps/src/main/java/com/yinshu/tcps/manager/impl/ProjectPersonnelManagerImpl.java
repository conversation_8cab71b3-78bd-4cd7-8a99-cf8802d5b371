package com.yinshu.tcps.manager.impl;

import java.util.Map;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.security.SecurityUtils;
import com.yinshu.tcps.entity.ProjectPersonnel;
import com.yinshu.tcps.service.ProjectPersonnelService;
import com.yinshu.tcps.manager.ProjectPersonnelManager;
import org.springframework.stereotype.Service;
import com.yinshu.utils.SnowflakeIdGenerator;
import com.yinshu.utils.DateUtils;

/**
 * 项目人员表 
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Service
public class ProjectPersonnelManagerImpl implements ProjectPersonnelManager {

	@Autowired
	private ProjectPersonnelService projectPersonnelService;

	@Autowired
	private SnowflakeIdGenerator snowflakeIdGenerator;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<ProjectPersonnel> queryList(ProjectPersonnel entity) {
		List<ProjectPersonnel> resultList = projectPersonnelService.queryList(entity);
		return resultList;
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<ProjectPersonnel> queryPageList(ProjectPersonnel entity) {
		IPage<ProjectPersonnel> resultList = projectPersonnelService.queryPageList(entity);
		return resultList;
	}
	
	/**
	 * 保存
	 * @param entity
	 */
	public void save(ProjectPersonnel entity) {
		entity.setId(snowflakeIdGenerator.nextIdStr());
		entity.setCreatedBy(SecurityUtils.getUsername());
		entity.setCreateTime(DateUtils.getNow());
		projectPersonnelService.save(entity);
	}
	
	/**
	 * 删除
	 * @param id
	 */
	
	public void remove(String id) {
		projectPersonnelService.removeById(id);	
	}
	
	/**
	 * 批量删除
	 * @param idList
	 */
	public void remove(List<String> idList) {
		projectPersonnelService.removeByIds(idList);	
	}
	
	/**
	 * 更新
	 * @param entity
	 */
	public void update(ProjectPersonnel entity) {
		entity.setUpdateTime(DateUtils.getNow());
		entity.setUpdateUser(SecurityUtils.getUsername());
		projectPersonnelService.updateById(entity);
	}
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	public ProjectPersonnel getById(String id) {
		return projectPersonnelService.getById(id);
	}
	
}
