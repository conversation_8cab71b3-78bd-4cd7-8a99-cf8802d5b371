package com.yinshu.tcps.manager.impl;

import java.util.Date;
import java.util.List;

import com.yinshu.sys.service.FileUploadService;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.TcpsRecordTest;
import com.yinshu.tcps.service.TcpsRecordTestService;
import com.yinshu.tcps.manager.TcpsRecordTestManager;
import com.yinshu.utils.SnowflakeIdGenerator;
import org.springframework.stereotype.Service;

/**
 * 试验检测管理表
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Service
public class TcpsRecordTestManagerImpl implements TcpsRecordTestManager {

	@Autowired
	private TcpsRecordTestService tcpsRecordTestService;

	@Autowired
	private SnowflakeIdGenerator snowflakeIdGenerator;

	@Autowired
	private FileUploadService fileUploadService;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<TcpsRecordTest> queryList(TcpsRecordTest entity) {
		List<TcpsRecordTest> resultList = tcpsRecordTestService.queryList(entity);
		return resultList;
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<TcpsRecordTest> queryPageList(TcpsRecordTest entity) {
		IPage<TcpsRecordTest> resultList = tcpsRecordTestService.queryPageList(entity);
		return resultList;
	}
	
	/**
	 * 保存
	 * @param entity
	 */
	public void save(TcpsRecordTest entity) {
		entity.setId(snowflakeIdGenerator.nextIdStr());
		entity.setRecordTime(new Date());
		entity.setCreatedAt(new Date());
		fileUploadService.saveBatchAll(entity.getId(), entity.getFileList());
		fileUploadService.saveBatchAll(entity.getId(), entity.getVideoList());
		tcpsRecordTestService.save(entity);
	}
	
	/**
	 * 删除
	 * @param id
	 */
	public void remove(String id) {
		tcpsRecordTestService.removeById(id);	
	}
	
	/**
	 * 批量删除
	 * @param idList
	 */
	public void remove(List<String> idList) {
		tcpsRecordTestService.removeByIds(idList);
		fileUploadService.removeByFids(idList.toArray(new String[0]));
	}
	
	/**
	 * 更新
	 * @param entity
	 */
	public void update(TcpsRecordTest entity) {
		tcpsRecordTestService.updateById(entity);
		fileUploadService.updateBatchAll(entity.getId(), "1", entity.getFileList());
		fileUploadService.updateBatchAll(entity.getId(), "2", entity.getVideoList());
	}
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	public TcpsRecordTest getById(String id) {
		return tcpsRecordTestService.getById(id);
	}
}
