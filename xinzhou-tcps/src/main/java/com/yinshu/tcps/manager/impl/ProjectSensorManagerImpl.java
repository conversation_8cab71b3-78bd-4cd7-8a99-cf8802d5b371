package com.yinshu.tcps.manager.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.sys.security.SecurityUtils;
import com.yinshu.tcps.entity.ProjectSensor;
import com.yinshu.tcps.service.ProjectSensorService;
import com.yinshu.tcps.manager.ProjectSensorManager;
import org.springframework.stereotype.Service;
import com.yinshu.utils.SnowflakeIdGenerator;

import com.yinshu.utils.DateUtils;

/**
 * 项目传感器表 
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Service
public class ProjectSensorManagerImpl implements ProjectSensorManager {

	@Autowired
	private ProjectSensorService projectSensorService;

	@Autowired
	private SnowflakeIdGenerator snowflakeIdGenerator;
	
	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	public List<ProjectSensor> queryList(ProjectSensor entity) {
		List<ProjectSensor> resultList = projectSensorService.queryList(entity);
		return resultList;
	}
	
	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	public IPage<ProjectSensor> queryPageList(ProjectSensor entity) {
		IPage<ProjectSensor> resultList = projectSensorService.queryPageList(entity);
		return resultList;
	}
	
	/**
	 * 保存
	 * @param entity
	 */
	public void save(ProjectSensor entity) {
		entity.setId(snowflakeIdGenerator.nextIdStr());
		entity.setCreateUser(SecurityUtils.getUsername());
		entity.setCreateTime(DateUtils.getNow());
		projectSensorService.save(entity);
	}
	
	/**
	 * 删除
	 * @param id
	 */
	
	public void remove(String id) {
		projectSensorService.removeById(id);	
	}
	
	/**
	 * 批量删除
	 * @param idList
	 */
	public void remove(List<String> idList) {
		projectSensorService.removeByIds(idList);	
	}
	
	/**
	 * 更新
	 * @param entity
	 */
	public void update(ProjectSensor entity) {
		entity.setUpdateUser(SecurityUtils.getUsername());
		entity.setUpdateTime(DateUtils.getNow());
		projectSensorService.updateById(entity);
	}
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	public ProjectSensor getById(String id) {
		return projectSensorService.getById(id);
	}
	
}
