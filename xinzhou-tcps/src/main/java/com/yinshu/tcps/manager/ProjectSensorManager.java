package com.yinshu.tcps.manager;

import java.util.Map;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.ProjectSensor;

/**
 * 项目传感器表 
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
public interface ProjectSensorManager {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<ProjectSensor> queryList(ProjectSensor entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<ProjectSensor> queryPageList(ProjectSensor entity);
	
	/**
	 * 保存
	 * @param entity
	 */
	void save(ProjectSensor entity);
	
	/**
	 * 删除
	 * @param id
	 */
	
	void remove(String id);
	
	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);
	
	/**
	 * 更新
	 * @param entity
	 */
	void update(ProjectSensor entity);
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	ProjectSensor getById(String id);

}

