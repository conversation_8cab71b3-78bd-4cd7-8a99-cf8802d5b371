package com.yinshu.tcps.manager;

import java.util.Map;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinshu.tcps.entity.ProjectPermit;

/**
 * 项目许可及证书 
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
public interface ProjectPermitManager {

	/**
	 * 条件查询
	 * @param entity
	 * @return
	 */
	List<ProjectPermit> queryList(ProjectPermit entity);

	/**
	 * 条件分页查询
	 * @param entity
	 * @return
	 */
	IPage<ProjectPermit> queryPageList(ProjectPermit entity);
	
	/**
	 * 保存
	 * @param entity
	 */
	void save(ProjectPermit entity);
	
	/**
	 * 删除
	 * @param id
	 */
	
	void remove(String id);
	
	/**
	 * 批量删除
	 * @param idList
	 */
	void remove(List<String> idList);
	
	/**
	 * 更新
	 * @param entity
	 */
	void update(ProjectPermit entity);
	
	/**
	 * 根据ID获取对象
	 * @param id
	 * @return
	 */
	ProjectPermit getById(String id);

}

